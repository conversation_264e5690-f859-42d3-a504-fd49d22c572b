#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  McpError,
  ReadResourceRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import { config } from 'dotenv';

// Load environment variables
config();

// Import tools and resources
import { EBaaSApiTool } from './tools/api-tool.js';
import { EBaaSGeneratorTool } from './tools/generator-tool.js';
import { EBaaSDocumentationTool } from './tools/documentation-tool.js';
import { EBaaSAnalyticsTool } from './tools/analytics-tool.js';

import { EBaaSConfigResource } from './resources/config-resource.js';
import { EBaaSSchemaResource } from './resources/schema-resource.js';
import { EBaaSTemplateResource } from './resources/template-resource.js';

class EBaaSMCPServer {
  private server: Server;
  private tools: Map<string, any> = new Map();
  private resources: Map<string, any> = new Map();

  constructor() {
    this.server = new Server(
      {
        name: 'e-baas-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          resources: {},
        },
      }
    );

    this.setupTools();
    this.setupResources();
    this.setupHandlers();
  }

  private setupTools(): void {
    // Initialize tools
    const apiTool = new EBaaSApiTool();
    const generatorTool = new EBaaSGeneratorTool();
    const docTool = new EBaaSDocumentationTool();
    const analyticsTool = new EBaaSAnalyticsTool();

    // Register tools
    this.tools.set('ebaas_api_call', apiTool);
    this.tools.set('ebaas_generate_code', generatorTool);
    this.tools.set('ebaas_generate_docs', docTool);
    this.tools.set('ebaas_get_analytics', analyticsTool);
  }

  private setupResources(): void {
    // Initialize resources
    const configResource = new EBaaSConfigResource();
    const schemaResource = new EBaaSSchemaResource();
    const templateResource = new EBaaSTemplateResource();

    // Register resources
    this.resources.set('config', configResource);
    this.resources.set('schema', schemaResource);
    this.resources.set('templates', templateResource);
  }

  private setupHandlers(): void {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'ebaas_api_call',
            description: 'Make API calls to E-BaaS backend services',
            inputSchema: {
              type: 'object',
              properties: {
                endpoint: {
                  type: 'string',
                  description: 'API endpoint path',
                },
                method: {
                  type: 'string',
                  enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
                  description: 'HTTP method',
                },
                data: {
                  type: 'object',
                  description: 'Request body data',
                },
                headers: {
                  type: 'object',
                  description: 'Additional headers',
                },
              },
              required: ['endpoint', 'method'],
            },
          },
          {
            name: 'ebaas_generate_code',
            description: 'Generate code templates for E-BaaS modules',
            inputSchema: {
              type: 'object',
              properties: {
                type: {
                  type: 'string',
                  enum: ['controller', 'service', 'entity', 'dto', 'usecase', 'migration'],
                  description: 'Type of code to generate',
                },
                name: {
                  type: 'string',
                  description: 'Name of the module/component',
                },
                options: {
                  type: 'object',
                  description: 'Additional generation options',
                },
              },
              required: ['type', 'name'],
            },
          },
          {
            name: 'ebaas_generate_docs',
            description: 'Generate documentation for E-BaaS APIs and modules',
            inputSchema: {
              type: 'object',
              properties: {
                type: {
                  type: 'string',
                  enum: ['api', 'module', 'endpoint', 'schema'],
                  description: 'Type of documentation to generate',
                },
                target: {
                  type: 'string',
                  description: 'Target module or endpoint',
                },
                format: {
                  type: 'string',
                  enum: ['markdown', 'openapi', 'postman'],
                  description: 'Documentation format',
                },
              },
              required: ['type', 'target'],
            },
          },
          {
            name: 'ebaas_get_analytics',
            description: 'Get analytics and metrics from E-BaaS services',
            inputSchema: {
              type: 'object',
              properties: {
                metric: {
                  type: 'string',
                  enum: ['usage', 'performance', 'errors', 'storage', 'auth'],
                  description: 'Type of analytics to retrieve',
                },
                timeframe: {
                  type: 'string',
                  enum: ['1h', '24h', '7d', '30d'],
                  description: 'Time period for analytics',
                },
                filters: {
                  type: 'object',
                  description: 'Additional filters for analytics',
                },
              },
              required: ['metric'],
            },
          },
        ],
      };
    });

    // List available resources
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      return {
        resources: [
          {
            uri: 'ebaas://config/server',
            name: 'E-BaaS Server Configuration',
            description: 'Server configuration settings and environment variables',
            mimeType: 'application/json',
          },
          {
            uri: 'ebaas://config/database',
            name: 'E-BaaS Database Configuration',
            description: 'Database connection and migration settings',
            mimeType: 'application/json',
          },
          {
            uri: 'ebaas://schema/auth',
            name: 'Authentication Schema',
            description: 'Authentication module database schema',
            mimeType: 'application/json',
          },
          {
            uri: 'ebaas://schema/storage',
            name: 'Storage Schema',
            description: 'Storage module database schema',
            mimeType: 'application/json',
          },
          {
            uri: 'ebaas://templates/controller',
            name: 'Controller Templates',
            description: 'Code templates for generating controllers',
            mimeType: 'text/plain',
          },
          {
            uri: 'ebaas://templates/service',
            name: 'Service Templates',
            description: 'Code templates for generating services',
            mimeType: 'text/plain',
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      if (!this.tools.has(name)) {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${name}`
        );
      }

      try {
        const tool = this.tools.get(name);
        const result = await tool.execute(args);

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      } catch (error) {
        throw new McpError(
          ErrorCode.InternalError,
          `Tool execution failed: ${error instanceof Error ? error.message : String(error)}`
        );
      }
    });

    // Handle resource reads
    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const { uri } = request.params;
      
      // Parse URI: ebaas://type/name
      const match = uri.match(/^ebaas:\/\/([^\/]+)\/(.+)$/);
      if (!match) {
        throw new McpError(
          ErrorCode.InvalidRequest,
          `Invalid resource URI: ${uri}`
        );
      }

      const [, type, name] = match;

      if (!this.resources.has(type)) {
        throw new McpError(
          ErrorCode.InvalidRequest,
          `Unknown resource type: ${type}`
        );
      }

      try {
        const resource = this.resources.get(type);
        const content = await resource.read(name);

        return {
          contents: [
            {
              uri,
              mimeType: content.mimeType || 'application/json',
              text: content.text,
            },
          ],
        };
      } catch (error) {
        throw new McpError(
          ErrorCode.InternalError,
          `Resource read failed: ${error instanceof Error ? error.message : String(error)}`
        );
      }
    });
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('E-BaaS MCP Server running on stdio');
  }
}

// Start the server
const server = new EBaaSMCPServer();
server.run().catch(console.error);