{"name": "@e-baas/mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for E-BaaS - AI development assistant", "type": "module", "main": "dist/index.js", "bin": {"ebaas-mcp": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "keywords": ["mcp", "model-context-protocol", "e-baas", "ai", "llm", "development-assistant"], "author": "E-BaaS Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.6.0", "commander": "^12.0.0", "dotenv": "^16.4.5", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.24", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "tsx": "^4.7.1", "typescript": "^5.3.3", "vitest": "^1.3.1"}, "engines": {"node": ">=18"}}