
import { useSupabase } from "@/contexts/SupabaseContext";
import { useToast } from "@/hooks/use-toast";

interface PostgrestQueryOptions {
  select?: string;
  filter?: Record<string, any>;
  order?: string;
  limit?: number;
  offset?: number;
}

export function usePostgrest() {
  const { postgrestUrl, headers, isConfigured } = useSupabase();
  const { toast } = useToast();

  const executeQuery = async (query: string) => {
    if (!isConfigured) throw new Error("PostgREST não configurado");

    const response = await fetch(`${postgrestUrl.replace('/rest/v1', '')}/rpc/exec_sql`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ query })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error);
    }

    return await response.json();
  };

  const select = async (table: string, options: PostgrestQueryOptions = {}) => {
    if (!isConfigured) throw new Error("PostgREST não configurado");

    let url = `${postgrestUrl}/${table}`;
    const params = new URLSearchParams();

    if (options.select) {
      params.append('select', options.select);
    }

    if (options.filter) {
      Object.entries(options.filter).forEach(([key, value]) => {
        params.append(key, `eq.${value}`);
      });
    }

    if (options.order) {
      params.append('order', options.order);
    }

    if (options.limit) {
      params.append('limit', options.limit.toString());
    }

    if (options.offset) {
      params.append('offset', options.offset.toString());
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error);
    }

    return await response.json();
  };

  const insert = async (table: string, data: any) => {
    if (!isConfigured) throw new Error("PostgREST não configurado");

    const response = await fetch(`${postgrestUrl}/${table}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error);
    }

    return await response.json();
  };

  const update = async (table: string, data: any, filter: Record<string, any>) => {
    if (!isConfigured) throw new Error("PostgREST não configurado");

    let url = `${postgrestUrl}/${table}`;
    const params = new URLSearchParams();

    Object.entries(filter).forEach(([key, value]) => {
      params.append(key, `eq.${value}`);
    });

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await fetch(url, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error);
    }

    return await response.json();
  };

  const deleteRecord = async (table: string, filter: Record<string, any>) => {
    if (!isConfigured) throw new Error("PostgREST não configurado");

    let url = `${postgrestUrl}/${table}`;
    const params = new URLSearchParams();

    Object.entries(filter).forEach(([key, value]) => {
      params.append(key, `eq.${value}`);
    });

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await fetch(url, {
      method: 'DELETE',
      headers
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error);
    }

    return response.status === 204;
  };

  const rpc = async (functionName: string, params: any = {}) => {
    if (!isConfigured) throw new Error("PostgREST não configurado");

    const response = await fetch(`${postgrestUrl}/rpc/${functionName}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error);
    }

    return await response.json();
  };

  return {
    executeQuery,
    select,
    insert,
    update,
    delete: deleteRecord,
    rpc,
    isConfigured
  };
}
