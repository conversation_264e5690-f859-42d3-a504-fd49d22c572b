import { useEBaaS } from '@/contexts/EBaaSContext';

export interface DatabaseStats {
  tableCount: number;
  totalRows: number;
  databaseSize: string;
  connectionStatus: 'online' | 'offline' | 'error';
  avgResponseTime: string;
}

export interface TableInfo {
  name: string;
  schema: string;
  rows: number;
  size: string;
  columns: number;
  type: 'table' | 'view' | 'materialized_view';
  lastModified: string;
}

export interface QueryResult {
  columns: string[];
  rows: any[][];
  rowCount: number;
  executionTime: number;
  success: boolean;
  error?: string;
}

export interface RecentQuery {
  id: string;
  query: string;
  executionTime: number;
  timestamp: string;
  success: boolean;
  rowsAffected?: number;
}

export interface ConnectionInfo {
  host: string;
  port: string;
  database: string;
  username: string;
  ssl: string;
  provider: 'postgresql' | 'mysql' | 'mongodb';
}

class DatabaseService {
  private baseURL: string;
  private apiKey: string;

  constructor(baseURL: string, apiKey: string) {
    this.baseURL = baseURL;
    this.apiKey = apiKey;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      // Fallback to mock data if API is not available
      console.warn(`Database API unavailable: ${endpoint}. Using mock data.`);
      return this.getMockData(endpoint, options);
    }

    return await response.json();
  }

  private getMockData(endpoint: string, options?: RequestInit) {
    switch (endpoint) {
      case '/admin/v1/database/stats':
        return {
          tableCount: 8,
          totalRows: 2450,
          databaseSize: '45.2 MB',
          connectionStatus: 'online',
          avgResponseTime: '142ms',
        } as DatabaseStats;

      case '/admin/v1/database/tables':
        return [
          {
            name: 'users',
            schema: 'public',
            rows: 1247,
            size: '12.4 MB',
            columns: 8,
            type: 'table',
            lastModified: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
          {
            name: 'profiles',
            schema: 'public',
            rows: 1247,
            size: '8.2 MB',
            columns: 6,
            type: 'table',
            lastModified: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          },
          {
            name: 'sessions',
            schema: 'auth',
            rows: 3456,
            size: '15.6 MB',
            columns: 5,
            type: 'table',
            lastModified: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          },
          {
            name: 'workspaces',
            schema: 'public',
            rows: 124,
            size: '2.1 MB',
            columns: 12,
            type: 'table',
            lastModified: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          },
          {
            name: 'api_keys',
            schema: 'public',
            rows: 89,
            size: '1.2 MB',
            columns: 7,
            type: 'table',
            lastModified: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          }
        ] as TableInfo[];

      case '/admin/v1/database/queries/recent':
        return [
          {
            id: '1',
            query: 'SELECT * FROM users WHERE active = true',
            executionTime: 145,
            timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
            success: true,
            rowsAffected: 1247,
          },
          {
            id: '2',
            query: 'SELECT count(*) FROM sessions WHERE expires_at > NOW()',
            executionTime: 89,
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            success: true,
            rowsAffected: 1,
          },
          {
            id: '3',
            query: 'SELECT pg_database_size(current_database())',
            executionTime: 203,
            timestamp: new Date(Date.now() - 12 * 60 * 1000).toISOString(),
            success: true,
            rowsAffected: 1,
          },
          {
            id: '4',
            query: 'SELECT schemaname, tablename FROM pg_tables WHERE schemaname = \'public\'',
            executionTime: 67,
            timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
            success: true,
            rowsAffected: 8,
          },
        ] as RecentQuery[];

      case '/admin/v1/database/connection':
        return {
          host: 'localhost',
          port: '5432',
          database: 'ebaas_workspace_123',
          username: 'ebaas_user',
          ssl: 'require',
          provider: 'postgresql',
        } as ConnectionInfo;

      default:
        if (endpoint.includes('/admin/v1/database/execute') && options?.method === 'POST') {
          const body = JSON.parse(options.body as string);
          return {
            columns: ['id', 'name', 'email'],
            rows: [
              [1, 'John Doe', '<EMAIL>'],
              [2, 'Jane Smith', '<EMAIL>'],
            ],
            rowCount: 2,
            executionTime: 156,
            success: true,
          } as QueryResult;
        }
        return {};
    }
  }

  async getStats(): Promise<DatabaseStats> {
    return await this.makeRequest('/admin/v1/database/stats');
  }

  async getTables(): Promise<TableInfo[]> {
    return await this.makeRequest('/admin/v1/database/tables');
  }

  async getRecentQueries(): Promise<RecentQuery[]> {
    return await this.makeRequest('/admin/v1/database/queries/recent');
  }

  async getConnectionInfo(): Promise<ConnectionInfo> {
    return await this.makeRequest('/admin/v1/database/connection');
  }

  async executeQuery(query: string): Promise<QueryResult> {
    return await this.makeRequest('/admin/v1/database/execute', {
      method: 'POST',
      body: JSON.stringify({ query }),
    });
  }

  async getTableData(tableName: string, limit: number = 100, offset: number = 0): Promise<QueryResult> {
    return await this.makeRequest(`/admin/v1/database/tables/${tableName}/data?limit=${limit}&offset=${offset}`);
  }

  async getTableSchema(tableName: string) {
    return await this.makeRequest(`/admin/v1/database/tables/${tableName}/schema`);
  }

  // Table operations
  async createTable(tableName: string, columns: any[]) {
    return await this.makeRequest('/admin/v1/database/tables', {
      method: 'POST',
      body: JSON.stringify({ tableName, columns }),
    });
  }

  async dropTable(tableName: string) {
    return await this.makeRequest(`/admin/v1/database/tables/${tableName}`, {
      method: 'DELETE',
    });
  }

  async addColumn(tableName: string, column: any) {
    return await this.makeRequest(`/admin/v1/database/tables/${tableName}/columns`, {
      method: 'POST',
      body: JSON.stringify(column),
    });
  }

  async dropColumn(tableName: string, columnName: string) {
    return await this.makeRequest(`/admin/v1/database/tables/${tableName}/columns/${columnName}`, {
      method: 'DELETE',
    });
  }
}

export const useDatabase = () => {
  const { config } = useEBaaS();
  
  return new DatabaseService(config.url, config.apiKey);
};