import { useEBaaS } from '@/contexts/EBaaSContext';

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: string;
  adminRole?: 'platform_admin' | 'workspace_admin' | 'workspace_owner';
  workspaceId: string;
  permissions: string[];
  isActive: boolean;
  emailConfirmed: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role: string;
  adminRole?: 'platform_admin' | 'workspace_admin' | 'workspace_owner';
  permissions?: string[];
  metadata?: Record<string, any>;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  role?: string;
  adminRole?: 'platform_admin' | 'workspace_admin' | 'workspace_owner';
  permissions?: string[];
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface OAuthProvider {
  id: string;
  name: string;
  enabled: boolean;
  clientId?: string;
  clientSecret?: string;
  redirectUrl?: string;
  scopes?: string[];
  configuration?: Record<string, any>;
}

export interface Session {
  id: string;
  userId: string;
  userAgent?: string;
  ipAddress?: string;
  location?: string;
  isActive: boolean;
  expiresAt: string;
  createdAt: string;
  lastActivityAt: string;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newUsersToday: number;
  totalSessions: number;
  activeSessions: number;
  averageSessionDuration: string;
}

export interface ApiKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  workspaceId: string;
  isActive: boolean;
  lastUsedAt?: string;
  expiresAt?: string;
  createdAt: string;
  createdBy: string;
}

export interface CreateApiKeyRequest {
  name: string;
  permissions: string[];
  expiresAt?: string;
}

class UserService {
  private baseURL: string;
  private apiKey: string;

  constructor(baseURL: string, apiKey: string) {
    this.baseURL = baseURL;
    this.apiKey = apiKey;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      // Fallback to mock data if API is not available
      console.warn(`User API unavailable: ${endpoint}. Using mock data.`);
      return this.getMockData(endpoint, options);
    }

    return await response.json();
  }

  private getMockData(endpoint: string, options?: RequestInit) {
    switch (endpoint) {
      case '/admin/v1/users':
        if (options?.method === 'POST') {
          const body = JSON.parse(options.body as string);
          return {
            id: `user_${Date.now()}`,
            email: body.email,
            firstName: body.firstName,
            lastName: body.lastName,
            role: body.role,
            adminRole: body.adminRole,
            workspaceId: 'workspace_123',
            permissions: body.permissions || [],
            isActive: true,
            emailConfirmed: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          } as User;
        }
        return [
          {
            id: 'user_1',
            email: '<EMAIL>',
            firstName: 'Platform',
            lastName: 'Admin',
            role: 'admin',
            adminRole: 'platform_admin',
            workspaceId: 'workspace_123',
            permissions: ['*'],
            isActive: true,
            emailConfirmed: true,
            lastLoginAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
            createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'user_2',
            email: '<EMAIL>',
            firstName: 'Workspace',
            lastName: 'Admin',
            role: 'admin',
            adminRole: 'workspace_admin',
            workspaceId: 'workspace_123',
            permissions: ['users:read', 'users:write', 'database:read', 'database:write'],
            isActive: true,
            emailConfirmed: true,
            lastLoginAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: 'user_3',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            role: 'user',
            workspaceId: 'workspace_123',
            permissions: ['read'],
            isActive: true,
            emailConfirmed: true,
            lastLoginAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          },
        ] as User[];

      case '/admin/v1/users/stats':
        return {
          totalUsers: 3,
          activeUsers: 3,
          newUsersToday: 0,
          totalSessions: 5,
          activeSessions: 2,
          averageSessionDuration: '2h 15m',
        } as UserStats;

      case '/admin/v1/oauth/providers':
        return [
          {
            id: 'google',
            name: 'Google',
            enabled: true,
            clientId: 'google_client_id_***',
            redirectUrl: 'https://admin.e-baas.apiservices.app.br/auth/callback/google',
            scopes: ['openid', 'email', 'profile'],
          },
          {
            id: 'github',
            name: 'GitHub',
            enabled: false,
            clientId: '',
            redirectUrl: 'https://admin.e-baas.apiservices.app.br/auth/callback/github',
            scopes: ['user:email'],
          },
          {
            id: 'facebook',
            name: 'Facebook',
            enabled: false,
            clientId: '',
            redirectUrl: 'https://admin.e-baas.apiservices.app.br/auth/callback/facebook',
            scopes: ['email'],
          },
        ] as OAuthProvider[];

      case '/admin/v1/sessions':
        return [
          {
            id: 'session_1',
            userId: 'user_1',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
            ipAddress: '*************',
            location: 'São Paulo, BR',
            isActive: true,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            lastActivityAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          },
          {
            id: 'session_2',
            userId: 'user_2',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            ipAddress: '*********',
            location: 'Rio de Janeiro, BR',
            isActive: true,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
            lastActivityAt: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
          },
        ] as Session[];

      case '/admin/v1/api-keys':
        if (options?.method === 'POST') {
          const body = JSON.parse(options.body as string);
          return {
            id: `key_${Date.now()}`,
            name: body.name,
            key: `ebaas_${Date.now()}_abcdef123456789`,
            permissions: body.permissions,
            workspaceId: 'workspace_123',
            isActive: true,
            expiresAt: body.expiresAt,
            createdAt: new Date().toISOString(),
            createdBy: 'current_user',
          } as ApiKey;
        }
        return [
          {
            id: 'key_1',
            name: 'Production API Key',
            key: 'ebaas_prod_1234567890abcdef',
            permissions: ['database:read', 'database:write', 'storage:read', 'storage:write'],
            workspaceId: 'workspace_123',
            isActive: true,
            lastUsedAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
            createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            createdBy: 'user_1',
          },
          {
            id: 'key_2',
            name: 'Development API Key',
            key: 'ebaas_dev_abcdef1234567890',
            permissions: ['database:read', 'storage:read'],
            workspaceId: 'workspace_123',
            isActive: true,
            lastUsedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            createdBy: 'user_2',
          },
        ] as ApiKey[];

      default:
        return {};
    }
  }

  // User Management
  async getUsers(): Promise<User[]> {
    return await this.makeRequest('/admin/v1/users');
  }

  async getUserById(id: string): Promise<User> {
    return await this.makeRequest(`/admin/v1/users/${id}`);
  }

  async createUser(userData: CreateUserRequest): Promise<User> {
    return await this.makeRequest('/admin/v1/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(id: string, userData: UpdateUserRequest): Promise<User> {
    return await this.makeRequest(`/admin/v1/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(id: string): Promise<void> {
    await this.makeRequest(`/admin/v1/users/${id}`, {
      method: 'DELETE',
    });
  }

  async getUserStats(): Promise<UserStats> {
    return await this.makeRequest('/admin/v1/users/stats');
  }

  // OAuth Management
  async getOAuthProviders(): Promise<OAuthProvider[]> {
    return await this.makeRequest('/admin/v1/oauth/providers');
  }

  async updateOAuthProvider(id: string, config: Partial<OAuthProvider>): Promise<OAuthProvider> {
    return await this.makeRequest(`/admin/v1/oauth/providers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  }

  // Session Management
  async getSessions(): Promise<Session[]> {
    return await this.makeRequest('/admin/v1/sessions');
  }

  async revokeSession(sessionId: string): Promise<void> {
    await this.makeRequest(`/admin/v1/sessions/${sessionId}`, {
      method: 'DELETE',
    });
  }

  // API Key Management
  async getApiKeys(): Promise<ApiKey[]> {
    return await this.makeRequest('/admin/v1/api-keys');
  }

  async createApiKey(keyData: CreateApiKeyRequest): Promise<ApiKey> {
    return await this.makeRequest('/admin/v1/api-keys', {
      method: 'POST',
      body: JSON.stringify(keyData),
    });
  }

  async revokeApiKey(id: string): Promise<void> {
    await this.makeRequest(`/admin/v1/api-keys/${id}`, {
      method: 'DELETE',
    });
  }

  async regenerateApiKey(id: string): Promise<ApiKey> {
    return await this.makeRequest(`/admin/v1/api-keys/${id}/regenerate`, {
      method: 'POST',
    });
  }
}

export const useUserService = () => {
  const { config } = useEBaaS();
  
  return new UserService(config.url, config.apiKey);
};