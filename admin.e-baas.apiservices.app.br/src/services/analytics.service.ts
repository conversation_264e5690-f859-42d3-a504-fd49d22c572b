import { useEBaaS } from '@/contexts/EBaaSContext';

export interface AnalyticsOverview {
  totalRequests: { value: number; change: string; trend: 'up' | 'down' | 'stable'; };
  activeUsers: { value: number; change: string; trend: 'up' | 'down' | 'stable'; };
  databaseQueries: { value: number; change: string; trend: 'up' | 'down' | 'stable'; };
  responseTime: { value: string; change: string; trend: 'up' | 'down' | 'stable'; };
}

export interface UsageMetrics {
  databaseStorage: { used: string; total: string; percentage: number; };
  fileStorage: { used: string; total: string; percentage: number; };
  monthlyRequests: { used: string; total: string; percentage: number; };
}

export interface ActivityEvent {
  id: string;
  type: 'success' | 'info' | 'warning' | 'error';
  message: string;
  timestamp: string;
  details?: string;
}

export interface WorkspaceInfo {
  id: string;
  name: string;
  plan: string;
  createdAt: string;
  lastActivity: string;
  isActive: boolean;
}

class AnalyticsService {
  private baseURL: string;
  private apiKey: string;

  constructor(baseURL: string, apiKey: string) {
    this.baseURL = baseURL;
    this.apiKey = apiKey;
  }

  private async makeRequest(endpoint: string) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      // Fallback to mock data if API is not available
      console.warn(`Analytics API unavailable: ${endpoint}. Using mock data.`);
      return this.getMockData(endpoint);
    }

    return await response.json();
  }

  private getMockData(endpoint: string) {
    switch (endpoint) {
      case '/admin/v1/analytics/overview':
        return {
          totalRequests: { value: 45231, change: '+20.1%', trend: 'up' },
          activeUsers: { value: 1247, change: '+18.2%', trend: 'up' },
          databaseQueries: { value: 12234, change: '+19%', trend: 'up' },
          responseTime: { value: '145ms', change: '-5%', trend: 'down' },
        } as AnalyticsOverview;

      case '/admin/v1/analytics/usage':
        return {
          databaseStorage: { used: '2.4 GB', total: '10 GB', percentage: 24 },
          fileStorage: { used: '156 MB', total: '5 GB', percentage: 3 },
          monthlyRequests: { used: '45K', total: '100K', percentage: 45 },
        } as UsageMetrics;

      case '/admin/v1/analytics/activity':
        return [
          {
            id: '1',
            type: 'success',
            message: 'Database backup completed',
            timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
          },
          {
            id: '2',
            type: 'info',
            message: 'New user registered',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          },
          {
            id: '3',
            type: 'warning',
            message: 'API rate limit warning',
            timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          },
        ] as ActivityEvent[];

      case '/admin/v1/workspace/info':
        return {
          id: 'workspace_123',
          name: 'My E-BaaS Project',
          plan: 'Professional',
          createdAt: '2025-01-01T00:00:00Z',
          lastActivity: new Date().toISOString(),
          isActive: true,
        } as WorkspaceInfo;

      default:
        return {};
    }
  }

  async getOverview(): Promise<AnalyticsOverview> {
    return await this.makeRequest('/admin/v1/analytics/overview');
  }

  async getUsageMetrics(): Promise<UsageMetrics> {
    return await this.makeRequest('/admin/v1/analytics/usage');
  }

  async getActivityEvents(): Promise<ActivityEvent[]> {
    return await this.makeRequest('/admin/v1/analytics/activity');
  }

  async getWorkspaceInfo(): Promise<WorkspaceInfo> {
    return await this.makeRequest('/admin/v1/workspace/info');
  }

  // Real-time analytics for specific timeframes
  async getMetrics(metric: string, timeframe: string = '30d') {
    return await this.makeRequest(`/admin/v1/metrics/${metric}?timeframe=${timeframe}`);
  }
}

export const useAnalytics = () => {
  const { config } = useEBaaS();
  
  return new AnalyticsService(config.url, config.apiKey);
};