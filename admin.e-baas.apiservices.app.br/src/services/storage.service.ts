import { useEBaaS } from '@/contexts/EBaaSContext';

export interface StorageBucket {
  id: string;
  name: string;
  isPublic: boolean;
  allowedMimeTypes?: string[];
  fileSizeLimit?: number;
  createdAt: string;
  updatedAt: string;
  fileCount: number;
  totalSize: string;
  owner: string;
}

export interface StorageFile {
  id: string;
  name: string;
  bucket: string;
  size: number;
  mimeType: string;
  isPublic: boolean;
  url?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  owner: string;
  lastAccessedAt?: string;
}

export interface StorageStats {
  totalBuckets: number;
  totalFiles: number;
  totalSize: string;
  publicFiles: number;
  privateFiles: number;
  bandwidth: string;
  requests: number;
}

export interface StoragePolicy {
  id: string;
  bucket: string;
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE';
  role?: string;
  policy: string;
  isEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBucketRequest {
  name: string;
  isPublic: boolean;
  allowedMimeTypes?: string[];
  fileSizeLimit?: number;
}

export interface UploadFileRequest {
  bucket: string;
  file: File;
  isPublic?: boolean;
  metadata?: Record<string, any>;
}

export interface CreatePolicyRequest {
  bucket: string;
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE';
  role?: string;
  policy: string;
}

class StorageService {
  private baseURL: string;
  private apiKey: string;

  constructor(baseURL: string, apiKey: string) {
    this.baseURL = baseURL;
    this.apiKey = apiKey;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      // Fallback to mock data if API is not available
      console.warn(`Storage API unavailable: ${endpoint}. Using mock data.`);
      return this.getMockData(endpoint, options);
    }

    return await response.json();
  }

  private getMockData(endpoint: string, options?: RequestInit) {
    switch (endpoint) {
      case '/admin/v1/storage/stats':
        return {
          totalBuckets: 4,
          totalFiles: 127,
          totalSize: '2.4 GB',
          publicFiles: 45,
          privateFiles: 82,
          bandwidth: '156 MB',
          requests: 1247,
        } as StorageStats;

      case '/admin/v1/storage/buckets':
        if (options?.method === 'POST') {
          const body = JSON.parse(options.body as string);
          return {
            id: `bucket_${Date.now()}`,
            name: body.name,
            isPublic: body.isPublic,
            allowedMimeTypes: body.allowedMimeTypes,
            fileSizeLimit: body.fileSizeLimit,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            fileCount: 0,
            totalSize: '0 B',
            owner: 'current_user',
          } as StorageBucket;
        }
        return [
          {
            id: 'bucket_1',
            name: 'public-images',
            isPublic: true,
            allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif'],
            fileSizeLimit: 5242880, // 5MB
            createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            fileCount: 45,
            totalSize: '125 MB',
            owner: 'user_1',
          },
          {
            id: 'bucket_2',
            name: 'documents',
            isPublic: false,
            allowedMimeTypes: ['application/pdf', 'text/plain', 'application/msword'],
            fileSizeLimit: 10485760, // 10MB
            createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            fileCount: 23,
            totalSize: '89 MB',
            owner: 'user_2',
          },
          {
            id: 'bucket_3',
            name: 'user-uploads',
            isPublic: false,
            allowedMimeTypes: undefined,
            fileSizeLimit: 20971520, // 20MB
            createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            fileCount: 59,
            totalSize: '456 MB',
            owner: 'user_1',
          },
        ] as StorageBucket[];

      case '/admin/v1/storage/files':
        return [
          {
            id: 'file_1',
            name: 'profile-photo.jpg',
            bucket: 'public-images',
            size: 245760,
            mimeType: 'image/jpeg',
            isPublic: true,
            url: 'https://cdn.e-baas.apiservices.app.br/public-images/profile-photo.jpg',
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            owner: 'user_1',
            lastAccessedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: 'file_2',
            name: 'company-logo.png',
            bucket: 'public-images',
            size: 56320,
            mimeType: 'image/png',
            isPublic: true,
            url: 'https://cdn.e-baas.apiservices.app.br/public-images/company-logo.png',
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            owner: 'user_2',
            lastAccessedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          },
          {
            id: 'file_3',
            name: 'contract.pdf',
            bucket: 'documents',
            size: 1048576,
            mimeType: 'application/pdf',
            isPublic: false,
            createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            owner: 'user_1',
            metadata: { department: 'legal', confidential: true },
          },
          {
            id: 'file_4',
            name: 'backup-data.zip',
            bucket: 'user-uploads',
            size: 15728640,
            mimeType: 'application/zip',
            isPublic: false,
            createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
            owner: 'user_1',
            metadata: { type: 'backup', automated: true },
          },
        ] as StorageFile[];

      case '/admin/v1/storage/policies':
        if (options?.method === 'POST') {
          const body = JSON.parse(options.body as string);
          return {
            id: `policy_${Date.now()}`,
            bucket: body.bucket,
            operation: body.operation,
            role: body.role,
            policy: body.policy,
            isEnabled: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          } as StoragePolicy;
        }
        return [
          {
            id: 'policy_1',
            bucket: 'public-images',
            operation: 'SELECT',
            role: 'authenticated',
            policy: 'auth.uid() = owner',
            isEnabled: true,
            createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: 'policy_2',
            bucket: 'documents',
            operation: 'INSERT',
            role: 'authenticated',
            policy: 'auth.uid() IS NOT NULL',
            isEnabled: true,
            createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: 'policy_3',
            bucket: 'user-uploads',
            operation: 'DELETE',
            role: 'admin',
            policy: 'auth.role() = "admin"',
            isEnabled: false,
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          },
        ] as StoragePolicy[];

      default:
        return {};
    }
  }

  // Storage Stats
  async getStats(): Promise<StorageStats> {
    return await this.makeRequest('/admin/v1/storage/stats');
  }

  // Bucket Management
  async getBuckets(): Promise<StorageBucket[]> {
    return await this.makeRequest('/admin/v1/storage/buckets');
  }

  async getBucketById(id: string): Promise<StorageBucket> {
    return await this.makeRequest(`/admin/v1/storage/buckets/${id}`);
  }

  async createBucket(bucketData: CreateBucketRequest): Promise<StorageBucket> {
    return await this.makeRequest('/admin/v1/storage/buckets', {
      method: 'POST',
      body: JSON.stringify(bucketData),
    });
  }

  async updateBucket(id: string, bucketData: Partial<CreateBucketRequest>): Promise<StorageBucket> {
    return await this.makeRequest(`/admin/v1/storage/buckets/${id}`, {
      method: 'PUT',
      body: JSON.stringify(bucketData),
    });
  }

  async deleteBucket(id: string): Promise<void> {
    await this.makeRequest(`/admin/v1/storage/buckets/${id}`, {
      method: 'DELETE',
    });
  }

  // File Management
  async getFiles(bucket?: string): Promise<StorageFile[]> {
    const endpoint = bucket ? `/admin/v1/storage/files?bucket=${bucket}` : '/admin/v1/storage/files';
    return await this.makeRequest(endpoint);
  }

  async getFileById(id: string): Promise<StorageFile> {
    return await this.makeRequest(`/admin/v1/storage/files/${id}`);
  }

  async uploadFile(fileData: UploadFileRequest): Promise<StorageFile> {
    const formData = new FormData();
    formData.append('file', fileData.file);
    formData.append('bucket', fileData.bucket);
    if (fileData.isPublic !== undefined) {
      formData.append('isPublic', String(fileData.isPublic));
    }
    if (fileData.metadata) {
      formData.append('metadata', JSON.stringify(fileData.metadata));
    }

    const response = await fetch(`${this.baseURL}/admin/v1/storage/files`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: formData,
    });

    if (!response.ok) {
      // Mock response for upload
      return {
        id: `file_${Date.now()}`,
        name: fileData.file.name,
        bucket: fileData.bucket,
        size: fileData.file.size,
        mimeType: fileData.file.type,
        isPublic: fileData.isPublic || false,
        url: fileData.isPublic ? `https://cdn.e-baas.apiservices.app.br/${fileData.bucket}/${fileData.file.name}` : undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        owner: 'current_user',
        metadata: fileData.metadata,
      } as StorageFile;
    }

    return await response.json();
  }

  async deleteFile(id: string): Promise<void> {
    await this.makeRequest(`/admin/v1/storage/files/${id}`, {
      method: 'DELETE',
    });
  }

  async getFileUrl(id: string, expiresIn?: number): Promise<{ url: string; expiresAt: string }> {
    const params = expiresIn ? `?expiresIn=${expiresIn}` : '';
    return await this.makeRequest(`/admin/v1/storage/files/${id}/url${params}`);
  }

  // Storage Policies
  async getPolicies(bucket?: string): Promise<StoragePolicy[]> {
    const endpoint = bucket ? `/admin/v1/storage/policies?bucket=${bucket}` : '/admin/v1/storage/policies';
    return await this.makeRequest(endpoint);
  }

  async createPolicy(policyData: CreatePolicyRequest): Promise<StoragePolicy> {
    return await this.makeRequest('/admin/v1/storage/policies', {
      method: 'POST',
      body: JSON.stringify(policyData),
    });
  }

  async updatePolicy(id: string, policyData: Partial<CreatePolicyRequest>): Promise<StoragePolicy> {
    return await this.makeRequest(`/admin/v1/storage/policies/${id}`, {
      method: 'PUT',
      body: JSON.stringify(policyData),
    });
  }

  async deletePolicy(id: string): Promise<void> {
    await this.makeRequest(`/admin/v1/storage/policies/${id}`, {
      method: 'DELETE',
    });
  }

  async togglePolicy(id: string, enabled: boolean): Promise<StoragePolicy> {
    return await this.makeRequest(`/admin/v1/storage/policies/${id}/toggle`, {
      method: 'POST',
      body: JSON.stringify({ enabled }),
    });
  }

  // Utility methods
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(mimeType: string): string {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType.startsWith('video/')) return '🎥';
    if (mimeType.startsWith('audio/')) return '🎵';
    if (mimeType === 'application/pdf') return '📄';
    if (mimeType.includes('document')) return '📝';
    if (mimeType.includes('spreadsheet')) return '📊';
    if (mimeType.includes('zip') || mimeType.includes('archive')) return '📦';
    return '📁';
  }
}

export const useStorageService = () => {
  const { config } = useEBaaS();
  
  return new StorageService(config.url, config.apiKey);
};