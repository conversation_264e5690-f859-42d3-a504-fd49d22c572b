
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface SupabaseConfig {
  url: string;
  key: string;
}

interface SupabaseContextType {
  config: SupabaseConfig | null;
  setConfig: (config: SupabaseConfig) => void;
  isConfigured: boolean;
  postgrestUrl: string;
  headers: Record<string, string>;
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(undefined);

export function SupabaseProvider({ children }: { children: ReactNode }) {
  const [config, setConfigState] = useState<SupabaseConfig | null>(null);

  useEffect(() => {
    // Load config from localStorage on mount
    const savedConfig = localStorage.getItem('supabase-config');
    if (savedConfig) {
      const parsedConfig = JSON.parse(savedConfig);
      setConfigState(parsedConfig);
    }
  }, []);

  const setConfig = (newConfig: SupabaseConfig) => {
    setConfigState(newConfig);
    localStorage.setItem('supabase-config', JSON.stringify(newConfig));
  };

  const postgrestUrl = config ? `${config.url}/rest/v1` : '';
  const headers = config ? {
    'apikey': config.key,
    'Authorization': `Bearer ${config.key}`,
    'Content-Type': 'application/json',
    'Prefer': 'return=representation'
  } : {};

  return (
    <SupabaseContext.Provider value={{
      config,
      setConfig,
      isConfigured: !!config,
      postgrestUrl,
      headers
    }}>
      {children}
    </SupabaseContext.Provider>
  );
}

export function useSupabase() {
  const context = useContext(SupabaseContext);
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
}
