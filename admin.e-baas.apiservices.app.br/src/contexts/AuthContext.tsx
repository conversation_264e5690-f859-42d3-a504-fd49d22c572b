import { createContext, useContext, ReactNode } from 'react';
import { useEBaaS } from './EBaaSContext';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any;
  isPlatformAdmin: boolean;
  isWorkspaceAdmin: boolean;
  canAccessAdmin: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const { user, isAuthenticated, login, logout, isLoading } = useEBaaS();

  const isPlatformAdmin = user?.isPlatformAdmin || false;
  const isWorkspaceAdmin = user?.isWorkspaceAdmin || false;
  const canAccessAdmin = user?.canAccessAdmin || false;

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      user,
      isPlatformAdmin,
      isWorkspaceAdmin,
      canAccessAdmin,
      login,
      logout,
      isLoading
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}