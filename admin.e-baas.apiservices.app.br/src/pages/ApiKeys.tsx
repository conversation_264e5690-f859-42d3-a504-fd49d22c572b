import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Key, Plus, Co<PERSON>, Eye, EyeOff, Trash2, Refresh<PERSON><PERSON>, Settings } from "lucide-react";
import { useUserService, ApiKey } from "@/services/user.service";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

export default function ApiKeys() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});

  const userService = useUserService();
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();

  const loadApiKeys = async () => {
    setLoading(true);
    try {
      const keys = await userService.getApiKeys();
      setApiKeys(keys);
    } catch (error: any) {
      console.error('Error loading API keys:', error);
      toast({
        title: "Error",
        description: "Failed to load API keys",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadApiKeys();
    }
  }, [isAuthenticated]);

  const toggleKeyVisibility = (keyId: string) => {
    setShowKeys(prev => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "API key copied to clipboard",
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">API Keys</h1>
          <p className="text-muted-foreground">
            Manage your API keys and access tokens
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={loadApiKeys}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            New API Key
          </Button>
        </div>
      </div>

      <Card className="gradient-border">
        <CardHeader>
          <CardTitle>API Keys</CardTitle>
          <CardDescription>
            Manage API keys for programmatic access to your E-BaaS workspace
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {apiKeys.length > 0 ? apiKeys.map((apiKey) => (
            <div key={apiKey.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex-1">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                    <Key className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold">{apiKey.name}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {showKeys[apiKey.id] ? apiKey.key : '••••••••••••••••••••••••••••••••'}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleKeyVisibility(apiKey.id)}
                      >
                        {showKeys[apiKey.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(apiKey.key)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex items-center space-x-2 mt-2">
                      {apiKey.permissions.map((permission) => (
                        <Badge key={permission} variant="outline" className="text-xs">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Last used: {apiKey.lastUsedAt ? formatTimestamp(apiKey.lastUsedAt) : 'Never'} • 
                      Created: {formatTimestamp(apiKey.createdAt)}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={apiKey.isActive ? "default" : "secondary"}>
                  {apiKey.isActive ? "Active" : "Inactive"}
                </Badge>
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )) : (
            <div className="text-center p-8 text-muted-foreground border-2 border-dashed rounded-lg">
              <Key className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No API keys found</p>
              <p className="mb-4">Create your first API key to get started with programmatic access</p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create API Key
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>API Documentation</CardTitle>
            <CardDescription>
              Learn how to use your API keys
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="p-3 rounded-lg glass-effect">
                <h4 className="font-medium">Authentication</h4>
                <p className="text-sm text-muted-foreground">
                  Include your API key in the Authorization header
                </p>
                <code className="text-xs bg-muted p-2 rounded block mt-1">
                  Authorization: Bearer your_api_key_here
                </code>
              </div>
              <div className="p-3 rounded-lg glass-effect">
                <h4 className="font-medium">Base URL</h4>
                <p className="text-sm text-muted-foreground">
                  All API requests should be made to:
                </p>
                <code className="text-xs bg-muted p-2 rounded block mt-1">
                  https://e-baas.apiservices.app.br/api/v1
                </code>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Key Permissions</CardTitle>
            <CardDescription>
              Available permissions for API keys
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {[
              { name: "database:read", description: "Read database records" },
              { name: "database:write", description: "Create, update, delete records" },
              { name: "storage:read", description: "Download files and list buckets" },
              { name: "storage:write", description: "Upload and delete files" },
              { name: "auth:read", description: "View user information" },
              { name: "auth:write", description: "Manage user accounts" },
            ].map((permission) => (
              <div key={permission.name} className="flex items-center justify-between p-2 rounded-lg glass-effect">
                <div>
                  <code className="text-sm font-medium">{permission.name}</code>
                  <p className="text-xs text-muted-foreground">{permission.description}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}