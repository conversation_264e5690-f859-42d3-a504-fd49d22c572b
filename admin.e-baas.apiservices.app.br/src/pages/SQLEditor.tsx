import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { useDatabase, QueryResult } from "@/services/database.service";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { 
  Database, 
  History, 
  BookOpen, 
  Play, 
  RefreshCw, 
  Save,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";

interface QueryHistoryItem {
  id: string;
  query: string;
  timestamp: string;
  success: boolean;
  executionTime: number;
  rowCount?: number;
}

export default function SQLEditor() {
  const [currentQuery, setCurrentQuery] = useState("");
  const [queryResult, setQueryResult] = useState<QueryResult | null>(null);
  const [queryHistory, setQueryHistory] = useState<QueryHistoryItem[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);
  
  const database = useDatabase();
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const savedHistory = localStorage.getItem('sql-query-history');
    if (savedHistory) {
      try {
        setQueryHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Error parsing query history:', error);
      }
    }

    // Load recent queries from backend
    const loadRecentQueries = async () => {
      try {
        const recentQueries = await database.getRecentQueries();
        const historyItems: QueryHistoryItem[] = recentQueries.map(q => ({
          id: q.id,
          query: q.query,
          timestamp: q.timestamp,
          success: q.success,
          executionTime: q.executionTime,
          rowCount: q.rowsAffected
        }));
        setQueryHistory(historyItems);
      } catch (error) {
        console.error('Error loading recent queries:', error);
      }
    };

    if (isAuthenticated) {
      loadRecentQueries();
    }
  }, [isAuthenticated]);

  const saveQueryToHistory = (query: string, success: boolean, executionTime: number, rowCount?: number) => {
    const historyItem: QueryHistoryItem = {
      id: Date.now().toString(),
      query: query.trim(),
      timestamp: new Date().toISOString(),
      success,
      executionTime,
      rowCount
    };

    const newHistory = [historyItem, ...queryHistory.slice(0, 19)]; // Keep last 20 queries
    setQueryHistory(newHistory);
    localStorage.setItem('sql-query-history', JSON.stringify(newHistory));
  };

  const executeQuery = async () => {
    if (!currentQuery.trim()) {
      toast({
        title: "Error",
        description: "Please enter a SQL query",
        variant: "destructive",
      });
      return;
    }

    setIsExecuting(true);
    const startTime = Date.now();

    try {
      const result = await database.executeQuery(currentQuery.trim());
      const executionTime = Date.now() - startTime;
      
      setQueryResult(result);
      saveQueryToHistory(currentQuery, result.success, executionTime, result.rowCount);
      
      if (result.success) {
        toast({
          title: "Query executed successfully",
          description: `${result.rowCount} rows returned in ${executionTime}ms`,
        });
      } else {
        toast({
          title: "Query failed",
          description: result.error || "Unknown error occurred",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      console.error('Error executing query:', error);
      saveQueryToHistory(currentQuery, false, executionTime);
      
      toast({
        title: "Execution failed",
        description: error.message || "Failed to execute query",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  };

  const loadQueryFromHistory = (query: string) => {
    setCurrentQuery(query);
  };

  const quickQueries = [
    "SELECT * FROM users LIMIT 10;",
    "SELECT COUNT(*) FROM users;",
    "SHOW TABLES;",
    "DESCRIBE users;",
    "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';",
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">SQL Editor</h1>
          <p className="text-muted-foreground mt-2">
            Execute SQL queries directly against your database
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Save className="w-4 h-4 mr-2" />
            Save Query
          </Button>
          <Button 
            onClick={executeQuery} 
            disabled={isExecuting || !currentQuery.trim()}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExecuting ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Play className="w-4 h-4 mr-2" />
            )}
            {isExecuting ? "Executing..." : "Run Query"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Query Editor */}
        <div className="lg:col-span-3 space-y-6">
          <Card className="gradient-border">
            <CardHeader>
              <CardTitle>Query Editor</CardTitle>
              <CardDescription>
                Write and execute SQL queries against your database
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="SELECT * FROM users WHERE active = true;"
                value={currentQuery}
                onChange={(e) => setCurrentQuery(e.target.value)}
                className="min-h-[200px] font-mono text-sm"
              />
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Database className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Connected to E-BaaS Database
                  </span>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <BookOpen className="w-4 h-4 mr-2" />
                    SQL Reference
                  </Button>
                  <Button variant="outline" size="sm">
                    Format Query
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Query Results */}
          {queryResult && (
            <Card className="gradient-border">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Query Results</CardTitle>
                    <CardDescription>
                      {queryResult.success ? (
                        <div className="flex items-center space-x-2 text-green-600">
                          <CheckCircle className="w-4 h-4" />
                          <span>
                            {queryResult.rowCount} rows • {queryResult.executionTime}ms
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2 text-red-600">
                          <XCircle className="w-4 h-4" />
                          <span>Query failed</span>
                        </div>
                      )}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {queryResult.success && queryResult.rows.length > 0 ? (
                  <div className="rounded-md border overflow-auto max-h-96">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {queryResult.columns.map((column, index) => (
                            <TableHead key={index} className="font-medium">
                              {column}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {queryResult.rows.map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {row.map((cell, cellIndex) => (
                              <TableCell key={cellIndex} className="max-w-xs truncate">
                                {cell === null ? (
                                  <span className="text-muted-foreground italic">NULL</span>
                                ) : (
                                  String(cell)
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : queryResult.success ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Query executed successfully, no data returned
                  </div>
                ) : (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-red-600 font-medium">Error:</p>
                    <p className="text-red-700 text-sm mt-1">{queryResult.error}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Queries */}
          <Card className="gradient-border">
            <CardHeader>
              <CardTitle>Quick Queries</CardTitle>
              <CardDescription>
                Common SQL queries to get started
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {quickQueries.map((query, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="w-full text-left justify-start h-auto p-2"
                  onClick={() => loadQueryFromHistory(query)}
                >
                  <code className="text-xs truncate">{query}</code>
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Query History */}
          <Card className="gradient-border">
            <CardHeader>
              <CardTitle>Query History</CardTitle>
              <CardDescription>
                Recent queries and their results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="max-h-96 overflow-y-auto">
                {queryHistory.map((item) => (
                  <div
                    key={item.id}
                    className="p-3 rounded-lg glass-effect cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => loadQueryFromHistory(item.query)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {item.success ? (
                          <CheckCircle className="w-3 h-3 text-green-500" />
                        ) : (
                          <XCircle className="w-3 h-3 text-red-500" />
                        )}
                        <Badge variant="outline" className="text-xs">
                          {item.executionTime}ms
                        </Badge>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(item.timestamp)}
                      </span>
                    </div>
                    <code className="text-xs text-muted-foreground block truncate">
                      {item.query}
                    </code>
                  </div>
                ))}
                {queryHistory.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No query history yet
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}