import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Zap, Plus, Play, Pause, Settings } from "lucide-react";

export default function Functions() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Edge Functions</h1>
          <p className="text-muted-foreground">
            Deploy and manage serverless functions
          </p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          New Function
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Functions Overview</CardTitle>
          <CardDescription>
            Manage your serverless functions and deployments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold">auth-webhook</h3>
                  <p className="text-sm text-muted-foreground">Authentication webhook handler</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">Active</Badge>
                <Button variant="outline" size="sm">
                  <Play className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold">email-notifications</h3>
                  <p className="text-sm text-muted-foreground">Send email notifications</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">Paused</Badge>
                <Button variant="outline" size="sm">
                  <Pause className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="text-center p-8 text-muted-foreground border-2 border-dashed rounded-lg">
              <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No more functions</p>
              <p className="mb-4">Create your first serverless function to get started</p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Function
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}