import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Database as DatabaseIcon, 
  Table, 
  Key, 
  Activity,
  Clock,
  TrendingUp,
  RefreshCw,
  ExternalLink,
  Copy,
  Search,
  Plus
} from "lucide-react";
import { useDatabase, DatabaseStats, TableInfo, RecentQuery, ConnectionInfo } from "@/services/database.service";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";

export default function Database() {
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [recentQueries, setRecentQueries] = useState<RecentQuery[]>([]);
  const [connectionInfo, setConnectionInfo] = useState<ConnectionInfo | null>(null);
  const [loading, setLoading] = useState(true);
  
  const database = useDatabase();
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const loadDatabaseData = async () => {
    setLoading(true);
    try {
      const [statsData, tablesData, queriesData, connectionData] = await Promise.all([
        database.getStats(),
        database.getTables(),
        database.getRecentQueries(),
        database.getConnectionInfo(),
      ]);
      
      setStats(statsData);
      setTables(tablesData);
      setRecentQueries(queriesData);
      setConnectionInfo(connectionData);
    } catch (error: any) {
      console.error('Error loading database data:', error);
      toast({
        title: "Error",
        description: "Failed to load database information",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadDatabaseData();
    }
  }, [isAuthenticated]);

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Copied to clipboard",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Database Explorer</h1>
          <p className="text-muted-foreground mt-2">
            Monitor and manage your E-BaaS database
          </p>
          {connectionInfo && (
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant="default">{connectionInfo.provider.toUpperCase()}</Badge>
              <span className="text-xs text-muted-foreground">
                {connectionInfo.host}:{connectionInfo.port}
              </span>
            </div>
          )}
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={loadDatabaseData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={() => navigate('/sql-editor')}>
            <Plus className="w-4 h-4 mr-2" />
            SQL Editor
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats && [
          {
            title: "Total Tables",
            value: stats.tableCount.toString(),
            description: "tables in database",
            icon: Table
          },
          {
            title: "Total Rows",
            value: stats.totalRows.toLocaleString(),
            description: "rows across all tables",
            icon: DatabaseIcon
          },
          {
            title: "Database Size",
            value: stats.databaseSize,
            description: "storage used",
            icon: DatabaseIcon
          },
          {
            title: "Avg Response Time",
            value: stats.avgResponseTime,
            description: stats.connectionStatus === 'online' ? 'connected and online' : 'connection status',
            icon: stats.connectionStatus === 'online' ? TrendingUp : Activity,
            color: stats.connectionStatus === 'online' ? 'text-green-400' : 'text-red-400'
          }
        ].map((stat) => (
          <Card key={stat.title} className="gradient-border">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className={`text-xs mt-1 ${stat.color || 'text-muted-foreground'}`}>
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Database Tables</CardTitle>
            <CardDescription>
              All tables in your database schema
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="max-h-96 overflow-y-auto">
              {tables.map((table) => (
                <div key={`${table.schema}.${table.name}`} className="flex items-center justify-between p-3 rounded-lg glass-effect hover:bg-muted/50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
                      <Table className="w-4 h-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">{table.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {table.rows.toLocaleString()} rows • {table.columns} columns • {table.size}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {table.schema}
                    </Badge>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => navigate(`/table-editor?table=${table.name}`)}
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
              {tables.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No tables found in database
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Recent Queries</CardTitle>
            <CardDescription>
              Latest database operations and their performance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="max-h-96 overflow-y-auto">
              {recentQueries.map((query) => (
                <div key={query.id} className="p-3 rounded-lg glass-effect">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Badge variant={query.success ? "default" : "destructive"} className="text-xs">
                        {query.executionTime}ms
                      </Badge>
                      {query.rowsAffected !== undefined && (
                        <Badge variant="outline" className="text-xs">
                          {query.rowsAffected} rows
                        </Badge>
                      )}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatTimestamp(query.timestamp)}
                    </span>
                  </div>
                  <code className="text-sm text-muted-foreground block overflow-hidden text-ellipsis">
                    {query.query}
                  </code>
                </div>
              ))}
              {recentQueries.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No recent queries
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Connection Information</CardTitle>
            <CardDescription>
              Database connection details for your application
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {connectionInfo && Object.entries({
              Host: connectionInfo.host,
              Port: connectionInfo.port,
              Database: connectionInfo.database,
              Username: connectionInfo.username,
              SSL: connectionInfo.ssl,
              Provider: connectionInfo.provider.toUpperCase()
            }).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center p-3 rounded-lg glass-effect">
                <span className="text-sm font-medium">{key}:</span>
                <div className="flex items-center space-x-2">
                  <code className="text-sm bg-muted px-2 py-1 rounded">{value}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(value)}
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
            {connectionInfo && (
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => copyToClipboard(`postgresql://${connectionInfo.username}@${connectionInfo.host}:${connectionInfo.port}/${connectionInfo.database}?sslmode=${connectionInfo.ssl}`)}
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy Connection String
              </Button>
            )}
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Database Tools</CardTitle>
            <CardDescription>
              Essential tools for database management and monitoring
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-3">
              <Button 
                variant="outline" 
                className="justify-start h-auto p-4"
                onClick={() => navigate('/sql-editor')}
              >
                <div className="text-left">
                  <div className="flex items-center space-x-2 mb-1">
                    <DatabaseIcon className="w-4 h-4" />
                    <p className="font-medium">SQL Editor</p>
                  </div>
                  <p className="text-sm text-muted-foreground">Execute SQL queries directly</p>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="justify-start h-auto p-4"
                onClick={() => navigate('/table-editor')}
              >
                <div className="text-left">
                  <div className="flex items-center space-x-2 mb-1">
                    <Table className="w-4 h-4" />
                    <p className="font-medium">Table Editor</p>
                  </div>
                  <p className="text-sm text-muted-foreground">Manage table structure and data</p>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="justify-start h-auto p-4"
                onClick={() => navigate('/analytics')}
              >
                <div className="text-left">
                  <div className="flex items-center space-x-2 mb-1">
                    <TrendingUp className="w-4 h-4" />
                    <p className="font-medium">Performance Analytics</p>
                  </div>
                  <p className="text-sm text-muted-foreground">Analyze database performance</p>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
