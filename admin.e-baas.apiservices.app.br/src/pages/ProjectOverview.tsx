
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Database, 
  Users, 
  Shield, 
  Activity, 
  ExternalLink,
  Code,
  Zap,
  Globe,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw
} from "lucide-react";
import { useAnalytics, AnalyticsOverview, UsageMetrics, ActivityEvent, WorkspaceInfo } from "@/services/analytics.service";
import { useAuth } from "@/contexts/AuthContext";

export default function ProjectOverview() {
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [usage, setUsage] = useState<UsageMetrics | null>(null);
  const [activity, setActivity] = useState<ActivityEvent[]>([]);
  const [workspaceInfo, setWorkspaceInfo] = useState<WorkspaceInfo | null>(null);
  const [loading, setLoading] = useState(true);
  
  const analytics = useAnalytics();
  const { isPlatformAdmin, user } = useAuth();

  const libraries = [
    { name: "TypeScript SDK", icon: Code, color: "bg-blue-500", description: "@e-baas/sdk" },
    { name: "REST API", icon: Globe, color: "bg-green-500", description: "Full REST API" },
    { name: "MCP Server", icon: Zap, color: "bg-purple-500", description: "AI Development" },
    { name: "WebSocket", icon: Activity, color: "bg-orange-500", description: "Realtime Features" },
  ];

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      const [overviewData, usageData, activityData, workspaceData] = await Promise.all([
        analytics.getOverview(),
        analytics.getUsageMetrics(),
        analytics.getActivityEvents(),
        analytics.getWorkspaceInfo(),
      ]);
      
      setOverview(overviewData);
      setUsage(usageData);
      setActivity(activityData);
      setWorkspaceInfo(workspaceData);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, []);

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />;
      default: return <Minus className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  };

  const stats = overview ? [
    { 
      title: "API Requests", 
      value: overview.totalRequests.value.toLocaleString(), 
      change: overview.totalRequests.change,
      trend: overview.totalRequests.trend,
      icon: Activity 
    },
    { 
      title: "Active Users", 
      value: overview.activeUsers.value.toLocaleString(), 
      change: overview.activeUsers.change,
      trend: overview.activeUsers.trend,
      icon: Users 
    },
    { 
      title: "Database Queries", 
      value: overview.databaseQueries.value.toLocaleString(), 
      change: overview.databaseQueries.change,
      trend: overview.databaseQueries.trend,
      icon: Database 
    },
    { 
      title: "Response Time", 
      value: overview.responseTime.value, 
      change: overview.responseTime.change,
      trend: overview.responseTime.trend,
      icon: Shield 
    },
  ] : [];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isPlatformAdmin 
              ? "Platform Administration" 
              : `Welcome to ${workspaceInfo?.name || 'your workspace'}`}
          </h1>
          <p className="text-muted-foreground mt-2">
            {isPlatformAdmin 
              ? "Manage the entire E-BaaS platform and all workspaces."
              : "Manage your database, authentication, storage, and more from this dashboard."}
          </p>
          {workspaceInfo && (
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant={workspaceInfo.isActive ? "default" : "secondary"}>
                {workspaceInfo.plan}
              </Badge>
              <span className="text-xs text-muted-foreground">
                Last activity: {formatTimestamp(workspaceInfo.lastActivity)}
              </span>
            </div>
          )}
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={loadAnalytics}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button className="bg-primary hover:bg-primary/90">
            <ExternalLink className="w-4 h-4 mr-2" />
            API Docs
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <Card key={stat.title} className="gradient-border">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center space-x-1 mt-1">
                {getTrendIcon(stat.trend)}
                <span className={`text-xs font-medium ${
                  stat.trend === 'up' ? 'text-green-600' : 
                  stat.trend === 'down' ? 'text-red-600' : 'text-muted-foreground'
                }`}>
                  {stat.change} from last month
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>E-BaaS Integration</CardTitle>
            <CardDescription>
              Choose your preferred way to integrate with E-BaaS
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {libraries.map((library) => (
              <div key={library.name} className="flex items-center justify-between p-3 rounded-lg glass-effect">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-md ${library.color} flex items-center justify-center`}>
                    <library.icon className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <span className="font-medium">{library.name}</span>
                    <p className="text-xs text-muted-foreground">{library.description}</p>
                  </div>
                  {library.name === "MCP Server" && (
                    <Badge variant="secondary" className="text-xs">AI</Badge>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm">Docs</Button>
                  <Button variant="ghost" size="sm">Examples</Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Usage Overview</CardTitle>
            <CardDescription>
              Current resource usage and limits for your workspace
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {usage && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Database Storage</span>
                    <Badge variant="secondary">{usage.databaseStorage.used} / {usage.databaseStorage.total}</Badge>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${usage.databaseStorage.percentage}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">File Storage</span>
                    <Badge variant="secondary">{usage.fileStorage.used} / {usage.fileStorage.total}</Badge>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${usage.fileStorage.percentage}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Monthly Requests</span>
                    <Badge variant="secondary">{usage.monthlyRequests.used} / {usage.monthlyRequests.total}</Badge>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${usage.monthlyRequests.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks to manage your E-BaaS workspace
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-3">
              <Button variant="outline" className="justify-start h-auto p-4">
                <div className="text-left">
                  <p className="font-medium">Database Explorer</p>
                  <p className="text-sm text-muted-foreground">Browse tables and execute queries</p>
                </div>
              </Button>
              <Button variant="outline" className="justify-start h-auto p-4">
                <div className="text-left">
                  <p className="font-medium">User Management</p>
                  <p className="text-sm text-muted-foreground">Manage authentication and users</p>
                </div>
              </Button>
              <Button variant="outline" className="justify-start h-auto p-4">
                <div className="text-left">
                  <p className="font-medium">Storage Manager</p>
                  <p className="text-sm text-muted-foreground">Upload and manage files</p>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest events from your workspace
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activity.map((event) => (
                <div key={event.id} className="flex items-start space-x-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    event.type === 'success' ? 'bg-green-500' :
                    event.type === 'info' ? 'bg-blue-500' :
                    event.type === 'warning' ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}></div>
                  <div className="space-y-1 flex-1">
                    <p className="text-sm font-medium">{event.message}</p>
                    <p className="text-xs text-muted-foreground">{formatTimestamp(event.timestamp)}</p>
                  </div>
                </div>
              ))}
              {activity.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No recent activity
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
