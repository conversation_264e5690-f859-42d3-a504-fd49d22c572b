import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Key, 
  Database,
  Edit,
  Trash2,
  Filter,
  RefreshCw,
  Eye,
  Settings
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDatabase, TableInfo, QueryResult } from "@/services/database.service";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useSearchParams, useNavigate } from "react-router-dom";

export default function TableEditor() {
  const [searchTerm, setSearchTerm] = useState("");
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const [tableData, setTableData] = useState<QueryResult | null>(null);
  const [loading, setLoading] = useState(true);
  
  const database = useDatabase();
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const tableFromUrl = searchParams.get('table');

  const loadTables = async () => {
    setLoading(true);
    try {
      const tablesData = await database.getTables();
      setTables(tablesData);
      
      // If there's a table in URL params, select it
      if (tableFromUrl && tablesData.find(t => t.name === tableFromUrl)) {
        setSelectedTable(tableFromUrl);
        await loadTableData(tableFromUrl);
      }
    } catch (error: any) {
      console.error('Error loading tables:', error);
      toast({
        title: "Error",
        description: "Failed to load tables",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadTableData = async (tableName: string) => {
    try {
      const data = await database.getTableData(tableName, 100, 0);
      setTableData(data);
    } catch (error: any) {
      console.error('Error loading table data:', error);
      toast({
        title: "Error",
        description: `Failed to load data for table ${tableName}`,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadTables();
    }
  }, [isAuthenticated, tableFromUrl]);

  const handleTableSelect = async (tableName: string) => {
    setSelectedTable(tableName);
    navigate(`/table-editor?table=${tableName}`);
    await loadTableData(tableName);
  };

  const filteredTables = tables.filter(table =>
    table.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    table.schema.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Table Editor</h1>
          <p className="text-muted-foreground mt-2">
            Browse, edit, and manage your database tables
          </p>
          {selectedTable && (
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant="default">{selectedTable}</Badge>
              <span className="text-xs text-muted-foreground">
                {tableData?.rowCount || 0} rows loaded
              </span>
            </div>
          )}
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={loadTables}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={() => navigate('/sql-editor')}>
            <Plus className="w-4 h-4 mr-2" />
            SQL Editor
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Tables List */}
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Database Tables</CardTitle>
            <CardDescription>
              Select a table to view and edit its data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tables..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {filteredTables.map((table) => (
                <div
                  key={`${table.schema}.${table.name}`}
                  className={`p-3 rounded-lg cursor-pointer transition-colors hover:bg-muted/50 ${
                    selectedTable === table.name ? 'bg-primary/10 border border-primary/20' : 'glass-effect'
                  }`}
                  onClick={() => handleTableSelect(table.name)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
                        <Database className="w-4 h-4 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{table.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {table.rows.toLocaleString()} rows • {table.schema}
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {table.type}
                    </Badge>
                  </div>
                </div>
              ))}
              {filteredTables.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No tables found
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Table Data */}
        <div className="lg:col-span-2">
          {selectedTable && tableData ? (
            <Card className="gradient-border">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Table: {selectedTable}</CardTitle>
                    <CardDescription>
                      {tableData.rowCount} rows • {tableData.columns.length} columns
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Filter className="w-4 h-4 mr-2" />
                      Filter
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem>
                          <Eye className="w-4 h-4 mr-2" />
                          View Schema
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="w-4 h-4 mr-2" />
                          Table Settings
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Structure
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="w-4 h-4 mr-2" />
                          Drop Table
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border overflow-auto max-h-96">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {tableData.columns.map((column, index) => (
                          <TableHead key={index} className="font-medium">
                            {column}
                          </TableHead>
                        ))}
                        <TableHead className="w-[50px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tableData.rows.map((row, rowIndex) => (
                        <TableRow key={rowIndex}>
                          {row.map((cell, cellIndex) => (
                            <TableCell key={cellIndex} className="max-w-xs truncate">
                              {cell === null ? (
                                <span className="text-muted-foreground italic">NULL</span>
                              ) : (
                                String(cell)
                              )}
                            </TableCell>
                          ))}
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem>
                                  <Edit className="w-4 h-4 mr-2" />
                                  Edit Row
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete Row
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                
                {tableData.rowCount > 100 && (
                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing first 100 rows of {tableData.rowCount}
                    </p>
                    <Button variant="outline" size="sm">
                      Load More
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card className="gradient-border">
              <CardContent className="flex items-center justify-center h-96">
                <div className="text-center">
                  <Database className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium">Select a table to get started</p>
                  <p className="text-sm text-muted-foreground">
                    Choose a table from the list to view and edit its data
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}