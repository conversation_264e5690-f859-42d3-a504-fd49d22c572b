import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { CodeEditor } from "@/components/CodeEditor";
import { useSupabase } from "@/contexts/SupabaseContext";
import { SupabaseConfig } from "@/components/SupabaseConfig";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Zap,
  Edit,
  Trash2,
  ExternalLink,
  Play,
  Globe
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface EdgeFunction {
  id: string;
  name: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  url: string;
  code?: string;
}

export default function CloudFunctions() {
  const [searchTerm, setSearchTerm] = useState("");
  const [functions, setFunctions] = useState<EdgeFunction[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFunction, setSelectedFunction] = useState<EdgeFunction | null>(null);
  const [functionName, setFunctionName] = useState("");
  const [functionCode, setFunctionCode] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const { isConfigured, config } = useSupabase();
  const { toast } = useToast();

  const defaultFunctionCode = `import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { name } = await req.json()
    
    const data = {
      message: \`Hello \${name}!\`,
      timestamp: new Date().toISOString(),
    }

    return new Response(
      JSON.stringify(data),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      },
    )
  }
})`;

  const fetchFunctions = async () => {
    if (!isConfigured) return;
    
    setLoading(true);
    try {
      // Simulated functions for demo - in real implementation this would fetch from Supabase Management API
      const mockFunctions: EdgeFunction[] = [
        {
          id: '1',
          name: 'hello-world',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          url: `${config?.url}/functions/v1/hello-world`,
          code: defaultFunctionCode
        },
        {
          id: '2',
          name: 'process-payment',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          url: `${config?.url}/functions/v1/process-payment`
        }
      ];
      
      setFunctions(mockFunctions);
    } catch (error: any) {
      console.error('Error fetching functions:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar functions: " + error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createFunction = async () => {
    if (!functionName.trim()) {
      toast({
        title: "Erro",
        description: "Nome da function é obrigatório",
        variant: "destructive",
      });
      return;
    }

    try {
      // In real implementation, this would create the function via Supabase Management API
      const newFunction: EdgeFunction = {
        id: Date.now().toString(),
        name: functionName,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        url: `${config?.url}/functions/v1/${functionName}`,
        code: functionCode || defaultFunctionCode
      };

      setFunctions([newFunction, ...functions]);
      setFunctionName("");
      setFunctionCode("");
      setDialogOpen(false);
      
      toast({
        title: "Sucesso",
        description: "Edge Function criada com sucesso!",
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const testFunction = async (func: EdgeFunction) => {
    try {
      const response = await fetch(func.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config?.key}`,
        },
        body: JSON.stringify({ name: 'Test User' })
      });

      if (response.ok) {
        toast({
          title: "Sucesso",
          description: "Function executada com sucesso!",
        });
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error: any) {
      toast({
        title: "Erro no Teste",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (isConfigured) {
      fetchFunctions();
    }
  }, [isConfigured]);

  const filteredFunctions = functions.filter(func => 
    func.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isConfigured) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight">Edge Functions</h1>
          <p className="text-muted-foreground mt-2">
            Configure o Supabase para começar
          </p>
        </div>
        <SupabaseConfig />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edge Functions</h1>
          <p className="text-muted-foreground mt-2">
            Gerencie suas Edge Functions serverless
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-primary hover:bg-primary/90">
              <Plus className="w-4 h-4 mr-2" />
              Nova Function
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-card border border-border max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Criar Edge Function</DialogTitle>
              <DialogDescription>
                Crie uma nova Edge Function serverless
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="function-name">Nome da Function</Label>
                <Input
                  id="function-name"
                  placeholder="hello-world"
                  value={functionName}
                  onChange={(e) => setFunctionName(e.target.value)}
                />
              </div>
              <div>
                <Label>Código TypeScript/JavaScript</Label>
                <CodeEditor
                  title=""
                  language="typescript"
                  initialCode={defaultFunctionCode}
                  height="400px"
                  onSave={async (code) => setFunctionCode(code)}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button onClick={createFunction}>
                  Criar Function
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Card className="gradient-border">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Edge Functions</CardTitle>
              <CardDescription>
                {loading ? "Carregando..." : `${filteredFunctions.length} functions disponíveis`}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar functions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 w-64"
                />
              </div>
              <Button variant="outline" size="sm" onClick={fetchFunctions} disabled={loading}>
                <Zap className="w-4 h-4 mr-2" />
                Atualizar
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Carregando functions...</p>
            </div>
          ) : filteredFunctions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Nenhuma function encontrada</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>Criada em</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredFunctions.map((func) => (
                  <TableRow key={func.id} className="hover:bg-accent/50 transition-colors">
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <Zap className="w-4 h-4 text-muted-foreground" />
                        <span>{func.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={func.status === "active" ? "default" : "secondary"}>
                        {func.status === "active" ? "Ativa" : "Inativa"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <code className="text-sm bg-muted px-2 py-1 rounded">{func.url}</code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(func.url, '_blank')}
                        >
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {new Date(func.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-popover border border-border">
                          <DropdownMenuItem onClick={() => testFunction(func)}>
                            <Play className="w-4 h-4 mr-2" />
                            Testar Function
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setSelectedFunction(func)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Editar Código
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Globe className="w-4 h-4 mr-2" />
                            Ver Logs
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-destructive">
                            <Trash2 className="w-4 h-4 mr-2" />
                            Deletar Function
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {selectedFunction && (
        <Card className="gradient-border">
          <CardHeader>
            <CardTitle>Editor de Código - {selectedFunction.name}</CardTitle>
            <CardDescription>
              Edite o código da sua Edge Function
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CodeEditor
              title=""
              language="typescript"
              initialCode={selectedFunction.code || defaultFunctionCode}
              height="500px"
              onSave={async (code) => {
                // In real implementation, this would update the function via Management API
                toast({
                  title: "Sucesso",
                  description: "Código da function atualizado!",
                });
              }}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
