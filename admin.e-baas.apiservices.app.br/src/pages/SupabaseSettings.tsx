
import { SupabaseConfig } from "@/components/SupabaseConfig";
import { useSupabase } from "@/contexts/SupabaseContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Database, CheckCircle, XCircle } from "lucide-react";

export default function SupabaseSettings() {
  const { isConfigured, config } = useSupabase();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Configurações</h1>
          <p className="text-muted-foreground mt-2">
            Configure sua conexão com o Supabase
          </p>
        </div>
        <Badge variant={isConfigured ? "default" : "destructive"} className="flex items-center space-x-1">
          {isConfigured ? <CheckCircle className="w-3 h-3" /> : <XCircle className="w-3 h-3" />}
          <span>{isConfigured ? "Conectado" : "Desconectado"}</span>
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SupabaseConfig />
        
        {isConfigured && (
          <Card className="gradient-border">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="w-5 h-5" />
                <span>Status da Conexão</span>
              </CardTitle>
              <CardDescription>
                Informações sobre sua conexão atual
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">URL:</span>
                <code className="text-sm bg-muted px-2 py-1 rounded">{config?.url}</code>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Status:</span>
                <Badge variant="default">Conectado</Badge>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
