
import { <PERSON>bar<PERSON>rov<PERSON>, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "./AppSidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Database, User, LogOut, Settings, Shield, Book } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useEBaaS } from "@/contexts/EBaaSContext";

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const { user, logout, isPlatformAdmin, isWorkspaceAdmin } = useAuth();
  const { config } = useEBaaS();

  const getUserInitials = (email: string) => {
    return email.split('@')[0].substring(0, 2).toUpperCase();
  };

  const getRoleBadge = () => {
    if (isPlatformAdmin) {
      return <Badge variant="default" className="text-xs">Platform Admin</Badge>;
    } else if (isWorkspaceAdmin) {
      return <Badge variant="secondary" className="text-xs">Workspace Admin</Badge>;
    }
    return <Badge variant="outline" className="text-xs">User</Badge>;
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <main className="flex-1 flex flex-col">
          <header className="border-b border-border bg-card px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <SidebarTrigger className="text-muted-foreground hover:text-foreground" />
                <div className="flex items-center space-x-2 text-sm">
                  <span className="text-foreground font-medium">E-BaaS Admin</span>
                  <div className="flex items-center space-x-1 text-green-400">
                    <Database className="w-3 h-3" />
                    <span className="text-xs">Connected</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Button variant="outline" size="sm" className="text-xs">
                  <Book className="w-3 h-3 mr-1" />
                  Docs
                </Button>
                
                {/* User Menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user?.email}`} alt={user?.email} />
                        <AvatarFallback>{getUserInitials(user?.email || 'U')}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{user?.email}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          {getRoleBadge()}
                          {isPlatformAdmin && (
                            <Shield className="w-3 h-3 text-primary" />
                          )}
                        </div>
                        {user?.workspaceId && (
                          <p className="text-xs leading-none text-muted-foreground">
                            Workspace: {user.workspaceId.substring(0, 8)}...
                          </p>
                        )}
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={logout} className="text-red-600">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </header>
          
          <div className="flex-1 p-6 animate-fade-in">
            {children}
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
}
