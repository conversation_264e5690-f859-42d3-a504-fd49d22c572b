
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Play, Save, Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface CodeEditorProps {
  title: string;
  initialCode?: string;
  language?: string;
  onExecute?: (code: string) => Promise<any>;
  onSave?: (code: string) => Promise<void>;
  height?: string;
  readOnly?: boolean;
}

export function CodeEditor({ 
  title, 
  initialCode = "", 
  language = "sql", 
  onExecute, 
  onSave,
  height = "400px",
  readOnly = false
}: CodeEditorProps) {
  const [code, setCode] = useState(initialCode);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    setCode(initialCode);
  }, [initialCode]);

  const handleExecute = async () => {
    if (!onExecute || !code.trim()) return;
    
    setLoading(true);
    try {
      await onExecute(code);
      toast({
        title: "Sucesso",
        description: "Código executado com sucesso!",
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!onSave) return;
    
    try {
      await onSave(code);
      toast({
        title: "Sucesso",
        description: "Código salvo com sucesso!",
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    toast({
      title: "Copiado",
      description: "Código copiado para a área de transferência",
    });
  };

  return (
    <Card className="gradient-border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleCopy}>
              <Copy className="w-4 h-4 mr-2" />
              Copiar
            </Button>
            {onSave && (
              <Button variant="outline" size="sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Salvar
              </Button>
            )}
            {onExecute && (
              <Button size="sm" onClick={handleExecute} disabled={loading || !code.trim()}>
                <Play className="w-4 h-4 mr-2" />
                {loading ? "Executando..." : "Executar"}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Textarea
          value={code}
          onChange={(e) => setCode(e.target.value)}
          placeholder={`Digite seu código ${language} aqui...`}
          className={`font-mono text-sm resize-none`}
          style={{ height }}
          readOnly={readOnly}
        />
      </CardContent>
    </Card>
  );
}
