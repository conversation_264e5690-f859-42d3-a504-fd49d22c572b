
import { 
  Database, 
  Table, 
  Users, 
  Shield, 
  FileText, 
  Settings, 
  Building2,
  Home,
  FolderOpen,
  Activity,
  Key,
  Globe,
  Logs,
  BarChart3,
  Book,
  Zap
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";
import { Badge } from "@/components/ui/badge";
import { useLocation, Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

export function AppSidebar() {
  const location = useLocation();
  const { user, isPlatformAdmin, isWorkspaceAdmin } = useAuth();

  // Project/Overview items - available to all admins
  const projectItems = [
    {
      title: "Dashboard",
      url: "/",
      icon: Home,
      description: "Overview and metrics"
    },
    {
      title: "Analytics",
      url: "/analytics", 
      icon: BarChart3,
      description: "Usage analytics"
    }
  ];

  // Database management items
  const databaseItems = [
    {
      title: "Database Explorer",
      url: "/database",
      icon: Database,
      description: "Browse tables and data"
    },
    {
      title: "Table Editor",
      url: "/table-editor",
      icon: Table,
      description: "Edit table structure"
    },
    {
      title: "SQL Editor",
      url: "/sql-editor",
      icon: FileText,
      description: "Execute SQL queries"
    }
  ];

  // Core services items
  const servicesItems = [
    {
      title: "Authentication",
      url: "/authentication",
      icon: Users,
      description: "User management"
    },
    {
      title: "Storage",
      url: "/storage",
      icon: FolderOpen,
      description: "File management"
    },
    {
      title: "Realtime",
      url: "/realtime",
      icon: Activity,
      description: "Live connections"
    },
    {
      title: "Edge Functions",
      url: "/functions",
      icon: Zap,
      description: "Serverless functions"
    }
  ];

  // Management items (varies by admin type)
  const managementItems = [
    ...(isPlatformAdmin ? [{
      title: "All Workspaces",
      url: "/workspaces",
      icon: Building2,
      description: "Platform management",
      platformOnly: true
    }] : []),
    {
      title: "API Keys",
      url: "/api-keys",
      icon: Key,
      description: "Access management"
    },
    {
      title: "Logs",
      url: "/logs",
      icon: Logs,
      description: "System logs"
    }
  ];

  // Documentation items
  const docItems = [
    {
      title: "API Docs",
      url: "/api-docs",
      icon: Globe,
      description: "REST API reference"
    },
    {
      title: "SDK Guide",
      url: "/docs/sdk",
      icon: Book,
      description: "TypeScript SDK"
    },
    {
      title: "MCP Guide",
      url: "/docs/mcp",
      icon: Book,
      description: "AI development"
    }
  ];

  const getWorkspaceDisplayName = () => {
    if (isPlatformAdmin) {
      return "Platform Administration";
    }
    if (user?.workspaceId) {
      return `Workspace ${user.workspaceId.substring(0, 8)}...`;
    }
    return "E-BaaS Workspace";
  };

  return (
    <Sidebar className="border-r border-sidebar-border">
      <SidebarHeader className="p-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
            <Database className="w-4 h-4 text-primary-foreground" />
          </div>
          <div className="flex-1">
            <h2 className="text-sm font-semibold">{getWorkspaceDisplayName()}</h2>
            <div className="flex items-center space-x-1">
              <p className="text-xs text-muted-foreground">Connected</p>
              {isPlatformAdmin && (
                <Badge variant="default" className="text-xs px-1 py-0">
                  Platform
                </Badge>
              )}
            </div>
          </div>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        {/* Project Overview */}
        <SidebarGroup>
          <SidebarGroupLabel>Overview</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {projectItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    isActive={location.pathname === item.url}
                    className="transition-colors duration-200"
                  >
                    <Link to={item.url}>
                      <item.icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Database */}
        <SidebarGroup>
          <SidebarGroupLabel>Database</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {databaseItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    isActive={location.pathname === item.url}
                    className="transition-colors duration-200"
                  >
                    <Link to={item.url}>
                      <item.icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Services */}
        <SidebarGroup>
          <SidebarGroupLabel>Services</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {servicesItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    isActive={location.pathname === item.url}
                    className="transition-colors duration-200"
                  >
                    <Link to={item.url}>
                      <item.icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Management */}
        <SidebarGroup>
          <SidebarGroupLabel>Management</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {managementItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    isActive={location.pathname === item.url}
                    className="transition-colors duration-200"
                  >
                    <Link to={item.url}>
                      <item.icon className="w-4 h-4" />
                      <div className="flex flex-col items-start">
                        <span>{item.title}</span>
                        {item.platformOnly && (
                          <Badge variant="outline" className="text-xs px-1 py-0 mt-0.5">
                            Platform
                          </Badge>
                        )}
                      </div>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Documentation */}
        <SidebarGroup>
          <SidebarGroupLabel>Documentation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {docItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    isActive={location.pathname === item.url}
                    className="transition-colors duration-200"
                  >
                    <Link to={item.url}>
                      <item.icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Link to="/settings">
                <Settings className="w-4 h-4" />
                <span>Settings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
