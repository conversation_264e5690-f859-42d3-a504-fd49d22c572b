
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useSupabase } from '@/contexts/SupabaseContext';
import { Database, Key, Globe } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export function SupabaseConfig() {
  const { setConfig, config } = useSupabase();
  const { toast } = useToast();
  const [url, setUrl] = useState(config?.url || '');
  const [key, setKey] = useState(config?.key || '');
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!url || !key) {
      toast({
        title: "Erro",
        description: "Por favor, preencha URL e Key",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      setConfig({ url, key });
      toast({
        title: "Sucesso",
        description: "Configuração do Supabase salva com sucesso!",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao salvar configuração",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="gradient-border max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Database className="w-5 h-5" />
          <span>Configuração do Supabase</span>
        </CardTitle>
        <CardDescription>
          Configure a URL e a key do seu Supabase self-hosted
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="url" className="flex items-center space-x-2">
            <Globe className="w-4 h-4" />
            <span>URL do Supabase</span>
          </Label>
          <Input
            id="url"
            type="url"
            placeholder="https://your-project.supabase.co"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="key" className="flex items-center space-x-2">
            <Key className="w-4 h-4" />
            <span>Anon Key</span>
          </Label>
          <Input
            id="key"
            type="password"
            placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            value={key}
            onChange={(e) => setKey(e.target.value)}
          />
        </div>
        
        <Button 
          onClick={handleSave} 
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? "Salvando..." : "Salvar Configuração"}
        </Button>
      </CardContent>
    </Card>
  );
}
