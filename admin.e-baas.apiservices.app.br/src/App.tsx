
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { EBaaSProvider } from "./contexts/EBaaSContext";
import { AuthProvider } from "./contexts/AuthContext";
import { Layout } from "./components/Layout";
import { AuthWrapper } from "./components/AuthWrapper";
import ProjectOverview from "./pages/ProjectOverview";
import TableEditor from "./pages/TableEditor";
import Database from "./pages/Database";
import Workspaces from "./pages/Workspaces";
import Analytics from "./pages/Analytics";
import Functions from "./pages/Functions";
import ApiKeys from "./pages/ApiKeys";
import Authentication from "./pages/Authentication";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";
import SupabaseSettings from "./pages/SupabaseSettings";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <EBaaSProvider>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              {/* Public routes */}
              <Route path="/login" element={<Login />} />
              
              {/* Protected admin routes */}
              <Route path="/*" element={
                <AuthWrapper requireAdmin={true}>
                  <Layout>
                    <Routes>
                      <Route path="/" element={<ProjectOverview />} />
                      <Route path="/analytics" element={<Analytics />} />
                      <Route path="/table-editor" element={<TableEditor />} />
                      <Route path="/database" element={<Database />} />
                      <Route path="/workspaces" element={<Workspaces />} />
                      <Route path="/functions" element={<Functions />} />
                      <Route path="/api-keys" element={<ApiKeys />} />
                      <Route path="/sql-editor" element={<div className="text-center p-8 text-muted-foreground">SQL Editor - Coming Soon</div>} />
                      <Route path="/authentication" element={<Authentication />} />
                      <Route path="/storage" element={<div className="text-center p-8 text-muted-foreground">Storage - Coming Soon</div>} />
                      <Route path="/realtime" element={<div className="text-center p-8 text-muted-foreground">Realtime - Coming Soon</div>} />
                      <Route path="/api-docs" element={<div className="text-center p-8 text-muted-foreground">API Documentation - Coming Soon</div>} />
                      <Route path="/docs/sdk" element={<div className="text-center p-8 text-muted-foreground">SDK Documentation - Coming Soon</div>} />
                      <Route path="/docs/mcp" element={<div className="text-center p-8 text-muted-foreground">MCP Documentation - Coming Soon</div>} />
                      <Route path="/logs" element={<div className="text-center p-8 text-muted-foreground">Logs - Coming Soon</div>} />
                      <Route path="/settings" element={<SupabaseSettings />} />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </Layout>
                </AuthWrapper>
              } />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </EBaaSProvider>
  </QueryClientProvider>
);

export default App;
