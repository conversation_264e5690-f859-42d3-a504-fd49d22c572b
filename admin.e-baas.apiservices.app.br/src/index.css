
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 214 19% 5%;
    --foreground: 210 20% 98%;

    --card: 217 19% 7%;
    --card-foreground: 210 20% 98%;

    --popover: 217 19% 7%;
    --popover-foreground: 210 20% 98%;

    --primary: 142 86% 28%;
    --primary-foreground: 210 20% 98%;

    --secondary: 217 19% 12%;
    --secondary-foreground: 210 20% 98%;

    --muted: 217 19% 12%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217 19% 12%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 217 19% 12%;
    --input: 217 19% 12%;
    --ring: 142 86% 28%;

    --radius: 0.5rem;

    --sidebar-background: 214 19% 4%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 142 86% 28%;
    --sidebar-primary-foreground: 210 20% 98%;
    --sidebar-accent: 217 19% 8%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 217 19% 12%;
    --sidebar-ring: 142 86% 28%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  }
}

@layer components {
  .gradient-border {
    background: linear-gradient(145deg, rgb(20, 20, 23), rgb(25, 25, 28));
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
