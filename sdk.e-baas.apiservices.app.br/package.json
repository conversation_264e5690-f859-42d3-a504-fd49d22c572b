{"name": "@e-baas/sdk", "version": "1.0.0", "description": "Official E-BaaS TypeScript SDK - Backend as a Service client library", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "dev": "tsup --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "typecheck": "tsc --noEmit", "prepare": "npm run build", "prepublishOnly": "npm run build && npm run test"}, "keywords": ["e-baas", "supabase", "baas", "backend-as-a-service", "sdk", "database", "auth", "storage", "realtime", "typescript"], "author": "E-BaaS Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/e-baas"}, "homepage": "https://e-baas.apiservices.app.br", "peerDependencies": {"typescript": ">=4.5.0"}, "dependencies": {"axios": "^1.9.0", "eventemitter3": "^4.0.7", "socket.io-client": "^4.8.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "jest": "^29.7.0", "rollup": "^4.12.0", "rollup-plugin-dts": "^6.1.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.3.3"}}