# E-BaaS SDK

The official TypeScript SDK for E-BaaS (Enterprise Backend as a Service) - A comprehensive backend-as-a-service platform similar to Supabase.

## Installation

```bash
npm install @e-baas/sdk
# or
yarn add @e-baas/sdk
```

## Quick Start

```typescript
import { createClient } from '@e-baas/sdk';

const ebaas = createClient('https://your-project.e-baas.io', 'your-anon-key');
```

## Features

- **Authentication** - Email/password and OAuth providers (Google, GitHub, Facebook)
- **Database** - PostgreSQL with real-time subscriptions and RPC support
- **Storage** - File uploads with CDN integration and image transformations
- **Realtime** - WebSocket subscriptions for live data updates
- **Edge Functions** - Serverless functions with Deno runtime
- **TypeScript** - Full type safety and IntelliSense support

## Usage Examples

### Authentication

```typescript
// Sign up with email/password
const { user, session, error } = await ebaas.auth.signUp({
  email: '<EMAIL>',
  password: 'secure-password',
  name: '<PERSON>'
});

// Sign in
const { user, session, error } = await ebaas.auth.signIn({
  email: '<EMAIL>',
  password: 'secure-password'
});

// Get current user
const user = await ebaas.auth.getUser();

// Sign out
await ebaas.auth.signOut();
```

### Database Operations

```typescript
// Select data
const { data, error } = await ebaas
  .from('users')
  .select('id, name, email')
  .eq('status', 'active')
  .order('created_at', { ascending: false })
  .limit(10)
  .execute();

// Insert data
const { data, error } = await ebaas
  .from('users')
  .insert([
    { name: 'John Doe', email: '<EMAIL>' },
    { name: 'Jane Smith', email: '<EMAIL>' }
  ]);

// Update data
const { data, error } = await ebaas
  .from('users')
  .update({ status: 'inactive' })
  .eq('id', 123);

// Delete data
const { data, error } = await ebaas
  .from('users')
  .delete()
  .eq('status', 'inactive');

// RPC (stored procedures)
const { data, error } = await ebaas.rpc('get_user_stats', {
  user_id: 123
});
```

### Storage

```typescript
// Upload file
const { data, error } = await ebaas.storage
  .from('avatars')
  .upload('user-123.jpg', file, {
    cacheControl: '3600',
    upsert: true
  });

// Download file
const { data, error } = await ebaas.storage
  .from('avatars')
  .download('user-123.jpg');

// Get public URL
const { data } = ebaas.storage
  .from('avatars')
  .getPublicUrl('user-123.jpg');

// Get signed URL (for private files)
const { data, error } = await ebaas.storage
  .from('private-docs')
  .createSignedUrl('document.pdf', 60); // 60 seconds

// List files
const { data, error } = await ebaas.storage
  .from('avatars')
  .list('', {
    limit: 100,
    offset: 0,
    sortBy: { column: 'name', order: 'asc' }
  });
```

### Realtime Subscriptions

```typescript
// Subscribe to database changes
const channel = ebaas.channel('realtime:users');

channel.onPostgresChanges({
  event: '*', // INSERT, UPDATE, DELETE, or *
  schema: 'public',
  table: 'users'
}, (payload) => {
  console.log('Change received!', payload);
});

channel.subscribe();

// Broadcast messages
channel.send({
  type: 'broadcast',
  event: 'message',
  payload: { text: 'Hello World!' }
});

// Presence tracking
channel.track({ user_id: 123, status: 'online' });

// Unsubscribe
channel.unsubscribe();
```

### Edge Functions

```typescript
// Invoke function
const { data, error } = await ebaas.functions.invoke('hello-world', {
  body: { name: 'John' },
  headers: { 'Content-Type': 'application/json' }
});

// List functions
const { data, error } = await ebaas.functions.list();

// Get function details
const { data, error } = await ebaas.functions.get('hello-world');
```

## Advanced Configuration

```typescript
const ebaas = createClient('https://your-project.e-baas.io', 'your-anon-key', {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  global: {
    headers: {
      'x-custom-header': 'custom-value'
    }
  }
});
```

## Error Handling

```typescript
import { EBaaSError } from '@e-baas/sdk';

try {
  const { data } = await ebaas.from('users').select('*').execute();
} catch (error) {
  if (error instanceof EBaaSError) {
    console.error('E-BaaS Error:', error.message);
    console.error('Status:', error.status);
    console.error('Details:', error.details);
  }
}
```

## TypeScript Support

The SDK is built with TypeScript and provides full type safety:

```typescript
interface User {
  id: number;
  name: string;
  email: string;
  created_at: string;
}

const { data, error } = await ebaas
  .from<User>('users')
  .select('*')
  .execute();

// data is now typed as User[] | null
```

## License

MIT