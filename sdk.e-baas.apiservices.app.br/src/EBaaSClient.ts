import { HttpClient } from './infra';
import { EBaaSClientOptions } from './infra/types/common';
import { AuthClient } from './modules/auth/AuthClient';
import { DatabaseClient } from './modules/database/DatabaseClient';
import { StorageClient } from './modules/storage/StorageClient';
import { RealtimeClient } from './modules/realtime/RealtimeClient';
import { FunctionsClient } from './modules/functions/FunctionsClient';

export class EBaaSClient {
  public readonly auth: AuthClient;
  public readonly database: DatabaseClient;
  public readonly storage: StorageClient;
  public readonly realtime: RealtimeClient;
  public readonly functions: FunctionsClient;

  private httpClient: HttpClient;

  constructor(supabaseUrl: string, supabaseKey: string, options?: EBaaSClientOptions['options']) {
    // Initialize HTTP client
    this.httpClient = new HttpClient({
      url: supabaseUrl,
      apiKey: supabaseKey,
      timeout: 30000,
      headers: options?.global?.headers
    });

    // Initialize all modules
    this.auth = new AuthClient(this.httpClient);
    this.database = new DatabaseClient(this.httpClient);
    this.storage = new StorageClient(this.httpClient);
    this.realtime = new RealtimeClient(supabaseUrl, supabaseKey, options?.realtime);
    this.functions = new FunctionsClient(this.httpClient);
  }

  // Convenience method for database access
  from(table: string) {
    return this.database.from(table);
  }

  // Convenience method for RPC calls
  rpc(fn: string, args?: Record<string, any>) {
    return this.database.rpc(fn, args);
  }

  // Convenience method for storage bucket access
  bucket(id: string) {
    return this.storage.from(id);
  }

  // Convenience method for realtime channel access
  channel(name: string) {
    return this.realtime.channel(name);
  }

  // Convenience method for edge functions
  invoke(functionName: string, options?: any) {
    return this.functions.invoke(functionName, options);
  }
}

// Factory function similar to Supabase's createClient
export function createClient(
  supabaseUrl: string, 
  supabaseKey: string, 
  options?: EBaaSClientOptions['options']
): EBaaSClient {
  return new EBaaSClient(supabaseUrl, supabaseKey, options);
}