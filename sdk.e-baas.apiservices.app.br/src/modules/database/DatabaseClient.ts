import { HttpClient } from 'infra';
import { QueryBuilder } from './QueryBuilder';

export class DatabaseClient {
  private httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  from(table: string): QueryBuilder {
    return new QueryBuilder(this.httpClient, table);
  }

  rpc(fn: string, args?: Record<string, any>): Promise<any> {
    return this.httpClient.post(`/rpc/${fn}`, args);
  }

  schema(schema: string): DatabaseClient {
    const client = new DatabaseClient(this.httpClient);
    client.httpClient.setSchema?.(schema);
    return client;
  }
}