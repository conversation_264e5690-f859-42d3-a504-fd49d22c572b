import { HttpClient, EBaaSError } from 'infra';
import { 
  DatabaseFilters, 
  SelectOptions, 
  InsertOptions, 
  UpdateOptions, 
  DeleteOptions 
} from 'infra/types/common';

export class QueryBuilder<T = any> {
  private httpClient: HttpClient;
  private table: string;
  private query: {
    select?: string;
    filters: Array<{ column: string; operator: string; value: any }>;
    order?: Array<{ column: string; ascending: boolean }>;
    limit?: number;
    offset?: number;
    range?: { from: number; to: number };
  };

  constructor(httpClient: HttpClient, table: string) {
    this.httpClient = httpClient;
    this.table = table;
    this.query = {
      filters: []
    };
  }

  select(columns = '*', options?: SelectOptions): this {
    this.query.select = columns;
    return this;
  }

  insert(data: Partial<T> | Partial<T>[], options?: InsertOptions): Promise<{ data: T[] | null; error: EBaaSError | null }> {
    return this.executeQuery('POST', `/api/database/${this.table}`, { data, options });
  }

  update(data: Partial<T>, options?: UpdateOptions): Promise<{ data: T[] | null; error: EBaaSError | null }> {
    return this.executeQuery('PATCH', `/api/database/${this.table}`, { 
      data, 
      options,
      filters: this.query.filters 
    });
  }

  upsert(data: Partial<T> | Partial<T>[], options?: InsertOptions): Promise<{ data: T[] | null; error: EBaaSError | null }> {
    return this.executeQuery('POST', `/api/database/${this.table}`, { 
      data, 
      options: { ...options, upsert: true } 
    });
  }

  delete(options?: DeleteOptions): Promise<{ data: T[] | null; error: EBaaSError | null }> {
    return this.executeQuery('DELETE', `/api/database/${this.table}`, { 
      options,
      filters: this.query.filters 
    });
  }

  // Filter methods
  eq(column: string, value: any): this {
    this.query.filters.push({ column, operator: 'eq', value });
    return this;
  }

  neq(column: string, value: any): this {
    this.query.filters.push({ column, operator: 'neq', value });
    return this;
  }

  gt(column: string, value: any): this {
    this.query.filters.push({ column, operator: 'gt', value });
    return this;
  }

  gte(column: string, value: any): this {
    this.query.filters.push({ column, operator: 'gte', value });
    return this;
  }

  lt(column: string, value: any): this {
    this.query.filters.push({ column, operator: 'lt', value });
    return this;
  }

  lte(column: string, value: any): this {
    this.query.filters.push({ column, operator: 'lte', value });
    return this;
  }

  like(column: string, pattern: string): this {
    this.query.filters.push({ column, operator: 'like', value: pattern });
    return this;
  }

  ilike(column: string, pattern: string): this {
    this.query.filters.push({ column, operator: 'ilike', value: pattern });
    return this;
  }

  is(column: string, value: any): this {
    this.query.filters.push({ column, operator: 'is', value });
    return this;
  }

  in(column: string, values: any[]): this {
    this.query.filters.push({ column, operator: 'in', value: values });
    return this;
  }

  contains(column: string, value: any): this {
    this.query.filters.push({ column, operator: 'contains', value });
    return this;
  }

  match(query: Record<string, any>): this {
    Object.entries(query).forEach(([column, value]) => {
      this.eq(column, value);
    });
    return this;
  }

  // Ordering methods
  order(column: string, options?: { ascending?: boolean }): this {
    if (!this.query.order) {
      this.query.order = [];
    }
    this.query.order.push({ 
      column, 
      ascending: options?.ascending !== false 
    });
    return this;
  }

  // Limiting methods
  limit(count: number): this {
    this.query.limit = count;
    return this;
  }

  range(from: number, to: number): this {
    this.query.range = { from, to };
    return this;
  }

  // Execution method for SELECT
  async execute(): Promise<{ data: T[] | null; error: EBaaSError | null; count?: number }> {
    return this.executeQuery('GET', `/api/database/${this.table}`, {
      select: this.query.select,
      filters: this.query.filters,
      order: this.query.order,
      limit: this.query.limit,
      range: this.query.range
    });
  }

  // Single row execution
  async single(): Promise<{ data: T | null; error: EBaaSError | null }> {
    this.limit(1);
    const result = await this.execute();
    
    return {
      data: result.data?.[0] || null,
      error: result.error,
    };
  }

  private async executeQuery(
    method: string, 
    url: string, 
    payload?: any
  ): Promise<{ data: T[] | null; error: EBaaSError | null; count?: number }> {
    try {
      let response;
      
      if (method === 'GET') {
        const params = new URLSearchParams();
        if (payload.select) params.append('select', payload.select);
        if (payload.filters?.length) params.append('filters', JSON.stringify(payload.filters));
        if (payload.order?.length) params.append('order', JSON.stringify(payload.order));
        if (payload.limit) params.append('limit', payload.limit.toString());
        if (payload.range) params.append('range', JSON.stringify(payload.range));
        
        response = await this.httpClient.get(`${url}?${params.toString()}`);
      } else {
        response = await this.httpClient[method.toLowerCase() as 'post' | 'patch' | 'delete'](url, payload);
      }

      return {
        data: response.data,
        error: null,
        count: response.headers?.['x-total-count'] ? parseInt(response.headers['x-total-count']) : undefined
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Database operation failed',
          0,
          error
        )
      };
    }
  }
}