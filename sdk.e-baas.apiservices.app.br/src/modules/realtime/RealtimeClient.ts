import { io, Socket } from 'socket.io-client';
import { RealtimeChannel } from './RealtimeChannel';
import { RealtimeClientOptions, RealtimeConnectionState } from './types/realtime.types';

export class RealtimeClient {
  private socket: Socket | null = null;
  private url: string;
  private apiKey: string;
  private options: RealtimeClientOptions;
  private channels: Map<string, RealtimeChannel> = new Map();
  private connectionState: RealtimeConnectionState = 'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(url: string, apiKey: string, options: RealtimeClientOptions = {}) {
    this.url = url;
    this.apiKey = apiKey;
    this.options = {
      heartbeatInterval: 30000,
      reconnectInterval: 5000,
      maxReconnectAttempts: 5,
      ...options
    };
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.connectionState = 'connecting';

      this.socket = io(this.url, {
        auth: {
          apikey: this.apiKey
        },
        transports: ['websocket'],
        reconnection: true,
        reconnectionDelay: this.options.reconnectInterval,
        reconnectionAttempts: this.options.maxReconnectAttempts,
        ...this.options.socketOptions
      });

      this.socket.on('connect', () => {
        this.connectionState = 'connected';
        this.reconnectAttempts = 0;
        this.setupHeartbeat();
        resolve();
      });

      this.socket.on('disconnect', (reason) => {
        this.connectionState = 'disconnected';
        this.channels.forEach(channel => {
          channel._setState('closed');
        });
      });

      this.socket.on('connect_error', (error) => {
        this.connectionState = 'error';
        this.reconnectAttempts++;
        
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          reject(new Error(`Failed to connect after ${this.maxReconnectAttempts} attempts: ${error.message}`));
        }
      });

      this.socket.on('reconnect', () => {
        this.connectionState = 'connected';
        this.reconnectAttempts = 0;
        
        // Rejoin all channels
        this.channels.forEach(channel => {
          channel._rejoin();
        });
      });

      // Handle channel-specific events
      this.socket.on('postgres_changes', (payload) => {
        const channel = this.channels.get(payload.topic);
        if (channel) {
          channel._trigger('postgres_changes', payload);
        }
      });

      this.socket.on('broadcast', (payload) => {
        const channel = this.channels.get(payload.topic);
        if (channel) {
          channel._trigger('broadcast', payload);
        }
      });

      this.socket.on('presence', (payload) => {
        const channel = this.channels.get(payload.topic);
        if (channel) {
          channel._trigger('presence', payload);
        }
      });
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.connectionState = 'disconnected';
    this.channels.clear();
  }

  channel(topic: string): RealtimeChannel {
    if (this.channels.has(topic)) {
      return this.channels.get(topic)!;
    }

    const channel = new RealtimeChannel(topic, this.socket);
    this.channels.set(topic, channel);

    // Auto-connect if client is already connected
    if (this.connectionState === 'connected') {
      channel._join();
    }

    return channel;
  }

  removeChannel(channel: RealtimeChannel): void {
    const topic = channel.topic;
    if (this.channels.has(topic)) {
      this.channels.delete(topic);
      channel.unsubscribe();
    }
  }

  removeAllChannels(): void {
    this.channels.forEach(channel => {
      channel.unsubscribe();
    });
    this.channels.clear();
  }

  getChannels(): RealtimeChannel[] {
    return Array.from(this.channels.values());
  }

  isConnected(): boolean {
    return this.connectionState === 'connected' && !!this.socket?.connected;
  }

  getConnectionState(): RealtimeConnectionState {
    return this.connectionState;
  }

  onConnectionStateChange(callback: (state: RealtimeConnectionState) => void): () => void {
    const handler = () => callback(this.connectionState);
    
    if (this.socket) {
      this.socket.on('connect', () => {
        this.connectionState = 'connected';
        handler();
      });
      
      this.socket.on('disconnect', () => {
        this.connectionState = 'disconnected';
        handler();
      });
      
      this.socket.on('connect_error', () => {
        this.connectionState = 'error';
        handler();
      });
    }

    return () => {
      if (this.socket) {
        this.socket.off('connect', handler);
        this.socket.off('disconnect', handler);
        this.socket.off('connect_error', handler);
      }
    };
  }

  private setupHeartbeat(): void {
    if (!this.options.heartbeatInterval || !this.socket) return;

    const interval = setInterval(() => {
      if (this.socket?.connected) {
        this.socket.emit('heartbeat', { timestamp: Date.now() });
      } else {
        clearInterval(interval);
      }
    }, this.options.heartbeatInterval);
  }

  // Internal method for channels to access socket
  _getSocket(): Socket | null {
    return this.socket;
  }
}