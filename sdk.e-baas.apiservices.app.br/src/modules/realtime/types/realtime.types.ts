export interface RealtimeEvent {
  schema: string;
  table: string;
  commit_timestamp: string;
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: Record<string, any>;
  old?: Record<string, any>;
  errors?: string[];
}

export interface ChannelSubscription {
  unsubscribe: () => void;
}

export interface PresenceState {
  [key: string]: {
    metas: Array<{
      [key: string]: any;
      phx_ref: string;
      phx_ref_prev?: string;
    }>;
  };
}

export interface PostgresChangesFilter {
  event: '*' | 'INSERT' | 'UPDATE' | 'DELETE';
  schema: string;
  table?: string;
  filter?: string;
}

export interface BroadcastFilter {
  event: string;
}

export interface PresenceFilter {
  event: 'sync' | 'join' | 'leave';
}

export type ChannelState = 'closed' | 'errored' | 'joined' | 'joining' | 'leaving';

export type RealtimeConnectionState = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface RealtimeClientOptions {
  heartbeatInterval?: number;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  socketOptions?: {
    [key: string]: any;
  };
}

export interface RealtimeSubscribeOptions {
  event?: '*' | 'INSERT' | 'UPDATE' | 'DELETE';
  schema?: string;
  table?: string;
  filter?: string;
}

export interface BroadcastPayload {
  type: 'broadcast';
  event: string;
  [key: string]: any;
}

export interface PresencePayload {
  [key: string]: any;
}