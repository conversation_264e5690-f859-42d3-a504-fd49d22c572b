import { EventEmitter } from 'eventemitter3';
import { Socket } from 'socket.io-client';
import { 
  ChannelSubscription, 
  PresenceState, 
  RealtimeEvent, 
  PostgresChangesFilter,
  BroadcastFilter,
  PresenceFilter,
  ChannelState 
} from './types/realtime.types';

export class RealtimeChannel extends EventEmitter {
  public topic: string;
  private socket: Socket | null;
  private joinedOnce: boolean = false;
  private state: ChannelState = 'closed';
  private subscriptions: Map<string, ChannelSubscription> = new Map();

  constructor(topic: string, socket: Socket | null) {
    super();
    this.topic = topic;
    this.socket = socket;
  }

  subscribe(callback?: (status: 'SUBSCRIBED' | 'CHANNEL_ERROR' | 'TIMED_OUT' | 'CLOSED') => void): RealtimeChannel {
    if (!this.joinedOnce) {
      this.joinedOnce = true;
      this._join();
    }

    if (callback) {
      this.on('status', callback);
    }

    return this;
  }

  onPostgresChanges(
    filter: PostgresChangesFilter,
    callback: (payload: RealtimeEvent) => void
  ): ChannelSubscription {
    const eventKey = `postgres_changes:${JSON.stringify(filter)}`;
    super.on(eventKey, callback);

    const subscription: ChannelSubscription = {
      unsubscribe: () => {
        super.off(eventKey, callback);
        this.subscriptions.delete(eventKey);
      }
    };

    this.subscriptions.set(eventKey, subscription);

    // Send subscription to server
    if (this.socket?.connected && this.state === 'joined') {
      this.socket.emit('subscribe_postgres_changes', {
        topic: this.topic,
        filter,
        ref: this.makeRef()
      });
    }

    return subscription;
  }

  onBroadcast(
    filter: BroadcastFilter,
    callback: (payload: any) => void
  ): ChannelSubscription {
    const eventKey = `broadcast:${JSON.stringify(filter)}`;
    super.on(eventKey, callback);

    const subscription: ChannelSubscription = {
      unsubscribe: () => {
        super.off(eventKey, callback);
        this.subscriptions.delete(eventKey);
      }
    };

    this.subscriptions.set(eventKey, subscription);

    return subscription;
  }

  onPresence(
    filter: PresenceFilter,
    callback: (payload: PresenceState) => void
  ): ChannelSubscription {
    const eventKey = `presence:${JSON.stringify(filter)}`;
    super.on(eventKey, callback);

    const subscription: ChannelSubscription = {
      unsubscribe: () => {
        super.off(eventKey, callback);
        this.subscriptions.delete(eventKey);
      }
    };

    this.subscriptions.set(eventKey, subscription);

    return subscription;
  }

  send(payload: { 
    type: 'broadcast'; 
    event: string; 
    [key: string]: any 
  }): Promise<'ok' | 'error' | 'timeout'> {
    return new Promise((resolve) => {
      if (this.socket?.connected && this.state === 'joined') {
        this.socket.emit('broadcast', {
          topic: this.topic,
          event: payload.event,
          payload: payload,
          ref: this.makeRef()
        });
        resolve('ok');
      } else {
        resolve('error');
      }
    });
  }

  track(payload: Record<string, any>): Promise<'ok' | 'error' | 'timeout'> {
    return new Promise((resolve) => {
      if (this.socket?.connected && this.state === 'joined') {
        this.socket.emit('presence', {
          topic: this.topic,
          event: 'track',
          payload: payload,
          ref: this.makeRef()
        });
        resolve('ok');
      } else {
        resolve('error');
      }
    });
  }

  untrack(): Promise<'ok' | 'error' | 'timeout'> {
    return new Promise((resolve) => {
      if (this.socket?.connected && this.state === 'joined') {
        this.socket.emit('presence', {
          topic: this.topic,
          event: 'untrack',
          ref: this.makeRef()
        });
        resolve('ok');
      } else {
        resolve('error');
      }
    });
  }

  unsubscribe(): Promise<'ok' | 'error' | 'timeout'> {
    return new Promise((resolve) => {
      this.state = 'leaving';
      
      // Unsubscribe all subscriptions
      this.subscriptions.forEach(subscription => {
        subscription.unsubscribe();
      });
      this.subscriptions.clear();

      if (this.socket?.connected) {
        this.socket.emit('leave', {
          topic: this.topic,
          ref: this.makeRef()
        });
      }

      this.state = 'closed';
      this.removeAllListeners();
      resolve('ok');
    });
  }

  // Internal methods for RealtimeClient
  _join(): void {
    if (this.state === 'leaving') return;
    
    this.state = 'joining';
    
    if (this.socket?.connected) {
      this.socket.emit('join', {
        topic: this.topic,
        ref: this.makeRef()
      });

      // Set joined state after a brief delay
      setTimeout(() => {
        if (this.state === 'joining') {
          this.state = 'joined';
          this.emit('status', 'SUBSCRIBED');
        }
      }, 100);
    }
  }

  _rejoin(): void {
    if (this.joinedOnce) {
      this._join();
      
      // Re-establish all subscriptions
      this.subscriptions.forEach((subscription, eventKey) => {
        if (eventKey.startsWith('postgres_changes:')) {
          const filter = JSON.parse(eventKey.replace('postgres_changes:', ''));
          this.socket?.emit('subscribe_postgres_changes', {
            topic: this.topic,
            filter,
            ref: this.makeRef()
          });
        }
      });
    }
  }

  _trigger(event: string, payload: any): void {
    // Trigger specific event listeners
    if (event === 'postgres_changes') {
      const eventKey = `postgres_changes:${JSON.stringify(payload.filter || {})}`;
      this.emit(eventKey, payload);
    } else if (event === 'broadcast') {
      const eventKey = `broadcast:${JSON.stringify({ event: payload.event })}`;
      this.emit(eventKey, payload);
    } else if (event === 'presence') {
      const eventKey = `presence:${JSON.stringify({ event: payload.event })}`;
      this.emit(eventKey, payload);
    }

    // Also trigger generic event
    this.emit(event, payload);
  }

  _setState(state: ChannelState): void {
    this.state = state;
    
    let status: 'SUBSCRIBED' | 'CHANNEL_ERROR' | 'TIMED_OUT' | 'CLOSED';
    switch (state) {
      case 'joined':
        status = 'SUBSCRIBED';
        break;
      case 'errored':
        status = 'CHANNEL_ERROR';
        break;
      case 'closed':
        status = 'CLOSED';
        break;
      default:
        return;
    }
    
    this.emit('status', status);
  }

  private makeRef(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  getState(): ChannelState {
    return this.state;
  }
}