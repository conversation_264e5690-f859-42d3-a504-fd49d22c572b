export interface FunctionInvokeOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  region?: string;
}

export interface FunctionResponse<T = any> {
  data: T;
  error?: string;
}

export interface EdgeFunction {
  id: string;
  name: string;
  slug: string;
  status: 'active' | 'inactive' | 'deploying' | 'failed';
  version: number;
  created_at: string;
  updated_at: string;
  entrypoint?: string;
  env?: Record<string, string>;
  import_map?: Record<string, any>;
  verify_jwt?: boolean;
  execution_stats?: {
    total_executions: number;
    total_errors: number;
    avg_execution_time: number;
    last_execution: string;
  };
}

export interface FunctionListResponse {
  functions: EdgeFunction[];
  count: number;
}

export interface FunctionDeployOptions {
  code: string | File;
  entrypoint?: string;
  env?: Record<string, string>;
  importMap?: Record<string, any>;
  verify?: boolean;
}

export interface FunctionDeployResponse {
  id: string;
  name: string;
  version: number;
  status: 'deploying' | 'active' | 'failed';
  deployment_id: string;
  created_at: string;
}

export interface FunctionLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  metadata?: Record<string, any>;
}

export interface FunctionMetrics {
  function_id: string;
  period: string;
  executions: {
    total: number;
    successful: number;
    failed: number;
  };
  performance: {
    avg_duration: number;
    min_duration: number;
    max_duration: number;
    p50_duration: number;
    p95_duration: number;
    p99_duration: number;
  };
  errors: {
    total: number;
    by_type: Record<string, number>;
  };
  bandwidth: {
    requests_in: number;
    requests_out: number;
    data_in: number;
    data_out: number;
  };
}