import { HttpClient, EBaaSError } from 'infra';
import { 
  FunctionInvokeOptions, 
  FunctionResponse, 
  EdgeFunction, 
  FunctionListResponse,
  FunctionDeployOptions,
  FunctionDeployResponse 
} from './types/functions.types';

export class FunctionsClient {
  private httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  async invoke<T = any>(
    functionName: string, 
    options?: FunctionInvokeOptions
  ): Promise<{ data: T | null; error: EBaaSError | null }> {
    try {
      const method = options?.method || 'POST';
      const headers = {
        'Content-Type': 'application/json',
        ...options?.headers
      };

      let response;
      const url = `/functions/v1/${functionName}`;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await this.httpClient.get<T>(url, { 
            headers,
            params: options?.body 
          });
          break;
        case 'POST':
          response = await this.httpClient.post<T>(url, options?.body, { headers });
          break;
        case 'PUT':
          response = await this.httpClient.put<T>(url, options?.body, { headers });
          break;
        case 'PATCH':
          response = await this.httpClient.patch<T>(url, options?.body, { headers });
          break;
        case 'DELETE':
          response = await this.httpClient.delete<T>(url, { 
            headers,
            data: options?.body 
          });
          break;
        default:
          throw new EBaaSError(`Unsupported HTTP method: ${method}`, 400);
      }

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Function invocation failed',
          0,
          error
        )
      };
    }
  }

  async list(): Promise<{ data: FunctionListResponse | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.get<FunctionListResponse>('/functions/v1');

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to list functions',
          0,
          error
        )
      };
    }
  }

  async get(functionName: string): Promise<{ data: EdgeFunction | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.get<EdgeFunction>(`/functions/v1/${functionName}/info`);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to get function',
          0,
          error
        )
      };
    }
  }

  async deploy(
    functionName: string, 
    options: FunctionDeployOptions
  ): Promise<{ data: FunctionDeployResponse | null; error: EBaaSError | null }> {
    try {
      const formData = new FormData();
      
      if (options.code instanceof File) {
        formData.append('code', options.code);
      } else if (typeof options.code === 'string') {
        formData.append('code', new Blob([options.code], { type: 'text/plain' }), 'index.ts');
      }

      if (options.entrypoint) {
        formData.append('entrypoint', options.entrypoint);
      }

      if (options.env) {
        formData.append('env', JSON.stringify(options.env));
      }

      if (options.importMap) {
        formData.append('importMap', JSON.stringify(options.importMap));
      }

      if (options.verify !== undefined) {
        formData.append('verify', String(options.verify));
      }

      const response = await this.httpClient.post<FunctionDeployResponse>(
        `/functions/v1/${functionName}/deploy`, 
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to deploy function',
          0,
          error
        )
      };
    }
  }

  async delete(functionName: string): Promise<{ data: { message: string } | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.delete<{ message: string }>(`/functions/v1/${functionName}`);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to delete function',
          0,
          error
        )
      };
    }
  }

  async updateEnv(
    functionName: string, 
    env: Record<string, string>
  ): Promise<{ data: EdgeFunction | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.put<EdgeFunction>(`/functions/v1/${functionName}/env`, { env });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to update function environment',
          0,
          error
        )
      };
    }
  }

  async getLogs(
    functionName: string, 
    options?: { 
      limit?: number; 
      offset?: number; 
      level?: 'info' | 'warn' | 'error' | 'debug' 
    }
  ): Promise<{ data: any[] | null; error: EBaaSError | null }> {
    try {
      const params = new URLSearchParams();
      
      if (options?.limit) {
        params.append('limit', String(options.limit));
      }
      
      if (options?.offset) {
        params.append('offset', String(options.offset));
      }
      
      if (options?.level) {
        params.append('level', options.level);
      }

      const url = `/functions/v1/${functionName}/logs${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await this.httpClient.get<any[]>(url);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to get function logs',
          0,
          error
        )
      };
    }
  }

  async getMetrics(
    functionName: string,
    options?: {
      period?: '1h' | '24h' | '7d' | '30d';
    }
  ): Promise<{ data: any | null; error: EBaaSError | null }> {
    try {
      const params = new URLSearchParams();
      
      if (options?.period) {
        params.append('period', options.period);
      }

      const url = `/functions/v1/${functionName}/metrics${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await this.httpClient.get<any>(url);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to get function metrics',
          0,
          error
        )
      };
    }
  }
}