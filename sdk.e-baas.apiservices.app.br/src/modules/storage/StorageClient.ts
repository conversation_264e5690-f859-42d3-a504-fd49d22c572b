import { HttpClient, EBaaSError } from 'infra';
import { StorageBucket } from './StorageBucket';
import { Bucket, BucketOptions, StorageUsage } from './types/storage.types';

export class StorageClient {
  private httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  from(bucketId: string): StorageBucket {
    return new StorageBucket(this.httpClient, bucketId);
  }

  async createBucket(id: string, options?: BucketOptions): Promise<{ data: Bucket | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.post<Bucket>('/storage/buckets', {
        id,
        ...options
      });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to create bucket',
          0,
          error
        )
      };
    }
  }

  async getBucket(id: string): Promise<{ data: Bucket | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.get<Bucket>(`/storage/buckets/${id}`);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to get bucket',
          0,
          error
        )
      };
    }
  }

  async listBuckets(): Promise<{ data: Bucket[] | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.get<Bucket[]>('/storage/buckets');

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to list buckets',
          0,
          error
        )
      };
    }
  }

  async updateBucket(id: string, options: Partial<BucketOptions>): Promise<{ data: Bucket | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.put<Bucket>(`/storage/buckets/${id}`, options);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to update bucket',
          0,
          error
        )
      };
    }
  }

  async deleteBucket(id: string): Promise<{ data: { message: string } | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.delete<{ message: string }>(`/storage/buckets/${id}`);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to delete bucket',
          0,
          error
        )
      };
    }
  }

  async getUsage(): Promise<{ data: StorageUsage | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.get<StorageUsage>('/storage/usage');

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to get storage usage',
          0,
          error
        )
      };
    }
  }
}