import { HttpClient, EBaaSError } from 'infra';
import { 
  StorageFile, 
  StorageUploadOptions, 
  StorageDownloadOptions, 
  StorageListOptions,
  StorageSignedUrlOptions,
  FileUploadResponse,
  FileListResponse,
  SignedUrlResponse,
  PublicUrlResponse
} from './types/storage.types';

export class StorageBucket {
  private httpClient: HttpClient;
  private bucketId: string;

  constructor(httpClient: HttpClient, bucketId: string) {
    this.httpClient = httpClient;
    this.bucketId = bucketId;
  }

  async upload(
    path: string, 
    file: File | Blob | ArrayBuffer | string, 
    options?: StorageUploadOptions
  ): Promise<{ data: FileUploadResponse | null; error: EBaaSError | null }> {
    try {
      const formData = new FormData();
      
      if (file instanceof File) {
        formData.append('file', file);
      } else if (file instanceof Blob) {
        formData.append('file', file, path);
      } else if (file instanceof ArrayBuffer) {
        formData.append('file', new Blob([file]), path);
      } else if (typeof file === 'string') {
        formData.append('file', new Blob([file], { type: 'text/plain' }), path);
      }

      formData.append('path', path);
      
      if (options?.cacheControl) {
        formData.append('cacheControl', options.cacheControl);
      }
      
      if (options?.contentType) {
        formData.append('contentType', options.contentType);
      }

      if (options?.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }

      const url = `/storage/buckets/${this.bucketId}/files${options?.upsert ? '?upsert=true' : ''}`;
      
      const response = await this.httpClient.post<FileUploadResponse>(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to upload file',
          0,
          error
        )
      };
    }
  }

  async download(path: string, options?: StorageDownloadOptions): Promise<{ data: Blob | null; error: EBaaSError | null }> {
    try {
      const params = new URLSearchParams();
      
      if (options?.transform) {
        Object.entries(options.transform).forEach(([key, value]) => {
          if (value !== undefined) {
            params.append(key, String(value));
          }
        });
      }

      const url = `/storage/buckets/${this.bucketId}/files/${path}${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await this.httpClient.get(url, {
        responseType: 'blob'
      });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to download file',
          0,
          error
        )
      };
    }
  }

  async list(prefix?: string, options?: StorageListOptions): Promise<{ data: FileListResponse | null; error: EBaaSError | null }> {
    try {
      const params = new URLSearchParams();
      
      if (prefix) {
        params.append('prefix', prefix);
      }
      
      if (options?.limit) {
        params.append('limit', String(options.limit));
      }
      
      if (options?.offset) {
        params.append('offset', String(options.offset));
      }
      
      if (options?.sortBy) {
        params.append('sortBy', JSON.stringify(options.sortBy));
      }
      
      if (options?.search) {
        params.append('search', options.search);
      }

      const url = `/storage/buckets/${this.bucketId}/files${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await this.httpClient.get<FileListResponse>(url);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to list files',
          0,
          error
        )
      };
    }
  }

  async remove(paths: string[]): Promise<{ data: { message: string } | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.delete<{ message: string }>(`/storage/buckets/${this.bucketId}/files`, {
        data: { paths }
      });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to remove files',
          0,
          error
        )
      };
    }
  }

  async move(fromPath: string, toPath: string): Promise<{ data: { message: string } | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.post<{ message: string }>(`/storage/buckets/${this.bucketId}/files/move`, {
        sourceKey: fromPath,
        destinationKey: toPath
      });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to move file',
          0,
          error
        )
      };
    }
  }

  async copy(fromPath: string, toPath: string): Promise<{ data: { message: string } | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.post<{ message: string }>(`/storage/buckets/${this.bucketId}/files/copy`, {
        sourceKey: fromPath,
        destinationKey: toPath
      });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to copy file',
          0,
          error
        )
      };
    }
  }

  async createSignedUrl(
    path: string, 
    expiresIn: number, 
    options?: StorageSignedUrlOptions
  ): Promise<{ data: SignedUrlResponse | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.post<SignedUrlResponse>(`/storage/buckets/${this.bucketId}/files/${path}/sign`, {
        expiresIn,
        ...options
      });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to create signed URL',
          0,
          error
        )
      };
    }
  }

  getPublicUrl(path: string, options?: { transform?: Record<string, any> }): PublicUrlResponse {
    const params = new URLSearchParams();
    
    if (options?.transform) {
      Object.entries(options.transform).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }

    const baseUrl = this.httpClient['baseURL'] || '';
    const publicUrl = `${baseUrl}/storage/buckets/${this.bucketId}/files/${path}${params.toString() ? `?${params.toString()}` : ''}`;

    return {
      data: {
        publicUrl
      }
    };
  }

  async getFileInfo(path: string): Promise<{ data: StorageFile | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.get<StorageFile>(`/storage/buckets/${this.bucketId}/files/${path}/info`);

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to get file info',
          0,
          error
        )
      };
    }
  }

  async updateFileMetadata(
    path: string, 
    metadata: Record<string, any>
  ): Promise<{ data: StorageFile | null; error: EBaaSError | null }> {
    try {
      const response = await this.httpClient.put<StorageFile>(`/storage/buckets/${this.bucketId}/files/${path}/metadata`, {
        metadata
      });

      return {
        data: response.data,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof EBaaSError ? error : new EBaaSError(
          error instanceof Error ? error.message : 'Failed to update file metadata',
          0,
          error
        )
      };
    }
  }
}