export interface StorageFile {
  id: string;
  name: string;
  bucket_id: string;
  path: string;
  size: number;
  mimetype: string;
  etag: string;
  created_at: string;
  updated_at: string;
  last_accessed_at?: string;
  metadata?: Record<string, any>;
  cache_control?: string;
  content_encoding?: string;
  content_disposition?: string;
  content_language?: string;
}

export interface Bucket {
  id: string;
  name: string;
  owner: string;
  public: boolean;
  created_at: string;
  updated_at: string;
  allowed_mime_types?: string[];
  file_size_limit?: number;
  versioning_enabled?: boolean;
}

export interface BucketOptions {
  public?: boolean;
  allowedMimeTypes?: string[];
  fileSizeLimit?: number;
  versioningEnabled?: boolean;
}

export interface StorageUploadOptions {
  cacheControl?: string;
  contentType?: string;
  upsert?: boolean;
  metadata?: Record<string, any>;
  duplex?: 'half';
}

export interface StorageDownloadOptions {
  transform?: {
    width?: number;
    height?: number;
    resize?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
    format?: 'jpeg' | 'png' | 'webp' | 'avif';
    quality?: number;
  };
}

export interface StorageListOptions {
  limit?: number;
  offset?: number;
  sortBy?: {
    column: 'name' | 'updated_at' | 'created_at' | 'last_accessed_at';
    order: 'asc' | 'desc';
  };
  search?: string;
}

export interface StorageSignedUrlOptions {
  download?: boolean;
  transform?: {
    width?: number;
    height?: number;
    resize?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
    format?: 'jpeg' | 'png' | 'webp' | 'avif';
    quality?: number;
  };
}

export interface FileUploadResponse {
  id: string;
  path: string;
  fullPath: string;
  key: string;
}

export interface FileListResponse {
  files: StorageFile[];
  count: number;
  hasMore: boolean;
}

export interface SignedUrlResponse {
  data: {
    signedUrl: string;
    path: string;
    token: string;
  };
}

export interface PublicUrlResponse {
  data: {
    publicUrl: string;
  };
}

export interface StorageUsage {
  total_size: number;
  file_count: number;
  bucket_count: number;
  quota_used_percentage: number;
  quota_limit?: number;
}