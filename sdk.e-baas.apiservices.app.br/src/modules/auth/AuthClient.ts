import { HttpClient, EBaaSError } from 'infra';
import { AuthUser, AuthSession, SignUpData, SignInData, AuthResponse } from './types/auth.types';

export class AuthClient {
  private httpClient: HttpClient;
  private currentSession: AuthSession | null = null;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
    this.loadSession();
  }

  async signUp(data: SignUpData): Promise<AuthResponse> {
    try {
      const response = await this.httpClient.post<AuthResponse>('/auth/signup', data);
      
      if (response.data.session) {
        this.setSession(response.data.session);
      }

      return response.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  async signIn(data: SignInData): Promise<AuthResponse> {
    try {
      const response = await this.httpClient.post<AuthResponse>('/auth/signin', data);
      
      if (response.data.session) {
        this.setSession(response.data.session);
      }

      return response.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  async signOut(): Promise<void> {
    try {
      await this.httpClient.post('/auth/signout');
      this.clearSession();
    } catch (error) {
      this.clearSession();
      throw this.handleAuthError(error);
    }
  }

  async getUser(): Promise<AuthUser | null> {
    if (!this.currentSession?.access_token) {
      return null;
    }

    try {
      const response = await this.httpClient.get<{ user: AuthUser }>('/auth/user');
      return response.data.user;
    } catch (error) {
      if (error instanceof EBaaSError && error.status === 401) {
        this.clearSession();
        return null;
      }
      throw this.handleAuthError(error);
    }
  }

  async refreshSession(): Promise<AuthSession | null> {
    if (!this.currentSession?.refresh_token) {
      return null;
    }

    try {
      const response = await this.httpClient.post<AuthResponse>('/auth/refresh', {
        refresh_token: this.currentSession.refresh_token
      });

      if (response.data.session) {
        this.setSession(response.data.session);
        return response.data.session;
      }

      return null;
    } catch (error) {
      this.clearSession();
      throw this.handleAuthError(error);
    }
  }

  async resetPassword(email: string): Promise<void> {
    try {
      await this.httpClient.post('/auth/reset-password', { email });
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  async updateUser(data: Partial<AuthUser>): Promise<AuthUser> {
    try {
      const response = await this.httpClient.put<{ user: AuthUser }>('/auth/user', data);
      return response.data.user;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  getSession(): AuthSession | null {
    return this.currentSession;
  }

  onAuthStateChange(callback: (event: string, session: AuthSession | null) => void): () => void {
    // Implementation for auth state change listener
    // This would integrate with the actual auth state management system
    return () => {};
  }

  private setSession(session: AuthSession): void {
    this.currentSession = session;
    this.httpClient.setAuthToken(session.access_token);
    
    // Persist session
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('ebaas.auth.session', JSON.stringify(session));
    }
  }

  private clearSession(): void {
    this.currentSession = null;
    this.httpClient.removeAuthToken();
    
    // Clear persisted session
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('ebaas.auth.session');
    }
  }

  private loadSession(): void {
    if (typeof localStorage !== 'undefined') {
      const storedSession = localStorage.getItem('ebaas.auth.session');
      if (storedSession) {
        try {
          const session = JSON.parse(storedSession) as AuthSession;
          this.setSession(session);
        } catch (error) {
          this.clearSession();
        }
      }
    }
  }

  private handleAuthError(error: any): EBaaSError {
    if (error instanceof EBaaSError) {
      return error;
    }
    
    return new EBaaSError(
      error.message || 'Authentication failed',
      error.status || 0,
      error
    );
  }
}