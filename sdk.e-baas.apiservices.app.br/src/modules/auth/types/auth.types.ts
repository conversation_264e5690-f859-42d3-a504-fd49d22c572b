export interface AuthUser {
  id: string;
  email: string;
  phone?: string;
  name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  email_verified: boolean;
  phone_verified: boolean;
  app_metadata?: Record<string, any>;
  user_metadata?: Record<string, any>;
}

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at: number;
  token_type: string;
  user: AuthUser;
}

export interface AuthResponse {
  user?: AuthUser;
  session?: AuthSession;
  error?: string;
}

export interface SignUpData {
  email: string;
  password: string;
  name?: string;
  phone?: string;
  data?: Record<string, any>;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface OAuthProvider {
  provider: 'google' | 'github' | 'facebook';
  redirectTo?: string;
  scopes?: string;
}