// Main client export
export { <PERSON><PERSON><PERSON><PERSON><PERSON>, createClient } from './EBaaSClient';

// Infrastructure exports
export { HttpClient, EBaaSError } from './infra';
export type { 
  EBaaSClientOptions, 
  DatabaseFilters, 
  QueryOptions,
  SelectOptions,
  InsertOptions,
  UpdateOptions,
  DeleteOptions
} from './infra/types/common';

// Auth exports
export { AuthClient } from './modules/auth/AuthClient';
export type { 
  AuthUser, 
  AuthSession, 
  AuthResponse, 
  SignUpData, 
  SignInData, 
  OAuthProvider 
} from './modules/auth/types/auth.types';

// Database exports
export { DatabaseClient } from './modules/database/DatabaseClient';
export { QueryBuilder } from './modules/database/QueryBuilder';

// Storage exports
export { StorageClient } from './modules/storage/StorageClient';
export { StorageBucket } from './modules/storage/StorageBucket';
export type { 
  StorageFile, 
  StorageUploadOptions, 
  StorageDownloadOptions,
  StorageListOptions,
  Bucket,
  BucketOptions,
  StorageUsage
} from './modules/storage/types/storage.types';

// Realtime exports
export { RealtimeClient } from './modules/realtime/RealtimeClient';
export { RealtimeChannel } from './modules/realtime/RealtimeChannel';
export type { 
  RealtimeEvent, 
  ChannelSubscription, 
  PresenceState,
  PostgresChangesFilter,
  BroadcastFilter,
  PresenceFilter,
  RealtimeClientOptions
} from './modules/realtime/types/realtime.types';

// Functions exports
export { FunctionsClient } from './modules/functions/FunctionsClient';
export type { 
  FunctionInvokeOptions, 
  FunctionResponse,
  EdgeFunction,
  FunctionListResponse,
  FunctionDeployOptions
} from './modules/functions/types/functions.types';

// Version
export const version = '1.0.0';