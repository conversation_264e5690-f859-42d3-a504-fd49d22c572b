export interface EBaaSClientOptions {
  supabaseUrl: string;
  supabaseKey: string;
  options?: {
    auth?: {
      autoRefreshToken?: boolean;
      persistSession?: boolean;
      detectSessionInUrl?: boolean;
    };
    realtime?: {
      params?: Record<string, string>;
    };
    global?: {
      headers?: Record<string, string>;
    };
  };
}

export interface DatabaseFilters {
  eq?: any;
  neq?: any;
  gt?: any;
  gte?: any;
  lt?: any;
  lte?: any;
  like?: string;
  ilike?: string;
  is?: any;
  in?: any[];
  contains?: any;
  contained_by?: any;
  range_gt?: any;
  range_gte?: any;
  range_lt?: any;
  range_lte?: any;
  range_adjacent?: any;
  overlaps?: any;
  text_search?: string;
  match?: Record<string, any>;
}

export interface QueryOptions {
  count?: 'exact' | 'planned' | 'estimated';
  head?: boolean;
}

export interface SelectOptions extends QueryOptions {
  columns?: string;
}

export interface InsertOptions extends QueryOptions {
  returning?: 'minimal' | 'representation';
  upsert?: boolean;
  onConflict?: string;
}

export interface UpdateOptions extends QueryOptions {
  returning?: 'minimal' | 'representation';
}

export interface DeleteOptions extends QueryOptions {
  returning?: 'minimal' | 'representation';
}