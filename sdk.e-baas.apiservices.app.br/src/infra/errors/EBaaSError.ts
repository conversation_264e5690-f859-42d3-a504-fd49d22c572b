export class EBaaSError extends Error {
  public readonly status: number;
  public readonly details?: any;

  constructor(message: string, status: number = 0, details?: any) {
    super(message);
    this.name = 'EBaaSError';
    this.status = status;
    this.details = details;

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, EBaaSError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      status: this.status,
      details: this.details,
      stack: this.stack
    };
  }

  toString() {
    return `${this.name}: ${this.message} (Status: ${this.status})`;
  }
}