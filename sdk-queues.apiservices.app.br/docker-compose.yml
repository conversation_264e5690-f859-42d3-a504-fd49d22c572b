services:
  redis_dev:
    image: redis:latest
    container_name: redis_dev
    ports:
      - "5673:6379"
    volumes:
      - ./src/infra/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_dev_data:/data
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    environment:
      - REDIS_PASSWORD=redis123
      - REDIS_USERNAME=default
    networks:
      - queue-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "redis123", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  redis_prod:
    image: redis:latest
    container_name: redis_prod
    ports:
      - "5672:6379"
    volumes:
      - ./src/infra/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_prod_data:/data
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    networks:
      - queue-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "redis123", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

networks:
  queue-network:
    driver: bridge

volumes:
  redis_dev_data:
    driver: local
  redis_prod_data:
    driver: local
