# Queue Manager SDK

Um SDK robusto para gerenciamento de filas usando Redis, com suporte a mensagens persistentes e em tempo real.

# Setup

- Instale o projeto com o `pnpm install`
- Execute o projeto com o `pnpm start`

## Características

- Filas persistentes e pub/sub em tempo real
- Sistema de prioridades para mensagens
- Confirmação de processamento (ACK)
- Retentativas automáticas
- Limpeza automática de mensagens antigas
- Dashboard para monitoramento
- API REST para integração

## Instalação

```bash
npm install queue-manager-sdk
## Ou
npm install sdk-queues
```

## Configuração

### Redis

O SDK requer um servidor Redis. Use o docker-compose fornecido:

```yaml
services:
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - ./src/infra/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_data:/data
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
```

### Inicialização

```typescript
import { QueueManager } from "queue-manager-sdk";

const queueManager = new QueueManager({
  host: "localhost",
  port: 6379,
  username: "publisher",
  password: "redis123",
});
```

## Uso

### Publisher

```typescript
// Publicar mensagem persistente (padrão)
await queueManager.publish("notification", {
  userId: "123",
  message: "Nova notificação",
});

// Publicar mensagem em tempo real
await queueManager.publish(
  "email",
  {
    to: "<EMAIL>",
    subject: "Assunto",
    body: "Conteúdo",
  },
  { persistent: false }
);

// Publicar com prioridade
await queueManager.publish(
  "payment",
  {
    orderId: "123",
    amount: 99.99,
  },
  { priority: 2 }
);
```

### Consumer

```typescript
// Processador de notificações
async function handleNotification(message: any) {
  console.log("🔔 Notificação recebida:", message);
  await processNotification(message);
}

// Processador de emails
async function handleEmail(message: any) {
  console.log("📧 Email recebido:", message);
  await sendEmail(message);
}

// Iniciar consumidores
queueManager.consumer("notification", handleNotification);
queueManager.consumer("email", handleEmail);
```

## API REST

O SDK inclui um servidor HTTP com os seguintes endpoints:

### Status e Monitoramento

```typescript
// Status de todas as filas
GET /api/queues

// Mensagens de uma fila específica
GET /api/queues/:queueName/messages?status=all|pending|completed

// Apenas mensagens pendentes
GET /api/queues/:queueName/pending

// Apenas mensagens completadas
GET /api/queues/:queueName/completed
```

### Publicação de Eventos

```typescript
POST /events
Content-Type: application/json

{
  "eventType": "notification",
  "data": {
    "payload": {
      "userId": "123",
      "message": "Nova notificação"
    }
  },
  "options": {
    "persistent": true,
    "priority": 1,
    "attempts": 3
  }
}
```

## Tipos de Eventos Suportados

- `email`: Envio de emails
- `notification`: Notificações do sistema
- `payment`: Processamento de pagamentos
- `order`: Processamento de pedidos

## Gerenciamento de Mensagens

### Estados das Mensagens

- `pending`: Aguardando processamento
- `processing`: Em processamento
- `completed`: Processada com sucesso
- `failed`: Falha no processamento

### Limpeza Automática

O SDK limpa automaticamente mensagens completadas após 24 horas.

### Retentativas

Mensagens com falha são reprocessadas automaticamente:

- Número padrão de tentativas: 3
- Configurável via `options.attempts`

## Monitoramento

### Status das Filas

```typescript
const status = await queueManager.getQueueStatus();
// Retorna:
{
  "notification": {
    "waiting": 5,
    "processing": 1,
    "completed": 10,
    "failed": 0,
    "total": 16
  }
}
```

### Mensagens da Fila

```typescript
// Todas as mensagens
const messages = await queueManager.getQueueMessages("notification", "all");

// Apenas pendentes
const pending = await queueManager.getPendingMessages("notification");

// Apenas completadas
const completed = await queueManager.getCompletedMessages("notification");
```

## Interface Web

O SDK inclui um dashboard web para monitoramento em tempo real:

- Lista de filas ativas
- Status de cada fila
- Mensagens pendentes e completadas
- Filtros por status
- Atualização em tempo real

## Exemplos

### Publisher Simples

```typescript
import { QueueManager } from "queue-manager-sdk";

const queueManager = new QueueManager({
  host: "localhost",
  port: 6379,
});

async function sendNotification() {
  await queueManager.publish("notification", {
    userId: "123",
    message: "Nova notificação",
  });
}
```

### Consumer com Retentativas

```typescript
const queueManager = new QueueManager({
  host: "localhost",
  port: 6379,
});

async function handleNotification(message: any) {
  try {
    await processNotification(message);
  } catch (error) {
    // O SDK gerenciará as retentativas automaticamente
    throw error;
  }
}

queueManager.consumer("notification", handleNotification);
```

### Simulação de request SSE

```sh
curl -N -H "Accept: text/event-stream" -H "Cache-Control: no-cache" http://localhost:3000/api/queues/notifications/monitor
```

## Contribuição

1. Fork o projeto
2. Crie sua feature branch (`git checkout -b feature/nova-feature`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova feature'`)
4. Push para a branch (`git push origin feature/nova-feature`)
5. Crie um Pull Request

## Licença

MIT
