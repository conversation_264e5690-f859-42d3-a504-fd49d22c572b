# Ignorar tudo
*

# Permitir apenas
!dist/**/*
!README.md
!package.json
!LICENSE

# Garantir que os arquivos de source não sejam incluídos
src/
tests/
samples/
examples/

# Arquivos de configuração
.gitignore
.env*
.editorconfig
.eslintrc
.prettierrc
jest.config.js
tsconfig.json
docker-compose.yml
redis.conf
.github

# IDE
.vscode/
.idea/

# Logs
logs/
*.log

# Dependências
node_modules/

# Arquivos de teste
*.test.ts
*.spec.ts
coverage/