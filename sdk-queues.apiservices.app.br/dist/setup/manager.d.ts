import Redis from "ioredis";
import { PublishOptions } from "../publisher";
interface QueueMessage {
    id: string;
    timestamp: string;
    content: any;
    options: PublishOptions;
    status: "pending" | "processing" | "completed" | "failed";
    attempts: number;
}
export declare class QueueManager {
    private publisher;
    private subscriber;
    private setup;
    private redis;
    private cleanupInterval;
    constructor(options?: any);
    publish(channel: string, message: any, options?: PublishOptions): Promise<number>;
    consumer(channel: string, callback: (message: any) => Promise<void>): Promise<void>;
    getSubscriberClient(): Redis;
    getCommandClient(): Redis;
    getQueueStatus(): Promise<Record<string, any>>;
    getPendingMessages(queueName: string): Promise<QueueMessage[]>;
    getCompletedMessages(queueName: string): Promise<QueueMessage[]>;
    getQueueMessages(queueName: string, status?: "pending" | "completed" | "all"): Promise<{
        pending?: QueueMessage[];
        completed?: QueueMessage[];
    }>;
    disconnect(): Promise<void>;
    /**
     * Limpa todas as mensagens pendentes de todas as filas
     * @returns O número de mensagens removidas por fila
     */
    clearAllPendingMessages(): Promise<Record<string, number>>;
    /**
     * Reenviar mensagens pendentes de uma fila específica
     * @param queueName Nome da fila
     * @param limit Número máximo de mensagens para reenviar
     * @param transform Função para transformar as mensagens antes de reenviar
     * @returns O número de mensagens reenviadas
     */
    resendPendingMessages(queueName: string, limit?: number, transform?: (message: any) => any): Promise<{
        reenviadas: number;
        erros: number;
    }>;
    /**
     * Exclui uma fila específica, removendo todas as suas mensagens
     * @param queueName Nome da fila a ser excluída
     * @returns Informações sobre a exclusão da fila
     */
    deleteQueue(queueName: string): Promise<{
        success: boolean;
        deleted: Record<string, number>;
    }>;
    /**
     * Inicia o job de limpeza de mensagens antigas
     */
    private startCleanupJob;
}
export {};
