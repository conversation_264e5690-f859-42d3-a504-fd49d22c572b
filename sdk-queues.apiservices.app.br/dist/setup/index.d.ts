import Redis, { RedisOptions } from "ioredis";
import { QueueManager } from "./manager";
/**
 * Classe que configura conexões Redis para o sistema de filas
 */
export declare class SetupQueueManager {
    private commandClient;
    private subscriberClient;
    /**
     * Inicializa conexões Redis para comandos e subscriber
     * @param options Opções de conexão Redis
     */
    constructor(options?: RedisOptions);
    /**
     * Configura handlers de eventos para clientes Redis
     * @param client Cliente Redis
     * @param type Tipo de cliente (command ou subscriber)
     */
    private setupEventHandlers;
    /**
     * Retorna o cliente Redis para comandos
     */
    getCommandClient(): Redis;
    /**
     * Retorna o cliente Redis para subscrição
     */
    getSubscriberClient(): Redis;
    /**
     * Cria uma instância do QueueManager
     */
    createQueueManager(): QueueManager;
    /**
     * Desconecta os clientes Redis
     */
    disconnect(): Promise<void>;
}
