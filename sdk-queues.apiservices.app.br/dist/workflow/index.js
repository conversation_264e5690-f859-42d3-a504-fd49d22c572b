"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Workflow = void 0;
const manager_1 = require("../setup/manager");
const redis_storage_1 = require("../storage/redis-storage");
const monitoring_1 = require("../infra/monitoring");
// Logger para o módulo workflow
const logger = (0, monitoring_1.createModuleLogger)("workflow");
class Workflow {
    constructor(options) {
        this.workflowCheckInterval = 5000; // Verificar cada 5 segundos
        this.watcherInterval = null;
        this.activeWorkflows = new Map();
        const redisOptions = options?.redis || {};
        this.queueManager = new manager_1.QueueManager(redisOptions);
        // Se o storage foi fornecido, use-o, caso contrário crie um Redis storage
        if (options?.storage) {
            this.storage = options.storage;
            logger.info("Workflow inicializado com storage customizado");
        }
        else {
            this.storage = new redis_storage_1.RedisWorkflowStorage(this.queueManager.getCommandClient());
            logger.info("Workflow inicializado com Redis storage padrão");
        }
        this.startWatcher();
    }
    /**
     * Define um novo fluxo de trabalho
     * @param id ID único do workflow
     * @param name Nome descritivo
     * @param steps Lista de etapas do workflow
     */
    async defineWorkflow(id, name, steps) {
        const workflow = {
            id,
            name,
            steps: steps.map((step) => ({
                ...step,
                status: "pending",
            })),
            status: "pending",
        };
        await this.saveWorkflow(workflow);
        logger.info(`Workflow '${name}' (${id}) definido com ${steps.length} etapas`, { workflowId: id, steps: steps.length });
        return id;
    }
    /**
     * Inicia a execução de um workflow
     * @param id ID do workflow
     * @param initialCheckpoint Dados iniciais de checkpoint (opcional)
     */
    async startWorkflow(id, initialCheckpoint) {
        const workflow = await this.storage.getWorkflow(id);
        if (!workflow) {
            logger.warn(`Tentativa de iniciar workflow inexistente: ${id}`);
            return false;
        }
        if (workflow.status === "running") {
            logger.info(`Workflow ${id} já está em execução`);
            return true; // Já está em execução
        }
        // Atualiza status e inicia
        workflow.status = "running";
        workflow.startedAt = new Date().toISOString();
        if (initialCheckpoint) {
            workflow.checkpointData = initialCheckpoint;
            logger.info(`Workflow ${id} iniciado com checkpoint inicial`, { workflowId: id, checkpoint: JSON.stringify(initialCheckpoint) });
        }
        else {
            logger.info(`Workflow ${id} iniciado`, { workflowId: id });
        }
        await this.storage.saveWorkflow(id, workflow);
        // Adiciona à lista de workflows ativos
        this.activeWorkflows.set(id, true);
        // Processa as etapas iniciais (sem dependências)
        await this.processNextSteps(id);
        return true;
    }
    /**
     * Pausa a execução de um workflow
     * @param id ID do workflow
     */
    async pauseWorkflow(id) {
        const workflow = await this.storage.getWorkflow(id);
        if (!workflow) {
            logger.warn(`Tentativa de pausar workflow inexistente: ${id}`);
            return false;
        }
        if (workflow.status !== "running") {
            logger.warn(`Não é possível pausar workflow ${id} com status ${workflow.status}`);
            return false;
        }
        workflow.status = "paused";
        await this.storage.saveWorkflow(id, workflow);
        logger.info(`Workflow ${id} pausado`, { workflowId: id });
        // Remove da lista de workflows ativos
        this.activeWorkflows.delete(id);
        return true;
    }
    /**
     * Retoma a execução de um workflow pausado
     * @param id ID do workflow
     */
    async resumeWorkflow(id) {
        const workflow = await this.storage.getWorkflow(id);
        if (!workflow) {
            logger.warn(`Tentativa de retomar workflow inexistente: ${id}`);
            return false;
        }
        if (workflow.status !== "paused") {
            logger.warn(`Não é possível retomar workflow ${id} com status ${workflow.status}`);
            return false;
        }
        workflow.status = "running";
        await this.storage.saveWorkflow(id, workflow);
        logger.info(`Workflow ${id} retomado`, { workflowId: id });
        // Adiciona novamente à lista de workflows ativos
        this.activeWorkflows.set(id, true);
        // Processa as próximas etapas
        await this.processNextSteps(id);
        return true;
    }
    /**
     * Cancela um workflow em execução
     * @param id ID do workflow
     */
    async cancelWorkflow(id) {
        const workflow = await this.storage.getWorkflow(id);
        if (!workflow) {
            logger.warn(`Tentativa de cancelar workflow inexistente: ${id}`);
            return false;
        }
        // Só pode cancelar se estiver em execução ou pausado
        if (workflow.status !== "running" && workflow.status !== "paused") {
            logger.warn(`Não é possível cancelar workflow ${id} com status ${workflow.status}`);
            return false;
        }
        workflow.status = "failed";
        await this.storage.saveWorkflow(id, workflow);
        logger.info(`Workflow ${id} cancelado`, { workflowId: id });
        // Remove da lista de workflows ativos
        this.activeWorkflows.delete(id);
        return true;
    }
    /**
     * Lista todos os workflows
     */
    async listWorkflows() {
        const workflows = await this.storage.listWorkflows();
        logger.info(`Listando ${workflows.length} workflows`);
        return workflows;
    }
    /**
     * Obtém um workflow pelo ID
     * @param id ID do workflow
     */
    async getWorkflow(id) {
        const workflow = await this.storage.getWorkflow(id);
        if (workflow) {
            logger.info(`Workflow ${id} encontrado`);
        }
        else {
            logger.info(`Workflow ${id} não encontrado`);
        }
        return workflow;
    }
    /**
     * Salva um checkpoint de dados para o workflow
     * @param id ID do workflow
     * @param data Dados do checkpoint
     */
    async saveCheckpoint(id, data) {
        const workflow = await this.storage.getWorkflow(id);
        if (!workflow) {
            logger.warn(`Tentativa de salvar checkpoint em workflow inexistente: ${id}`);
            return false;
        }
        workflow.checkpointData = {
            ...workflow.checkpointData,
            ...data,
        };
        await this.storage.saveWorkflow(id, workflow);
        logger.info(`Checkpoint salvo para workflow ${id}`, { workflowId: id, data: JSON.stringify(data) });
        return true;
    }
    /**
     * Marca uma etapa como concluída e avança o workflow
     * @param workflowId ID do workflow
     * @param stepId ID da etapa
     * @param success Se a etapa foi concluída com sucesso
     * @param resultData Dados de resultado da etapa (opcional)
     */
    async completeStep(workflowId, stepId, success = true, resultData) {
        const workflow = await this.storage.getWorkflow(workflowId);
        if (!workflow) {
            logger.warn(`Tentativa de completar etapa em workflow inexistente: ${workflowId}`);
            return false;
        }
        if (workflow.status !== "running") {
            logger.warn(`Não é possível completar etapa em workflow ${workflowId} com status ${workflow.status}`);
            return false;
        }
        // Encontra e atualiza a etapa
        const stepIndex = workflow.steps.findIndex((step) => step.id === stepId);
        if (stepIndex === -1) {
            logger.warn(`Etapa ${stepId} não encontrada no workflow ${workflowId}`);
            return false;
        }
        workflow.steps[stepIndex].status = success ? "completed" : "failed";
        // Se falhou e não tinha tratamento, marca workflow como falho
        if (!success) {
            workflow.status = "failed";
            workflow.checkpointData = {
                ...workflow.checkpointData,
                lastError: {
                    stepId,
                    timestamp: new Date().toISOString(),
                    resultData,
                },
            };
            logger.error(`Etapa ${stepId} do workflow ${workflowId} falhou`, {
                workflowId,
                stepId,
                resultData: JSON.stringify(resultData),
            });
            await this.storage.saveWorkflow(workflowId, workflow);
            return false;
        }
        // Salva dados de resultado no checkpoint, se fornecidos
        if (resultData) {
            workflow.checkpointData = {
                ...workflow.checkpointData,
                [stepId]: resultData,
            };
        }
        await this.storage.saveWorkflow(workflowId, workflow);
        logger.info(`Etapa ${stepId} do workflow ${workflowId} marcada como ${success ? "concluída" : "falha"}`, {
            workflowId,
            stepId,
            success,
        });
        // Processa próximas etapas, se houver
        await this.processNextSteps(workflowId);
        return true;
    }
    /**
     * Salva um workflow
     * @param workflow Workflow a ser salvo
     */
    async saveWorkflow(workflow) {
        await this.storage.saveWorkflow(workflow.id, workflow);
        logger.debug(`Workflow ${workflow.id} salvo no storage`);
    }
    /**
     * Processa as próximas etapas de um workflow
     * @param workflowId ID do workflow
     */
    async processNextSteps(workflowId) {
        const workflow = await this.storage.getWorkflow(workflowId);
        if (!workflow || workflow.status !== "running") {
            return;
        }
        let processedCount = 0;
        const pendingSteps = workflow.steps.filter((step) => step.status === "pending");
        logger.debug(`Processando próximas etapas do workflow ${workflowId}, ${pendingSteps.length} etapas pendentes`);
        for (const step of pendingSteps) {
            // Verifica se todas as dependências foram concluídas
            if (step.dependencies && step.dependencies.length > 0) {
                const allDependenciesCompleted = step.dependencies.every((depId) => {
                    const depStep = workflow.steps.find((s) => s.id === depId);
                    return depStep && depStep.status === "completed";
                });
                if (!allDependenciesCompleted) {
                    continue; // Pula esta etapa pois depende de outras não concluídas
                }
            }
            // Publica a etapa
            logger.info(`Executando etapa ${step.id} do workflow ${workflowId}`, { workflowId, stepId: step.id });
            step.status = "processing";
            try {
                await this.queueManager.getCommandClient().publish(step.channel, JSON.stringify({
                    step: step.id,
                    workflow: workflowId,
                    message: step.message,
                    timestamp: new Date().toISOString(),
                }));
                processedCount++;
            }
            catch (error) {
                logger.error(`Erro ao publicar etapa ${step.id} do workflow ${workflowId}:`, error);
                step.status = "failed";
                workflow.status = "failed";
            }
        }
        // Verifica se todas as etapas foram concluídas
        const allCompleted = workflow.steps.every((step) => step.status === "completed" || step.status === "failed");
        const anyFailed = workflow.steps.some((step) => step.status === "failed");
        if (allCompleted) {
            workflow.status = anyFailed ? "failed" : "completed";
            workflow.completedAt = new Date().toISOString();
            logger.info(`Workflow ${workflowId} finalizado com status ${workflow.status}`, { workflowId, status: workflow.status });
            this.activeWorkflows.delete(workflowId);
        }
        else if (processedCount > 0) {
            logger.info(`${processedCount} etapas iniciadas no workflow ${workflowId}`, { workflowId, processedCount });
        }
        await this.storage.saveWorkflow(workflowId, workflow);
    }
    /**
     * Inicia o watcher de workflows
     */
    startWatcher() {
        if (this.watcherInterval) {
            clearInterval(this.watcherInterval);
        }
        this.watcherInterval = setInterval(async () => {
            const activeIds = Array.from(this.activeWorkflows.keys());
            if (activeIds.length > 0) {
                logger.debug(`Verificando status de ${activeIds.length} workflows ativos`);
                for (const id of activeIds) {
                    await this.checkWorkflowStatus(id);
                }
            }
        }, this.workflowCheckInterval);
        // Não bloqueia o processo Node.js
        this.watcherInterval.unref();
        logger.info(`Watcher de workflows iniciado com intervalo de ${this.workflowCheckInterval}ms`);
    }
    /**
     * Verifica o status de um workflow
     * @param workflowId ID do workflow
     */
    async checkWorkflowStatus(workflowId) {
        try {
            const workflow = await this.storage.getWorkflow(workflowId);
            if (!workflow) {
                this.activeWorkflows.delete(workflowId);
                return;
            }
            if (workflow.status === "running") {
                await this.processNextSteps(workflowId);
            }
            else if (workflow.status !== "paused") {
                this.activeWorkflows.delete(workflowId);
            }
        }
        catch (error) {
            logger.error(`Erro ao verificar status do workflow ${workflowId}:`, error);
        }
    }
    /**
     * Altera o intervalo de verificação do watcher
     * @param intervalMs Intervalo em milissegundos
     */
    setWatcherInterval(intervalMs) {
        this.workflowCheckInterval = intervalMs;
        logger.info(`Intervalo do watcher alterado para ${intervalMs}ms`);
        // Reinicia o watcher com o novo intervalo
        this.startWatcher();
    }
    /**
     * Desconecta o workflow
     */
    async disconnect() {
        if (this.watcherInterval) {
            clearInterval(this.watcherInterval);
            this.watcherInterval = null;
        }
        logger.info("Desconectando workflow");
        await this.queueManager.disconnect();
    }
}
exports.Workflow = Workflow;
