import { PublishOptions } from "../publisher";
import { RedisOptions } from "ioredis";
import { WorkflowStorage } from "../storage/interfaces";
interface WorkflowStep {
    id: string;
    channel: string;
    message: any;
    options?: PublishOptions;
    dependencies?: string[];
    parallel?: boolean;
    status?: "pending" | "processing" | "completed" | "failed";
}
interface WorkflowDefinition {
    id: string;
    name: string;
    steps: WorkflowStep[];
    status?: "pending" | "running" | "completed" | "failed" | "paused";
    startedAt?: string;
    completedAt?: string;
    checkpointData?: Record<string, any>;
}
export interface WorkflowOptions {
    redis?: RedisOptions;
    storage?: WorkflowStorage;
}
export declare class Workflow {
    private queueManager;
    private workflowCheckInterval;
    private watcherInterval;
    private activeWorkflows;
    private storage;
    constructor(options?: WorkflowOptions);
    /**
     * Define um novo fluxo de trabalho
     * @param id ID único do workflow
     * @param name Nome descritivo
     * @param steps Lista de etapas do workflow
     */
    defineWorkflow(id: string, name: string, steps: WorkflowStep[]): Promise<string>;
    /**
     * Inicia a execução de um workflow
     * @param id ID do workflow
     * @param initialCheckpoint Dados iniciais de checkpoint (opcional)
     */
    startWorkflow(id: string, initialCheckpoint?: Record<string, any>): Promise<boolean>;
    /**
     * Pausa a execução de um workflow
     * @param id ID do workflow
     */
    pauseWorkflow(id: string): Promise<boolean>;
    /**
     * Retoma a execução de um workflow pausado
     * @param id ID do workflow
     */
    resumeWorkflow(id: string): Promise<boolean>;
    /**
     * Cancela um workflow em execução
     * @param id ID do workflow
     */
    cancelWorkflow(id: string): Promise<boolean>;
    /**
     * Lista todos os workflows
     */
    listWorkflows(): Promise<WorkflowDefinition[]>;
    /**
     * Obtém um workflow pelo ID
     * @param id ID do workflow
     */
    getWorkflow(id: string): Promise<WorkflowDefinition | null>;
    /**
     * Salva um checkpoint de dados para o workflow
     * @param id ID do workflow
     * @param data Dados do checkpoint
     */
    saveCheckpoint(id: string, data: Record<string, any>): Promise<boolean>;
    /**
     * Marca uma etapa como concluída e avança o workflow
     * @param workflowId ID do workflow
     * @param stepId ID da etapa
     * @param success Se a etapa foi concluída com sucesso
     * @param resultData Dados de resultado da etapa (opcional)
     */
    completeStep(workflowId: string, stepId: string, success?: boolean, resultData?: Record<string, any>): Promise<boolean>;
    /**
     * Salva um workflow
     * @param workflow Workflow a ser salvo
     */
    private saveWorkflow;
    /**
     * Processa as próximas etapas de um workflow
     * @param workflowId ID do workflow
     */
    private processNextSteps;
    /**
     * Inicia o watcher de workflows
     */
    private startWatcher;
    /**
     * Verifica o status de um workflow
     * @param workflowId ID do workflow
     */
    private checkWorkflowStatus;
    /**
     * Altera o intervalo de verificação do watcher
     * @param intervalMs Intervalo em milissegundos
     */
    setWatcherInterval(intervalMs: number): void;
    /**
     * Desconecta o workflow
     */
    disconnect(): Promise<void>;
}
export {};
