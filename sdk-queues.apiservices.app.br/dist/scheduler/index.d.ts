import { RedisOptions } from "ioredis";
import { PublishOptions } from "../publisher";
import { SchedulerStorage } from "../storage/interfaces";
interface ScheduledTask {
    id: string;
    channel: string;
    message: any;
    options: PublishOptions;
    schedule: {
        type: "cron" | "interval" | "datetime";
        value: string | number;
        timezone?: string;
    };
    lastRun?: string;
    nextRun?: string;
    enabled: boolean;
}
export interface SchedulerOptions {
    redis?: RedisOptions;
    storage?: SchedulerStorage;
}
export declare class Scheduler {
    private setup;
    private publisher;
    private schedulerInterval;
    private scheduleCheckInterval;
    private storage;
    constructor(options?: SchedulerOptions);
    /**
     * Agenda uma tarefa para ser executada em um intervalo regular
     * @param id Identificador único da tarefa
     * @param channel Nome do canal/fila
     * @param message Conteúdo da mensagem
     * @param intervalMs Intervalo em milissegundos
     * @param options Opções de publicação
     */
    scheduleInterval(id: string, channel: string, message: any, intervalMs: number, options?: PublishOptions): Promise<string>;
    /**
     * Agenda uma tarefa para ser executada de acordo com uma expressão cron
     * @param id Identificador único da tarefa
     * @param channel Nome do canal/fila
     * @param message Conteúdo da mensagem
     * @param cronExpression Expressão cron (ex: "0 * * * *" para a cada hora)
     * @param timezone Fuso horário (ex: "America/Sao_Paulo")
     * @param options Opções de publicação
     */
    scheduleCron(id: string, channel: string, message: any, cronExpression: string, timezone?: string, options?: PublishOptions): Promise<string>;
    /**
     * Agenda uma tarefa para ser executada em uma data específica
     * @param id Identificador único da tarefa
     * @param channel Nome do canal/fila
     * @param message Conteúdo da mensagem
     * @param date Data e hora para execução (pode ser string ISO ou objeto Date)
     * @param options Opções de publicação
     */
    scheduleAt(id: string, channel: string, message: any, date: Date | string, options?: PublishOptions): Promise<string>;
    /**
     * Pausa uma tarefa agendada
     * @param id ID da tarefa
     */
    pauseTask(id: string): Promise<boolean>;
    /**
     * Retoma uma tarefa agendada
     * @param id ID da tarefa
     */
    resumeTask(id: string): Promise<boolean>;
    /**
     * Remove uma tarefa agendada
     * @param id ID da tarefa
     */
    removeTask(id: string): Promise<boolean>;
    /**
     * Lista todas as tarefas agendadas
     */
    listTasks(): Promise<ScheduledTask[]>;
    /**
     * Obtém uma tarefa específica pelo ID
     * @param id ID da tarefa
     */
    getTask(id: string): Promise<ScheduledTask | null>;
    /**
     * Salva uma tarefa
     * @param task Tarefa a ser salva
     */
    private saveTask;
    /**
     * Inicia o scheduler para processar tarefas agendadas
     */
    private startScheduler;
    /**
     * Processa as tarefas agendadas que estão prontas para execução
     */
    private processScheduledTasks;
    /**
     * Obtém as tarefas agendadas sem gerar logs
     * Método interno usado pelo processador de tarefas
     */
    private getTasksWithoutLogging;
    /**
     * Executa uma tarefa agendada
     * @param task Tarefa a ser executada
     */
    private executeTask;
    /**
     * Calcula a próxima execução de uma tarefa com base no tipo de agendamento
     * @param schedule Configuração de agendamento
     * @param lastRun Última execução (opcional)
     */
    private calculateNextRun;
    /**
     * Altera o intervalo de verificação do scheduler
     * @param intervalMs Intervalo em milissegundos
     */
    setCheckInterval(intervalMs: number): void;
    /**
     * Desconecta o scheduler
     */
    disconnect(): Promise<void>;
}
export {};
