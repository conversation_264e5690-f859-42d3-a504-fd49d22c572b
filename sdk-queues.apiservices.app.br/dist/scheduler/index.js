"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Scheduler = void 0;
const setup_1 = require("../setup");
const publisher_1 = require("../publisher");
const redis_storage_1 = require("../storage/redis-storage");
const monitoring_1 = require("../infra/monitoring");
// Logger para o módulo scheduler
const logger = (0, monitoring_1.createModuleLogger)("scheduler");
class Scheduler {
    constructor(options) {
        this.schedulerInterval = null;
        this.scheduleCheckInterval = 1000; // Verificar a cada 1 segundo por padrão
        // Usar as opções Redis exatamente como foram passadas, sem modificar o username
        const schedulerRedisOptions = options?.redis || {};
        this.setup = new setup_1.SetupQueueManager(schedulerRedisOptions);
        this.publisher = new publisher_1.Publisher(schedulerRedisOptions);
        // Se o storage foi fornecido, use-o, caso contrário crie um Redis storage
        if (options?.storage) {
            this.storage = options.storage;
            logger.info("Scheduler inicializado com storage customizado");
        }
        else {
            this.storage = new redis_storage_1.RedisSchedulerStorage(this.setup.getCommandClient());
            logger.info("Scheduler inicializado com Redis storage padrão");
        }
        this.startScheduler();
    }
    /**
     * Agenda uma tarefa para ser executada em um intervalo regular
     * @param id Identificador único da tarefa
     * @param channel Nome do canal/fila
     * @param message Conteúdo da mensagem
     * @param intervalMs Intervalo em milissegundos
     * @param options Opções de publicação
     */
    async scheduleInterval(id, channel, message, intervalMs, options = { persistent: true }) {
        const task = {
            id,
            channel,
            message,
            options,
            schedule: {
                type: "interval",
                value: intervalMs,
            },
            enabled: true,
        };
        await this.saveTask(task);
        logger.info(`Tarefa ${id} agendada para executar a cada ${intervalMs}ms no canal ${channel}`, {
            taskId: id,
            channel,
            intervalMs,
        });
        return id;
    }
    /**
     * Agenda uma tarefa para ser executada de acordo com uma expressão cron
     * @param id Identificador único da tarefa
     * @param channel Nome do canal/fila
     * @param message Conteúdo da mensagem
     * @param cronExpression Expressão cron (ex: "0 * * * *" para a cada hora)
     * @param timezone Fuso horário (ex: "America/Sao_Paulo")
     * @param options Opções de publicação
     */
    async scheduleCron(id, channel, message, cronExpression, timezone = "UTC", options = { persistent: true }) {
        const task = {
            id,
            channel,
            message,
            options,
            schedule: {
                type: "cron",
                value: cronExpression,
                timezone,
            },
            enabled: true,
        };
        await this.saveTask(task);
        logger.info(`Tarefa ${id} agendada com expressão cron "${cronExpression}" (${timezone}) no canal ${channel}`, {
            taskId: id,
            channel,
            cronExpression,
            timezone,
        });
        return id;
    }
    /**
     * Agenda uma tarefa para ser executada em uma data específica
     * @param id Identificador único da tarefa
     * @param channel Nome do canal/fila
     * @param message Conteúdo da mensagem
     * @param date Data e hora para execução (pode ser string ISO ou objeto Date)
     * @param options Opções de publicação
     */
    async scheduleAt(id, channel, message, date, options = { persistent: true }) {
        const dateStr = date instanceof Date ? date.toISOString() : date;
        const task = {
            id,
            channel,
            message,
            options,
            schedule: {
                type: "datetime",
                value: dateStr,
            },
            enabled: true,
        };
        await this.saveTask(task);
        logger.info(`Tarefa ${id} agendada para executar em ${dateStr} no canal ${channel}`, {
            taskId: id,
            channel,
            scheduledDate: dateStr,
        });
        return id;
    }
    /**
     * Pausa uma tarefa agendada
     * @param id ID da tarefa
     */
    async pauseTask(id) {
        const task = await this.storage.getTask(id);
        if (!task) {
            logger.warn(`Tentativa de pausar tarefa inexistente: ${id}`);
            return false;
        }
        task.enabled = false;
        await this.storage.saveTask(id, task);
        logger.info(`Tarefa ${id} pausada`, { taskId: id });
        return true;
    }
    /**
     * Retoma uma tarefa agendada
     * @param id ID da tarefa
     */
    async resumeTask(id) {
        const task = await this.storage.getTask(id);
        if (!task) {
            logger.warn(`Tentativa de retomar tarefa inexistente: ${id}`);
            return false;
        }
        task.enabled = true;
        // Atualiza próxima execução se for necessário
        if (task.schedule.type === "interval") {
            task.nextRun = new Date(Date.now() + Number(task.schedule.value)).toISOString();
        }
        else if (task.schedule.type === "datetime") {
            // Se a data já passou, não reativa
            if (new Date(task.schedule.value) < new Date()) {
                logger.warn(`Não foi possível retomar tarefa ${id} pois a data agendada já passou`, {
                    taskId: id,
                    scheduledDate: task.schedule.value,
                });
                return false;
            }
        }
        await this.storage.saveTask(id, task);
        logger.info(`Tarefa ${id} retomada`, { taskId: id });
        return true;
    }
    /**
     * Remove uma tarefa agendada
     * @param id ID da tarefa
     */
    async removeTask(id) {
        const result = await this.storage.removeTask(id);
        if (result) {
            logger.info(`Tarefa ${id} removida`, { taskId: id });
        }
        else {
            logger.warn(`Tentativa de remover tarefa inexistente: ${id}`);
        }
        return result;
    }
    /**
     * Lista todas as tarefas agendadas
     */
    async listTasks() {
        const tasks = await this.storage.listTasks();
        logger.info(`Listando ${tasks.length} tarefas agendadas`);
        return tasks;
    }
    /**
     * Obtém uma tarefa específica pelo ID
     * @param id ID da tarefa
     */
    async getTask(id) {
        const task = await this.storage.getTask(id);
        if (task) {
            logger.info(`Tarefa ${id} encontrada`);
        }
        else {
            logger.info(`Tarefa ${id} não encontrada`);
        }
        return task;
    }
    /**
     * Salva uma tarefa
     * @param task Tarefa a ser salva
     */
    async saveTask(task) {
        // Calcula a próxima execução
        task.nextRun = this.calculateNextRun(task.schedule);
        logger.debug(`Próxima execução da tarefa ${task.id} calculada: ${task.nextRun}`);
        await this.storage.saveTask(task.id, task);
    }
    /**
     * Inicia o scheduler para processar tarefas agendadas
     */
    startScheduler() {
        if (this.schedulerInterval) {
            clearInterval(this.schedulerInterval);
        }
        this.schedulerInterval = setInterval(async () => {
            await this.processScheduledTasks();
        }, this.scheduleCheckInterval);
        // Não bloqueia o processo Node.js
        this.schedulerInterval.unref();
        logger.info(`Scheduler iniciado com intervalo de verificação de ${this.scheduleCheckInterval}ms`);
    }
    /**
     * Processa as tarefas agendadas que estão prontas para execução
     */
    async processScheduledTasks() {
        try {
            // Usa um método interno para obter as tarefas sem gerar logs
            const tasks = await this.getTasksWithoutLogging();
            const now = new Date();
            logger.debug(`Verificando ${tasks.length} tarefas agendadas`);
            for (const task of tasks) {
                // Pula tarefas desativadas
                if (!task.enabled) {
                    continue;
                }
                // Pula tarefas sem próxima execução definida
                if (!task.nextRun) {
                    continue;
                }
                // Verifica se é hora de executar
                const nextRunDate = new Date(task.nextRun);
                if (nextRunDate <= now) {
                    await this.executeTask(task);
                }
            }
        }
        catch (error) {
            logger.error("Erro ao processar tarefas agendadas:", error);
        }
    }
    /**
     * Obtém as tarefas agendadas sem gerar logs
     * Método interno usado pelo processador de tarefas
     */
    async getTasksWithoutLogging() {
        try {
            // Usa o parâmetro skipLogging=true para evitar o registro de logs
            return await this.storage.listTasks(true);
        }
        catch (error) {
            logger.error("Erro ao obter tarefas agendadas:", error);
            return [];
        }
    }
    /**
     * Executa uma tarefa agendada
     * @param task Tarefa a ser executada
     */
    async executeTask(task) {
        try {
            logger.info(`Executando tarefa ${task.id} no canal ${task.channel}`, { taskId: task.id, channel: task.channel });
            // Publica a mensagem
            await this.publisher.publish(task.channel, task.message, task.options);
            // Atualiza última execução
            task.lastRun = new Date().toISOString();
            // Calcula próxima execução (null para tarefas one-time)
            if (task.schedule.type !== "datetime") {
                task.nextRun = this.calculateNextRun(task.schedule, new Date());
                logger.debug(`Próxima execução agendada para ${task.nextRun}`, { taskId: task.id });
            }
            else {
                // Para tarefas de data específica, desativa após executar
                task.enabled = false;
                task.nextRun = null;
                logger.info(`Tarefa ${task.id} de data específica concluída e desativada`, { taskId: task.id });
            }
            // Salva a tarefa atualizada
            await this.storage.saveTask(task.id, task);
        }
        catch (error) {
            logger.error(`Erro ao executar tarefa ${task.id}:`, error);
        }
    }
    /**
     * Calcula a próxima execução de uma tarefa com base no tipo de agendamento
     * @param schedule Configuração de agendamento
     * @param lastRun Última execução (opcional)
     */
    calculateNextRun(schedule, lastRun) {
        const now = lastRun || new Date();
        switch (schedule.type) {
            case "interval":
                // Para intervalos, simplesmente adiciona o intervalo à data atual
                return new Date(now.getTime() + Number(schedule.value)).toISOString();
            case "datetime":
                // Para datas específicas, retorna a data configurada
                return schedule.value;
            case "cron":
                // Para expressões cron, calcula a próxima ocorrência
                // Aqui usamos uma implementação simplificada para cron diário no formato "hora minuto"
                try {
                    // Implementação simplificada para "minuto hora * * *"
                    const [minute, hour] = schedule.value.split(" ");
                    const nextDate = new Date(now);
                    nextDate.setHours(parseInt(hour), parseInt(minute), 0, 0);
                    // Se a data calculada já passou, adiciona um dia
                    if (nextDate <= now) {
                        nextDate.setDate(nextDate.getDate() + 1);
                    }
                    return nextDate.toISOString();
                }
                catch (error) {
                    logger.error("Erro ao calcular próxima execução para expressão cron:", error);
                    // Fallback para 24h depois em caso de erro
                    return new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString();
                }
            default:
                return now.toISOString();
        }
    }
    /**
     * Altera o intervalo de verificação do scheduler
     * @param intervalMs Intervalo em milissegundos
     */
    setCheckInterval(intervalMs) {
        this.scheduleCheckInterval = intervalMs;
        logger.info(`Intervalo de verificação alterado para ${intervalMs}ms`);
        // Reinicia o scheduler com o novo intervalo
        this.startScheduler();
    }
    /**
     * Desconecta o scheduler
     */
    async disconnect() {
        if (this.schedulerInterval) {
            clearInterval(this.schedulerInterval);
            this.schedulerInterval = null;
        }
        logger.info("Desconectando scheduler");
        await this.publisher.disconnect();
        await this.setup.disconnect();
    }
}
exports.Scheduler = Scheduler;
