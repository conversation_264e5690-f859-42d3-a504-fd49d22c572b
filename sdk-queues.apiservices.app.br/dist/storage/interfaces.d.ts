/**
 * Interface para abstração de armazenamento de tarefas agendadas
 */
export interface SchedulerStorage {
    /**
     * Salva uma tarefa agendada
     * @param taskId ID da tarefa
     * @param taskData Dados da tarefa em formato JSON
     */
    saveTask(taskId: string, taskData: any): Promise<void>;
    /**
     * Obtém uma tarefa pelo ID
     * @param taskId ID da tarefa
     */
    getTask(taskId: string): Promise<any | null>;
    /**
     * Lista todas as tarefas agendadas
     * @param skipLogging Se verdadeiro, não registra logs de atividade (opcional)
     */
    listTasks(skipLogging?: boolean): Promise<any[]>;
    /**
     * Remove uma tarefa pelo ID
     * @param taskId ID da tarefa
     */
    removeTask(taskId: string): Promise<boolean>;
    /**
     * Verifica se uma tarefa existe
     * @param taskId ID da tarefa
     */
    taskExists(taskId: string): Promise<boolean>;
}
/**
 * Interface para abstração de armazenamento de workflows
 */
export interface WorkflowStorage {
    /**
     * Salva um workflow
     * @param workflowId ID do workflow
     * @param workflowData Dados do workflow em formato JSON
     */
    saveWorkflow(workflowId: string, workflowData: any): Promise<void>;
    /**
     * Obtém um workflow pelo ID
     * @param workflowId ID do workflow
     */
    getWorkflow(workflowId: string): Promise<any | null>;
    /**
     * Lista todos os workflows
     */
    listWorkflows(): Promise<any[]>;
    /**
     * Remove um workflow pelo ID
     * @param workflowId ID do workflow
     */
    removeWorkflow(workflowId: string): Promise<boolean>;
    /**
     * Verifica se um workflow existe
     * @param workflowId ID do workflow
     */
    workflowExists(workflowId: string): Promise<boolean>;
}
