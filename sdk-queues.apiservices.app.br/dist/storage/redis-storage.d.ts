import { SchedulerStorage, WorkflowStorage } from "./interfaces";
import Redis from "ioredis";
import { RedisOptions } from "ioredis";
/**
 * Implementação de armazenamento Redis para o Scheduler
 */
export declare class RedisSchedulerStorage implements SchedulerStorage {
    private redis;
    constructor(redis: Redis);
    saveTask(taskId: string, taskData: any): Promise<void>;
    getTask(taskId: string): Promise<any | null>;
    listTasks(): Promise<any[]>;
    removeTask(taskId: string): Promise<boolean>;
    taskExists(taskId: string): Promise<boolean>;
}
/**
 * Implementação de armazenamento Redis para o Workflow
 */
export declare class RedisWorkflowStorage implements WorkflowStorage {
    private redis;
    constructor(redis: Redis);
    saveWorkflow(workflowId: string, workflowData: any): Promise<void>;
    getWorkflow(workflowId: string): Promise<any | null>;
    listWorkflows(): Promise<any[]>;
    removeWorkflow(workflowId: string): Promise<boolean>;
    workflowExists(workflowId: string): Promise<boolean>;
}
/**
 * Factory para criar instâncias de storage Redis
 */
export declare class RedisStorageFactory {
    static createSchedulerStorage(redisOptions: RedisOptions): RedisSchedulerStorage;
    static createWorkflowStorage(redisOptions: RedisOptions): RedisWorkflowStorage;
}
