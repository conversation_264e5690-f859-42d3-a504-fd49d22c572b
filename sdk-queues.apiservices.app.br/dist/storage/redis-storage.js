"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisStorageFactory = exports.RedisWorkflowStorage = exports.RedisSchedulerStorage = void 0;
const ioredis_1 = __importDefault(require("ioredis"));
const monitoring_1 = require("../infra/monitoring");
// Logger para o módulo redis-storage
const logger = (0, monitoring_1.createModuleLogger)("redis-storage");
/**
 * Implementação de armazenamento Redis para o Scheduler
 */
class RedisSchedulerStorage {
    constructor(redis) {
        this.redis = redis;
        logger.info("RedisSchedulerStorage inicializado");
    }
    async saveTask(taskId, taskData) {
        const taskKey = `scheduler:tasks:${taskId}`;
        await this.redis.set(taskKey, JSON.stringify(taskData));
        await this.redis.sadd("scheduler:taskids", taskId);
        logger.info(`Tarefa ${taskId} salva no Redis`);
    }
    async getTask(taskId) {
        const taskKey = `scheduler:tasks:${taskId}`;
        const data = await this.redis.get(taskKey);
        if (!data) {
            logger.info(`Tarefa ${taskId} não encontrada`);
            return null;
        }
        logger.info(`Tarefa ${taskId} recuperada do Redis`);
        return JSON.parse(data);
    }
    async listTasks() {
        const taskIds = await this.redis.smembers("scheduler:taskids");
        const tasks = [];
        logger.info(`Listando ${taskIds.length} tarefas do Redis`);
        for (const taskId of taskIds) {
            const task = await this.getTask(taskId);
            if (task) {
                tasks.push(task);
            }
        }
        return tasks;
    }
    async removeTask(taskId) {
        const taskKey = `scheduler:tasks:${taskId}`;
        const exists = await this.redis.exists(taskKey);
        if (!exists) {
            logger.info(`Tentativa de remover tarefa ${taskId} inexistente`);
            return false;
        }
        await this.redis.del(taskKey);
        await this.redis.srem("scheduler:taskids", taskId);
        logger.info(`Tarefa ${taskId} removida do Redis`);
        return true;
    }
    async taskExists(taskId) {
        const taskKey = `scheduler:tasks:${taskId}`;
        const exists = await this.redis.exists(taskKey);
        logger.debug(`Verificando existência da tarefa ${taskId}: ${exists === 1 ? "existe" : "não existe"}`);
        return exists === 1;
    }
}
exports.RedisSchedulerStorage = RedisSchedulerStorage;
/**
 * Implementação de armazenamento Redis para o Workflow
 */
class RedisWorkflowStorage {
    constructor(redis) {
        this.redis = redis;
        logger.info("RedisWorkflowStorage inicializado");
    }
    async saveWorkflow(workflowId, workflowData) {
        const workflowKey = `workflow:${workflowId}`;
        await this.redis.set(workflowKey, JSON.stringify(workflowData));
        logger.info(`Workflow ${workflowId} salvo no Redis`);
    }
    async getWorkflow(workflowId) {
        const workflowKey = `workflow:${workflowId}`;
        const data = await this.redis.get(workflowKey);
        if (!data) {
            logger.info(`Workflow ${workflowId} não encontrado`);
            return null;
        }
        logger.info(`Workflow ${workflowId} recuperado do Redis`);
        return JSON.parse(data);
    }
    async listWorkflows() {
        const keys = await this.redis.keys("workflow:*");
        const workflows = [];
        logger.info(`Listando ${keys.length} workflows do Redis`);
        for (const key of keys) {
            const workflowId = key.replace("workflow:", "");
            const workflow = await this.getWorkflow(workflowId);
            if (workflow) {
                workflows.push(workflow);
            }
        }
        return workflows;
    }
    async removeWorkflow(workflowId) {
        const workflowKey = `workflow:${workflowId}`;
        const exists = await this.redis.exists(workflowKey);
        if (!exists) {
            logger.info(`Tentativa de remover workflow ${workflowId} inexistente`);
            return false;
        }
        await this.redis.del(workflowKey);
        logger.info(`Workflow ${workflowId} removido do Redis`);
        return true;
    }
    async workflowExists(workflowId) {
        const workflowKey = `workflow:${workflowId}`;
        const exists = await this.redis.exists(workflowKey);
        logger.debug(`Verificando existência do workflow ${workflowId}: ${exists === 1 ? "existe" : "não existe"}`);
        return exists === 1;
    }
}
exports.RedisWorkflowStorage = RedisWorkflowStorage;
/**
 * Factory para criar instâncias de storage Redis
 */
class RedisStorageFactory {
    static createSchedulerStorage(redisOptions) {
        logger.info("Criando RedisSchedulerStorage via factory");
        // Ajuste das credenciais para compatibilidade
        if (redisOptions.username === "default") {
            redisOptions.username = undefined;
        }
        if (redisOptions.password === "") {
            redisOptions.password = undefined;
        }
        const redis = new ioredis_1.default(redisOptions);
        return new RedisSchedulerStorage(redis);
    }
    static createWorkflowStorage(redisOptions) {
        logger.info("Criando RedisWorkflowStorage via factory");
        // Ajuste das credenciais para compatibilidade
        if (redisOptions.username === "default") {
            redisOptions.username = undefined;
        }
        if (redisOptions.password === "") {
            redisOptions.password = undefined;
        }
        const redis = new ioredis_1.default(redisOptions);
        return new RedisWorkflowStorage(redis);
    }
}
exports.RedisStorageFactory = RedisStorageFactory;
