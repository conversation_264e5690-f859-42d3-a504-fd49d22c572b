import { SchedulerStorage, WorkflowStorage } from "./interfaces";
import * as mysql from "mysql2/promise";
/**
 * Configuração de conexão MySQL
 */
export interface MySQLConfig {
    host: string;
    port?: number;
    user: string;
    password: string;
    database: string;
    connectionLimit?: number;
}
/**
 * Tipo de log para registros no sistema
 */
export declare enum LogType {
    INFO = "info",
    WARNING = "warning",
    ERROR = "error",
    SUCCESS = "success"
}
/**
 * Interface para logs do sistema
 */
export interface SystemLog {
    id?: string;
    type: LogType;
    source: string;
    action: string;
    details?: any;
    status: "success" | "error" | "pending";
    createdAt?: Date;
}
/**
 * Classe base para armazenamento MySQL com funcionalidades comuns
 */
export declare class MySQLBaseStorage {
    protected pool: mysql.Pool;
    constructor(config: MySQLConfig);
    /**
     * Inicializa a tabela de logs do sistema
     */
    private initializeLogsTable;
    /**
     * Registra um log no sistema
     * @param log Dados do log
     */
    logActivity(log: SystemLog): Promise<string>;
    /**
     * Obtém logs do sistema com filtros opcionais
     * @param filters Filtros para os logs (opcional)
     * @param limit Limite de registros a retornar (opcional)
     * @param offset Deslocamento para paginação (opcional)
     */
    getLogs(filters?: {
        type?: LogType;
        source?: string;
        action?: string;
        status?: "success" | "error" | "pending";
        startDate?: Date;
        endDate?: Date;
    }, limit?: number, offset?: number): Promise<SystemLog[]>;
    /**
     * Fecha a conexão com o banco de dados
     */
    close(): Promise<void>;
}
/**
 * Implementação de armazenamento MySQL para o Scheduler
 */
export declare class MySQLSchedulerStorage extends MySQLBaseStorage implements SchedulerStorage {
    constructor(config: MySQLConfig);
    /**
     * Inicializa as tabelas necessárias
     */
    private initializeTables;
    saveTask(taskId: string, taskData: any): Promise<void>;
    getTask(taskId: string): Promise<any | null>;
    listTasks(skipLogging?: boolean): Promise<any[]>;
    removeTask(taskId: string): Promise<boolean>;
    taskExists(taskId: string): Promise<boolean>;
}
/**
 * Implementação de armazenamento MySQL para o Workflow
 */
export declare class MySQLWorkflowStorage extends MySQLBaseStorage implements WorkflowStorage {
    constructor(config: MySQLConfig);
    /**
     * Inicializa as tabelas necessárias
     */
    private initializeTables;
    saveWorkflow(workflowId: string, workflowData: any): Promise<void>;
    getWorkflow(workflowId: string): Promise<any | null>;
    listWorkflows(): Promise<any[]>;
    removeWorkflow(workflowId: string): Promise<boolean>;
    workflowExists(workflowId: string): Promise<boolean>;
}
/**
 * Factory para criar instâncias de storage MySQL
 */
export declare class MySQLStorageFactory {
    static createSchedulerStorage(config: MySQLConfig): MySQLSchedulerStorage;
    static createWorkflowStorage(config: MySQLConfig): MySQLWorkflowStorage;
}
