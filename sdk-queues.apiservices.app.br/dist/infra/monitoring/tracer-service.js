"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TracingService = void 0;
const api_1 = require("@opentelemetry/api");
const logger_1 = require("./logger");
// Logger para o serviço de tracing
const logger = (0, logger_1.createModuleLogger)("tracing-service");
/**
 * Classe de serviço de tracing utilizando recursos do monitoring
 */
class TracingService {
    constructor() {
        logger.info("Serviço de tracing inicializado");
    }
    /**
     * Inicia uma nova trace para rastreamento
     * @param name Nome da trace
     * @param attributes Atributos adicionais
     */
    async startTrace(name, attributes = {}) {
        const tracer = api_1.trace.getTracer("sdk-queues");
        const span = tracer.startSpan(name, {
            kind: api_1.SpanKind.INTERNAL,
            attributes: attributes,
        });
        // Extrair o ID da trace
        const traceId = span.spanContext().traceId;
        // Armazenar span em contexto
        api_1.trace.setSpan(api_1.context.active(), span);
        logger.info(`Trace iniciada: ${name}`, { traceId, ...attributes });
        return traceId;
    }
    /**
     * Encerra uma trace
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param status Status de conclusão
     */
    async endTrace(_traceId, status = "completed") {
        // Obtém o span atual do contexto
        const currentSpan = api_1.trace.getSpan(api_1.context.active());
        if (!currentSpan) {
            logger.warn("Tentativa de encerrar trace sem span ativo");
            return false;
        }
        // Define o status
        if (status === "error") {
            currentSpan.setStatus({
                code: api_1.SpanStatusCode.ERROR,
            });
        }
        else {
            currentSpan.setStatus({
                code: api_1.SpanStatusCode.OK,
            });
        }
        // Encerra o span
        currentSpan.end();
        logger.info(`Trace encerrada com status: ${status}`);
        return true;
    }
    /**
     * Inicia um novo span dentro de uma trace
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param name Nome do span
     * @param parentId ID do span pai (não utilizado - usa o contexto atual)
     * @param attributes Atributos adicionais
     */
    async startSpan(_traceId, name, _parentId, attributes = {}) {
        const tracer = api_1.trace.getTracer("sdk-queues");
        const span = tracer.startSpan(name, {
            kind: api_1.SpanKind.INTERNAL,
            attributes: attributes,
        });
        // Extrair o ID do span
        const spanId = span.spanContext().spanId;
        // Armazenar span em contexto
        api_1.trace.setSpan(api_1.context.active(), span);
        logger.info(`Span iniciado: ${name}`, { spanId, ...attributes });
        return spanId;
    }
    /**
     * Encerra um span
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param spanId ID do span (não utilizado - usa o contexto atual)
     * @param status Status de conclusão
     */
    async endSpan(_traceId, _spanId, status = "completed") {
        // Obtém o span atual do contexto
        const currentSpan = api_1.trace.getSpan(api_1.context.active());
        if (!currentSpan) {
            logger.warn("Tentativa de encerrar span sem span ativo");
            return false;
        }
        // Define o status
        if (status === "error") {
            currentSpan.setStatus({
                code: api_1.SpanStatusCode.ERROR,
            });
        }
        else {
            currentSpan.setStatus({
                code: api_1.SpanStatusCode.OK,
            });
        }
        // Encerra o span
        currentSpan.end();
        logger.info(`Span encerrado com status: ${status}`);
        return true;
    }
    /**
     * Adiciona um evento a um span
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param spanId ID do span (não utilizado - usa o contexto atual)
     * @param name Nome do evento
     * @param attributes Atributos do evento
     */
    async addEvent(_traceId, _spanId, name, attributes = {}) {
        // Obtém o span atual do contexto
        const currentSpan = api_1.trace.getSpan(api_1.context.active());
        if (!currentSpan) {
            logger.warn("Tentativa de adicionar evento sem span ativo");
            return false;
        }
        // Adiciona o evento ao span
        currentSpan.addEvent(name, attributes);
        logger.info(`Evento adicionado: ${name}`, attributes);
        return true;
    }
    /**
     * Adiciona atributos a um span
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param spanId ID do span (não utilizado - usa o contexto atual)
     * @param attributes Atributos a adicionar
     */
    async addAttributes(_traceId, _spanId, attributes) {
        // Obtém o span atual do contexto
        const currentSpan = api_1.trace.getSpan(api_1.context.active());
        if (!currentSpan) {
            logger.warn("Tentativa de adicionar atributos sem span ativo");
            return false;
        }
        // Adiciona os atributos ao span
        currentSpan.setAttributes(attributes);
        logger.info("Atributos adicionados ao span", attributes);
        return true;
    }
    /**
     * Método para manter compatibilidade com a API anterior
     */
    async disconnect() {
        logger.info("Serviço de tracing desconectado");
    }
}
exports.TracingService = TracingService;
