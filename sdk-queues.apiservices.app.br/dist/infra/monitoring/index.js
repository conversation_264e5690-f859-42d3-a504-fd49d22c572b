"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TracingService = exports.openTelemetry = exports.createModuleLogger = void 0;
require("./tracing");
const logger_1 = require("./logger");
Object.defineProperty(exports, "createModuleLogger", { enumerable: true, get: function () { return logger_1.createModuleLogger; } });
const tracer_1 = __importDefault(require("./tracer"));
exports.openTelemetry = tracer_1.default;
const tracer_service_1 = require("./tracer-service");
Object.defineProperty(exports, "TracingService", { enumerable: true, get: function () { return tracer_service_1.TracingService; } });
