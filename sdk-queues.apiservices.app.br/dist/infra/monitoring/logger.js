"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createModuleLogger = createModuleLogger;
const sdk_logs_1 = require("@opentelemetry/sdk-logs");
const sdk_logs_2 = require("@opentelemetry/sdk-logs");
const exporter_logs_otlp_http_1 = require("@opentelemetry/exporter-logs-otlp-http");
const resources_1 = require("@opentelemetry/resources");
const semantic_conventions_1 = require("@opentelemetry/semantic-conventions");
const winston = __importStar(require("winston"));
const api_1 = require("@opentelemetry/api");
const NODE_ENV = process.env.NODE_ENV || "production";
const logExporter = new exporter_logs_otlp_http_1.OTLPLogExporter({
    url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT ? `${process.env.OTEL_EXPORTER_OTLP_ENDPOINT}/v1/logs` : "http://localhost:4318/v1/logs",
    headers: {
        "Content-Type": "application/json",
    },
});
const resourceAttributes = {
    [semantic_conventions_1.SemanticResourceAttributes.SERVICE_NAME]: "sdk-queues-manager",
    [semantic_conventions_1.SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: NODE_ENV,
};
// @ts-ignore
const loggerProvider = new sdk_logs_1.LoggerProvider({
    // @ts-ignore
    resource: new resources_1.Resource(resourceAttributes),
});
loggerProvider.addLogRecordProcessor(new sdk_logs_2.BatchLogRecordProcessor(logExporter));
function createModuleLogger(moduleName) {
    const winstonLogger = winston.createLogger({
        level: process.env.LOG_LEVEL || "info",
        format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
        defaultMeta: {
            service: "sdk-queues-manager",
            module: moduleName,
            environment: NODE_ENV,
        },
        transports: [new winston.transports.Console()],
    });
    const logToOtel = (level, message, meta) => {
        try {
            const span = api_1.trace.getActiveSpan();
            let traceId, spanId;
            if (span) {
                try {
                    const context = span.spanContext();
                    traceId = context?.traceId;
                    spanId = context?.spanId;
                }
                catch (e) { }
            }
            const otelLogger = loggerProvider.getLogger(moduleName);
            otelLogger.emit({
                severityText: level,
                body: message,
                attributes: {
                    ...(Array.isArray(meta) && meta.length > 0 ? meta[0] : meta),
                    traceId,
                    spanId,
                    service: "sdk-queues-manager",
                    module: moduleName,
                    environment: NODE_ENV,
                },
            });
        }
        catch (error) {
            console.error("Erro ao enviar log para OpenTelemetry:", error);
        }
    };
    return {
        debug: (message, ...meta) => {
            winstonLogger.debug(message, ...meta);
            logToOtel("DEBUG", message, meta);
        },
        info: (message, ...meta) => {
            winstonLogger.info(message, ...meta);
            logToOtel("INFO", message, meta);
        },
        warn: (message, ...meta) => {
            winstonLogger.warn(message, ...meta);
            logToOtel("WARN", message, meta);
        },
        error: (message, ...meta) => {
            winstonLogger.error(message, ...meta);
            logToOtel("ERROR", message, meta);
        },
    };
}
