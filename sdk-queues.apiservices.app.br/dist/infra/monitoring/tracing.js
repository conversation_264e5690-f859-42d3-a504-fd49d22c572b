"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const tracer_1 = __importDefault(require("./tracer"));
const logger_1 = require("./logger");
const apmLogger = (0, logger_1.createModuleLogger)("APM");
async function initializeTracing() {
    try {
        await tracer_1.default.start();
        apmLogger.info("Tracing inicializado com sucesso");
    }
    catch (error) {
        apmLogger.error("Erro ao inicializar tracing", error);
    }
}
process.on("SIGTERM", async () => {
    try {
        await tracer_1.default.shutdown();
        apmLogger.info("Tracing finalizado");
    }
    catch (error) {
        apmLogger.error("Erro ao finalizar tracing", error);
    }
});
process.on("beforeExit", () => {
    apmLogger.info("Tentativa de encerramento do processo detectada");
});
initializeTracing();
exports.default = tracer_1.default;
