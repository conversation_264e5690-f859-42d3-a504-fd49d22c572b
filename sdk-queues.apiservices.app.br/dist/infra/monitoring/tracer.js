"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sdk_node_1 = require("@opentelemetry/sdk-node");
const exporter_trace_otlp_http_1 = require("@opentelemetry/exporter-trace-otlp-http");
const resources_1 = require("@opentelemetry/resources");
const semantic_conventions_1 = require("@opentelemetry/semantic-conventions");
const auto_instrumentations_node_1 = require("@opentelemetry/auto-instrumentations-node");
const instrumentation_express_1 = require("@opentelemetry/instrumentation-express");
const instrumentation_http_1 = require("@opentelemetry/instrumentation-http");
const sdk_trace_base_1 = require("@opentelemetry/sdk-trace-base");
const NODE_ENV = process.env.NODE_ENV || "production";
const OTLP_ENDPOINT = process.env.OTEL_EXPORTER_OTLP_ENDPOINT || "http://localhost:4318";
const resourceAttributes = {
    [semantic_conventions_1.SemanticResourceAttributes.SERVICE_NAME]: "ai-planify",
    [semantic_conventions_1.SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: NODE_ENV,
    [semantic_conventions_1.SemanticResourceAttributes.SERVICE_VERSION]: process.env.npm_package_version || "1.0.0",
};
// @ts-ignore
const sdk = new sdk_node_1.NodeSDK({
    // @ts-ignore
    resource: new resources_1.Resource(resourceAttributes),
    spanProcessor: new sdk_trace_base_1.BatchSpanProcessor(new exporter_trace_otlp_http_1.OTLPTraceExporter({
        url: `${OTLP_ENDPOINT}/v1/traces`,
        headers: {
            "Content-Type": "application/json",
        },
    })),
    instrumentations: [
        new instrumentation_http_1.HttpInstrumentation(),
        new instrumentation_express_1.ExpressInstrumentation(),
        (0, auto_instrumentations_node_1.getNodeAutoInstrumentations)({
            "@opentelemetry/instrumentation-fs": {
                enabled: false,
            },
            "@opentelemetry/instrumentation-express": {
                enabled: true,
            },
            "@opentelemetry/instrumentation-http": {
                enabled: true,
            },
        }),
    ],
});
exports.default = sdk;
