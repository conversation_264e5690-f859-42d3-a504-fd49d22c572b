/**
 * Classe de serviço de tracing utilizando recursos do monitoring
 */
export declare class TracingService {
    constructor();
    /**
     * Inicia uma nova trace para rastreamento
     * @param name Nome da trace
     * @param attributes Atributos adicionais
     */
    startTrace(name: string, attributes?: Record<string, any>): Promise<string>;
    /**
     * Encerra uma trace
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param status Status de conclusão
     */
    endTrace(_traceId: string, status?: "completed" | "error"): Promise<boolean>;
    /**
     * Inicia um novo span dentro de uma trace
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param name Nome do span
     * @param parentId ID do span pai (não utilizado - usa o contexto atual)
     * @param attributes Atributos adicionais
     */
    startSpan(_traceId: string, name: string, _parentId?: string, attributes?: Record<string, any>): Promise<string>;
    /**
     * Encerra um span
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param spanId ID do span (não utilizado - usa o contexto atual)
     * @param status Status de conclusão
     */
    endSpan(_traceId: string, _spanId: string, status?: "completed" | "error"): Promise<boolean>;
    /**
     * Adiciona um evento a um span
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param spanId ID do span (não utilizado - usa o contexto atual)
     * @param name Nome do evento
     * @param attributes Atributos do evento
     */
    addEvent(_traceId: string, _spanId: string, name: string, attributes?: Record<string, any>): Promise<boolean>;
    /**
     * Adiciona atributos a um span
     * @param traceId ID da trace (não utilizado - usa o contexto atual)
     * @param spanId ID do span (não utilizado - usa o contexto atual)
     * @param attributes Atributos a adicionar
     */
    addAttributes(_traceId: string, _spanId: string, attributes: Record<string, any>): Promise<boolean>;
    /**
     * Método para manter compatibilidade com a API anterior
     */
    disconnect(): Promise<void>;
}
