import { RedisOptions } from "ioredis";
export declare const EventTypes: {
    readonly EMAIL: "email";
    readonly NOTIFICATION: "notification";
    readonly PAYMENT: "payment";
    readonly ORDER: "order";
};
export type EventType = (typeof EventTypes)[keyof typeof EventTypes] | string;
export interface PublishOptions {
    persistent?: boolean;
    priority?: number;
    delay?: number;
    attempts?: number;
}
export declare class Publisher {
    private setup;
    constructor(options?: RedisOptions);
    private validateEventType;
    private publishToPersistentQueue;
    private publishToRealtimeChannel;
    publish(channel: string, message: any, options?: PublishOptions): Promise<number>;
    disconnect(): Promise<void>;
}
