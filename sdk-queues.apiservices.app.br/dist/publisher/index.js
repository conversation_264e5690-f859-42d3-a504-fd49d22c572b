"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Publisher = exports.EventTypes = void 0;
const setup_1 = require("../setup");
const monitoring_1 = require("../infra/monitoring");
// Logger para o módulo publisher
const logger = (0, monitoring_1.createModuleLogger)("publisher");
// Tipos de eventos suportados
exports.EventTypes = {
    EMAIL: "email",
    NOTIFICATION: "notification",
    PAYMENT: "payment",
    ORDER: "order",
};
class Publisher {
    constructor(options) {
        // Nova abordagem para configurar as credenciais
        const redisOptions = { ...options };
        const redisUsername = redisOptions.username || process.env.REDIS_USERNAME || "default";
        const redisPassword = redisOptions.password || process.env.REDIS_PASSWORD || "";
        // Remove as propriedades existentes
        delete redisOptions.username;
        delete redisOptions.password;
        // Adiciona as credenciais apenas se não forem valores padrão/vazios
        if (redisUsername !== "default") {
            redisOptions.username = redisUsername;
        }
        if (redisPassword) {
            redisOptions.password = redisPassword;
        }
        this.setup = new setup_1.SetupQueueManager(redisOptions);
        logger.info("Publisher inicializado");
    }
    validateEventType(eventType) {
        // Se não for informado um tipo de evento, usa a fila padrão
        if (!eventType) {
            return;
        }
        // Verifica se é um tipo de evento predefinido
        const predefinedEvents = Object.values(exports.EventTypes);
        const normalizedEventType = eventType.toLowerCase();
        if (predefinedEvents.includes(normalizedEventType)) {
            return; // Evento predefinido válido
        }
        logger.info(`Tipo de evento customizado: ${eventType}`);
    }
    async publishToPersistentQueue(channel, message, options) {
        const redis = this.setup.getCommandClient();
        const queueKey = `queue:${channel}:messages`;
        const messageId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const messageData = {
            id: messageId,
            timestamp: new Date().toISOString(),
            content: message,
            options,
            status: "pending",
            attempts: 0,
        };
        // Adiciona à fila persistente com base na prioridade
        let result;
        if (options.priority && options.priority > 1) {
            // Mensagens de alta prioridade vão para o início da fila
            result = await redis.rpush(queueKey, JSON.stringify(messageData));
            logger.info(`Mensagem ${messageId} adicionada ao início da fila ${queueKey} com prioridade ${options.priority}`, { messageId });
        }
        else {
            // Mensagens normais vão para o final da fila
            result = await redis.lpush(queueKey, JSON.stringify(messageData));
            logger.info(`Mensagem ${messageId} adicionada ao final da fila ${queueKey}`, { messageId });
        }
        // Notifica sobre nova mensagem
        const pubSubClient = this.setup.getSubscriberClient();
        await pubSubClient.publish(`queue:${channel}:notifications`, JSON.stringify({
            type: "new_message",
            messageId,
            priority: options.priority,
        }));
        logger.info(`Notificação enviada para o canal queue:${channel}:notifications`, { messageId });
        return result;
    }
    async publishToRealtimeChannel(channel, message, options) {
        const messageData = {
            id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            data: message,
            options,
        };
        const redis = this.setup.getSubscriberClient();
        const result = await redis.publish(`pubsub:${channel}`, JSON.stringify(messageData));
        logger.info(`Mensagem publicada em tempo real no canal pubsub:${channel}`, { messageId: messageData.id });
        return result;
    }
    async publish(channel = "default", message, options = { persistent: true }) {
        this.validateEventType(channel);
        const { persistent = true, priority = 1, attempts = 3 } = options;
        const publishOptions = { ...options, priority, attempts };
        logger.info(`Publicando no canal ${channel} (${persistent ? "persistente" : "tempo real"})`, {
            channel,
            persistent,
            priority,
            attempts,
        });
        if (persistent) {
            return this.publishToPersistentQueue(channel, message, publishOptions);
        }
        else {
            return this.publishToRealtimeChannel(channel, message, publishOptions);
        }
    }
    async disconnect() {
        logger.info("Desconectando publisher");
        await this.setup.disconnect();
    }
}
exports.Publisher = Publisher;
