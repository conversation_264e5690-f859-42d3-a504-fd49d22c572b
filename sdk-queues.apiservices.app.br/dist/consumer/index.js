"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Consumer = void 0;
const setup_1 = require("../setup");
const publisher_1 = require("../publisher");
const monitoring_1 = require("../infra/monitoring");
// Logger para o módulo consumer
const logger = (0, monitoring_1.createModuleLogger)("consumer");
class Consumer {
    constructor(options) {
        this.handlers = new Map();
        const { queueName, concurrency, ...redisOptions } = options;
        this.setup = new setup_1.SetupQueueManager(redisOptions);
        this.queueName = queueName;
        this.concurrency = concurrency || 1;
        logger.info("Consumer inicializado");
    }
    /**
     * Registra um handler para processar mensagens da fila
     * @param handler Função que processa a mensagem e retorna um booleano indicando sucesso
     */
    registerHandler(handler) {
        this.handlers.set(this.queueName, handler);
        logger.info(`Handler registrado para a fila ${this.queueName}`);
    }
    /**
     * Inicia o consumo de mensagens da fila
     */
    async start() {
        if (!this.handlers.has(this.queueName)) {
            throw new Error(`Nenhum handler registrado para a fila ${this.queueName}`);
        }
        const handler = this.handlers.get(this.queueName);
        // Iniciar o consumer diretamente sem registrar um novo handler
        await this.consumerStart(this.queueName);
        logger.info(`Consumidor iniciado para a fila ${this.queueName}`);
    }
    validateEventType(eventType) {
        // Verifica se é um tipo de evento predefinido
        const predefinedEvents = Object.values(publisher_1.EventTypes);
        const normalizedEventType = eventType.toLowerCase();
        if (predefinedEvents.includes(normalizedEventType)) {
            return; // Evento predefinido válido
        }
        // Se não for um evento predefinido, permite eventos customizados
        logger.info(`Tipo de evento customizado registrado: ${eventType}`);
    }
    async processPendingMessages(eventType) {
        const redis = this.setup.getCommandClient();
        const queueKey = `queue:${eventType}:messages`;
        const processingKey = `queue:${eventType}:processing`;
        const failedKey = `queue:${eventType}:failed`;
        while (true) {
            const message = await redis.rpoplpush(queueKey, processingKey);
            if (!message)
                break;
            try {
                const data = JSON.parse(message);
                const handler = this.handlers.get(eventType);
                if (handler) {
                    // Atualiza status para processing
                    data.status = "processing";
                    data.attempts += 1;
                    await redis.lset(processingKey, -1, JSON.stringify(data));
                    logger.info(`Processando mensagem ${data.id} do tipo ${eventType}`, { messageId: data.id });
                    await handler(data.content, eventType);
                    await this.acknowledgeMessage(eventType, data.id);
                }
                else {
                    logger.warn(`⚠️ Nenhum handler encontrado para o tipo de evento: ${eventType}`);
                    // Retorna para a fila principal se não houver handler
                    await redis.rpoplpush(processingKey, queueKey);
                }
            }
            catch (error) {
                logger.error(`❌  Erro ao processar mensagem da fila ${queueKey}:`, error);
                // Atualiza status e tentativas
                const data = JSON.parse(message);
                data.status = "failed";
                if (data.attempts >= (data.options.attempts || 3)) {
                    // Move para fila de falhas se excedeu tentativas
                    logger.warn(`Mensagem ${data.id} excedeu o número máximo de tentativas e foi movida para fila de falhas`);
                    await redis.lrem(processingKey, 1, message);
                    await redis.lpush(failedKey, JSON.stringify(data));
                }
                else {
                    // Retorna para a fila principal para nova tentativa
                    logger.info(`Mensagem ${data.id} retornada para fila principal para nova tentativa (${data.attempts}/${data.options.attempts || 3})`);
                    await redis.rpoplpush(processingKey, queueKey);
                }
            }
        }
    }
    async acknowledgeMessage(eventType, messageId) {
        const redis = this.setup.getCommandClient();
        const processingKey = `queue:${eventType}:processing`;
        const completedKey = `queue:${eventType}:completed`;
        // Encontra e atualiza a mensagem
        const messages = await redis.lrange(processingKey, 0, -1);
        for (const message of messages) {
            const data = JSON.parse(message);
            if (data.id === messageId) {
                data.status = "completed";
                await redis.lrem(processingKey, 1, message);
                await redis.lpush(completedKey, JSON.stringify(data));
                logger.info(`Mensagem ${messageId} processada com sucesso`, { messageId });
                break;
            }
        }
    }
    async consumer(channel, callback) {
        this.validateEventType(channel);
        // Apenas registra o callback sem sobrescrever handlers existentes
        if (!this.handlers.has(channel)) {
            this.handlers.set(channel, callback);
            logger.info(`Consumidor registrado para o canal ${channel}`);
        }
        await this.consumerStart(channel);
    }
    /**
     * Método interno que inicia o consumo de mensagens
     */
    async consumerStart(channel) {
        const subscriberClient = this.setup.getSubscriberClient();
        // Subscribe to realtime channel
        const realTimeChannel = `pubsub:${channel}`;
        await subscriberClient.subscribe(realTimeChannel);
        logger.info(`Inscrito no canal de tempo real: ${realTimeChannel}`);
        // Subscribe to queue notifications
        const notificationChannel = `queue:${channel}:notifications`;
        await subscriberClient.subscribe(notificationChannel);
        logger.info(`Inscrito no canal de notificações: ${notificationChannel}`);
        // Process pending messages first
        logger.info(`Processando mensagens pendentes para ${channel}`);
        await this.processPendingMessages(channel);
        // Handle incoming messages
        subscriberClient.on("message", async (receivedChannel, message) => {
            try {
                if (receivedChannel === realTimeChannel) {
                    const data = JSON.parse(message);
                    logger.info(`Mensagem em tempo real recebida no canal ${receivedChannel}`, { messageId: data.id });
                    const handler = this.handlers.get(channel);
                    if (handler) {
                        await handler(data.data, channel);
                    }
                }
                else if (receivedChannel === notificationChannel) {
                    const notification = JSON.parse(message);
                    if (notification.type === "new_message") {
                        logger.info(`Notificação de nova mensagem recebida`, { messageId: notification.messageId });
                        await this.processPendingMessages(channel);
                    }
                }
            }
            catch (error) {
                logger.error(`❌ Erro ao processar mensagem do canal ${receivedChannel}:`, error);
            }
        });
    }
    getSubscriberClient() {
        return this.setup.getSubscriberClient();
    }
    getCommandClient() {
        return this.setup.getCommandClient();
    }
    async disconnect() {
        logger.info("Desconectando consumer");
        await this.setup.disconnect();
    }
}
exports.Consumer = Consumer;
