import { RedisOptions } from "ioredis";
export declare class Consumer {
    private setup;
    private handlers;
    private queueName;
    private concurrency;
    constructor(options: RedisOptions & {
        queueName: string;
        concurrency?: number;
    });
    /**
     * Registra um handler para processar mensagens da fila
     * @param handler <PERSON><PERSON> que processa a mensagem e retorna um booleano indicando sucesso
     */
    registerHandler(handler: (message: any, queueName: string) => Promise<boolean>): void;
    /**
     * Inicia o consumo de mensagens da fila
     */
    start(): Promise<void>;
    private validateEventType;
    private processPendingMessages;
    private acknowledgeMessage;
    consumer(channel: string, callback: (message: any) => Promise<void>): Promise<void>;
    /**
     * Método interno que inicia o consumo de mensagens
     */
    private consumerStart;
    getSubscriberClient(): import("ioredis").default;
    getCommandClient(): import("ioredis").default;
    disconnect(): Promise<void>;
}
