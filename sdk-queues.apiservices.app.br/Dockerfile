FROM node:21-alpine

WORKDIR /app

# Install build essentials
RUN apk add --no-cache python3 make g++

# Define build arguments
ARG PORT=4000
ARG HOST=0.0.0.0
ARG REDIS_HOST=localhost
ARG REDIS_PORT=6379
ARG REDIS_USERNAME=default
ARG REDIS_PASSWORD=redis123
ARG REDIS_DB=0
ARG QUEUE_NAME=default
ARG TZ="America/Sao_Paulo"
ARG PAT_TOKEN
ARG DB_HOST
ARG DB_PORT=3306
ARG DB_NAME
ARG DB_USER
ARG DB_PASSWORD
ARG OTEL_EXPORTER_OTLP_ENDPOINT
ARG OTEL_SERVICE_NAME
ARG OTEL_RESOURCE_ATTRIBUTES
ARG NODE_ENV=development
ARG LOG_LEVEL=info

# Copy package files first for better caching
COPY package.json pnpm-lock.yaml tsconfig.json ./

# Install pnpm and dependencies
RUN npm install -g pnpm typescript ts-node && \
    pnpm install

# Copy source code
COPY . .

# Build TypeScript
RUN pnpm run build

# Set environment variables from build args
ENV PORT=${PORT} \
    HOST=${HOST} \
    REDIS_HOST=${REDIS_HOST} \
    REDIS_PORT=${REDIS_PORT} \
    REDIS_USERNAME=${REDIS_USERNAME} \
    REDIS_PASSWORD=${REDIS_PASSWORD} \
    REDIS_DB=${REDIS_DB} \
    QUEUE_NAME=${QUEUE_NAME} \
    TZ=${TZ} \
    PAT_TOKEN=${PAT_TOKEN} \
    DB_HOST=${DB_HOST} \
    DB_PORT=${DB_PORT} \
    DB_NAME=${DB_NAME} \
    DB_USER=${DB_USER} \
    DB_PASSWORD=${DB_PASSWORD} \
    OTEL_EXPORTER_OTLP_ENDPOINT=${OTEL_EXPORTER_OTLP_ENDPOINT} \
    OTEL_SERVICE_NAME=${OTEL_SERVICE_NAME} \
    OTEL_RESOURCE_ATTRIBUTES=${OTEL_RESOURCE_ATTRIBUTES} \
    NODE_ENV=${NODE_ENV} \
    LOG_LEVEL=${LOG_LEVEL}

# Expose port
EXPOSE ${PORT}

# Start the server
CMD ["pnpm", "start"]