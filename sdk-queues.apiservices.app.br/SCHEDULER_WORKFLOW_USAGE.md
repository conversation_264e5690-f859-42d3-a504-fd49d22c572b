# Guia de Uso - Scheduler e Workflow com MySQL

Este guia mostra como você pode usar as funcionalidades de Scheduler e Workflow com armazenamento MySQL em seus aplicativos, de forma semelhante ao trigger.dev.

## Configuração

Primeiro, configure o armazenamento MySQL:

```typescript
import { Scheduler, Workflow, MySQLStorageFactory } from "sdk-queues";

// Configuração do MySQL
const mysqlConfig = {
  host: process.env.MYSQL_HOST || "localhost",
  port: parseInt(process.env.MYSQL_PORT || "3306"),
  user: process.env.MYSQL_USER || "root",
  password: process.env.MYSQL_PASSWORD || "",
  database: process.env.MYSQL_DATABASE || "queues",
};

// Cria instâncias do scheduler e workflow
const scheduler = new Scheduler({
  storage: MySQLStorageFactory.createSchedulerStorage(mysqlConfig),
});

const workflow = new Workflow({
  storage: MySQLStorageFactory.createWorkflowStorage(mysqlConfig),
});
```

## Exemplo 1: Tarefas CRON

Similar ao exemplo CRON do trigger.dev, você pode definir tarefas agendadas:

```typescript
// Definir uma tarefa CRON para sincronizar com Airtable
await scheduler.define({
  id: "sync-airtable",
  cron: "0 */1 * * *", // A cada hora
  handler: async ({ timestamp, lastTimestamp }) => {
    console.log(`Executando sincronização Airtable: ${new Date().toISOString()}`);
    console.log(`Timestamp: ${timestamp}, Último timestamp: ${lastTimestamp}`);

    // Sua lógica para consumir a API do Airtable
    // await airtable<StoreItems>("Store items")...

    return { success: true, syncedItems: 10 };
  },
});

// Iniciar o scheduler
await scheduler.start();
```

## Exemplo 2: Workflows com Passos Sequenciais

Similar ao exemplo WORKFLOW do trigger.dev:

```typescript
// Definir um workflow com passos sequenciais
await workflow.define({
  id: "onboarding-workflow",
  handler: async ({ id, steps, data }) => {
    // Dados recebidos ao executar o workflow
    const { userId, email } = data;

    for (const step of steps) {
      switch (step.type) {
        case "wait":
          console.log(`Esperando ${step.seconds} segundos...`);
          await new Promise((resolve) => setTimeout(resolve, step.seconds * 1000));
          break;

        case "email":
          console.log(`Enviando email para ${email}`);
          // Lógica de envio de emails
          break;

        case "conditional":
          const shouldContinue = await evaluateCondition(step.condition, data);
          if (!shouldContinue) {
            return {
              id,
              status: "canceled",
              reason: "condition-not-met",
              condition: step.condition,
            };
          }
          break;
      }
    }

    return { id, status: "completed" };
  },
});

// Iniciar o workflow manager
await workflow.start();

// Executar um workflow
const result = await workflow.execute("onboarding-workflow", {
  userId: "user-123",
  email: "<EMAIL>",
  steps: [
    { type: "wait", seconds: 30 },
    { type: "email", template: "welcome" },
    { type: "conditional", condition: "status === 'active'" },
  ],
});
```

## Exemplo 3: Tarefas com Retry

Similar ao exemplo RETRY do trigger.dev:

```typescript
// Função utilitária para retry
async function retry<T>(fn: () => Promise<T>, options = { maxAttempts: 3, delay: 1000 }): Promise<T> {
  let attempt = 0;

  while (true) {
    try {
      return await fn();
    } catch (error) {
      attempt++;
      console.error(`Tentativa ${attempt} falhou:`, error);

      if (attempt >= options.maxAttempts) {
        throw error;
      }

      await new Promise((resolve) => setTimeout(resolve, options.delay));
    }
  }
}

// Definir uma tarefa com retry
await scheduler.define({
  id: "task-with-retry",
  handler: async (task) => {
    return await retry(
      async () => {
        // Lógica que pode falhar
        return { success: true };
      },
      { maxAttempts: 5, delay: 2000 }
    );
  },
});
```

## Exemplo 4: Agents

Similar ao exemplo AGENTS do trigger.dev:

```typescript
await workflow.define({
  id: "agent-workflow",
  handler: async ({ objective, url }) => {
    const MAX_ACTIONS = 5;
    let input = { objective, url };

    for (let i = 0; i < MAX_ACTIONS; i++) {
      // Executar uma ação do agente
      try {
        const actionResult = await executeAgentAction(input);

        if (actionResult.success) {
          return actionResult.result;
        }

        // Continuar com a próxima ação
        input = actionResult.next;
      } catch (error) {
        console.error(`Ação falhou:`, error);
        throw new Error(`Ação falhou: ${error.message}`);
      }
    }

    throw new Error("Agente esgotou o número máximo de ações");
  },
});

// Executar o agente
const result = await workflow.execute("agent-workflow", {
  objective: "Extrair informações do site",
  url: "https://exemplo.com",
});
```

## Usando o MySQL para Persistência

Todas as configurações e dados dos seus jobs e workflows são automaticamente salvos no MySQL, permitindo:

1. Persistência entre reinicializações do sistema
2. Compartilhamento de configurações entre diferentes instâncias
3. Rastreamento do histórico de execuções

## Manipulação Direta do Storage (Avançado)

Você também pode interagir diretamente com o armazenamento:

```typescript
// Verificar se uma tarefa existe
const exists = await scheduler.storage.taskExists("sync-airtable");

// Obter todas as tarefas definidas
const allTasks = await scheduler.storage.listTasks();

// Remover uma tarefa
await scheduler.storage.removeTask("old-task");

// Salvar/atualizar uma tarefa manualmente
await scheduler.storage.saveTask("manual-task", {
  id: "manual-task",
  cron: "0 0 * * *",
  data: { description: "Tarefa manual" },
});
```

## Desconexão Adequada

Sempre feche as conexões quando não forem mais necessárias:

```typescript
// Desconectar o scheduler e workflow
await scheduler.disconnect();
await workflow.disconnect();
```
