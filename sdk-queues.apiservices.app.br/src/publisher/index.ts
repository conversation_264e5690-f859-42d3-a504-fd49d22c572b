import { SetupQueueManager } from "../setup";
import { RedisOptions } from "ioredis";
import { createModuleLogger } from "../infra/monitoring";

// Logger para o módulo publisher
const logger = createModuleLogger("publisher");

// Tipos de eventos suportados
export const EventTypes = {
  EMAIL: "email",
  NOTIFICATION: "notification",
  PAYMENT: "payment",
  ORDER: "order",
} as const;

export type EventType = (typeof EventTypes)[keyof typeof EventTypes] | string;

export interface PublishOptions {
  persistent?: boolean;
  priority?: number;
  delay?: number;
  attempts?: number;
}

interface QueueMessage {
  id: string;
  timestamp: string;
  content: any;
  options: PublishOptions;
  status: "pending" | "processing" | "completed" | "failed";
  attempts: number;
}

export class Publisher {
  private setup: SetupQueueManager;

  constructor(options?: RedisOptions) {
    // Nova abordagem para configurar as credenciais
    const redisOptions = { ...options };
    const redisUsername = redisOptions.username || process.env.REDIS_USERNAME || "default";
    const redisPassword = redisOptions.password || process.env.REDIS_PASSWORD || "";

    // Remove as propriedades existentes
    delete redisOptions.username;
    delete redisOptions.password;

    // Adiciona as credenciais apenas se não forem valores padrão/vazios
    if (redisUsername !== "default") {
      redisOptions.username = redisUsername;
    }

    if (redisPassword) {
      redisOptions.password = redisPassword;
    }

    this.setup = new SetupQueueManager(redisOptions);
    logger.info("Publisher inicializado");
  }

  private validateEventType(eventType: string): void {
    // Se não for informado um tipo de evento, usa a fila padrão
    if (!eventType) {
      return;
    }

    // Verifica se é um tipo de evento predefinido
    const predefinedEvents = Object.values(EventTypes) as string[];
    const normalizedEventType = eventType.toLowerCase();

    if (predefinedEvents.includes(normalizedEventType)) {
      return; // Evento predefinido válido
    }

    logger.info(`Tipo de evento customizado: ${eventType}`);
  }

  private async publishToPersistentQueue(channel: string, message: any, options: PublishOptions): Promise<number> {
    const redis = this.setup.getCommandClient();
    const queueKey = `queue:${channel}:messages`;
    const messageId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const messageData: QueueMessage = {
      id: messageId,
      timestamp: new Date().toISOString(),
      content: message,
      options,
      status: "pending",
      attempts: 0,
    };

    // Adiciona à fila persistente com base na prioridade
    let result: number;
    if (options.priority && options.priority > 1) {
      // Mensagens de alta prioridade vão para o início da fila
      result = await redis.rpush(queueKey, JSON.stringify(messageData));
      logger.info(`Mensagem ${messageId} adicionada ao início da fila ${queueKey} com prioridade ${options.priority}`, { messageId });
    } else {
      // Mensagens normais vão para o final da fila
      result = await redis.lpush(queueKey, JSON.stringify(messageData));
      logger.info(`Mensagem ${messageId} adicionada ao final da fila ${queueKey}`, { messageId });
    }

    // Notifica sobre nova mensagem
    const pubSubClient = this.setup.getSubscriberClient();
    await pubSubClient.publish(
      `queue:${channel}:notifications`,
      JSON.stringify({
        type: "new_message",
        messageId,
        priority: options.priority,
      })
    );

    logger.info(`Notificação enviada para o canal queue:${channel}:notifications`, { messageId });

    return result;
  }

  private async publishToRealtimeChannel(channel: string, message: any, options: PublishOptions): Promise<number> {
    const messageData = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      data: message,
      options,
    };

    const redis = this.setup.getSubscriberClient();
    const result = await redis.publish(`pubsub:${channel}`, JSON.stringify(messageData));

    logger.info(`Mensagem publicada em tempo real no canal pubsub:${channel}`, { messageId: messageData.id });

    return result;
  }

  async publish(channel: string = "default", message: any, options: PublishOptions = { persistent: true }): Promise<number> {
    this.validateEventType(channel);

    const { persistent = true, priority = 1, attempts = 3 } = options;
    const publishOptions = { ...options, priority, attempts };

    logger.info(`Publicando no canal ${channel} (${persistent ? "persistente" : "tempo real"})`, {
      channel,
      persistent,
      priority,
      attempts,
    });

    if (persistent) {
      return this.publishToPersistentQueue(channel, message, publishOptions);
    } else {
      return this.publishToRealtimeChannel(channel, message, publishOptions);
    }
  }

  async disconnect(): Promise<void> {
    logger.info("Desconectando publisher");
    await this.setup.disconnect();
  }
}
