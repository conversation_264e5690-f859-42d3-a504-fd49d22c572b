import { Consumer } from "../consumer";
import { Publisher } from "../publisher";
import { SetupQueueManager } from "./index";
import Redis from "ioredis";
import { PublishOptions } from "../publisher";
import { createModuleLogger } from "../infra/monitoring";

// Logger para o módulo queue-manager
const logger = createModuleLogger("queue-manager");

interface QueueMessage {
  id: string;
  timestamp: string;
  content: any;
  options: PublishOptions;
  status: "pending" | "processing" | "completed" | "failed";
  attempts: number;
}

export class QueueManager {
  private publisher: Publisher;
  private subscriber: Consumer;
  private setup: SetupQueueManager;
  private redis: Redis;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(options?: any) {
    this.setup = new SetupQueueManager(options);
    this.publisher = new Publisher(options);
    this.subscriber = new Consumer(options);
    this.redis = this.setup.getCommandClient();
    logger.info("QueueManager inicializado");
    this.startCleanupJob();
  }

  async publish(channel: string, message: any, options: PublishOptions = { persistent: true }): Promise<number> {
    const defaultOptions: PublishOptions = {
      persistent: true,
      ...options,
    };
    return await this.publisher.publish(channel, message, defaultOptions);
  }

  async consumer(channel: string, callback: (message: any) => Promise<void>): Promise<void> {
    return this.subscriber.consumer(channel, callback);
  }

  getSubscriberClient(): Redis {
    return this.subscriber.getSubscriberClient();
  }

  getCommandClient(): Redis {
    return this.redis;
  }

  async getQueueStatus() {
    const redis = this.getCommandClient();
    const queues: Record<string, any> = {};

    // Busca todas as filas
    const keys = await redis.keys("queue:*");
    const queueNames = [...new Set(keys.map((k) => k.split(":")[1]))];

    for (const queue of queueNames) {
      const waiting = await redis.llen(`queue:${queue}:messages`);
      const processing = await redis.llen(`queue:${queue}:processing`);
      const completed = await redis.llen(`queue:${queue}:completed`);
      const failed = await redis.llen(`queue:${queue}:failed`);

      queues[queue] = {
        waiting,
        processing,
        completed,
        failed,
        total: waiting + processing + completed + failed,
      };
    }

    return queues;
  }

  async getPendingMessages(queueName: string): Promise<QueueMessage[]> {
    const redis = this.getCommandClient();
    const queueKey = `queue:${queueName}:messages`;
    const messages = await redis.lrange(queueKey, 0, -1);

    return messages.map((message) => {
      const data = JSON.parse(message) as QueueMessage;
      return {
        ...data,
        status: "pending",
      };
    });
  }

  async getCompletedMessages(queueName: string): Promise<QueueMessage[]> {
    const redis = this.getCommandClient();
    const completedKey = `queue:${queueName}:completed`;
    const messages = await redis.lrange(completedKey, 0, -1);

    return messages.map((message) => {
      const data = JSON.parse(message) as QueueMessage;
      return {
        ...data,
        status: "completed",
      };
    });
  }

  async getQueueMessages(
    queueName: string,
    status: "pending" | "completed" | "all" = "all"
  ): Promise<{
    pending?: QueueMessage[];
    completed?: QueueMessage[];
  }> {
    const result: { pending?: QueueMessage[]; completed?: QueueMessage[] } = {};

    if (status === "pending" || status === "all") {
      result.pending = await this.getPendingMessages(queueName);
    }

    if (status === "completed" || status === "all") {
      result.completed = await this.getCompletedMessages(queueName);
    }

    return result;
  }

  async disconnect(): Promise<void> {
    await this.publisher.disconnect();
    await this.subscriber.disconnect();
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    logger.info("QueueManager desconectado");
  }

  /**
   * Limpa todas as mensagens pendentes de todas as filas
   * @returns O número de mensagens removidas por fila
   */
  async clearAllPendingMessages(): Promise<Record<string, number>> {
    const redis = this.getCommandClient();
    const result: Record<string, number> = {};

    const status = await this.getQueueStatus();
    const queueNames = Object.keys(status);

    for (const queueName of queueNames) {
      const messagesKey = `queue:${queueName}:messages`;
      const messagesCount = await redis.llen(messagesKey);
      if (messagesCount > 0) {
        await redis.del(messagesKey);
        result[queueName] = messagesCount;
      } else {
        result[queueName] = 0;
      }

      const processingKey = `queue:${queueName}:processing`;
      const processingCount = await redis.llen(processingKey);
      if (processingCount > 0) {
        await redis.del(processingKey);
        result[`${queueName}_processing`] = processingCount;
      }

      const failedKey = `queue:${queueName}:failed`;
      const failedCount = await redis.llen(failedKey);
      if (failedCount > 0) {
        await redis.del(failedKey);
        result[`${queueName}_failed`] = failedCount;
      }
    }

    return result;
  }

  /**
   * Reenviar mensagens pendentes de uma fila específica
   * @param queueName Nome da fila
   * @param limit Número máximo de mensagens para reenviar
   * @param transform Função para transformar as mensagens antes de reenviar
   * @returns O número de mensagens reenviadas
   */
  async resendPendingMessages(queueName: string, limit?: number, transform?: (message: any) => any): Promise<{ reenviadas: number; erros: number }> {
    const redis = this.getCommandClient();
    const messagesKey = `queue:${queueName}:messages`;
    const failedKey = `queue:${queueName}:failed`;

    let messages: string[] = [];

    if (limit && limit > 0) {
      messages = await redis.lrange(messagesKey, 0, limit - 1);
    } else {
      messages = await redis.lrange(messagesKey, 0, -1);
    }

    const failedMessages = await redis.lrange(failedKey, 0, -1);
    messages = [...messages, ...failedMessages];

    let reenviadas = 0;
    let erros = 0;

    for (const messageStr of messages) {
      try {
        const message = JSON.parse(messageStr) as QueueMessage;
        let conteudo = message.content;

        if (transform && typeof transform === "function") {
          conteudo = transform(conteudo);
        }

        await this.publish(queueName, conteudo, message.options);
        reenviadas++;
      } catch (error) {
        console.error(`Erro ao reenviar mensagem para a fila ${queueName}:`, error);
        erros++;
      }
    }

    return { reenviadas, erros };
  }

  /**
   * Exclui uma fila específica, removendo todas as suas mensagens
   * @param queueName Nome da fila a ser excluída
   * @returns Informações sobre a exclusão da fila
   */
  async deleteQueue(queueName: string): Promise<{
    success: boolean;
    deleted: Record<string, number>;
  }> {
    const redis = this.getCommandClient();
    const deleted: Record<string, number> = {};

    const messagesKey = `queue:${queueName}:messages`;
    const processingKey = `queue:${queueName}:processing`;
    const completedKey = `queue:${queueName}:completed`;
    const failedKey = `queue:${queueName}:failed`;

    const messagesCount = await redis.llen(messagesKey);
    const processingCount = await redis.llen(processingKey);
    const completedCount = await redis.llen(completedKey);
    const failedCount = await redis.llen(failedKey);

    if (messagesCount > 0) {
      await redis.del(messagesKey);
      deleted.pending = messagesCount;
    }

    if (processingCount > 0) {
      await redis.del(processingKey);
      deleted.processing = processingCount;
    }

    if (completedCount > 0) {
      await redis.del(completedKey);
      deleted.completed = completedCount;
    }

    if (failedCount > 0) {
      await redis.del(failedKey);
      deleted.failed = failedCount;
    }

    const total = messagesCount + processingCount + completedCount + failedCount;
    deleted.total = total;

    return {
      success: true,
      deleted,
    };
  }

  /**
   * Inicia o job de limpeza de mensagens antigas
   */
  private startCleanupJob(): void {
    const cleanupIntervalMs = 24 * 60 * 60 * 1000; // 24 horas

    // Executa a limpeza a cada 24 horas
    this.cleanupInterval = setInterval(async () => {
      try {
        logger.info("Iniciando limpeza de mensagens completadas antigas");
        const keys = await this.redis.keys("queue:*:completed");

        for (const key of keys) {
          const messages = await this.redis.lrange(key, 0, -1);
          const now = Date.now();
          let removedCount = 0;

          for (const message of messages) {
            try {
              const data = JSON.parse(message);
              // Remove mensagens mais antigas que 24 horas
              if (now - new Date(data.timestamp).getTime() > 24 * 60 * 60 * 1000) {
                await this.redis.lrem(key, 1, message);
                removedCount++;
              }
            } catch (error) {
              logger.error("Erro ao processar mensagem para limpeza:", error);
            }
          }

          if (removedCount > 0) {
            logger.info(`Removidas ${removedCount} mensagens antigas da fila ${key}`);
          }
        }
        logger.info("Limpeza de mensagens completadas finalizada");
      } catch (error) {
        logger.error("Erro durante a limpeza de mensagens:", error);
      }
    }, cleanupIntervalMs);

    // Não bloqueia o processo Node.js
    this.cleanupInterval.unref();

    logger.info(`Job de limpeza agendado para executar a cada ${cleanupIntervalMs / 1000 / 60 / 60} horas`);
  }
}
