import Redis, { RedisOptions } from "ioredis";
import { QueueManager } from "./manager";
import { createModuleLogger } from "../infra/monitoring";

// Logger para o módulo setup
const logger = createModuleLogger("setup");

/**
 * Classe que configura conexões Redis para o sistema de filas
 */
export class SetupQueueManager {
  private commandClient: Redis;
  private subscriberClient: Redis;

  /**
   * Inicializa conexões Redis para comandos e subscriber
   * @param options Opções de conexão Redis
   */
  constructor(options?: RedisOptions) {
    const redisUsername = process.env.REDIS_USERNAME || "default";
    const redisPassword = process.env.REDIS_PASSWORD || "";

    // Configuração básica
    const defaultOptions: RedisOptions = {
      host: process.env.REDIS_HOST || "localhost",
      port: Number(process.env.REDIS_PORT) || 6379,
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        logger.info(`Tentativa de reconexão ${times} em ${delay}ms`);
        return delay;
      },
      reconnectOnError: (err) => {
        const targetError = "READONLY";
        if (err.message.includes(targetError)) {
          logger.info(`Reconexão automática devido ao erro: ${err.message}`);
          return true;
        }
        return false;
      },
      enableReadyCheck: true,
      maxRetriesPerRequest: null,
    };

    // Adicionar credenciais apenas se não for "default" ou vazio
    if (redisUsername !== "default") {
      defaultOptions.username = redisUsername;
    }

    if (redisPassword) {
      defaultOptions.password = redisPassword;
    }

    const mergedOptions = { ...defaultOptions, ...options };

    logger.info(`Conectando ao Redis ${mergedOptions.host}:${mergedOptions.port}`);
    logger.info(
      `Configuração Redis: ${JSON.stringify({
        host: mergedOptions.host,
        port: mergedOptions.port,
        username: mergedOptions.username || "<não definido>",
        password: mergedOptions.password ? "******" : "<não definido>",
      })}`
    );

    // Cliente para comandos (publicação e manipulação de listas)
    this.commandClient = new Redis(mergedOptions);

    // Cliente dedicado para subscrição (não pode ser usado para outros comandos)
    this.subscriberClient = new Redis(mergedOptions);

    // Registro de eventos
    this.setupEventHandlers(this.commandClient, "command");
    this.setupEventHandlers(this.subscriberClient, "subscriber");
  }

  /**
   * Configura handlers de eventos para clientes Redis
   * @param client Cliente Redis
   * @param type Tipo de cliente (command ou subscriber)
   */
  private setupEventHandlers(client: Redis, type: string): void {
    client.on("connect", () => {
      logger.info(`Cliente Redis (${type}) conectado`);
    });

    client.on("ready", () => {
      logger.info(`Cliente Redis (${type}) pronto para uso`);
    });

    client.on("error", (err) => {
      logger.error(`Erro no cliente Redis (${type}):`, err);
    });

    client.on("close", () => {
      logger.warn(`Conexão Redis (${type}) fechada`);
    });

    client.on("reconnecting", (delay) => {
      logger.info(`Reconectando ao Redis (${type}) em ${delay}ms`);
    });

    client.on("end", () => {
      logger.info(`Cliente Redis (${type}) finalizado`);
    });
  }

  /**
   * Retorna o cliente Redis para comandos
   */
  getCommandClient(): Redis {
    return this.commandClient;
  }

  /**
   * Retorna o cliente Redis para subscrição
   */
  getSubscriberClient(): Redis {
    return this.subscriberClient;
  }

  /**
   * Cria uma instância do QueueManager
   */
  createQueueManager(): QueueManager {
    return new QueueManager(this);
  }

  /**
   * Desconecta os clientes Redis
   */
  async disconnect(): Promise<void> {
    logger.info("Desconectando clientes Redis");

    try {
      // Verificar se as conexões ainda estão ativas antes de desconectar
      const commands = this.commandClient.status === "ready" ? this.commandClient.quit() : Promise.resolve();
      const subscriber = this.subscriberClient.status === "ready" ? this.subscriberClient.quit() : Promise.resolve();

      await Promise.all([commands, subscriber]);
      logger.info("Clientes Redis desconectados com sucesso");
    } catch (error) {
      logger.error("Erro ao desconectar clientes Redis:", error);
    }
  }
}
