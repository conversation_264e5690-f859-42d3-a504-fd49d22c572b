import { SchedulerStorage, WorkflowStorage } from "./interfaces";
import Redis from "ioredis";
import { RedisOptions } from "ioredis";
import { createModuleLogger } from "../infra/monitoring";

// Logger para o módulo redis-storage
const logger = createModuleLogger("redis-storage");

/**
 * Implementação de armazenamento Redis para o Scheduler
 */
export class RedisSchedulerStorage implements SchedulerStorage {
  private redis: Redis;

  constructor(redis: Redis) {
    this.redis = redis;
    logger.info("RedisSchedulerStorage inicializado");
  }

  async saveTask(taskId: string, taskData: any): Promise<void> {
    const taskKey = `scheduler:tasks:${taskId}`;
    await this.redis.set(taskKey, JSON.stringify(taskData));
    await this.redis.sadd("scheduler:taskids", taskId);
    logger.info(`Tarefa ${taskId} salva no Redis`);
  }

  async getTask(taskId: string): Promise<any | null> {
    const taskKey = `scheduler:tasks:${taskId}`;
    const data = await this.redis.get(taskKey);

    if (!data) {
      logger.info(`Tarefa ${taskId} não encontrada`);
      return null;
    }

    logger.info(`Tarefa ${taskId} recuperada do Redis`);
    return JSON.parse(data);
  }

  async listTasks(): Promise<any[]> {
    const taskIds = await this.redis.smembers("scheduler:taskids");
    const tasks: any[] = [];

    logger.info(`Listando ${taskIds.length} tarefas do Redis`);

    for (const taskId of taskIds) {
      const task = await this.getTask(taskId);
      if (task) {
        tasks.push(task);
      }
    }

    return tasks;
  }

  async removeTask(taskId: string): Promise<boolean> {
    const taskKey = `scheduler:tasks:${taskId}`;
    const exists = await this.redis.exists(taskKey);

    if (!exists) {
      logger.info(`Tentativa de remover tarefa ${taskId} inexistente`);
      return false;
    }

    await this.redis.del(taskKey);
    await this.redis.srem("scheduler:taskids", taskId);
    logger.info(`Tarefa ${taskId} removida do Redis`);

    return true;
  }

  async taskExists(taskId: string): Promise<boolean> {
    const taskKey = `scheduler:tasks:${taskId}`;
    const exists = await this.redis.exists(taskKey);
    logger.debug(`Verificando existência da tarefa ${taskId}: ${exists === 1 ? "existe" : "não existe"}`);
    return exists === 1;
  }
}

/**
 * Implementação de armazenamento Redis para o Workflow
 */
export class RedisWorkflowStorage implements WorkflowStorage {
  private redis: Redis;

  constructor(redis: Redis) {
    this.redis = redis;
    logger.info("RedisWorkflowStorage inicializado");
  }

  async saveWorkflow(workflowId: string, workflowData: any): Promise<void> {
    const workflowKey = `workflow:${workflowId}`;
    await this.redis.set(workflowKey, JSON.stringify(workflowData));
    logger.info(`Workflow ${workflowId} salvo no Redis`);
  }

  async getWorkflow(workflowId: string): Promise<any | null> {
    const workflowKey = `workflow:${workflowId}`;
    const data = await this.redis.get(workflowKey);

    if (!data) {
      logger.info(`Workflow ${workflowId} não encontrado`);
      return null;
    }

    logger.info(`Workflow ${workflowId} recuperado do Redis`);
    return JSON.parse(data);
  }

  async listWorkflows(): Promise<any[]> {
    const keys = await this.redis.keys("workflow:*");
    const workflows: any[] = [];

    logger.info(`Listando ${keys.length} workflows do Redis`);

    for (const key of keys) {
      const workflowId = key.replace("workflow:", "");
      const workflow = await this.getWorkflow(workflowId);
      if (workflow) {
        workflows.push(workflow);
      }
    }

    return workflows;
  }

  async removeWorkflow(workflowId: string): Promise<boolean> {
    const workflowKey = `workflow:${workflowId}`;
    const exists = await this.redis.exists(workflowKey);

    if (!exists) {
      logger.info(`Tentativa de remover workflow ${workflowId} inexistente`);
      return false;
    }

    await this.redis.del(workflowKey);
    logger.info(`Workflow ${workflowId} removido do Redis`);
    return true;
  }

  async workflowExists(workflowId: string): Promise<boolean> {
    const workflowKey = `workflow:${workflowId}`;
    const exists = await this.redis.exists(workflowKey);
    logger.debug(`Verificando existência do workflow ${workflowId}: ${exists === 1 ? "existe" : "não existe"}`);
    return exists === 1;
  }
}

/**
 * Factory para criar instâncias de storage Redis
 */
export class RedisStorageFactory {
  static createSchedulerStorage(redisOptions: RedisOptions): RedisSchedulerStorage {
    logger.info("Criando RedisSchedulerStorage via factory");

    // Ajuste das credenciais para compatibilidade
    if (redisOptions.username === "default") {
      redisOptions.username = undefined;
    }

    if (redisOptions.password === "") {
      redisOptions.password = undefined;
    }

    const redis = new Redis(redisOptions);
    return new RedisSchedulerStorage(redis);
  }

  static createWorkflowStorage(redisOptions: RedisOptions): RedisWorkflowStorage {
    logger.info("Criando RedisWorkflowStorage via factory");

    // Ajuste das credenciais para compatibilidade
    if (redisOptions.username === "default") {
      redisOptions.username = undefined;
    }

    if (redisOptions.password === "") {
      redisOptions.password = undefined;
    }

    const redis = new Redis(redisOptions);
    return new RedisWorkflowStorage(redis);
  }
}
