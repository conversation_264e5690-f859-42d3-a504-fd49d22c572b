import { SchedulerStorage, WorkflowStorage } from "./interfaces";
import * as mysql from "mysql2/promise";
import { createModuleLogger } from "../infra/monitoring";

// Logger para o módulo mysql-storage
const logger = createModuleLogger("mysql-storage");

/**
 * Configuração de conexão MySQL
 */
export interface MySQLConfig {
  host: string;
  port?: number;
  user: string;
  password: string;
  database: string;
  connectionLimit?: number;
}

/**
 * Tipo de log para registros no sistema
 */
export enum LogType {
  INFO = "info",
  WARNING = "warning",
  ERROR = "error",
  SUCCESS = "success",
}

/**
 * Interface para logs do sistema
 */
export interface SystemLog {
  id?: string;
  type: LogType;
  source: string;
  action: string;
  details?: any;
  status: "success" | "error" | "pending";
  createdAt?: Date;
}

/**
 * Parseia um campo JSON de forma segura
 * @param data Dados a serem parseados
 */
function safeParseJSON(data: any): any {
  if (!data) return null;

  // Se já é um objeto, retorna diretamente
  if (typeof data === "object") return data;

  try {
    return JSON.parse(data);
  } catch (e) {
    logger.error("Erro ao parsear JSON:", e);
    return data; // Retorna os dados originais se não conseguir parsear
  }
}

/**
 * Classe base para armazenamento MySQL com funcionalidades comuns
 */
export class MySQLBaseStorage {
  protected pool: mysql.Pool;

  constructor(config: MySQLConfig) {
    this.pool = mysql.createPool({
      host: config.host,
      port: config.port || 3306,
      user: config.user,
      password: config.password,
      database: config.database,
      connectionLimit: config.connectionLimit || 10,
      waitForConnections: true,
    });

    logger.info(`Conexão MySQL inicializada: ${config.host}:${config.port || 3306}/${config.database}`);
    this.initializeLogsTable();
  }

  /**
   * Inicializa a tabela de logs do sistema
   */
  private async initializeLogsTable(): Promise<void> {
    try {
      await this.pool.execute(`
        CREATE TABLE IF NOT EXISTS system_logs (
          id VARCHAR(36) PRIMARY KEY,
          type ENUM('info', 'warning', 'error', 'success') NOT NULL,
          source VARCHAR(255) NOT NULL,
          action VARCHAR(255) NOT NULL,
          details JSON,
          status ENUM('success', 'error', 'pending') NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      logger.info("Tabela de logs do sistema inicializada");
    } catch (error) {
      logger.error("Erro ao inicializar tabela de logs:", error);
      throw error;
    }
  }

  /**
   * Registra um log no sistema
   * @param log Dados do log
   */
  async logActivity(log: SystemLog): Promise<string> {
    try {
      // Gera um ID único se não for fornecido
      const logId = log.id || `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      await this.pool.execute("INSERT INTO system_logs (id, type, source, action, details, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())", [
        logId,
        log.type,
        log.source,
        log.action,
        log.details ? JSON.stringify(log.details) : null,
        log.status,
      ]);

      logger.debug(`Log registrado: [${log.type}] ${log.source} - ${log.action}`, { logId });
      return logId;
    } catch (error) {
      logger.error("Erro ao registrar log:", error);
      throw error;
    }
  }

  /**
   * Obtém logs do sistema com filtros opcionais
   * @param filters Filtros para os logs (opcional)
   * @param limit Limite de registros a retornar (opcional)
   * @param offset Deslocamento para paginação (opcional)
   */
  async getLogs(
    filters?: {
      type?: LogType;
      source?: string;
      action?: string;
      status?: "success" | "error" | "pending";
      startDate?: Date;
      endDate?: Date;
    },
    limit: number = 100,
    offset: number = 0
  ): Promise<SystemLog[]> {
    try {
      const conditions: string[] = [];
      const params: any[] = [];

      // Adiciona filtros se fornecidos
      if (filters) {
        if (filters.type) {
          conditions.push("type = ?");
          params.push(filters.type);
        }

        if (filters.source) {
          conditions.push("source = ?");
          params.push(filters.source);
        }

        if (filters.action) {
          conditions.push("action = ?");
          params.push(filters.action);
        }

        if (filters.status) {
          conditions.push("status = ?");
          params.push(filters.status);
        }

        if (filters.startDate) {
          conditions.push("created_at >= ?");
          params.push(filters.startDate);
        }

        if (filters.endDate) {
          conditions.push("created_at <= ?");
          params.push(filters.endDate);
        }
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

      const [rows] = await this.pool.execute(`SELECT * FROM system_logs ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`, [
        ...params,
        limit,
        offset,
      ]);

      const rowsArray = rows as any[];
      return rowsArray.map((row) => ({
        id: row.id,
        type: row.type as LogType,
        source: row.source,
        action: row.action,
        details: safeParseJSON(row.details),
        status: row.status as "success" | "error" | "pending",
        createdAt: row.created_at,
      }));
    } catch (error) {
      logger.error("Erro ao obter logs:", error);
      throw error;
    }
  }

  /**
   * Fecha a conexão com o banco de dados
   */
  async close(): Promise<void> {
    try {
      await this.pool.end();
      logger.info("Conexão MySQL fechada");
    } catch (error) {
      logger.error("Erro ao fechar conexão MySQL:", error);
      throw error;
    }
  }
}

/**
 * Implementação de armazenamento MySQL para o Scheduler
 */
export class MySQLSchedulerStorage extends MySQLBaseStorage implements SchedulerStorage {
  constructor(config: MySQLConfig) {
    super(config);
    this.initializeTables();
    logger.info("MySQLSchedulerStorage inicializado");
  }

  /**
   * Inicializa as tabelas necessárias
   */
  private async initializeTables(): Promise<void> {
    try {
      await this.pool.execute(`
        CREATE TABLE IF NOT EXISTS scheduler_tasks (
          id VARCHAR(36) PRIMARY KEY,
          data JSON NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
      logger.info("Tabela de tarefas do scheduler inicializada");
    } catch (error) {
      logger.error("Erro ao inicializar tabelas:", error);
      throw error;
    }
  }

  async saveTask(taskId: string, taskData: any): Promise<void> {
    try {
      // Verifica se a tarefa já existe
      const exists = await this.taskExists(taskId);
      const jsonData = JSON.stringify(taskData);

      if (exists) {
        // Atualiza a tarefa existente
        await this.pool.execute("UPDATE scheduler_tasks SET data = ?, updated_at = NOW() WHERE id = ?", [jsonData, taskId]);
        logger.info(`Tarefa ${taskId} atualizada no MySQL`);
      } else {
        // Insere uma nova tarefa
        await this.pool.execute("INSERT INTO scheduler_tasks (id, data) VALUES (?, ?)", [taskId, jsonData]);
        logger.info(`Tarefa ${taskId} criada no MySQL`);
      }

      // Registra o log de atividade
      await this.logActivity({
        type: LogType.INFO,
        source: "scheduler",
        action: exists ? "update_task" : "create_task",
        details: { taskId, channel: taskData.channel },
        status: "success",
      });
    } catch (error) {
      logger.error(`Erro ao salvar tarefa ${taskId}:`, error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "scheduler",
        action: "save_task",
        details: { taskId, error: error.message },
        status: "error",
      });

      throw error;
    }
  }

  async getTask(taskId: string): Promise<any | null> {
    try {
      const [rows] = await this.pool.execute("SELECT data FROM scheduler_tasks WHERE id = ?", [taskId]);

      const rowsArray = rows as any[];

      if (rowsArray.length === 0) {
        return null;
      }

      const result = safeParseJSON(rowsArray[0].data);

      // Registra o log
      await this.logActivity({
        type: LogType.INFO,
        source: "scheduler",
        action: "get_task",
        details: {
          taskId,
          found: true,
        },
        status: "success",
      });

      return result;
    } catch (error) {
      logger.error(`Erro ao obter tarefa ${taskId}:`, error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "scheduler",
        action: "get_task",
        details: {
          taskId,
          error: error.message,
        },
        status: "error",
      });

      throw error;
    }
  }

  async listTasks(skipLogging: boolean = false): Promise<any[]> {
    try {
      const [rows] = await this.pool.execute("SELECT data FROM scheduler_tasks");

      const rowsArray = rows as any[];
      const result = rowsArray.map((row) => safeParseJSON(row.data));

      // Registra o log apenas se skipLogging for false
      if (!skipLogging) {
        await this.logActivity({
          type: LogType.INFO,
          source: "scheduler",
          action: "list_tasks",
          details: {
            count: result.length,
          },
          status: "success",
        });
      }

      return result;
    } catch (error) {
      logger.error("Erro ao listar tarefas:", error);

      // Registra o log de erro apenas se skipLogging for false
      if (!skipLogging) {
        await this.logActivity({
          type: LogType.ERROR,
          source: "scheduler",
          action: "list_tasks",
          details: {
            error: error.message,
          },
          status: "error",
        });
      }

      throw error;
    }
  }

  async removeTask(taskId: string): Promise<boolean> {
    try {
      const [result] = await this.pool.execute("DELETE FROM scheduler_tasks WHERE id = ?", [taskId]);

      const resultObj = result as mysql.ResultSetHeader;
      const success = resultObj.affectedRows > 0;

      // Registra o log
      await this.logActivity({
        type: LogType.INFO,
        source: "scheduler",
        action: "remove_task",
        details: {
          taskId,
          success,
        },
        status: "success",
      });

      return success;
    } catch (error) {
      logger.error(`Erro ao remover tarefa ${taskId}:`, error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "scheduler",
        action: "remove_task",
        details: {
          taskId,
          error: error.message,
        },
        status: "error",
      });

      throw error;
    }
  }

  async taskExists(taskId: string): Promise<boolean> {
    try {
      const [rows] = await this.pool.execute("SELECT 1 FROM scheduler_tasks WHERE id = ?", [taskId]);

      const rowsArray = rows as any[];
      const exists = rowsArray.length > 0;

      // Registra o log
      await this.logActivity({
        type: LogType.INFO,
        source: "scheduler",
        action: "check_task_exists",
        details: {
          taskId,
          exists,
        },
        status: "success",
      });

      return exists;
    } catch (error) {
      logger.error(`Erro ao verificar existência da tarefa ${taskId}:`, error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "scheduler",
        action: "check_task_exists",
        details: {
          taskId,
          error: error.message,
        },
        status: "error",
      });

      throw error;
    }
  }
}

/**
 * Implementação de armazenamento MySQL para o Workflow
 */
export class MySQLWorkflowStorage extends MySQLBaseStorage implements WorkflowStorage {
  constructor(config: MySQLConfig) {
    super(config);
    this.initializeTables();
  }

  /**
   * Inicializa as tabelas necessárias
   */
  private async initializeTables(): Promise<void> {
    try {
      await this.pool.execute(`
        CREATE TABLE IF NOT EXISTS workflows (
          id VARCHAR(255) PRIMARY KEY,
          data JSON NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
    } catch (error) {
      logger.error("Erro ao inicializar tabelas de workflow:", error);
      throw error;
    }
  }

  async saveWorkflow(workflowId: string, workflowData: any): Promise<void> {
    try {
      await this.pool.execute("INSERT INTO workflows (id, data) VALUES (?, ?) ON DUPLICATE KEY UPDATE data = ?", [
        workflowId,
        JSON.stringify(workflowData),
        JSON.stringify(workflowData),
      ]);

      // Registra o log
      await this.logActivity({
        type: LogType.INFO,
        source: "workflow",
        action: "save_workflow",
        details: {
          workflowId,
          workflowData,
        },
        status: "success",
      });
    } catch (error) {
      logger.error(`Erro ao salvar workflow ${workflowId}:`, error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "workflow",
        action: "save_workflow",
        details: {
          workflowId,
          error: error.message,
        },
        status: "error",
      });

      throw error;
    }
  }

  async getWorkflow(workflowId: string): Promise<any | null> {
    try {
      const [rows] = await this.pool.execute("SELECT data FROM workflows WHERE id = ?", [workflowId]);

      const rowsArray = rows as any[];

      if (rowsArray.length === 0) {
        return null;
      }

      const result = safeParseJSON(rowsArray[0].data);

      // Registra o log
      await this.logActivity({
        type: LogType.INFO,
        source: "workflow",
        action: "get_workflow",
        details: {
          workflowId,
          found: true,
        },
        status: "success",
      });

      return result;
    } catch (error) {
      logger.error(`Erro ao obter workflow ${workflowId}:`, error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "workflow",
        action: "get_workflow",
        details: {
          workflowId,
          error: error.message,
        },
        status: "error",
      });

      throw error;
    }
  }

  async listWorkflows(): Promise<any[]> {
    try {
      const [rows] = await this.pool.execute("SELECT data FROM workflows");

      const rowsArray = rows as any[];
      const result = rowsArray.map((row) => safeParseJSON(row.data));

      // Registra o log
      await this.logActivity({
        type: LogType.INFO,
        source: "workflow",
        action: "list_workflows",
        details: {
          count: result.length,
        },
        status: "success",
      });

      return result;
    } catch (error) {
      logger.error("Erro ao listar workflows:", error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "workflow",
        action: "list_workflows",
        details: {
          error: error.message,
        },
        status: "error",
      });

      throw error;
    }
  }

  async removeWorkflow(workflowId: string): Promise<boolean> {
    try {
      const [result] = await this.pool.execute("DELETE FROM workflows WHERE id = ?", [workflowId]);

      const resultObj = result as mysql.ResultSetHeader;
      const success = resultObj.affectedRows > 0;

      // Registra o log
      await this.logActivity({
        type: LogType.INFO,
        source: "workflow",
        action: "remove_workflow",
        details: {
          workflowId,
          success,
        },
        status: "success",
      });

      return success;
    } catch (error) {
      logger.error(`Erro ao remover workflow ${workflowId}:`, error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "workflow",
        action: "remove_workflow",
        details: {
          workflowId,
          error: error.message,
        },
        status: "error",
      });

      throw error;
    }
  }

  async workflowExists(workflowId: string): Promise<boolean> {
    try {
      const [rows] = await this.pool.execute("SELECT 1 FROM workflows WHERE id = ?", [workflowId]);

      const rowsArray = rows as any[];
      const exists = rowsArray.length > 0;

      // Registra o log
      await this.logActivity({
        type: LogType.INFO,
        source: "workflow",
        action: "check_workflow_exists",
        details: {
          workflowId,
          exists,
        },
        status: "success",
      });

      return exists;
    } catch (error) {
      logger.error(`Erro ao verificar existência do workflow ${workflowId}:`, error);

      // Registra o log de erro
      await this.logActivity({
        type: LogType.ERROR,
        source: "workflow",
        action: "check_workflow_exists",
        details: {
          workflowId,
          error: error.message,
        },
        status: "error",
      });

      throw error;
    }
  }
}

/**
 * Factory para criar instâncias de storage MySQL
 */
export class MySQLStorageFactory {
  static createSchedulerStorage(config: MySQLConfig): MySQLSchedulerStorage {
    logger.info("Criando MySQLSchedulerStorage via factory");
    return new MySQLSchedulerStorage(config);
  }

  static createWorkflowStorage(config: MySQLConfig): MySQLWorkflowStorage {
    logger.info("Criando MySQLWorkflowStorage via factory");
    return new MySQLWorkflowStorage(config);
  }
}
