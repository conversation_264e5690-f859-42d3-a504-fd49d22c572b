import { trace, context, SpanStatusCode, SpanKind, SpanAttributes } from "@opentelemetry/api";
import { createModuleLogger } from "./logger";

// Logger para o serviço de tracing
const logger = createModuleLogger("tracing-service");

/**
 * Classe de serviço de tracing utilizando recursos do monitoring
 */
export class TracingService {
  constructor() {
    logger.info("Serviço de tracing inicializado");
  }

  /**
   * Inicia uma nova trace para rastreamento
   * @param name Nome da trace
   * @param attributes Atributos adicionais
   */
  async startTrace(name: string, attributes: Record<string, any> = {}): Promise<string> {
    const tracer = trace.getTracer("sdk-queues");
    const span = tracer.startSpan(name, {
      kind: SpanKind.INTERNAL,
      attributes: attributes as SpanAttributes,
    });

    // Extrair o ID da trace
    const traceId = span.spanContext().traceId;

    // Armazenar span em contexto
    trace.setSpan(context.active(), span);

    logger.info(`Trace iniciada: ${name}`, { traceId, ...attributes });

    return traceId;
  }

  /**
   * Encerra uma trace
   * @param traceId ID da trace (não utilizado - usa o contexto atual)
   * @param status Status de conclusão
   */
  async endTrace(_traceId: string, status: "completed" | "error" = "completed"): Promise<boolean> {
    // Obtém o span atual do contexto
    const currentSpan = trace.getSpan(context.active());

    if (!currentSpan) {
      logger.warn("Tentativa de encerrar trace sem span ativo");
      return false;
    }

    // Define o status
    if (status === "error") {
      currentSpan.setStatus({
        code: SpanStatusCode.ERROR,
      });
    } else {
      currentSpan.setStatus({
        code: SpanStatusCode.OK,
      });
    }

    // Encerra o span
    currentSpan.end();

    logger.info(`Trace encerrada com status: ${status}`);
    return true;
  }

  /**
   * Inicia um novo span dentro de uma trace
   * @param traceId ID da trace (não utilizado - usa o contexto atual)
   * @param name Nome do span
   * @param parentId ID do span pai (não utilizado - usa o contexto atual)
   * @param attributes Atributos adicionais
   */
  async startSpan(_traceId: string, name: string, _parentId?: string, attributes: Record<string, any> = {}): Promise<string> {
    const tracer = trace.getTracer("sdk-queues");
    const span = tracer.startSpan(name, {
      kind: SpanKind.INTERNAL,
      attributes: attributes as SpanAttributes,
    });

    // Extrair o ID do span
    const spanId = span.spanContext().spanId;

    // Armazenar span em contexto
    trace.setSpan(context.active(), span);

    logger.info(`Span iniciado: ${name}`, { spanId, ...attributes });

    return spanId;
  }

  /**
   * Encerra um span
   * @param traceId ID da trace (não utilizado - usa o contexto atual)
   * @param spanId ID do span (não utilizado - usa o contexto atual)
   * @param status Status de conclusão
   */
  async endSpan(_traceId: string, _spanId: string, status: "completed" | "error" = "completed"): Promise<boolean> {
    // Obtém o span atual do contexto
    const currentSpan = trace.getSpan(context.active());

    if (!currentSpan) {
      logger.warn("Tentativa de encerrar span sem span ativo");
      return false;
    }

    // Define o status
    if (status === "error") {
      currentSpan.setStatus({
        code: SpanStatusCode.ERROR,
      });
    } else {
      currentSpan.setStatus({
        code: SpanStatusCode.OK,
      });
    }

    // Encerra o span
    currentSpan.end();

    logger.info(`Span encerrado com status: ${status}`);
    return true;
  }

  /**
   * Adiciona um evento a um span
   * @param traceId ID da trace (não utilizado - usa o contexto atual)
   * @param spanId ID do span (não utilizado - usa o contexto atual)
   * @param name Nome do evento
   * @param attributes Atributos do evento
   */
  async addEvent(_traceId: string, _spanId: string, name: string, attributes: Record<string, any> = {}): Promise<boolean> {
    // Obtém o span atual do contexto
    const currentSpan = trace.getSpan(context.active());

    if (!currentSpan) {
      logger.warn("Tentativa de adicionar evento sem span ativo");
      return false;
    }

    // Adiciona o evento ao span
    currentSpan.addEvent(name, attributes as SpanAttributes);

    logger.info(`Evento adicionado: ${name}`, attributes);
    return true;
  }

  /**
   * Adiciona atributos a um span
   * @param traceId ID da trace (não utilizado - usa o contexto atual)
   * @param spanId ID do span (não utilizado - usa o contexto atual)
   * @param attributes Atributos a adicionar
   */
  async addAttributes(_traceId: string, _spanId: string, attributes: Record<string, any>): Promise<boolean> {
    // Obtém o span atual do contexto
    const currentSpan = trace.getSpan(context.active());

    if (!currentSpan) {
      logger.warn("Tentativa de adicionar atributos sem span ativo");
      return false;
    }

    // Adiciona os atributos ao span
    currentSpan.setAttributes(attributes as SpanAttributes);

    logger.info("Atributos adicionados ao span", attributes);
    return true;
  }

  /**
   * Método para manter compatibilidade com a API anterior
   */
  async disconnect(): Promise<void> {
    logger.info("Serviço de tracing desconectado");
  }
}
