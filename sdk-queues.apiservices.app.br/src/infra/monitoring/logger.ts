import { LoggerProvider } from "@opentelemetry/sdk-logs";
import { BatchLogRecordProcessor } from "@opentelemetry/sdk-logs";
import { OTLPLogExporter } from "@opentelemetry/exporter-logs-otlp-http";
import { Resource } from "@opentelemetry/resources";
import { SemanticResourceAttributes } from "@opentelemetry/semantic-conventions";
import * as winston from "winston";
import { trace } from "@opentelemetry/api";

const NODE_ENV = process.env.NODE_ENV || "production";

const logExporter = new OTLPLogExporter({
  url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT ? `${process.env.OTEL_EXPORTER_OTLP_ENDPOINT}/v1/logs` : "http://localhost:4318/v1/logs",
  headers: {
    "Content-Type": "application/json",
  },
});

const resourceAttributes = {
  [SemanticResourceAttributes.SERVICE_NAME]: "sdk-queues-manager",
  [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: NODE_ENV,
};

// @ts-ignore
const loggerProvider = new LoggerProvider({
  // @ts-ignore
  resource: new Resource(resourceAttributes) as any,
});

loggerProvider.addLogRecordProcessor(new BatchLogRecordProcessor(logExporter));

export function createModuleLogger(moduleName: string) {
  const winstonLogger = winston.createLogger({
    level: process.env.LOG_LEVEL || "info",
    format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
    defaultMeta: {
      service: "sdk-queues-manager",
      module: moduleName,
      environment: NODE_ENV,
    },
    transports: [new winston.transports.Console()],
  });

  const logToOtel = (level: string, message: string, meta?: any) => {
    try {
      const span = trace.getActiveSpan();
      let traceId, spanId;

      if (span) {
        try {
          const context = span.spanContext();
          traceId = context?.traceId;
          spanId = context?.spanId;
        } catch (e) {}
      }

      const otelLogger = loggerProvider.getLogger(moduleName);
      otelLogger.emit({
        severityText: level,
        body: message,
        attributes: {
          ...(Array.isArray(meta) && meta.length > 0 ? meta[0] : meta),
          traceId,
          spanId,
          service: "sdk-queues-manager",
          module: moduleName,
          environment: NODE_ENV,
        },
      });
    } catch (error) {
      console.error("Erro ao enviar log para OpenTelemetry:", error);
    }
  };

  return {
    debug: (message: string, ...meta: any[]) => {
      winstonLogger.debug(message, ...meta);
      logToOtel("DEBUG", message, meta);
    },
    info: (message: string, ...meta: any[]) => {
      winstonLogger.info(message, ...meta);
      logToOtel("INFO", message, meta);
    },
    warn: (message: string, ...meta: any[]) => {
      winstonLogger.warn(message, ...meta);
      logToOtel("WARN", message, meta);
    },
    error: (message: string, ...meta: any[]) => {
      winstonLogger.error(message, ...meta);
      logToOtel("ERROR", message, meta);
    },
  };
}
