import sdk from "./tracer";
import { createModuleLogger } from "./logger";

const apmLogger = createModuleLogger("APM");

async function initializeTracing(): Promise<void> {
  try {
    await sdk.start();
    apmLogger.info("Tracing inicializado com sucesso");
  } catch (error: unknown) {
    apmLogger.error("Erro ao inicializar tracing", error);
  }
}

process.on("SIGTERM", async () => {
  try {
    await sdk.shutdown();
    apmLogger.info("Tracing finalizado");
  } catch (error: unknown) {
    apmLogger.error("Erro ao finalizar tracing", error);
  }
});

process.on("beforeExit", () => {
  apmLogger.info("Tentativa de encerramento do processo detectada");
});

initializeTracing();

export default sdk;
