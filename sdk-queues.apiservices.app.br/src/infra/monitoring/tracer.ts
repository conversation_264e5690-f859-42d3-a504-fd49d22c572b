import { Node<PERSON><PERSON> } from "@opentelemetry/sdk-node";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { Resource } from "@opentelemetry/resources";
import { SemanticResourceAttributes } from "@opentelemetry/semantic-conventions";
import { getNodeAutoInstrumentations } from "@opentelemetry/auto-instrumentations-node";
import { ExpressInstrumentation } from "@opentelemetry/instrumentation-express";
import { HttpInstrumentation } from "@opentelemetry/instrumentation-http";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-base";

const NODE_ENV = process.env.NODE_ENV || "production";
const OTLP_ENDPOINT = process.env.OTEL_EXPORTER_OTLP_ENDPOINT || "http://localhost:4318";

const resourceAttributes = {
  [SemanticResourceAttributes.SERVICE_NAME]: "ai-planify",
  [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: NODE_ENV,
  [SemanticResourceAttributes.SERVICE_VERSION]: process.env.npm_package_version || "1.0.0",
};

// @ts-ignore
const sdk = new NodeSDK({
  // @ts-ignore
  resource: new Resource(resourceAttributes) as any,
  spanProcessor: new BatchSpanProcessor(
    new OTLPTraceExporter({
      url: `${OTLP_ENDPOINT}/v1/traces`,
      headers: {
        "Content-Type": "application/json",
      },
    })
  ),
  instrumentations: [
    new HttpInstrumentation(),
    new ExpressInstrumentation(),
    getNodeAutoInstrumentations({
      "@opentelemetry/instrumentation-fs": {
        enabled: false,
      },
      "@opentelemetry/instrumentation-express": {
        enabled: true,
      },
      "@opentelemetry/instrumentation-http": {
        enabled: true,
      },
    }),
  ],
});

export default sdk;
