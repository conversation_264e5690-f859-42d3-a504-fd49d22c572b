{"name": "sdk-queues", "version": "1.0.0", "description": "A queue manager for Node.js", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"prebuild": "rm -rf dist", "build": "tsc", "dev": "tsc --watch", "test": "jest", "start": "pnpm run build && ts-node --transpile-only samples/server.ts", "consumer": "pnpm run build && ts-node --transpile-only samples/consumer.ts", "publisher": "pnpm run build && ts-node --transpile-only samples/publisher.ts", "commit": "pnpm run build && git add . && git-cz && git push", "mysql-example": "pnpm run build && ts-node --transpile-only samples/mysql-integration.ts"}, "keywords": ["redis", "pubsub", "sdk", "typescript"], "author": "lucca-rodrigues - <EMAIL>", "license": "ISC", "packageManager": "pnpm@10.4.1", "dependencies": {"@fastify/cors": "^10.0.2", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.57.1", "@opentelemetry/exporter-logs-otlp-http": "^0.200.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/instrumentation-express": "^0.48.0", "@opentelemetry/instrumentation-http": "^0.200.0", "@opentelemetry/resources": "^1.16.0", "@opentelemetry/sdk-logs": "^0.200.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/semantic-conventions": "^1.30.0", "@sinclair/typebox": "^0.34.27", "dotenv": "^16.4.7", "fastify": "^5.2.1", "ioredis": "^5.3.2", "mysql2": "3.14.0", "winston": "^3.17.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.17.19", "git-cz": "^4.9.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "publishConfig": {"access": "public"}}