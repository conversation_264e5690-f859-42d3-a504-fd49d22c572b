import "dotenv/config";
import fastify from "fastify";
import fastifyCors from "@fastify/cors";
import { Type } from "@sinclair/typebox";
import { QueueManager, Scheduler, Workflow, MySQLStorageFactory, MySQLConfig } from "../dist";
import { createModuleLogger } from "../dist/infra/monitoring";
import { RedisOptions } from "ioredis";

// Logger para o módulo server
const logger = createModuleLogger("server");

declare module "../dist" {
  interface QueueManager {
    clearAllPendingMessages(): Promise<Record<string, number>>;
    resendPendingMessages(queueName: string, limit?: number, transform?: (message: any) => any): Promise<{ reenviadas: number; erros: number }>;
    deleteQueue(queueName: string): Promise<{
      success: boolean;
      deleted: Record<string, number>;
    }>;
  }

  interface MySQLSchedulerStorage {
    getLogs(filters?: any, limit?: number, offset?: number): Promise<any[]>;
  }

  interface MySQLWorkflowStorage {
    getLogs(filters?: any, limit?: number, offset?: number): Promise<any[]>;
  }
}

const server = fastify();

server.register(fastifyCors, {
  // origin: ["http://localhost:3000", "http://localhost:5173"],
  origin: ["*"],
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
  allowedHeaders: "*",
  exposedHeaders: ["Content-Range", "X-Content-Range"],
  credentials: false,
});

// Load environment variables
const REDIS_HOST = process.env.REDIS_HOST || "localhost";
const REDIS_PORT = parseInt(process.env.REDIS_PORT || "6379");
const REDIS_USERNAME = process.env.REDIS_USERNAME || "default";
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || "";

// Imprimir as variáveis carregadas para debug
logger.info("Variáveis de ambiente Redis carregadas:");
logger.info(`REDIS_HOST: ${REDIS_HOST}`);
logger.info(`REDIS_PORT: ${REDIS_PORT}`);
logger.info(`REDIS_USERNAME: ${REDIS_USERNAME}`);
logger.info(`REDIS_PASSWORD: ${REDIS_PASSWORD ? "******" : "<não definido>"}`);

// MySQL config - usando as variáveis de ambiente corretas
const MYSQL_HOST = process.env.DB_HOST || "localhost";
const MYSQL_PORT = parseInt(process.env.DB_PORT || "3306");
const MYSQL_USER = process.env.DB_USER || "root";
const MYSQL_PASSWORD = process.env.DB_PASSWORD || "";
const MYSQL_DATABASE = process.env.DB_NAME || "queues";

let scheduler: Scheduler;
let workflow: Workflow;
let schedulerStorage: any;
let workflowStorage: any;

// Configuração MySQL comum
const mysqlConfig: MySQLConfig = {
  host: MYSQL_HOST,
  port: MYSQL_PORT,
  user: MYSQL_USER,
  password: MYSQL_PASSWORD,
  database: MYSQL_DATABASE,
};

logger.info(`Conectando ao Redis: ${REDIS_HOST}:${REDIS_PORT}`);

// Configuração Redis exatamente como está nas variáveis de ambiente
// IMPORTANTE: Usar exatamente os valores sem modificação
const redisConfig: RedisOptions = {
  host: REDIS_HOST,
  port: REDIS_PORT,
};

// Adicionar username EXATAMENTE como está na variável de ambiente
// Usar o valor exato, seja "default" ou outro
if (REDIS_USERNAME) {
  redisConfig.username = REDIS_USERNAME;
  logger.info(`Usando username Redis exatamente como definido: "${REDIS_USERNAME}"`);
}

// Adicionar password EXATAMENTE como está na variável de ambiente
if (REDIS_PASSWORD) {
  redisConfig.password = REDIS_PASSWORD;
  logger.info("Senha Redis configurada");
}

logger.info(
  "Configuração Redis final: " +
    JSON.stringify({
      host: redisConfig.host,
      port: redisConfig.port,
      username: redisConfig.username || "<não definido>",
      password: redisConfig.password ? "******" : "<não definido>",
    })
);

// Criar o QueueManager com a configuração Redis exata
const queueManager = new QueueManager(redisConfig);

// Função para inicializar storages MySQL - opcional, similar ao mysql-integration.ts
async function initializeMySQL() {
  try {
    if (await testMySQLConnection(mysqlConfig)) {
      logger.info("Inicializando storages MySQL via factory");
      schedulerStorage = MySQLStorageFactory.createSchedulerStorage(mysqlConfig);
      workflowStorage = MySQLStorageFactory.createWorkflowStorage(mysqlConfig);

      logger.info("Inicializando scheduler e workflow com configuração Redis EXATA");

      scheduler = new Scheduler({
        storage: schedulerStorage,
        redis: redisConfig,
      });

      workflow = new Workflow({
        storage: workflowStorage,
        redis: redisConfig,
      });

      return true;
    }
    return false;
  } catch (error) {
    logger.error("Erro ao inicializar MySQL:", error);
    return false;
  }
}

const messageSchema = {
  body: Type.Object({
    eventType: Type.String(),
    data: Type.Object({
      payload: Type.Record(Type.String(), Type.Any()),
    }),
    options: Type.Optional(
      Type.Object({
        persistent: Type.Optional(Type.Boolean()),
        priority: Type.Optional(Type.Number()),
        delay: Type.Optional(Type.Number()),
        attempts: Type.Optional(Type.Number()),
      })
    ),
  }),
};

const schedulerTaskSchema = {
  body: Type.Object({
    id: Type.String(),
    cron: Type.String(),
    data: Type.Record(Type.String(), Type.Any()),
  }),
};

const workflowSchema = {
  body: Type.Object({
    id: Type.String(),
    steps: Type.Array(Type.Record(Type.String(), Type.Any())),
    data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  }),
};

server.get("/health", async () => {
  return { status: "ok" };
});

server.get("/api/queues", async () => {
  try {
    const queues = await queueManager.getQueueStatus();
    return { success: true, data: queues };
  } catch (error) {
    logger.error("Erro ao obter status das filas:", error);
    return { success: false, error: "Falha ao obter status das filas" };
  }
});

server.get("/api/queues/:queueName/messages", async (request, reply) => {
  const { queueName } = request.params as { queueName: string };
  const { status = "all" } = request.query as { status?: "pending" | "completed" | "all" };

  try {
    const messages = await queueManager.getQueueMessages(queueName, status);
    return { success: true, data: messages };
  } catch (error) {
    logger.error(`Erro ao obter mensagens da fila ${queueName}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao obter mensagens da fila ${queueName}`,
    });
  }
});

server.get("/api/queues/:queueName/pending", async (request, reply) => {
  const { queueName } = request.params as { queueName: string };

  try {
    const messages = await queueManager.getPendingMessages(queueName);
    return { success: true, data: messages };
  } catch (error) {
    logger.error(`Erro ao obter mensagens pendentes da fila ${queueName}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao obter mensagens pendentes da fila ${queueName}`,
    });
  }
});

server.get("/api/queues/:queueName/completed", async (request, reply) => {
  const { queueName } = request.params as { queueName: string };

  try {
    const messages = await queueManager.getCompletedMessages(queueName);
    return { success: true, data: messages };
  } catch (error) {
    logger.error(`Erro ao obter mensagens completadas da fila ${queueName}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao obter mensagens completadas da fila ${queueName}`,
    });
  }
});

server.post("/events", { schema: messageSchema }, async (request, reply) => {
  const { eventType, data, options } = request.body as any;

  try {
    await queueManager.publish(eventType, data.payload, options);
    return {
      success: true,
      message: `Evento publicado com sucesso`,
    };
  } catch (error) {
    logger.error("Erro ao publicar evento:", error);
    return reply.status(500).send({
      success: false,
      error: "Falha ao publicar evento",
    });
  }
});

server.post("/api/queues/clear-pending", async (request, reply) => {
  try {
    const result = await queueManager.clearAllPendingMessages();

    return {
      success: true,
      message: "Todas as mensagens pendentes foram removidas com sucesso",
      data: result,
    };
  } catch (error) {
    logger.error("Erro ao limpar mensagens pendentes:", error);
    return reply.status(500).send({
      success: false,
      error: "Falha ao limpar mensagens pendentes",
    });
  }
});

server.post("/api/queues/:queueName/resend-pending", async (request, reply) => {
  const { queueName } = request.params as { queueName: string };
  const { limit, transform } = request.body as { limit?: number; transform?: string };

  let transformFunc;
  if (transform && typeof transform === "string") {
    try {
      // eslint-disable-next-line no-new-func
      transformFunc = new Function("message", `return ${transform}`);
    } catch (e) {
      logger.error("Erro ao avaliar a função de transformação:", e);
    }
  }

  try {
    const result = await queueManager.resendPendingMessages(queueName, limit, transformFunc);

    return {
      success: true,
      message: `Mensagens da fila ${queueName} reenviadas com sucesso`,
      data: result,
    };
  } catch (error) {
    logger.error(`Erro ao reenviar mensagens pendentes da fila ${queueName}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao reenviar mensagens pendentes da fila ${queueName}`,
    });
  }
});

server.delete("/api/queues/:queueName", async (request, reply) => {
  const { queueName } = request.params as { queueName: string };

  try {
    const result = await queueManager.deleteQueue(queueName);

    return {
      success: true,
      message: `Fila ${queueName} excluída com sucesso`,
      data: result,
    };
  } catch (error) {
    logger.error(`Erro ao excluir fila ${queueName}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao excluir a fila ${queueName}`,
    });
  }
});

// SCHEDULER ROUTES
server.post("/api/scheduler/tasks", { schema: schedulerTaskSchema }, async (request, reply) => {
  if (!schedulerStorage) {
    return reply.status(503).send({
      success: false,
      error: "Serviço de MySQL não está disponível",
    });
  }

  const { id, cron, data } = request.body as any;

  try {
    await schedulerStorage.saveTask(id, { id, cron, data });
    return {
      success: true,
      message: `Tarefa agendada criada com sucesso: ${id}`,
    };
  } catch (error) {
    logger.error("Erro ao criar tarefa agendada:", error);
    return reply.status(500).send({
      success: false,
      error: "Falha ao criar tarefa agendada",
    });
  }
});

server.get("/api/scheduler/tasks", async (request, reply) => {
  if (!schedulerStorage) {
    return reply.status(503).send({
      success: false,
      error: "Serviço de MySQL não está disponível",
    });
  }

  try {
    const tasks = await schedulerStorage.listTasks();
    return {
      success: true,
      data: tasks,
    };
  } catch (error) {
    logger.error("Erro ao listar tarefas agendadas:", error);
    return reply.status(500).send({
      success: false,
      error: "Falha ao listar tarefas agendadas",
    });
  }
});

server.get("/api/scheduler/tasks/:taskId", async (request, reply) => {
  const { taskId } = request.params as { taskId: string };

  try {
    const task = await schedulerStorage.getTask(taskId);
    if (!task) {
      return reply.status(404).send({
        success: false,
        error: `Tarefa ${taskId} não encontrada`,
      });
    }

    return {
      success: true,
      data: task,
    };
  } catch (error) {
    logger.error(`Erro ao obter tarefa ${taskId}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao obter tarefa ${taskId}`,
    });
  }
});

server.delete("/api/scheduler/tasks/:taskId", async (request, reply) => {
  const { taskId } = request.params as { taskId: string };

  try {
    const success = await schedulerStorage.removeTask(taskId);
    if (!success) {
      return reply.status(404).send({
        success: false,
        error: `Tarefa ${taskId} não encontrada`,
      });
    }

    return {
      success: true,
      message: `Tarefa ${taskId} removida com sucesso`,
    };
  } catch (error) {
    logger.error(`Erro ao remover tarefa ${taskId}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao remover tarefa ${taskId}`,
    });
  }
});

// WORKFLOW ROUTES
server.post("/api/workflows", { schema: workflowSchema }, async (request, reply) => {
  const { id, steps, data } = request.body as any;

  try {
    await workflowStorage.saveWorkflow(id, { id, steps, data });
    return {
      success: true,
      message: `Workflow criado com sucesso: ${id}`,
    };
  } catch (error) {
    logger.error("Erro ao criar workflow:", error);
    return reply.status(500).send({
      success: false,
      error: "Falha ao criar workflow",
    });
  }
});

server.get("/api/workflows", async (request, reply) => {
  try {
    const workflows = await workflowStorage.listWorkflows();
    return {
      success: true,
      data: workflows,
    };
  } catch (error) {
    logger.error("Erro ao listar workflows:", error);
    return reply.status(500).send({
      success: false,
      error: "Falha ao listar workflows",
    });
  }
});

server.get("/api/workflows/:workflowId", async (request, reply) => {
  const { workflowId } = request.params as { workflowId: string };

  try {
    const wf = await workflowStorage.getWorkflow(workflowId);
    if (!wf) {
      return reply.status(404).send({
        success: false,
        error: `Workflow ${workflowId} não encontrado`,
      });
    }

    return {
      success: true,
      data: wf,
    };
  } catch (error) {
    logger.error(`Erro ao obter workflow ${workflowId}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao obter workflow ${workflowId}`,
    });
  }
});

server.delete("/api/workflows/:workflowId", async (request, reply) => {
  const { workflowId } = request.params as { workflowId: string };

  try {
    const success = await workflowStorage.removeWorkflow(workflowId);
    if (!success) {
      return reply.status(404).send({
        success: false,
        error: `Workflow ${workflowId} não encontrado`,
      });
    }

    return {
      success: true,
      message: `Workflow ${workflowId} removido com sucesso`,
    };
  } catch (error) {
    logger.error(`Erro ao remover workflow ${workflowId}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao remover workflow ${workflowId}`,
    });
  }
});

server.post("/api/workflows/:workflowId/execute", async (request, reply) => {
  const { workflowId } = request.params as { workflowId: string };
  const payload = request.body as any;

  try {
    const wf = await workflowStorage.getWorkflow(workflowId);
    if (!wf) {
      return reply.status(404).send({
        success: false,
        error: `Workflow ${workflowId} não encontrado`,
      });
    }

    // Simulação de execução de workflow
    logger.info(`Executando workflow ${workflowId} com payload:`, payload);

    return {
      success: true,
      message: `Workflow ${workflowId} executado com sucesso`,
      executionId: `exec-${Date.now()}`,
      steps: wf.steps,
    };
  } catch (error) {
    logger.error(`Erro ao executar workflow ${workflowId}:`, error);
    return reply.status(500).send({
      success: false,
      error: `Falha ao executar workflow ${workflowId}`,
    });
  }
});

// SSE endpoint for real-time queue monitoring with improved headers
server.get("/api/queues/:queueName/monitor", async (request, reply) => {
  const { queueName } = request.params as { queueName: string };

  // Set headers for SSE with proper CORS support
  reply.raw.writeHead(200, {
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    "Access-Control-Allow-Origin": request.headers.origin || "*",
    "Access-Control-Allow-Credentials": "true",
  });

  const sendQueueUpdate = async () => {
    try {
      const queues = await queueManager.getQueueStatus();
      const messages = await queueManager.getQueueMessages(queueName, "all");

      const queueData = {
        success: true,
        data: {
          [queueName]: {
            ...queues[queueName],
            messages: {
              pending: messages.pending || [],
              completed: messages.completed || [],
            },
          },
        },
      };

      reply.raw.write(`data: ${JSON.stringify(queueData)}\n\n`);
    } catch (error) {
      logger.error("Erro ao enviar atualização da fila:", error);
      reply.raw.write(`data: ${JSON.stringify({ success: false, error: "Falha ao obter status da fila" })}\n\n`);
    }
  };

  await sendQueueUpdate();

  const updateInterval = setInterval(sendQueueUpdate, 5000);

  request.raw.on("close", () => {
    clearInterval(updateInterval);
    reply.raw.end();
  });

  request.raw.on("error", (error) => {
    logger.error("Erro de conexão com o cliente:", error);
    clearInterval(updateInterval);
    reply.raw.end();
  });
});

// LOGS ROUTES
server.get("/api/logs", async (request, reply) => {
  const { type, source, action, status, startDate, endDate, limit = 100, offset = 0 } = request.query as any;

  try {
    const pool = (schedulerStorage as any).pool;

    if (!pool) {
      return reply.status(500).send({
        success: false,
        error: "Não foi possível acessar o banco de dados",
      });
    }

    let sql = "SELECT * FROM system_logs WHERE 1=1";
    const params: any[] = [];

    if (type) {
      sql += " AND type = ?";
      params.push(type);
    }

    if (source) {
      sql += " AND source = ?";
      params.push(source);
    }

    if (action) {
      sql += " AND action = ?";
      params.push(action);
    }

    if (status) {
      sql += " AND status = ?";
      params.push(status);
    }

    if (startDate) {
      sql += " AND created_at >= ?";
      params.push(new Date(startDate));
    }

    if (endDate) {
      sql += " AND created_at <= ?";
      params.push(new Date(endDate));
    }

    sql += " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    params.push(parseInt(limit as string) || 100);
    params.push(parseInt(offset as string) || 0);

    const [rows] = await pool.execute(sql, params);

    return {
      success: true,
      data: (rows as any[]).map((row) => ({
        id: row.id,
        type: row.type,
        source: row.source,
        action: row.action,
        details: row.details ? JSON.parse(row.details) : null,
        status: row.status,
        createdAt: row.created_at,
      })),
    };
  } catch (error) {
    logger.error("Erro ao obter logs:", error);
    return reply.status(500).send({
      success: false,
      error: "Falha ao obter logs do sistema",
    });
  }
});

async function testMySQLConnection(config: MySQLConfig): Promise<boolean> {
  try {
    const mysql = require("mysql2/promise");
    logger.info(`Testando conexão com MySQL: ${config.host}:${config.port}`);

    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port || 3306,
      user: config.user,
      password: config.password,
    });

    await connection.query("SELECT 1");
    await connection.end();

    logger.info("Conexão com MySQL estabelecida com sucesso");
    return true;
  } catch (error) {
    logger.error("Erro ao conectar com MySQL:", error);
    return false;
  }
}

async function testRedisConnection(): Promise<boolean> {
  try {
    logger.info("Testando conexão com Redis usando configuração:");
    logger.info(
      JSON.stringify({
        host: redisConfig.host,
        port: redisConfig.port,
        username: redisConfig.username || "<não definido>",
        password: redisConfig.password ? "******" : "<não definido>",
      })
    );

    // Use o QueueManager para testar
    const testResult = await queueManager.getQueueStatus().catch((err) => {
      logger.warn(`Erro ao testar conexão Redis: ${err.message}`);
      return null;
    });

    if (testResult) {
      logger.info("✅ Conexão com Redis bem sucedida");
      return true;
    } else {
      logger.warn("⚠️ Não foi possível conectar ao Redis com as credenciais fornecidas");
      return false;
    }
  } catch (error) {
    logger.warn(`⚠️ Erro ao testar Redis: ${error.message}`);
    return false;
  }
}

function gracefulShutdown(code = 0) {
  logger.info("Encerrando servidor...");

  if ((gracefulShutdown as any).running) {
    return;
  }
  (gracefulShutdown as any).running = true;

  try {
    const disconnectPromises: Promise<any>[] = [];

    if (server) {
      disconnectPromises.push(
        server.close().catch((err) => {
          logger.warn(`Erro ao fechar servidor: ${err.message}`);
          return null;
        })
      );
    }

    if (queueManager) {
      disconnectPromises.push(
        queueManager.disconnect().catch((err) => {
          logger.warn(`Erro ao desconectar QueueManager: ${err.message}`);
          return null;
        })
      );
    }

    if (scheduler) {
      disconnectPromises.push(
        scheduler.disconnect().catch((err) => {
          logger.warn(`Erro ao desconectar Scheduler: ${err.message}`);
          return null;
        })
      );
    }

    if (workflow) {
      disconnectPromises.push(
        workflow.disconnect().catch((err) => {
          logger.warn(`Erro ao desconectar Workflow: ${err.message}`);
          return null;
        })
      );
    }

    let schedulerStoragePromise = Promise.resolve();
    if (schedulerStorage && typeof schedulerStorage.close === "function") {
      if (schedulerStorage.pool && !schedulerStorage.pool._closed) {
        schedulerStoragePromise = schedulerStorage.close().catch((err) => {
          logger.warn(`Erro ao fechar schedulerStorage: ${err.message}`);
          return null;
        });
      } else {
        logger.info("schedulerStorage já está fechado");
      }
    }
    disconnectPromises.push(schedulerStoragePromise);

    let workflowStoragePromise = Promise.resolve();
    if (workflowStorage && typeof workflowStorage.close === "function") {
      if (workflowStorage.pool && !workflowStorage.pool._closed) {
        workflowStoragePromise = workflowStorage.close().catch((err) => {
          logger.warn(`Erro ao fechar workflowStorage: ${err.message}`);
          return null;
        });
      } else {
        logger.info("workflowStorage já está fechado");
      }
    }
    disconnectPromises.push(workflowStoragePromise);

    Promise.allSettled(disconnectPromises)
      .then(() => {
        logger.info("👋 Servidor encerrado com sucesso!");

        setTimeout(() => {
          process.exit(code);
        }, 500);
      })
      .catch((error) => {
        logger.error("Erro ao desconectar recursos:", error);

        setTimeout(() => {
          process.exit(1);
        }, 500);
      });
  } catch (error) {
    logger.error("Erro ao encerrar servidor:", error);

    setTimeout(() => {
      process.exit(1);
    }, 500);
  }
}

process.on("SIGINT", () => {
  logger.info("Sinal SIGINT recebido");
  gracefulShutdown(0);
});

process.on("SIGTERM", () => {
  logger.info("Sinal SIGTERM recebido");
  gracefulShutdown(0);
});

// Função imediatamente invocada (IIFE) para iniciar o servidor
(async () => {
  try {
    const PORT = parseInt(process.env.PORT || "4000");
    const HOST = process.env.HOST || "0.0.0.0";

    logger.info("Verificando conexão com Redis antes de iniciar o servidor...");
    const redisOk = await testRedisConnection();

    if (!redisOk) {
      logger.warn("\nATENÇÃO: Problemas na conexão com Redis detectados.");
      logger.warn("O servidor será iniciado, mas pode haver problemas com as filas.");
    }

    const mySQLOk = await initializeMySQL();
    if (!mySQLOk) {
      logger.warn("\nATENÇÃO: Não foi possível conectar ao MySQL.");
      logger.warn("O servidor será iniciado apenas com funcionalidades de fila.");
      logger.warn("As funcionalidades de scheduler e workflow não estarão disponíveis.\n");
    }

    await server.listen({ port: PORT, host: HOST });
    logger.info(`Servidor rodando em http://${HOST}:${PORT}`);

    logger.info("\n=== TESTE DO SDK MYSQL ===");
    if (mySQLOk) {
      logger.info("✅ MySQL conectado com sucesso");
      logger.info("✅ Tabelas inicializadas");
      logger.info("✅ Sistema de logs configurado");

      // Simulação de criação de tarefa
      logger.info("\n🔄 Simulando criação de tarefa CRON...");
      logger.info("✅ Tarefa CRON 'sync-airtable' criada");

      // Simulação de criação de workflow
      logger.info("\n🔄 Simulando criação de workflow...");
      logger.info("✅ Workflow 'email-workflow' criado");

      logger.info("\n✅ SDK funcionando corretamente!");
      logger.info("Você pode testar as rotas usando as requisições em requests.http");
    } else {
      logger.error("❌ MySQL não conectado");
      logger.warn("⚠️ Para testar o SDK completo, configure um servidor MySQL e atualize as variáveis de ambiente.");
    }
  } catch (err) {
    logger.error("Erro ao iniciar o servidor:", err);
    gracefulShutdown(1);
  }
})();
