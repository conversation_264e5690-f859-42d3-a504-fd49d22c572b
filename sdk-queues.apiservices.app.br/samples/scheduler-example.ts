import { QueueManager, Scheduler } from "../dist";
import "dotenv/config";

// Configuração de conexão com o Redis
const REDIS_HOST = process.env.REDIS_HOST || "localhost";
const REDIS_PORT = parseInt(process.env.REDIS_PORT || "6379");
const REDIS_USERNAME = process.env.REDIS_USERNAME || "default";
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || "redis123";

const redisConfig = {
  host: REDIS_HOST,
  port: REDIS_PORT,
  username: REDIS_USERNAME,
  password: REDIS_PASSWORD,
};

async function main() {
  console.log("🕒 Iniciando exemplo de agendamento de tarefas...");

  // Inicializa componentes
  const queueManager = new QueueManager(redisConfig);
  const scheduler = new Scheduler(redisConfig);

  // Configura os consumidores para processar as mensagens agendadas
  await setupConsumers(queueManager);

  // 1. Agenda uma tarefa para executar a cada 5 segundos
  const intervalTaskId = await scheduler.scheduleInterval(
    "relatorio-periodico",
    "relatorios",
    {
      tipo: "resumo-diario",
      formato: "json",
    },
    5000 // intervalo de 5 segundos
  );

  console.log(`✅ Tarefa agendada em intervalo: ${intervalTaskId}`);

  // 2. Agenda uma tarefa para executar em uma data específica (30 segundos no futuro)
  const futureDate = new Date(Date.now() + 30000);
  const datetimeTaskId = await scheduler.scheduleAt(
    "envio-email-marketing",
    "emails",
    {
      tipo: "campanha",
      destinatarios: ["<EMAIL>"],
      assunto: "Novidades da semana!",
      conteudo: "Confira nossas novidades...",
    },
    futureDate
  );

  console.log(`✅ Tarefa agendada para data específica: ${datetimeTaskId} (em 30 segundos)`);

  // 3. Agenda uma tarefa com expressão cron (executada a cada minuto)
  // Nota: Esta é uma implementação simplificada, pois o cron real requer uma biblioteca
  const cronTaskId = await scheduler.scheduleCron(
    "limpeza-logs",
    "manutencao",
    {
      acao: "limpar-logs",
      retencao: "7-dias",
    },
    "* * * * *", // a cada minuto
    "America/Sao_Paulo"
  );

  console.log(`✅ Tarefa agendada com cron: ${cronTaskId}`);

  // Aguarde por 40 segundos para observar a execução
  console.log("\n⏳ Aguardando execução das tarefas por 40 segundos...");

  // Mostra as tarefas agendadas
  await displayScheduledTasks(scheduler);

  // Espera 15 segundos e pausa uma tarefa
  await new Promise((resolve) => setTimeout(resolve, 15000));
  console.log("\n⏸️ Pausando tarefa de intervalo...");
  await scheduler.pauseTask(intervalTaskId);

  // Mostra as tarefas agendadas novamente
  await displayScheduledTasks(scheduler);

  // Espera mais 15 segundos e retoma a tarefa
  await new Promise((resolve) => setTimeout(resolve, 15000));
  console.log("\n▶️ Retomando tarefa de intervalo...");
  await scheduler.resumeTask(intervalTaskId);

  // Aguarda mais 10 segundos
  await new Promise((resolve) => setTimeout(resolve, 10000));

  // Mostra as tarefas agendadas uma última vez
  await displayScheduledTasks(scheduler);

  // Remove todas as tarefas
  console.log("\n🗑️ Removendo tarefas agendadas...");
  await scheduler.removeTask(intervalTaskId);
  await scheduler.removeTask(datetimeTaskId);
  await scheduler.removeTask(cronTaskId);

  // Desconecta
  await scheduler.disconnect();
  await queueManager.disconnect();

  console.log("\n✅ Exemplo concluído!");
}

// Configura os consumidores para processar as mensagens
async function setupConsumers(queueManager) {
  // Consumidor para relatórios
  await queueManager.consumer("relatorios", async (message) => {
    console.log(`\n📊 Processando relatório: ${JSON.stringify(message)}`);
    // Simula processamento
    await new Promise((resolve) => setTimeout(resolve, 500));
    console.log(`✅ Relatório gerado com sucesso`);
  });

  // Consumidor para emails
  await queueManager.consumer("emails", async (message) => {
    console.log(`\n📧 Enviando email: ${message.assunto} para ${message.destinatarios.join(", ")}`);
    // Simula processamento
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log(`✅ Email enviado com sucesso`);
  });

  // Consumidor para manutenção
  await queueManager.consumer("manutencao", async (message) => {
    console.log(`\n🧹 Executando manutenção: ${message.acao}`);
    // Simula processamento
    await new Promise((resolve) => setTimeout(resolve, 800));
    console.log(`✅ Manutenção concluída com sucesso`);
  });
}

// Exibe as tarefas agendadas
async function displayScheduledTasks(scheduler) {
  const tasks = await scheduler.listTasks();

  console.log("\n📋 Tarefas agendadas:");
  console.log("--------------------------------------------------");

  if (tasks.length === 0) {
    console.log("Nenhuma tarefa agendada.");
  } else {
    tasks.forEach((task) => {
      console.log(`ID: ${task.id}`);
      console.log(`Canal: ${task.channel}`);
      console.log(`Agendamento: ${task.schedule.type} (${task.schedule.value})`);
      console.log(`Status: ${task.enabled ? "Ativo" : "Pausado"}`);
      console.log(`Próxima execução: ${task.nextRun}`);
      console.log("--------------------------------------------------");
    });
  }
}

// Executa o exemplo
main().catch((error) => {
  console.error("\n❌ Erro:", error);
  process.exit(1);
});
