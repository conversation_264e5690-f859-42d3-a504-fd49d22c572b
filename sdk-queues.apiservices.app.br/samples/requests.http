### Health Check
GET http://localhost:4000/health

### Status de todas as filas
GET http://localhost:4000/api/queues

### Visualizar todas as mensagens de uma fila
GET http://localhost:4000/api/queues/notification/messages

### Visualizar mensagens com filtro específico
GET http://localhost:4000/api/queues/notification/messages?status=pending

### Visualizar apenas mensagens pendentes
GET http://localhost:4000/api/queues/notification/pending

### Visualizar apenas mensagens completadas
GET http://localhost:4000/api/queues/notification/completed

### Enviar notificação (formato simplificado direto)
POST http://localhost:4000/events
Content-Type: application/json

{
  "eventType": "notification",
  "data": {
    "userId": "123",
    "title": "Nova mensagem",
    "description": "Você recebeu uma nova mensagem",
    "type": "success",
    "notificationStatus": "created",
    "targetType": "system",
    "targetDate": "2025-02-25T16:24:37.296Z"
  }
}

### Enviar conquista (formato simplificado direto)
POST http://localhost:4000/events
Content-Type: application/json

{
  "eventType": "achievement",
  "data": {
    "userId": "123",
    "badgeCode": "BCS001"
  },
  "options": {
    "persistent": true
  }
}

### Enviar email (Tempo real)
POST http://localhost:4000/events
Content-Type: application/json

{
  "eventType": "email",
  "data": {
    "to": "<EMAIL>",
    "subject": "Notificação Instantânea",
    "body": "Esta é uma mensagem em tempo real!"
  },
  "options": {
    "persistent": false
  }
}

### Enviar evento de pagamento (Com prioridade)
POST http://localhost:4000/events
Content-Type: application/json

{
  "eventType": "payment",
  "data": {
    "orderId": "123",
    "amount": 99.99,
    "currency": "BRL"
  },
  "options": {
    "priority": 2,
    "attempts": 5
  }
}

### Enviar evento de pedido (Com delay)
POST http://localhost:4000/events
Content-Type: application/json

{
  "eventType": "order",
  "data": {
    "orderId": "123",
    "items": [
      {
        "id": "1",
        "quantity": 2
      }
    ]
  },
  "options": {
    "delay": 5000,
    "attempts": 3
  }
}

### Enviar um array de itens para uma fila
POST http://localhost:4000/events
Content-Type: application/json

{
  "eventType": "bulk-notification",
  "data": [
    {
      "userId": "123",
      "title": "Notificação 1",
      "description": "Primeira mensagem do lote",
      "type": "success",
      "notificationStatus": "created"
    },
    {
      "userId": "456",
      "title": "Notificação 2", 
      "description": "Segunda mensagem do lote",
      "type": "info",
      "notificationStatus": "created"
    },
    {
      "userId": "789",
      "title": "Notificação 3",
      "description": "Terceira mensagem do lote",
      "type": "warning",
      "notificationStatus": "created"
    }
  ],
  "options": {
    "persistent": true,
    "priority": 1
  }
}

### Limpar todas as mensagens pendentes de todas as filas
POST http://localhost:4000/api/queues/clear-pending
Content-Type: application/json

{}

### Excluir uma fila específica (notification-queue)
DELETE http://localhost:4000/api/queues/notification-queue

### Excluir uma fila específica (achievement-queue)
DELETE http://localhost:4000/api/queues/achievement-queue

### SCHEDULER E WORKFLOWS - MySQL Storage ###

### Criar uma tarefa agendada (CRON)
POST http://localhost:4000/api/scheduler/tasks
Content-Type: application/json

{
  "id": "sync-airtable",
  "cron": "0 */1 * * *",
  "data": {
    "description": "Sincronização com Airtable",
    "parameters": {
      "table": "Store items"
    }
  }
}

### Listar todas as tarefas agendadas
GET http://localhost:4000/api/scheduler/tasks

### Obter uma tarefa específica
GET http://localhost:4000/api/scheduler/tasks/sync-airtable

### Remover uma tarefa agendada
DELETE http://localhost:4000/api/scheduler/tasks/sync-airtable

### Criar um workflow
POST http://localhost:4000/api/workflows
Content-Type: application/json

{
  "id": "email-workflow",
  "steps": [
    { "type": "wait", "seconds": 60 },
    { "type": "email", "action": "send", "recipient": "<EMAIL>" },
    { "type": "conditional", "condition": "status == 'active'", "action": "continue" }
  ],
  "data": {
    "description": "Workflow de envio de emails com espera"
  }
}

### Listar todos os workflows
GET http://localhost:4000/api/workflows

### Obter um workflow específico
GET http://localhost:4000/api/workflows/email-workflow

### Remover um workflow
DELETE http://localhost:4000/api/workflows/email-workflow

### Executar um workflow
POST http://localhost:4000/api/workflows/email-workflow/execute
Content-Type: application/json

{
  "status": "active",
  "user": {
    "email": "<EMAIL>",
    "name": "Usuário Teste"
  }
}

### Obter todos os logs do sistema
GET http://localhost:4000/api/logs

### Obter logs filtrados por tipo
GET http://localhost:4000/api/logs?type=info

### Obter logs filtrados por fonte
GET http://localhost:4000/api/logs?source=scheduler

### Obter logs filtrados por ação
GET http://localhost:4000/api/logs?action=save_task

### Obter logs filtrados por status
GET http://localhost:4000/api/logs?status=success

### Obter logs com múltiplos filtros
GET http://localhost:4000/api/logs?source=workflow&type=error&limit=50

### Obter logs com período específico
GET http://localhost:4000/api/logs?startDate=2023-01-01T00:00:00Z&endDate=2023-12-31T23:59:59Z
