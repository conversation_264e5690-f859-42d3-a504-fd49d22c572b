import { Consumer } from "../dist";
import "dotenv/config";
import { RedisOptions } from "ioredis";

// Carregando configurações do Redis a partir das variáveis de ambiente
const REDIS_HOST = process.env.REDIS_HOST || "localhost";
const REDIS_PORT = parseInt(process.env.REDIS_PORT || "6379");
const REDIS_USERNAME = process.env.REDIS_USERNAME || "default";
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || "";

// Configuração base Redis
const baseRedisConfig: RedisOptions = {
  host: REDIS_HOST,
  port: REDIS_PORT,
};

// Adicionar credenciais apenas se não forem valores padrão/vazios
if (REDIS_USERNAME !== "default") {
  baseRedisConfig.username = REDIS_USERNAME;
}

if (REDIS_PASSWORD) {
  baseRedisConfig.password = REDIS_PASSWORD;
}

// Inicializando consumidores
console.log("🚀 Iniciando consumidores...");
console.log(`Conectando ao Redis: ${REDIS_HOST}:${REDIS_PORT}`);
console.log(
  "Credenciais: " +
    JSON.stringify({
      username: baseRedisConfig.username || "<não definido>",
      password: baseRedisConfig.password ? "******" : "<não definido>",
    })
);

// Consumidor de notificações
const notificationConsumer = new Consumer({
  queueName: "notification",
  ...baseRedisConfig,
  concurrency: 5,
});

// Consumidor de emails
const emailConsumer = new Consumer({
  queueName: "email",
  ...baseRedisConfig,
  concurrency: 3,
});

// Consumidor de pagamentos
const paymentConsumer = new Consumer({
  queueName: "payment",
  ...baseRedisConfig,
  concurrency: 2,
});

// Consumidor de pedidos
const orderConsumer = new Consumer({
  queueName: "order",
  ...baseRedisConfig,
  concurrency: 2,
});

// Registrar handlers
notificationConsumer.registerHandler(async (message, _) => {
  console.log("\n📬 Mensagem de notificação recebida:");
  console.log(JSON.stringify(message, null, 2));
  return true;
});

emailConsumer.registerHandler(async (message, _) => {
  console.log("\n📧 Mensagem de email recebida:");
  console.log(JSON.stringify(message, null, 2));
  return true;
});

paymentConsumer.registerHandler(async (message, _) => {
  console.log("\n💰 Mensagem de pagamento recebida:");
  console.log(JSON.stringify(message, null, 2));
  return true;
});

orderConsumer.registerHandler(async (message, _) => {
  console.log("\n📦 Mensagem de pedido recebida:");
  console.log(JSON.stringify(message, null, 2));
  return true;
});

// Iniciar consumidores
notificationConsumer.start().then(() => {
  console.log("📡 Consumidor de notificações iniciado");
});

emailConsumer.start().then(() => {
  console.log("📡 Consumidor de emails iniciado");
});

paymentConsumer.start().then(() => {
  console.log("📡 Consumidor de pagamentos iniciado");
});

orderConsumer.start().then(() => {
  console.log("📡 Consumidor de pedidos iniciado");
});

// Tratar sinais de encerramento
process.on("SIGINT", async () => {
  console.log("\nEncerrando consumidores...");

  await notificationConsumer.disconnect();
  await emailConsumer.disconnect();
  await paymentConsumer.disconnect();
  await orderConsumer.disconnect();

  console.log("👋 Consumidores encerrados com sucesso!");
  process.exit(0);
});
