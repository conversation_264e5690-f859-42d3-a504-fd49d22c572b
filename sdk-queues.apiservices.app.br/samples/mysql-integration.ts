import { QueueManager, Scheduler, Workflow, MySQLConfig, MySQLStorageFactory } from "../dist";
import "dotenv/config";

// Configuração de conexão com o Redis
const REDIS_HOST = process.env.REDIS_HOST || "localhost";
const REDIS_PORT = parseInt(process.env.REDIS_PORT || "6379");
const REDIS_USERNAME = process.env.REDIS_USERNAME || "default";
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || "redis123";

const redisConfig = {
  host: REDIS_HOST,
  port: REDIS_PORT,
  username: REDIS_USERNAME,
  password: REDIS_PASSWORD,
};

// Configuração de conexão com o MySQL
const mysqlConfig: MySQLConfig = {
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "queues_db",
};

// Log da configuração sem exibir a senha
console.log("MySQL config:", {
  host: mysqlConfig.host,
  port: mysqlConfig.port,
  user: mysqlConfig.user,
  database: mysqlConfig.database,
});

async function main() {
  console.log("🔄 Iniciando exemplo de integração com MySQL...");

  // Método 1: Usando a factory para criar os storages
  const schedulerStorage = MySQLStorageFactory.createSchedulerStorage(mysqlConfig);
  const workflowStorage = MySQLStorageFactory.createWorkflowStorage(mysqlConfig);

  // Método 2: Criando instâncias diretamente
  // const schedulerStorage = new MySQLSchedulerStorage(mysqlConfig);
  // const workflowStorage = new MySQLWorkflowStorage(mysqlConfig);

  // Inicializa componentes com MySQL storage
  const scheduler = new Scheduler({
    redis: redisConfig,
    storage: schedulerStorage,
  });

  const workflow = new Workflow({
    redis: redisConfig,
    storage: workflowStorage,
  });

  const queueManager = new QueueManager(redisConfig);

  // Configura os consumidores
  await setupConsumers(queueManager);

  try {
    // Agenda uma tarefa (armazenada no MySQL)
    const taskId = await scheduler.scheduleInterval(
      "mysql-example-task",
      "mysql-demo",
      {
        operation: "demonstracao",
        database: "MySQL",
        timestamp: new Date().toISOString(),
      },
      10000 // a cada 10 segundos
    );

    console.log(`✅ Tarefa agendada no MySQL com ID: ${taskId}`);

    // Define um workflow (armazenado no MySQL)
    const workflowId = await workflow.defineWorkflow("mysql-workflow-example", "Demonstração MySQL", [
      {
        id: "etapa1",
        channel: "mysql-demo",
        message: {
          step: 1,
          action: "processar-mysql",
        },
      },
      {
        id: "etapa2",
        channel: "mysql-demo",
        dependencies: ["etapa1"],
        message: {
          step: 2,
          action: "finalizar-mysql",
        },
      },
    ]);

    console.log(`✅ Workflow definido no MySQL com ID: ${workflowId}`);

    // Inicia o workflow
    await workflow.startWorkflow(workflowId);
    console.log(`▶️ Workflow iniciado`);

    // Aguarda por 30 segundos para ver o processamento
    console.log("\n⏳ Aguardando processamento (30 segundos)...");
    await new Promise((resolve) => setTimeout(resolve, 30000));

    // Lista tarefas agendadas (armazenadas no MySQL)
    const tasks = await scheduler.listTasks();
    console.log("\n📋 Tarefas agendadas no MySQL:");
    console.log(tasks);

    // Lista workflows (armazenados no MySQL)
    const workflows = await workflow.listWorkflows();
    console.log("\n📋 Workflows no MySQL:");
    console.log(workflows);

    // Limpa os recursos
    console.log("\n🧹 Limpando recursos...");
    await scheduler.removeTask(taskId);
  } catch (error) {
    console.error("❌ Erro durante a execução:", error);
  } finally {
    // Desconecta
    await scheduler.disconnect();
    await workflow.disconnect();
    await queueManager.disconnect();

    console.log("✅ Exemplo concluído!");
  }
}

// Configura os consumidores
async function setupConsumers(queueManager) {
  await queueManager.consumer("mysql-demo", async (message) => {
    console.log(`\n📊 Processando mensagem do MySQL: ${JSON.stringify(message)}`);

    // Se for uma mensagem de workflow, extrai informações adicionais
    if (message.workflowContext) {
      const { workflowId, stepId } = message.workflowContext;
      console.log(`🔄 Mensagem de workflow - ID: ${workflowId}, Etapa: ${stepId}`);

      // Completa a etapa do workflow
      const workflow = new Workflow({
        redis: redisConfig,
        storage: MySQLStorageFactory.createWorkflowStorage(mysqlConfig),
      });

      await workflow.completeStep(workflowId, stepId, true, {
        processadoEm: new Date().toISOString(),
        resultado: "sucesso",
      });

      console.log(`✅ Etapa ${stepId} do workflow ${workflowId} concluída`);
    }

    // Simula processamento
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log(`✅ Processamento concluído com sucesso`);
  });
}

// Executa o exemplo
main().catch((error) => {
  console.error("❌ Erro:", error);
  process.exit(1);
});
