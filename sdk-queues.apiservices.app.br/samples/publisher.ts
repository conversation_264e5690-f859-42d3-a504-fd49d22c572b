import { Publisher } from "../dist";
import "dotenv/config";
import { RedisOptions } from "ioredis";

// Carregando configurações do Redis a partir das variáveis de ambiente
const REDIS_HOST = process.env.REDIS_HOST || "localhost";
const REDIS_PORT = parseInt(process.env.REDIS_PORT || "6379");
const REDIS_USERNAME = process.env.REDIS_USERNAME || "default";
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || "";

// Configuração base Redis
const baseRedisConfig: RedisOptions = {
  host: REDIS_HOST,
  port: REDIS_PORT,
};

// Adicionar credenciais apenas se não forem valores padrão/vazios
if (REDIS_USERNAME !== "default") {
  baseRedisConfig.username = REDIS_USERNAME;
}

if (REDIS_PASSWORD) {
  baseRedisConfig.password = REDIS_PASSWORD;
}

// Inicializando consumidores
console.log("🚀 Iniciando consumidores...");
console.log(`Conectando ao Redis: ${REDIS_HOST}:${REDIS_PORT}`);
console.log(
  "Credenciais: " +
    JSON.stringify({
      username: baseRedisConfig.username || "<não definido>",
      password: baseRedisConfig.password ? "******" : "<não definido>",
    })
);

const publisher = new Publisher(baseRedisConfig);

async function runExamples() {
  try {
    // Exemplo 1: Notificação (persistente)
    const notificationData = {
      userId: "123",
      title: "Nova mensagem",
      message: "Você recebeu uma nova mensagem",
    };

    console.log("\n📤 Publishing to notification (persistent):");
    console.log({
      message: notificationData,
      options: {
        persistent: true,
        priority: 1,
        attempts: 3,
      },
    });

    await publisher.publish("notification", notificationData, {
      persistent: true,
      priority: 1,
      attempts: 3,
    });

    // Exemplo 2: Email (não persistente)
    const emailData = {
      to: "<EMAIL>",
      subject: "Notificação do sistema",
      body: "Esta é uma mensagem de teste.",
    };

    console.log("\n📤 Publishing to email (non-persistent):");
    console.log({
      message: emailData,
      options: {
        persistent: false,
      },
    });

    await publisher.publish("email", emailData, {
      persistent: false,
    });

    // Exemplo 3: Pagamento (com prioridade alta)
    const paymentData = {
      orderId: "order-123",
      amount: 100.5,
      currency: "BRL",
      paymentMethod: "credit_card",
    };

    console.log("\n📤 Publishing to payment (high priority):");
    console.log({
      message: paymentData,
      options: {
        persistent: true,
        priority: 10,
        attempts: 5,
      },
    });

    await publisher.publish("payment", paymentData, {
      persistent: true,
      priority: 10,
      attempts: 5,
    });

    // Exemplo 4: Pedido (com delay)
    const orderData = {
      orderId: "order-123",
      items: [
        { productId: "prod-1", quantity: 2 },
        { productId: "prod-2", quantity: 1 },
      ],
      customer: {
        id: "cust-123",
        name: "João Silva",
      },
    };

    console.log("\n📤 Publishing to order (with delay):");
    console.log({
      message: orderData,
      options: {
        persistent: true,
        delay: 5000, // 5 segundos
      },
    });

    await publisher.publish("order", orderData, {
      persistent: true,
      delay: 5000,
    });

    console.log("\n✅ Mensagens publicadas com sucesso!");

    // Ao usar com delay, aguardamos um pouco antes de desconectar
    await new Promise((resolve) => setTimeout(resolve, 6000));

    console.log("👋 Mensagens enviadas! O publisher continua ativo.");
    console.log("Pressione Ctrl+C para encerrar o processo.");
  } catch (error) {
    console.error("❌ Erro ao publicar mensagens:", error);
  }
}

// Executar exemplos
runExamples().catch((error) => {
  console.error("❌ Erro inesperado:", error);
});

// Tratar sinais de encerramento
process.on("SIGINT", async () => {
  console.log("\nEncerrando publisher...");

  await publisher.disconnect();

  console.log("👋 Publisher encerrado com sucesso!");
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\nEncerrando publisher...");

  await publisher.disconnect();

  console.log("👋 Publisher encerrado com sucesso!");
  process.exit(0);
});
