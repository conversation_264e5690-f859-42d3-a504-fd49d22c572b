# CDN Integration Guide

E-BaaS includes comprehensive CDN (Content Delivery Network) integration to accelerate file delivery and improve global performance. This guide covers setup, configuration, and usage.

## Overview

The CDN integration provides:
- **Global File Distribution**: Serve files from edge locations worldwide
- **Automatic Cache Management**: Smart caching with automatic purging
- **Multiple Provider Support**: Cloudflare, AWS CloudFront, and custom CDN providers
- **Performance Metrics**: Track bandwidth, requests, and cache hit ratios
- **CLI Management Tools**: Easy-to-use command-line interface

## Supported CDN Providers

### 1. Cloudflare (Recommended)
- Free tier available
- Global edge network
- Advanced caching rules
- Real-time purging
- Analytics and metrics

### 2. AWS CloudFront
- Enterprise-grade performance
- Integration with AWS ecosystem
- Custom SSL certificates
- Geographic restrictions

### 3. Custom CDN
- Support for any CDN with REST API
- Flexible configuration
- Custom purging endpoints

## Configuration

### Environment Variables

Add these environment variables to your `.env` file:

```env
# CDN Configuration
CDN_ENABLED=true
CDN_PROVIDER=cloudflare  # cloudflare, aws, custom
CDN_BASE_URL=https://cdn.yourdomain.com
CDN_CACHE_ENABLED=true
CDN_CACHE_MAX_AGE=3600  # 1 hour in seconds
CDN_PURGE_ON_UPDATE=true

# Cloudflare Configuration
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ZONE_ID=your_zone_id
CLOUDFLARE_ACCOUNT_ID=your_account_id

# AWS CloudFront Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_CLOUDFRONT_DISTRIBUTION_ID=your_distribution_id

# Custom CDN Configuration
CUSTOM_CDN_API_ENDPOINT=https://api.yourcdn.com
CUSTOM_CDN_API_KEY=your_api_key
CUSTOM_CDN_PURGE_ENDPOINT=https://api.yourcdn.com/purge
```

### Cloudflare Setup

1. **Create Cloudflare Account**
   - Sign up at https://cloudflare.com
   - Add your domain to Cloudflare

2. **Get API Token**
   ```bash
   # Navigate to: My Profile > API Tokens > Create Token
   # Use "Zone:Zone:Read, Zone:Page Rules:Edit" permissions
   ```

3. **Configure DNS**
   ```bash
   # Add CNAME record for your CDN subdomain
   # Type: CNAME
   # Name: cdn
   # Content: yourdomain.com
   # Proxy status: Proxied (orange cloud)
   ```

4. **Set Environment Variables**
   ```env
   CDN_ENABLED=true
   CDN_PROVIDER=cloudflare
   CDN_BASE_URL=https://cdn.yourdomain.com
   CLOUDFLARE_API_TOKEN=your_token_here
   CLOUDFLARE_ZONE_ID=your_zone_id
   CLOUDFLARE_ACCOUNT_ID=your_account_id
   ```

### AWS CloudFront Setup

1. **Create Distribution**
   ```bash
   # Go to AWS CloudFront Console
   # Create Distribution with your origin domain
   ```

2. **Configure Origin**
   ```bash
   # Origin Domain: yourdomain.com
   # Origin Path: /storage/v1/object/public
   # Origin Protocol Policy: HTTPS Only
   ```

3. **Set Caching Behavior**
   ```bash
   # Path Pattern: *
   # Viewer Protocol Policy: Redirect HTTP to HTTPS
   # Cache Policy: CachingOptimized
   ```

## API Usage

### File Upload with CDN
```javascript
// File upload automatically generates CDN URLs
const response = await fetch('/storage/v1/object/my-bucket/image.jpg', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_api_key',
    'Content-Type': 'image/jpeg'
  },
  body: fileBuffer
});

const result = await response.json();
console.log('CDN URL:', result.fullPath);
// Output: https://cdn.yourdomain.com/storage/my-bucket/image.jpg
```

### CDN Management Endpoints

#### Get CDN Information
```bash
GET /storage/v1/cdn/info
```

Response:
```json
{
  "enabled": true,
  "provider": "cloudflare",
  "cacheEnabled": true,
  "cacheMaxAge": 3600,
  "purgeOnUpdate": true
}
```

#### Purge CDN Cache
```bash
POST /storage/v1/cdn/purge
Content-Type: application/json

{
  "bucketName": "my-bucket",        # Optional: specific bucket
  "filePath": "path/to/file.jpg"    # Optional: specific file
}
```

#### Get CDN Metrics
```bash
GET /storage/v1/cdn/metrics?startDate=2024-01-01&endDate=2024-01-31
```

Response:
```json
{
  "requests": 150000,
  "bandwidth": 50000000000,
  "cacheHitRatio": 85.5,
  "topUrls": [
    {
      "url": "/storage/bucket/popular-image.jpg",
      "requests": 5000,
      "bandwidth": **********
    }
  ]
}
```

## CLI Management

### Installation
The CDN CLI is included with E-BaaS:

```bash
npm run cdn --help
```

### Commands

#### Check CDN Status
```bash
npm run cdn info
```

#### Purge Cache
```bash
# Purge specific file
npm run cdn purge -- --bucket my-bucket --file path/to/file.jpg

# Purge entire bucket
npm run cdn purge -- --bucket my-bucket

# Purge all cache
npm run cdn purge -- --all
```

#### View Metrics
```bash
# Current metrics
npm run cdn metrics

# Date range
npm run cdn metrics -- --start 2024-01-01 --end 2024-01-31
```

#### Test URL Generation
```bash
npm run cdn test-url my-bucket path/to/file.jpg
```

## Automatic Cache Management

### Cache Invalidation
The system automatically purges CDN cache when:
- Files are updated or overwritten
- Files are deleted
- Multipart uploads are completed

### Cache Headers
Files are served with appropriate cache headers:
```http
Cache-Control: public, max-age=3600, s-maxage=3600
Vary: Accept-Encoding
ETag: "md5-hash-of-file"
Last-Modified: Thu, 01 Jan 2024 00:00:00 GMT
```

### Custom Cache Control
Set custom cache duration per file:
```javascript
const uploadResponse = await storageService.uploadFile({
  bucketName: 'my-bucket',
  fileName: 'image.jpg',
  cacheControl: 'max-age=86400', // 24 hours
  // ... other options
}, fileBuffer);
```

## Performance Optimization

### Best Practices

1. **Enable Compression**
   ```env
   # Cloudflare automatically compresses text files
   # For AWS, enable Gzip in CloudFront
   ```

2. **Set Appropriate Cache Times**
   ```env
   # Static assets: 1 year
   CDN_CACHE_MAX_AGE=31536000
   
   # Dynamic content: 1 hour
   CDN_CACHE_MAX_AGE=3600
   ```

3. **Use Image Optimization**
   ```javascript
   // E-BaaS supports automatic image transformation
   const optimizedUrl = `/storage/v1/object/public/bucket/image.jpg?transform=w=800,h=600,f=webp,q=85`;
   ```

### Monitoring

#### Cache Hit Ratio
Monitor cache effectiveness:
```bash
npm run cdn metrics
# Target: >80% cache hit ratio
```

#### Bandwidth Usage
Track data transfer:
```javascript
const metrics = await storageService.getCDNMetrics();
console.log(`Monthly bandwidth: ${metrics.bandwidth / 1024 / 1024 / 1024} GB`);
```

## Troubleshooting

### Common Issues

#### CDN Not Working
1. Check environment variables:
   ```bash
   npm run cdn info
   ```

2. Verify DNS configuration:
   ```bash
   nslookup cdn.yourdomain.com
   ```

3. Test API connectivity:
   ```bash
   curl -I https://cdn.yourdomain.com/storage/test-bucket/test-file.jpg
   ```

#### Cache Not Purging
1. Check purge permissions:
   ```bash
   npm run cdn purge -- --bucket test-bucket
   ```

2. Verify API tokens have correct permissions

3. Check CDN provider logs

#### Slow Performance
1. Check cache hit ratio:
   ```bash
   npm run cdn metrics
   ```

2. Verify edge location coverage

3. Monitor origin server performance

### Debug Mode
Enable detailed logging:
```env
DEBUG=cdn:*
NODE_ENV=development
```

## Security Considerations

### API Token Security
- Store tokens in environment variables only
- Use tokens with minimal required permissions
- Rotate tokens regularly

### Access Control
- CDN respects bucket visibility settings
- Private buckets require authentication
- Signed URLs work with CDN

### SSL/TLS
- Always use HTTPS for CDN endpoints
- Verify SSL certificate validity
- Enable HSTS headers

## Cost Optimization

### Cloudflare
- Free tier: 100GB bandwidth/month
- Pro tier: $20/month for enhanced features
- Monitor usage in Cloudflare dashboard

### AWS CloudFront
- Pay-per-use pricing
- Free tier: 50GB data transfer
- Monitor costs in AWS Console

### Custom CDN
- Varies by provider
- Compare pricing models
- Consider geographic distribution needs

## Migration Guide

### From Direct File Serving
1. Enable CDN in configuration
2. Update client applications to use new URLs
3. Test thoroughly in staging environment
4. Monitor performance metrics

### Between CDN Providers
1. Set up new provider configuration
2. Update CDN_PROVIDER environment variable
3. Purge old CDN cache
4. Update DNS records if needed

## API Reference

See the complete API documentation for all CDN-related endpoints:
- [Storage API Documentation](./STORAGE_API.md)
- [API Reference](./API_REFERENCE.md)

## Support

For issues and questions:
- Check the troubleshooting section above
- Review CDN provider documentation
- Open an issue in the project repository