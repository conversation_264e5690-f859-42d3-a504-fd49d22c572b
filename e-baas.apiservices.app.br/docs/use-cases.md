# E-BaaS - Casos de Uso e Exemplos Práticos

## 🎯 Visão Geral

Este documento apresenta casos de uso reais e exemplos práticos de como usar E-BaaS para diferentes tipos de aplicações.

## 📱 Caso de Uso 1: App de E-commerce

### Cenário
Empresa desenvolve um app de e-commerce mobile e web com React Native + Next.js.

### Implementação

#### Setup Inicial
```typescript
import { createClient } from '@e-baas/sdk'

const ebaas = createClient(
  'https://loja-abc.ebaas.io',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
)
```

#### Autenticação de Clientes
```typescript
// Cadastro de cliente
const { user, error } = await ebaas.auth.signUp({
  email: '<EMAIL>',
  password: 'senhaSegura123',
  data: {
    nome: '<PERSON>',
    telefone: '11999999999'
  }
})

// Login com Google
const { data } = await ebaas.auth.signInWithOAuth({
  provider: 'google'
})
```

#### Catálogo de Produtos
```typescript
// Listar produtos com filtros
const { data: produtos } = await ebaas
  .from('produtos')
  .select(`
    id, nome, preco, descricao, categoria,
    imagens (url, alt),
    categoria (nome, slug)
  `)
  .eq('ativo', true)
  .gte('preco', filtros.precoMin)
  .lte('preco', filtros.precoMax)
  .ilike('nome', `%${busca}%`)
  .order('created_at', { ascending: false })
  .limit(20)

// Produto específico
const { data: produto } = await ebaas
  .from('produtos')
  .select('*, avaliacoes(nota, comentario, usuario(nome))')
  .eq('id', produtoId)
  .single()
```

#### Carrinho de Compras
```typescript
// Adicionar ao carrinho
const { data } = await ebaas
  .from('carrinho_itens')
  .insert({
    user_id: user.id,
    produto_id: produtoId,
    quantidade: 2,
    preco_unitario: produto.preco
  })

// Carrinho do usuário
const { data: carrinho } = await ebaas
  .from('carrinho_itens')
  .select(`
    *, 
    produto:produtos(nome, preco, imagem_url)
  `)
  .eq('user_id', user.id)
```

#### Processamento de Pedidos
```typescript
// Criar pedido
const { data: pedido } = await ebaas.rpc('processar_pedido', {
  user_id: user.id,
  endereco_entrega: enderecoSelecionado,
  forma_pagamento: 'cartao',
  cupom_desconto: 'PRIMEIRA10'
})

// Acompanhar pedido em tempo real
const canal = ebaas.channel(`pedido:${pedido.id}`)
canal.onPostgresChanges({
  event: 'UPDATE',
  schema: 'public',
  table: 'pedidos',
  filter: `id=eq.${pedido.id}`
}, (payload) => {
  setStatusPedido(payload.new.status)
})
```

### Admin do E-commerce (Workspace Admin)

```bash
# O dono da loja acessa via API REST
# Analytics de vendas
curl -H "Authorization: Bearer ${WORKSPACE_ADMIN_TOKEN}" \
  https://api.ebaas.io/admin/v1/workspace/analytics?period=30d

# Gerenciar produtos via API personalizada
curl -X POST https://loja-abc.ebaas.io/admin/produtos \
  -H "Authorization: Bearer ${WORKSPACE_ADMIN_TOKEN}" \
  -d '{"nome": "Novo Produto", "preco": 99.90}'
```

---

## 🏥 Caso de Uso 2: Sistema de Saúde

### Cenário
Clínica médica desenvolve sistema para agendamentos e prontuários.

### Implementação

#### Autenticação Multi-perfil
```typescript
const ebaas = createClient('https://clinica-saude.ebaas.io', API_KEY)

// Login médico
const { user } = await ebaas.auth.signIn({
  email: '<EMAIL>',
  password: 'senhaSegura'
})

// user.role pode ser: 'medico', 'enfermeiro', 'recepcionista'
```

#### Agendamentos com RLS
```sql
-- Política RLS: médicos só veem seus pacientes
CREATE POLICY "medicos_podem_ver_seus_pacientes" ON consultas
  FOR SELECT TO authenticated
  USING (medico_id = auth.uid());
```

```typescript
// Consultas do médico (automaticamente filtrado por RLS)
const { data: consultas } = await ebaas
  .from('consultas')
  .select(`
    id, data_hora, status,
    paciente:pacientes(nome, cpf, telefone),
    medico:usuarios(nome)
  `)
  .gte('data_hora', hoje)
  .order('data_hora')
```

#### Prontuário Eletrônico
```typescript
// Adicionar entrada no prontuário
const { data } = await ebaas
  .from('prontuario_entradas')
  .insert({
    paciente_id: pacienteId,
    medico_id: user.id,
    tipo: 'consulta',
    observacoes: 'Paciente apresenta...',
    prescricoes: [
      { medicamento: 'Paracetamol', dosagem: '500mg', frequencia: '8h' }
    ]
  })

// Histórico do paciente
const { data: historico } = await ebaas
  .from('prontuario_entradas')
  .select('*, medico:usuarios(nome, especialidade)')
  .eq('paciente_id', pacienteId)
  .order('created_at', { ascending: false })
```

#### Notificações em Tempo Real
```typescript
// Notificar recepção sobre nova consulta
const canal = ebaas.channel('recepcao')
canal.send({
  type: 'broadcast',
  event: 'nova_consulta',
  consulta_id: novaConsulta.id,
  paciente: paciente.nome,
  horario: novaConsulta.data_hora
})

// Recepção escuta notificações
canal.onBroadcast({ event: 'nova_consulta' }, (payload) => {
  exibirNotificacao(`Nova consulta: ${payload.paciente}`)
})
```

### Admin da Clínica

```bash
# Relatórios de atendimento
curl -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  https://api.ebaas.io/admin/v1/workspace/analytics?period=30d

# Gerenciar médicos e enfermeiros
curl -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  https://api.ebaas.io/admin/v1/workspace/users
```

---

## 🎓 Caso de Uso 3: Plataforma de Ensino

### Cenário
Escola online com cursos, aulas e exercícios.

### Implementação

#### Sistema de Cursos
```typescript
// Cursos disponíveis
const { data: cursos } = await ebaas
  .from('cursos')
  .select(`
    id, titulo, descricao, preco, categoria,
    instrutor:usuarios(nome, avatar_url),
    modulos(id, titulo, ordem, aulas_count)
  `)
  .eq('publicado', true)

// Matricular aluno
const { data } = await ebaas
  .from('matriculas')
  .insert({
    user_id: user.id,
    curso_id: cursoId,
    data_matricula: new Date(),
    status: 'ativo'
  })
```

#### Progresso do Aluno
```typescript
// Marcar aula como assistida
const { data } = await ebaas
  .from('progresso_aulas')
  .upsert({
    user_id: user.id,
    aula_id: aulaId,
    assistida: true,
    tempo_assistido: tempoSegundos,
    data_conclusao: new Date()
  })

// Calcular progresso do curso
const { data: progresso } = await ebaas.rpc('calcular_progresso_curso', {
  user_id: user.id,
  curso_id: cursoId
})
```

#### Exercícios e Avaliações
```typescript
// Submeter resposta de exercício
const { data } = await ebaas
  .from('exercicio_respostas')
  .insert({
    user_id: user.id,
    exercicio_id: exercicioId,
    respostas: respostasDoAluno,
    tempo_gasto: tempoSegundos
  })

// Calcular nota via Edge Function
const { data: resultado } = await ebaas.functions.invoke('calcular-nota', {
  body: {
    exercicio_id: exercicioId,
    respostas: respostasDoAluno
  }
})
```

#### Chat da Turma
```typescript
// Enviar mensagem na turma
const canal = ebaas.channel(`turma:${turmaId}`)
canal.send({
  type: 'broadcast',
  event: 'nova_mensagem',
  user_id: user.id,
  user_nome: user.nome,
  mensagem: 'Olá pessoal!',
  timestamp: new Date()
})

// Receber mensagens
canal.onBroadcast({ event: 'nova_mensagem' }, (payload) => {
  adicionarMensagemNoChat(payload)
})
```

---

## 🏢 Caso de Uso 4: SaaS Empresarial

### Cenário
Software de gestão empresarial com multi-tenancy.

### Implementação

#### Multi-tenancy com Workspaces
```typescript
// Cada empresa tem seu workspace
const empresa1 = createClient('https://empresa1.ebaas.io', 'key1')
const empresa2 = createClient('https://empresa2.ebaas.io', 'key2')

// Ou usando workspace_id
const { data } = await ebaas
  .from('projetos')
  .select('*')
  // RLS automaticamente filtra por workspace_id
```

#### Gestão de Projetos
```typescript
// Projetos da empresa
const { data: projetos } = await ebaas
  .from('projetos')
  .select(`
    id, nome, status, prazo,
    responsavel:usuarios(nome),
    tarefas:tarefas(count)
  `)
  .order('created_at', { ascending: false })

// Criar projeto
const { data: projeto } = await ebaas
  .from('projetos')
  .insert({
    nome: 'Website da Empresa',
    descricao: 'Redesign do site corporativo',
    responsavel_id: user.id,
    prazo: '2025-03-01',
    status: 'planejamento'
  })
  .select()
  .single()
```

#### Relatórios Financeiros
```typescript
// Relatório de vendas via RPC
const { data: relatorio } = await ebaas.rpc('relatorio_vendas', {
  data_inicio: '2025-01-01',
  data_fim: '2025-01-31',
  agrupado_por: 'mes'
})

// Gráfico de receita em tempo real
const canal = ebaas.channel('financeiro')
canal.onPostgresChanges({
  event: '*',
  schema: 'public',
  table: 'vendas'
}, (payload) => {
  atualizarGraficoReceita()
})
```

### Admin da Empresa

```bash
# Analytics da empresa
curl -H "Authorization: Bearer ${EMPRESA_ADMIN_TOKEN}" \
  https://api.ebaas.io/admin/v1/workspace/analytics

# Gerenciar usuários da empresa
curl -H "Authorization: Bearer ${EMPRESA_ADMIN_TOKEN}" \
  https://api.ebaas.io/admin/v1/workspace/users

# API keys para integrações
curl -X POST https://api.ebaas.io/admin/v1/workspace/api-keys \
  -H "Authorization: Bearer ${EMPRESA_ADMIN_TOKEN}" \
  -d '{"name": "Integração ERP", "permissions": ["read:all"]}'
```

---

## 🎮 Caso de Uso 5: Jogo Online

### Cenário
Jogo multiplayer com ranking, conquistas e chat.

### Implementação

#### Sistema de Jogadores
```typescript
// Perfil do jogador
const { data: jogador } = await ebaas
  .from('jogadores')
  .select(`
    id, nickname, nivel, experiencia, moedas,
    conquistas:jogador_conquistas(conquista:conquistas(*)),
    estatisticas(partidas_jogadas, vitorias, derrotas)
  `)
  .eq('user_id', user.id)
  .single()
```

#### Partidas em Tempo Real
```typescript
// Criar sala de jogo
const { data: sala } = await ebaas
  .from('salas_jogo')
  .insert({
    criador_id: user.id,
    nome: 'Sala do João',
    max_jogadores: 4,
    modo_jogo: 'battle_royale',
    status: 'aguardando'
  })

// Entrar na sala
const canal = ebaas.channel(`sala:${salaId}`)
canal.track({
  jogador_id: user.id,
  nickname: jogador.nickname,
  status: 'pronto'
})

// Sincronizar estado do jogo
canal.send({
  type: 'broadcast',
  event: 'acao_jogador',
  jogador_id: user.id,
  acao: 'mover',
  posicao: { x: 100, y: 200 }
})
```

#### Sistema de Ranking
```typescript
// Top 10 jogadores
const { data: ranking } = await ebaas
  .from('jogadores')
  .select('nickname, nivel, experiencia, vitorias')
  .order('experiencia', { ascending: false })
  .limit(10)

// Atualizar pontuação após partida
await ebaas.rpc('atualizar_pontuacao', {
  jogador_id: user.id,
  pontos_ganhos: 150,
  resultado: 'vitoria'
})
```

#### Chat Global
```typescript
// Chat do jogo
const chatGlobal = ebaas.channel('chat:global')
chatGlobal.send({
  type: 'broadcast',
  event: 'mensagem',
  jogador_id: user.id,
  nickname: jogador.nickname,
  mensagem: 'Alguém quer jogar?',
  timestamp: Date.now()
})
```

---

## 🏗️ Arquiteturas Comuns

### 1. Frontend Web + Mobile

```typescript
// Next.js (Web)
const ebaasWeb = createClient(EBAAS_URL, EBAAS_ANON_KEY, {
  auth: { persistSession: true }
})

// React Native (Mobile)
const ebaasMobile = createClient(EBAAS_URL, EBAAS_ANON_KEY, {
  auth: {
    storage: AsyncStorage, // React Native storage
    autoRefreshToken: true
  }
})
```

### 2. Microservices Backend

```typescript
// Serviço de usuários
const ebaasUsers = createClient(EBAAS_URL, SERVICE_KEY_USERS)

// Serviço de pagamentos
const ebaasPayments = createClient(EBAAS_URL, SERVICE_KEY_PAYMENTS)

// API Gateway usando E-BaaS
app.post('/api/checkout', async (req, res) => {
  const { data: pedido } = await ebaasPayments
    .from('pedidos')
    .insert(req.body)
  
  // Processar pagamento via Edge Function
  await ebaasPayments.functions.invoke('processar-pagamento', {
    body: { pedido_id: pedido.id }
  })
})
```

### 3. Serverless + E-BaaS

```typescript
// Vercel/Netlify Function
export default async function handler(req, res) {
  const ebaas = createClient(
    process.env.EBAAS_URL,
    process.env.EBAAS_SERVICE_KEY
  )
  
  const { data } = await ebaas.from('analytics').insert({
    evento: req.body.evento,
    timestamp: new Date(),
    user_id: req.body.user_id
  })
  
  res.json({ success: true })
}
```

## 🔧 Integrações Externas

### Webhooks

```typescript
// Receber webhook do payment provider
app.post('/webhooks/payment', async (req, res) => {
  const { payment_id, status } = req.body
  
  await ebaas
    .from('pagamentos')
    .update({ status })
    .eq('external_id', payment_id)
  
  // Notificar usuário via realtime
  const canal = ebaas.channel(`user:${user_id}`)
  canal.send({
    type: 'broadcast',
    event: 'pagamento_atualizado',
    status
  })
})
```

### APIs Externas

```typescript
// Edge Function para integração
const { data } = await ebaas.functions.invoke('integrar-crm', {
  body: {
    cliente: dadosCliente,
    acao: 'criar_lead'
  }
})
```

## 📊 Monitoramento e Analytics

### Custom Analytics

```typescript
// Rastrear eventos customizados
await ebaas
  .from('eventos_analytics')
  .insert({
    user_id: user.id,
    evento: 'produto_visualizado',
    metadata: {
      produto_id: produtoId,
      categoria: 'eletronicos',
      origem: 'busca'
    },
    timestamp: new Date()
  })

// Relatório customizado
const { data } = await ebaas.rpc('relatorio_conversao', {
  periodo: '30d',
  segmento: 'mobile'
})
```

---

## 🎯 Resumo dos Benefícios

### Para Desenvolvedores
- ✅ **Desenvolvimento rápido** - APIs prontas para usar
- ✅ **Escalabilidade automática** - Infraestrutura gerenciada
- ✅ **Realtime nativo** - WebSocket integrado
- ✅ **TypeScript completo** - Type safety garantido

### Para Empresas/Clientes
- ✅ **Time to market reduzido** - Foco no negócio
- ✅ **Custos otimizados** - Pay per use
- ✅ **Segurança enterprise** - RLS e auth robusto
- ✅ **Admin interface** - Controle total dos dados

### Para Platform Owners
- ✅ **Multi-tenancy nativo** - Isolamento por workspace
- ✅ **Analytics completos** - Visão global e por cliente
- ✅ **API management** - Controle de acesso granular
- ✅ **Monetização flexível** - Diferentes planos e limites

---

**Próximos Documentos:**
- [Features Complete](./features.md) - Lista completa de funcionalidades
- [SDK Guide](./sdk-guide.md) - Documentação técnica do SDK
- [API REST](./api-rest.md) - API para administradores