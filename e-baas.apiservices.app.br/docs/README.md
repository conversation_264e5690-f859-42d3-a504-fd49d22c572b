# E-BaaS Documentation

## 📚 Documentação Completa do E-BaaS

Bem-vindo à documentação oficial do E-BaaS (Enterprise Backend as a Service) - uma plataforma completa de backend-as-a-service similar ao Supabase.

### 📑 Índice da Documentação

#### 🎯 Para Desenvolvedores/Clientes
- **[SDK TypeScript](./sdk-guide.md)** - Guia completo do SDK para clientes finais
- **[Casos de Uso](./use-cases.md)** - Exemplos práticos e casos de uso do E-BaaS
- **[Funcionalidades](./features.md)** - Todas as funcionalidades e diferenciais

#### 🔧 Para Administradores
- **[API REST](./api-rest.md)** - Documentação completa da API REST para admins
- **[Admin Interface](./admin-interface.md)** - Guia da interface administrativa

#### ⚙️ Documentação Técnica
- **[Arquitetura](./architecture.md)** - Visão geral da arquitetura do sistema
- **[Deployment](./deployment.md)** - Guia de deployment e configuração

---

## 🎯 Público-Alvo

### 👨‍💻 **Desenvolvedores/Clientes Finais**
Desenvolvedores que usam E-BaaS como backend para suas aplicações.

**Use:** 
- ✅ SDK TypeScript (`@e-baas/sdk`)
- ✅ Documentação: [SDK Guide](./sdk-guide.md)

### 👨‍💼 **Administradores de Workspace** 
Donos de projetos que contrataram E-BaaS para gerenciar seus dados.

**Use:**
- ✅ API REST direta com token de admin
- ✅ Interface administrativa (quando disponível)
- ✅ Documentação: [API REST](./api-rest.md)

### 🔧 **Platform Admins**
Administradores da plataforma E-BaaS (donos da plataforma).

**Use:**
- ✅ API REST com privilégios de platform admin
- ✅ Acesso a todas as funcionalidades e analytics globais
- ✅ Documentação: [API REST](./api-rest.md)

---

## 🚀 Quick Start

### Para Desenvolvedores (SDK)
```bash
npm install @e-baas/sdk
```

```typescript
import { createClient } from '@e-baas/sdk'

const ebaas = createClient('https://my-project.ebaas.io', 'client-api-key')
const { data } = await ebaas.from('users').select('*')
```

### Para Admins (API REST)
```bash
curl -H "Authorization: Bearer admin-token" \
     https://api.ebaas.io/admin/v1/workspace
```

---

## 📋 Documentos Disponíveis

| Documento | Descrição | Público |
|-----------|-----------|---------|
| [SDK Guide](./sdk-guide.md) | Guia completo do TypeScript SDK | Desenvolvedores |
| [API REST](./api-rest.md) | Documentação da API REST | Admins |
| [Use Cases](./use-cases.md) | Casos de uso e exemplos | Todos |
| [Features](./features.md) | Funcionalidades e diferenciais | Todos |
| [Admin Interface](./admin-interface.md) | Interface administrativa | Admins |
| [Architecture](./architecture.md) | Arquitetura técnica | Técnico |

---

## 🆘 Suporte

- 📖 **Documentação**: Este diretório contém todas as informações
- 🐛 **Issues**: Para reportar bugs ou sugestões
- 💬 **Suporte**: Para dúvidas técnicas

**Versão da Documentação:** 1.0.0  
**Última Atualização:** Dezembro 2025