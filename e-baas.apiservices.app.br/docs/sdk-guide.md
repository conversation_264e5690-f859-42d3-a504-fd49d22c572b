# E-BaaS SDK TypeScript - G<PERSON><PERSON>to

## 📦 Instalação

```bash
npm install @e-baas/sdk
# ou
yarn add @e-baas/sdk
```

## 🚀 Quick Start

```typescript
import { createClient } from '@e-baas/sdk'

const ebaas = createClient(
  'https://your-project.ebaas.io', 
  'your-anon-key'
)
```

## 🔑 Configuração Avançada

```typescript
import { createClient } from '@e-baas/sdk'

const ebaas = createClient(
  'https://your-project.ebaas.io',
  'your-anon-key',
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    },
    realtime: {
      params: {
        eventsPerSecond: 10
      }
    },
    global: {
      headers: {
        'x-custom-header': 'custom-value'
      }
    }
  }
)
```

## 🔐 Authentication

### Sign Up
```typescript
const { user, session, error } = await ebaas.auth.signUp({
  email: '<EMAIL>',
  password: 'secure-password',
  name: '<PERSON>'
})
```

### Sign In
```typescript
const { user, session, error } = await ebaas.auth.signIn({
  email: '<EMAIL>',
  password: 'secure-password'
})
```

### OAuth Providers
```typescript
// Get OAuth URL
const { data } = await ebaas.auth.signInWithOAuth({
  provider: 'google',
  options: {
    redirectTo: 'https://yourapp.com/callback'
  }
})

// Redirect user to data.url
window.location.href = data.url
```

### Current User
```typescript
const user = await ebaas.auth.getUser()
if (user) {
  console.log('Logged in as:', user.email)
}
```

### Sign Out
```typescript
await ebaas.auth.signOut()
```

## 📊 Database Operations

### Select Data
```typescript
// Select all
const { data, error } = await ebaas
  .from('users')
  .select('*')

// Select specific columns
const { data, error } = await ebaas
  .from('users')
  .select('id, name, email')

// With filters
const { data, error } = await ebaas
  .from('users')
  .select('*')
  .eq('status', 'active')
  .order('created_at', { ascending: false })
  .limit(10)
```

### Insert Data
```typescript
const { data, error } = await ebaas
  .from('users')
  .insert([
    { name: 'John Doe', email: '<EMAIL>' },
    { name: 'Jane Smith', email: '<EMAIL>' }
  ])
```

### Update Data
```typescript
const { data, error } = await ebaas
  .from('users')
  .update({ status: 'inactive' })
  .eq('id', 123)
```

### Delete Data
```typescript
const { data, error } = await ebaas
  .from('users')
  .delete()
  .eq('status', 'inactive')
```

### Advanced Filtering
```typescript
const { data, error } = await ebaas
  .from('products')
  .select('*')
  .gte('price', 100)
  .lte('price', 500)
  .like('name', '%phone%')
  .in('category', ['electronics', 'gadgets'])
```

### RPC (Stored Procedures)
```typescript
const { data, error } = await ebaas.rpc('get_user_stats', {
  user_id: 123
})
```

## 📁 Storage

### Upload File
```typescript
const { data, error } = await ebaas.storage
  .from('avatars')
  .upload('user-123.jpg', file, {
    cacheControl: '3600',
    upsert: true
  })
```

### Download File
```typescript
const { data, error } = await ebaas.storage
  .from('avatars')
  .download('user-123.jpg')
```

### Image Transformations
```typescript
const { data, error } = await ebaas.storage
  .from('images')
  .download('photo.jpg', {
    transform: {
      width: 300,
      height: 300,
      resize: 'cover',
      format: 'webp',
      quality: 80
    }
  })
```

### Public URLs
```typescript
const { data } = ebaas.storage
  .from('avatars')
  .getPublicUrl('user-123.jpg')

console.log(data.publicUrl)
```

### Signed URLs (Private Files)
```typescript
const { data, error } = await ebaas.storage
  .from('private-docs')
  .createSignedUrl('document.pdf', 60) // 60 seconds

console.log(data.signedUrl)
```

### List Files
```typescript
const { data, error } = await ebaas.storage
  .from('avatars')
  .list('', {
    limit: 100,
    offset: 0,
    sortBy: { column: 'name', order: 'asc' }
  })
```

### File Operations
```typescript
// Move file
await ebaas.storage
  .from('avatars')
  .move('old-path.jpg', 'new-path.jpg')

// Copy file
await ebaas.storage
  .from('avatars')
  .copy('source.jpg', 'destination.jpg')

// Delete files
await ebaas.storage
  .from('avatars')
  .remove(['file1.jpg', 'file2.jpg'])
```

## ⚡ Realtime

### Database Changes
```typescript
const channel = ebaas.channel('realtime:users')

channel.onPostgresChanges({
  event: '*', // INSERT, UPDATE, DELETE, or *
  schema: 'public',
  table: 'users'
}, (payload) => {
  console.log('Change received!', payload)
})

channel.subscribe()
```

### Broadcast Messages
```typescript
const channel = ebaas.channel('room:chat')

// Send message
channel.send({
  type: 'broadcast',
  event: 'message',
  text: 'Hello World!',
  user_id: 123
})

// Listen for messages
channel.onBroadcast({ event: 'message' }, (payload) => {
  console.log('New message:', payload)
})

channel.subscribe()
```

### Presence Tracking
```typescript
const channel = ebaas.channel('room:game')

// Track user presence
channel.track({ 
  user_id: 123, 
  status: 'online',
  last_seen: new Date()
})

// Listen for presence changes
channel.onPresence({ event: 'sync' }, (payload) => {
  console.log('Online users:', payload)
})

channel.subscribe()
```

### Unsubscribe
```typescript
// Unsubscribe from specific channel
channel.unsubscribe()

// Remove all channels
ebaas.realtime.removeAllChannels()
```

## 🚀 Edge Functions

### Invoke Function
```typescript
const { data, error } = await ebaas.functions.invoke('hello-world', {
  body: { name: 'John' },
  headers: { 'Content-Type': 'application/json' }
})
```

### HTTP Methods
```typescript
// GET request
const { data, error } = await ebaas.functions.invoke('get-data', {
  method: 'GET'
})

// POST with data
const { data, error } = await ebaas.functions.invoke('process-payment', {
  method: 'POST',
  body: { amount: 100, currency: 'USD' }
})
```

### Function Management
```typescript
// List functions
const { data, error } = await ebaas.functions.list()

// Get function details
const { data, error } = await ebaas.functions.get('hello-world')
```

## 🔧 Error Handling

```typescript
import { EBaaSError } from '@e-baas/sdk'

try {
  const { data } = await ebaas.from('users').select('*')
} catch (error) {
  if (error instanceof EBaaSError) {
    console.error('E-BaaS Error:', error.message)
    console.error('Status:', error.status)
    console.error('Details:', error.details)
  }
}
```

## 🔍 TypeScript Support

### Typed Queries
```typescript
interface User {
  id: number
  name: string
  email: string
  created_at: string
}

const { data, error } = await ebaas
  .from<User>('users')
  .select('*')

// data is now typed as User[] | null
```

### Custom Types
```typescript
import type { 
  EBaaSClient,
  AuthUser,
  AuthSession,
  StorageFile
} from '@e-baas/sdk'

const client: EBaaSClient = createClient(url, key)
const user: AuthUser = await client.auth.getUser()
```

## 🌐 Convenção de URLs

O SDK espera que sua instância E-BaaS esteja configurada com as seguintes rotas:

- **Auth**: `/auth/v1/*`
- **Database**: `/rest/v1/*` 
- **Storage**: `/storage/v1/*`
- **Realtime**: WebSocket em `/realtime/*`
- **Functions**: `/functions/v1/*`

## 📱 Uso em Diferentes Ambientes

### React
```typescript
import { createClient } from '@e-baas/sdk'
import { useEffect, useState } from 'react'

const ebaas = createClient(url, key)

function App() {
  const [user, setUser] = useState(null)

  useEffect(() => {
    ebaas.auth.getUser().then(setUser)
    
    const { data: { subscription } } = ebaas.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  return <div>{user ? `Hello ${user.email}` : 'Not logged in'}</div>
}
```

### Vue.js
```typescript
import { createClient } from '@e-baas/sdk'
import { ref, onMounted } from 'vue'

const ebaas = createClient(url, key)

export default {
  setup() {
    const users = ref([])

    onMounted(async () => {
      const { data } = await ebaas.from('users').select('*')
      users.value = data
    })

    return { users }
  }
}
```

### Next.js
```typescript
// pages/api/users.ts
import { createClient } from '@e-baas/sdk'

const ebaas = createClient(
  process.env.EBAAS_URL!,
  process.env.EBAAS_SERVICE_KEY! // Use service key for server-side
)

export default async function handler(req, res) {
  const { data, error } = await ebaas.from('users').select('*')
  
  if (error) {
    return res.status(400).json({ error: error.message })
  }
  
  res.json(data)
}
```

## ⚙️ Configurações Avançadas

### Custom Headers
```typescript
const ebaas = createClient(url, key, {
  global: {
    headers: {
      'x-client-version': '1.0.0',
      'x-custom-header': 'value'
    }
  }
})
```

### Timeout Configuration
```typescript
// O SDK usa um timeout padrão de 30 segundos
// Para alterar, você pode configurar no HttpClient interno
```

### Debug Mode
```typescript
// Para debug, você pode interceptar as requests
const ebaas = createClient(url, key)

// Acessar o HttpClient interno para logging (desenvolvimento apenas)
console.log('SDK initialized with URL:', url)
```

## 🔗 Integração com Outros Serviços

### Webhook Handling
```typescript
// Em uma função edge ou API route
export async function POST(request: Request) {
  const payload = await request.json()
  
  // Processar webhook do E-BaaS
  if (payload.type === 'user.created') {
    await ebaas.from('user_profiles').insert({
      user_id: payload.record.id,
      created_at: new Date()
    })
  }
  
  return new Response('OK')
}
```

### Background Jobs
```typescript
// Usar com sistema de filas
await ebaas.functions.invoke('process-background-job', {
  body: { 
    task: 'send-email',
    user_id: 123,
    template: 'welcome'
  }
})
```

## 📚 Recursos Adicionais

- **[API REST Documentation](./api-rest.md)** - Para funcionalidades avançadas
- **[Use Cases](./use-cases.md)** - Exemplos práticos de implementação
- **[Features](./features.md)** - Lista completa de funcionalidades

## 🆘 Troubleshooting

### Erros Comuns

**"Invalid API Key"**
- Verifique se a API key está correta
- Confirme se está usando a key certa (anon vs service)

**"Cross-origin blocked"**
- Configure CORS no seu projeto E-BaaS
- Verifique se o domínio está autorizado

**"Rate limit exceeded"**
- Implemente retry logic com backoff
- Considere otimizar as queries

**"Network timeout"**
- Verifique a conectividade
- Considere aumentar timeout se necessário

---

**Versão do SDK:** 1.0.0  
**Compatibilidade:** E-BaaS v1.0+  
**Última Atualização:** Dezembro 2025