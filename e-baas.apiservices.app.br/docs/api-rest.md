# E-BaaS API REST - Documentação Completa

## 🎯 Para Administradores

Esta documentação é destinada aos **administradores** que gerenciam a plataforma E-BaaS ou seus workspaces. Para desenvolvedores que usam E-BaaS como backend, consulte o [SDK Guide](./sdk-guide.md).

## 🔑 Autenticação

### Tipos de Admin

#### 🏢 Platform Admin
**Quem:** Dono da plataforma E-BaaS  
**Acesso:** Todas as funcionalidades, todos os workspaces  
**Token:** JWT com `admin_role: "platform_admin"`

#### 👤 Workspace Admin  
**Quem:** Cliente que contratou E-BaaS  
**Acesso:** Apenas dados do seu workspace  
**Token:** JWT com `admin_role: "workspace_admin"` ou `"workspace_owner"`

### Obtendo Token de Admin

```bash
# Login como admin
curl -X POST https://api.ebaas.io/auth/v1/signin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'

# Resposta inclui admin_role no token
{
  "user": { ... },
  "accessToken": "eyJ...",
  "refreshToken": "...",
  "session": {
    "admin_role": "workspace_admin",
    "can_access_admin": true,
    "permissions": [...]
  }
}
```

### Headers de Autenticação

```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🏢 Platform Admin Endpoints

### Gerenciar Todos os Workspaces

```bash
# Listar todos os workspaces
GET /admin/v1/platform/workspaces
Authorization: Bearer {platform-admin-token}

# Resposta
{
  "workspaces": [
    {
      "id": "ws-123",
      "name": "Cliente ABC",
      "owner": {
        "id": "user-456",
        "email": "<EMAIL>",
        "fullName": "João Silva"
      },
      "createdAt": "2025-01-01T00:00:00Z",
      "isActive": true,
      "userCount": 25,
      "storageUsage": 1048576,
      "apiCallsThisMonth": 15000
    }
  ]
}
```

### Analytics Globais

```bash
# Analytics da plataforma inteira
GET /admin/v1/platform/analytics?period=30d
Authorization: Bearer {platform-admin-token}

# Resposta
{
  "period": "30d",
  "workspaces": {
    "total": 50,
    "active": 48,
    "growth": 12
  },
  "users": {
    "total": 1250,
    "verified": 1100,
    "growth": 8.5
  },
  "apiCalls": {
    "total": 2500000,
    "successful": 2487500,
    "errors": 12500
  },
  "storage": {
    "totalUsage": 1073741824,
    "totalFiles": 25000
  }
}
```

### Gerenciar Usuários Globalmente

```bash
# Listar todos os usuários (todos os workspaces)
GET /admin/v1/platform/users?page=1&limit=50&search=john
Authorization: Bearer {platform-admin-token}

# Resposta
{
  "users": [
    {
      "id": "user-123",
      "email": "<EMAIL>",
      "fullName": "John Doe",
      "role": "authenticated",
      "adminRole": "workspace_admin",
      "workspaceId": "ws-123",
      "isActive": true,
      "canAccessAdmin": true,
      "permissions": [...]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1250,
    "pages": 25
  }
}
```

### Atribuir Roles de Admin

```bash
# Tornar usuário admin de workspace
POST /admin/v1/platform/assign-admin-role
Authorization: Bearer {platform-admin-token}
Content-Type: application/json

{
  "userId": "user-456",
  "adminRole": "workspace_admin",
  "workspaceId": "ws-123"
}

# Resposta
{
  "message": "Admin role assigned successfully",
  "user": {
    "id": "user-456",
    "email": "<EMAIL>",
    "adminRole": "workspace_admin",
    "workspaceId": "ws-123",
    "permissions": [...]
  }
}
```

## 👤 Workspace Admin Endpoints

### Informações do Workspace

```bash
# Detalhes do workspace atual
GET /admin/v1/workspace
Authorization: Bearer {workspace-admin-token}

# Resposta
{
  "id": "ws-123",
  "name": "Minha Empresa",
  "description": "Sistema de gestão",
  "owner": {
    "id": "user-456",
    "email": "<EMAIL>",
    "fullName": "Maria Silva"
  },
  "createdAt": "2025-01-01T00:00:00Z",
  "isActive": true,
  "stats": {
    "userCount": 25,
    "adminCount": 3,
    "storageUsage": 1048576,
    "apiCallsThisMonth": 15000
  }
}
```

### Usuários do Workspace

```bash
# Listar usuários do workspace
GET /admin/v1/workspace/users?page=1&limit=20&search=ana
Authorization: Bearer {workspace-admin-token}

# Resposta
{
  "users": [
    {
      "id": "user-789",
      "email": "<EMAIL>",
      "fullName": "Ana Costa",
      "role": "editor",
      "adminRole": null,
      "provider": "email",
      "isActive": true,
      "createdAt": "2025-01-15T00:00:00Z",
      "lastSignIn": "2025-01-20T10:30:00Z",
      "permissions": ["read:all", "write:own"]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "pages": 2
  }
}
```

### Analytics do Workspace

```bash
# Analytics do workspace atual
GET /admin/v1/workspace/analytics?period=7d
Authorization: Bearer {workspace-admin-token}

# Resposta
{
  "period": "7d",
  "workspaceId": "ws-123",
  "users": {
    "total": 25,
    "active": 18,
    "growth": 2
  },
  "apiCalls": {
    "total": 15000,
    "successful": 14850,
    "errors": 150
  },
  "storage": {
    "usage": 1048576,
    "files": 250
  },
  "database": {
    "tables": 8,
    "records": 10000
  }
}
```

### API Keys do Workspace

```bash
# Listar API keys do workspace
GET /admin/v1/workspace/api-keys
Authorization: Bearer {workspace-admin-token}

# Resposta
{
  "apiKeys": [
    {
      "id": "key-123",
      "name": "Frontend App",
      "permissions": ["read:all", "write:own"],
      "expiresAt": "2025-12-31T23:59:59Z",
      "createdAt": "2025-01-01T00:00:00Z",
      "lastUsedAt": "2025-01-20T15:45:00Z",
      "isActive": true
    }
  ]
}

# Criar nova API key
POST /admin/v1/workspace/api-keys
Authorization: Bearer {workspace-admin-token}
Content-Type: application/json

{
  "name": "Mobile App",
  "permissions": ["read:own", "write:own"],
  "expiresAt": "2025-12-31T23:59:59Z"
}
```

### Database Management

```bash
# Schemas do banco de dados
GET /admin/v1/workspace/database/schemas
Authorization: Bearer {workspace-admin-token}

# Resposta
{
  "schemas": [
    {
      "name": "public",
      "tables": [
        {
          "name": "users",
          "columns": 8,
          "records": 25,
          "size": "2.1 MB"
        }
      ]
    }
  ]
}
```

### Storage Management

```bash
# Uso do storage
GET /admin/v1/workspace/storage/usage
Authorization: Bearer {workspace-admin-token}

# Resposta
{
  "totalSize": 1048576,
  "fileCount": 250,
  "bucketCount": 3,
  "quotaUsed": 1.5,
  "quotaLimit": 5368709120,
  "buckets": [
    {
      "name": "avatars",
      "fileCount": 25,
      "size": 524288,
      "public": true
    }
  ]
}
```

## 🔒 Permissões e Scoping

### Workspace Scoping

Workspace admins automaticamente têm suas requisições limitadas ao seu workspace:

```bash
# Esta requisição automaticamente filtra pelo workspace do admin
GET /admin/v1/workspace/users
Authorization: Bearer {workspace-admin-token}

# Internamente se torna:
# SELECT * FROM users WHERE workspace_id = 'ws-123'
```

### Verificação de Permissões

```bash
# Verificar se tem uma permissão específica
POST /admin/v1/check-permission
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "permission": "manage:workspace_users"
}

# Resposta
{
  "hasPermission": true,
  "permission": "manage:workspace_users",
  "userPermissions": [...],
  "isPlatformAdmin": false,
  "isWorkspaceAdmin": true
}
```

## 🔄 Integração com Operações Regulares

### Usando APIs Existentes como Admin

Os admins podem usar todas as APIs REST existentes com privilégios elevados:

```bash
# Operações de usuário (como admin)
GET /api/users
Authorization: Bearer {admin-token}

# Operações de banco (com privilégios)
POST /database/v1/tables
Authorization: Bearer {admin-token}

# Storage com acesso completo
GET /storage/v1/buckets
Authorization: Bearer {admin-token}
```

## 📊 Monitoramento e Logs

### Logs de Atividade

```bash
# Logs de atividade do workspace (futura implementação)
GET /admin/v1/workspace/activity-logs?page=1&limit=50
Authorization: Bearer {workspace-admin-token}

# Logs da plataforma (platform admin)
GET /admin/v1/platform/activity-logs?page=1&limit=100
Authorization: Bearer {platform-admin-token}
```

### Métricas em Tempo Real

```bash
# Métricas em tempo real
GET /admin/v1/workspace/metrics/realtime
Authorization: Bearer {workspace-admin-token}

# Resposta
{
  "activeUsers": 8,
  "activeConnections": 15,
  "requestsPerMinute": 25,
  "errorRate": 0.02
}
```

## 🚨 Error Handling

### Códigos de Erro Comuns

```bash
# 401 - Token inválido ou expirado
{
  "error": "Invalid or expired token",
  "details": "Token verification failed"
}

# 403 - Sem permissão de admin
{
  "error": "Admin access required",
  "details": "User does not have admin privileges"
}

# 403 - Tentativa de acessar outro workspace
{
  "error": "Access denied",
  "details": "Cannot access resources from other workspaces"
}

# 403 - Operação requer platform admin
{
  "error": "Platform admin access required",
  "details": "This operation requires platform administrator privileges"
}
```

## 🔧 Configuração de Desenvolvimento

### Testando Permissões Admin

```bash
# 1. Criar usuário admin via API ou banco
# 2. Atribuir admin_role
# 3. Fazer login e obter token
# 4. Testar endpoints admin

# Exemplo de setup inicial (via SQL ou seed):
UPDATE users SET admin_role = 'platform_admin' WHERE email = '<EMAIL>';
UPDATE users SET admin_role = 'workspace_admin' WHERE email = '<EMAIL>';
```

### Environment Variables

```bash
# No .env do E-BaaS
JWT_SECRET=seu-jwt-secret
PLATFORM_ADMIN_EMAIL=<EMAIL>

# Headers de desenvolvimento
X-Debug-Admin=true  # Para debug de permissões
```

## 🔄 Migração de Dados Existentes

### Convertendo Usuários em Admins

```sql
-- Tornar usuário platform admin
UPDATE users 
SET admin_role = 'platform_admin' 
WHERE email = '<EMAIL>';

-- Tornar usuário workspace admin
UPDATE users 
SET admin_role = 'workspace_admin',
    workspace_id = 'ws-123'
WHERE email = '<EMAIL>';

-- Tornar dono de workspace
UPDATE users 
SET admin_role = 'workspace_owner' 
WHERE id = (SELECT owner_id FROM workspaces WHERE id = 'ws-123');
```

## 📚 Recursos Relacionados

- **[SDK Guide](./sdk-guide.md)** - Para desenvolvedores que usam E-BaaS
- **[Admin Interface](./admin-interface.md)** - Interface web administrativa
- **[Use Cases](./use-cases.md)** - Casos de uso práticos

## 🆘 Troubleshooting Admin

### Problemas Comuns

**"Admin access required"**
- Verificar se usuário tem `admin_role` atribuído
- Confirmar que token inclui `can_access_admin: true`

**"Cannot access resources from other workspaces"**
- Workspace admins só acessam seu workspace
- Platform admins podem acessar qualquer workspace

**"Platform admin access required"**
- Operação específica para platform admins
- Workspace admins não têm essa permissão

---

**API Version:** v1  
**Base URL:** https://api.ebaas.io/admin/v1/  
**Última Atualização:** Dezembro 2025