const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'src/infra/database/data.sqlite');
console.log('Testing database connection to:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
  } else {
    console.log('✅ Connected to SQLite database successfully');
    
    // Test query
    db.all("SELECT name FROM sqlite_master WHERE type='table';", [], (err, rows) => {
      if (err) {
        console.error('❌ Error querying tables:', err.message);
      } else {
        console.log('📊 Available tables:', rows.map(row => row.name));
      }
      
      // Close database
      db.close((err) => {
        if (err) {
          console.error('❌ Error closing database:', err.message);
        } else {
          console.log('✅ Database connection closed');
        }
        process.exit(0);
      });
    });
  }
});