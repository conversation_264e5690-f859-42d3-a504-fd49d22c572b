{"version": 3, "file": "cdn-cli.js", "sourceRoot": "", "sources": ["../../src/scripts/cdn-cli.ts"], "names": [], "mappings": ";;;AAEA,yCAAoC;AACpC,gEAA4D;AAE5D,mBAAO;KACJ,IAAI,CAAC,SAAS,CAAC;KACf,WAAW,CAAC,2BAA2B,CAAC;KACxC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEpB,mBAAO;KACJ,OAAO,CAAC,MAAM,CAAC;KACf,WAAW,CAAC,mCAAmC,CAAC;KAChD,MAAM,CAAC,KAAK,IAAI,EAAE;IACjB,MAAM,UAAU,GAAG,IAAI,wBAAU,EAAE,CAAC;IAEpC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,cAAc,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,eAAe,UAAU,CAAC,WAAW,EAAE,IAAI,MAAM,EAAE,CAAC,CAAC;IAEjE,IAAI,UAAU,CAAC,SAAS,EAAE,EAAE;QAC1B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC3C,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC/C,OAAO,CAAC,GAAG,CACT,gBAAgB,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAClE,CAAC;gBACF,OAAO,CAAC,GAAG,CACT,sBAAsB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAC1D,CAAC;aACH;SACF;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;SACxD;KACF;AACH,CAAC,CAAC,CAAC;AAEL,mBAAO;KACJ,OAAO,CAAC,OAAO,CAAC;KAChB,WAAW,CAAC,iBAAiB,CAAC;KAC9B,MAAM,CAAC,uBAAuB,EAAE,sBAAsB,CAAC;KACvD,MAAM,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;KACjD,MAAM,CAAC,WAAW,EAAE,iBAAiB,CAAC;KACtC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IACxB,MAAM,UAAU,GAAG,IAAI,wBAAU,EAAE,CAAC;IAEpC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE;QAC3B,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAEzC,IAAI;QACF,IAAI,MAAM,CAAC;QAEX,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,MAAM,GAAG,MAAM,UAAU,CAAC,QAAQ,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;SACxC;aAAM,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE;YACzC,MAAM,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,oBAAoB,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;SACnE;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE;YACzB,MAAM,GAAG,MAAM,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACtD,OAAO,CAAC,GAAG,CAAC,wBAAwB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;SACvD;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACjB;QAED,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC3C,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;aAC/C;SACF;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,4BAA4B,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACjB;KACF;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;AACH,CAAC,CAAC,CAAC;AAEL,mBAAO;KACJ,OAAO,CAAC,SAAS,CAAC;KAClB,WAAW,CAAC,kBAAkB,CAAC;KAC/B,MAAM,CAAC,oBAAoB,EAAE,yBAAyB,CAAC;KACvD,MAAM,CAAC,kBAAkB,EAAE,uBAAuB,CAAC;KACnD,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IACxB,MAAM,UAAU,GAAG,IAAI,wBAAU,EAAE,CAAC;IAEpC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE;QAC3B,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACtE,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAEhE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAE1C,IAAI;QACF,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEhE,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YACtE,OAAO,CAAC,GAAG,CACT,sBAAsB,CACpB,OAAO,CAAC,SAAS;gBACjB,IAAI;gBACJ,IAAI;gBACJ,IAAI,CACL,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAClB,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,sBAAsB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAEvE,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC9B,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;oBACrC,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;oBAC1C,OAAO,CAAC,GAAG,CAAC,kBAAkB,GAAG,CAAC,QAAQ,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;oBAC/D,OAAO,CAAC,GAAG,CACT,mBAAmB,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CACjE,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;SACF;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;SACvC;KACF;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,CAAC,GAAG,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;AACH,CAAC,CAAC,CAAC;AAEL,mBAAO;KACJ,OAAO,CAAC,UAAU,CAAC;KACnB,WAAW,CAAC,yBAAyB,CAAC;KACtC,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC;KACnC,QAAQ,CAAC,QAAQ,EAAE,WAAW,CAAC;KAC/B,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE;IACjC,MAAM,UAAU,GAAG,IAAI,wBAAU,EAAE,CAAC;IAEpC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACvC,OAAO,CAAC,GAAG,CAAC,aAAa,MAAM,EAAE,CAAC,CAAC;IACnC,OAAO,CAAC,GAAG,CAAC,gBAAgB,QAAQ,EAAE,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,IAAI,UAAU,CAAC,SAAS,EAAE,EAAE;QAC1B,MAAM,MAAM,GAAG,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,cAAc,MAAM,EAAE,CAAC,CAAC;KACrC;SAAM;QACL,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CACT,qDAAqD,MAAM,IAAI,QAAQ,EAAE,CAC1E,CAAC;KACH;AACH,CAAC,CAAC,CAAC;AAEL,+BAA+B;AAC/B,mBAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAE5B,mCAAmC;AACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;IACjC,mBAAO,CAAC,UAAU,EAAE,CAAC;CACtB"}