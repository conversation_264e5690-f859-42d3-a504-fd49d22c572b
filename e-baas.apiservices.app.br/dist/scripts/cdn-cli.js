#!/usr/bin/env npx tsx
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const cdn_service_1 = require("../modules/storage/cdn.service");
commander_1.program
    .name("cdn-cli")
    .description("E-BaaS CDN Management CLI")
    .version("1.0.0");
commander_1.program
    .command("info")
    .description("Show CDN configuration and status")
    .action(async () => {
    const cdnService = new cdn_service_1.CDNService();
    console.log("🌐 CDN Configuration:");
    console.log(`  Enabled: ${cdnService.isEnabled()}`);
    console.log(`  Provider: ${cdnService.getProvider() || "None"}`);
    if (cdnService.isEnabled()) {
        console.log("\n📊 Testing CDN connectivity...");
        try {
            const metrics = await cdnService.getMetrics();
            console.log("✅ CDN connection successful");
            if (metrics) {
                console.log(`  Requests: ${metrics.requests}`);
                console.log(`  Bandwidth: ${(metrics.bandwidth / 1024 / 1024).toFixed(2)} MB`);
                console.log(`  Cache Hit Ratio: ${metrics.cacheHitRatio.toFixed(2)}%`);
            }
        }
        catch (error) {
            console.log("❌ CDN connection failed:", error.message);
        }
    }
});
commander_1.program
    .command("purge")
    .description("Purge CDN cache")
    .option("-b, --bucket <bucket>", "Bucket name to purge")
    .option("-f, --file <file>", "File path to purge")
    .option("-a, --all", "Purge all cache")
    .action(async (options) => {
    const cdnService = new cdn_service_1.CDNService();
    if (!cdnService.isEnabled()) {
        console.log("❌ CDN is not enabled");
        process.exit(1);
    }
    console.log("🗑️  Purging CDN cache...");
    try {
        let result;
        if (options.all) {
            result = await cdnService.purgeAll();
            console.log("🌐 Purging all cache...");
        }
        else if (options.bucket && options.file) {
            result = await cdnService.purgeFile(options.bucket, options.file);
            console.log(`📄 Purging file: ${options.bucket}/${options.file}`);
        }
        else if (options.bucket) {
            result = await cdnService.purgeBucket(options.bucket);
            console.log(`🗂️  Purging bucket: ${options.bucket}`);
        }
        else {
            console.log("❌ Please specify --bucket, --file, or --all");
            process.exit(1);
        }
        if (result.success) {
            console.log("✅ Cache purged successfully");
            if (result.purgeId) {
                console.log(`   Purge ID: ${result.purgeId}`);
            }
        }
        else {
            console.log(`❌ Failed to purge cache: ${result.error}`);
            process.exit(1);
        }
    }
    catch (error) {
        console.log(`❌ Error: ${error.message}`);
        process.exit(1);
    }
});
commander_1.program
    .command("metrics")
    .description("Show CDN metrics")
    .option("-s, --start <date>", "Start date (ISO format)")
    .option("-e, --end <date>", "End date (ISO format)")
    .action(async (options) => {
    const cdnService = new cdn_service_1.CDNService();
    if (!cdnService.isEnabled()) {
        console.log("❌ CDN is not enabled");
        process.exit(1);
    }
    const startDate = options.start ? new Date(options.start) : undefined;
    const endDate = options.end ? new Date(options.end) : undefined;
    console.log("📊 Fetching CDN metrics...");
    try {
        const metrics = await cdnService.getMetrics(startDate, endDate);
        if (metrics) {
            console.log("\n📈 CDN Metrics:");
            console.log(`  Total Requests: ${metrics.requests.toLocaleString()}`);
            console.log(`  Total Bandwidth: ${(metrics.bandwidth /
                1024 /
                1024 /
                1024).toFixed(2)} GB`);
            console.log(`  Cache Hit Ratio: ${metrics.cacheHitRatio.toFixed(2)}%`);
            if (metrics.topUrls.length > 0) {
                console.log("\n🔝 Top URLs:");
                metrics.topUrls.forEach((url, index) => {
                    console.log(`  ${index + 1}. ${url.url}`);
                    console.log(`     Requests: ${url.requests.toLocaleString()}`);
                    console.log(`     Bandwidth: ${(url.bandwidth / 1024 / 1024).toFixed(2)} MB`);
                });
            }
        }
        else {
            console.log("❌ No metrics available");
        }
    }
    catch (error) {
        console.log(`❌ Error fetching metrics: ${error.message}`);
        process.exit(1);
    }
});
commander_1.program
    .command("test-url")
    .description("Test CDN URL generation")
    .argument("<bucket>", "Bucket name")
    .argument("<path>", "File path")
    .action(async (bucket, filePath) => {
    const cdnService = new cdn_service_1.CDNService();
    console.log("🔗 URL Generation Test:");
    console.log(`  Bucket: ${bucket}`);
    console.log(`  File Path: ${filePath}`);
    console.log("");
    if (cdnService.isEnabled()) {
        const cdnUrl = cdnService.generateFileUrl(bucket, filePath);
        console.log(`✅ CDN URL: ${cdnUrl}`);
    }
    else {
        console.log("❌ CDN is not enabled");
        console.log(`   Direct URL would be: /storage/v1/object/public/${bucket}/${filePath}`);
    }
});
// Parse command line arguments
commander_1.program.parse(process.argv);
// Show help if no command provided
if (!process.argv.slice(2).length) {
    commander_1.program.outputHelp();
}
//# sourceMappingURL=cdn-cli.js.map