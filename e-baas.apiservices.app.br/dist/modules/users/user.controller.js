"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const infra_1 = require("../../infra");
const user_service_1 = __importDefault(require("./user.service"));
const user_useCases_1 = __importDefault(require("./user.useCases"));
const user_dto_1 = require("./dto/user.dto");
const controller = (0, express_1.Router)();
const userUseCases = new user_useCases_1.default();
const userServices = new user_service_1.default(userUseCases);
controller.get("/users", (req, res) => userServices.getAll(req, res));
controller.get("/users/:id", (req, res) => userServices.getOne(req, res));
controller.post("/users", (0, infra_1.validator)(user_dto_1.UserDto), (req, res) => userServices.create(req, res));
controller.put("/users/:id", (0, infra_1.validator)(user_dto_1.UserDto), (req, res) => userServices.update(req, res));
controller.delete("/users/:id", (req, res) => userServices.delete(req, res));
exports.default = controller;
//# sourceMappingURL=user.controller.js.map