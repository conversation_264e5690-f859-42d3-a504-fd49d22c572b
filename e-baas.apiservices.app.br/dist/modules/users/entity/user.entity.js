"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const config_1 = __importDefault(require("config"));
let User = class User {
    async hashPassword() {
        if (this.password && !this.password.startsWith('$2')) { // Check if not already hashed
            const saltRounds = config_1.default.security?.bcryptSaltRounds || 10;
            this.password = await bcryptjs_1.default.hash(this.password, saltRounds);
        }
    }
    async comparePassword(candidatePassword) {
        if (!this.password)
            return false;
        return bcryptjs_1.default.compare(candidatePassword, this.password);
    }
    // Get user's full name
    get fullName() {
        return [this.firstName, this.lastName].filter(Boolean).join(' ') || this.email;
    }
    // Check if user email is verified
    get isVerified() {
        return this.emailVerified && !!this.confirmedAt;
    }
    // Check if user is banned
    get isBanned() {
        return this.bannedUntil ? new Date() < this.bannedUntil : false;
    }
    // Get safe user data (without sensitive fields)
    toSafeObject() {
        const { password, emailVerifyToken, passwordResetToken, ...safeUser } = this;
        return safeUser;
    }
    // Get JWT payload
    getJwtPayload() {
        return {
            sub: this.id,
            user_id: this.id,
            email: this.email,
            role: this.role,
            admin_role: this.adminRole,
            workspaceId: this.workspaceId,
            workspace_id: this.workspaceId,
            email_verified: this.emailVerified,
            app_metadata: this.rawAppMetaData || {},
            user_metadata: this.rawUserMetaData || {},
            permissions: this.getPermissions(),
            can_access_admin: this.canAccessAdmin,
            is_platform_admin: this.isPlatformAdmin,
            is_workspace_admin: this.isWorkspaceAdmin,
            aud: 'authenticated',
            exp: Math.floor(Date.now() / 1000) + (60 * 60),
            iat: Math.floor(Date.now() / 1000),
        };
    }
    // Get user permissions based on role and admin role
    getPermissions() {
        const permissions = [];
        // Admin role permissions (higher level)
        switch (this.adminRole) {
            case 'platform_admin':
                permissions.push('platform:manage:all', 'platform:read:all', 'platform:write:all', 'platform:delete:all', 'manage:all_workspaces', 'manage:all_users', 'manage:platform_settings', 'manage:billing', 'manage:api_keys', 'view:analytics:global');
                break;
            case 'workspace_owner':
                permissions.push('workspace:manage:all', 'workspace:read:all', 'workspace:write:all', 'workspace:delete:all', 'manage:workspace_settings', 'manage:workspace_users', 'manage:workspace_billing', 'manage:workspace_api_keys', 'view:analytics:workspace');
                break;
            case 'workspace_admin':
                permissions.push('workspace:manage:content', 'workspace:read:all', 'workspace:write:all', 'manage:workspace_users', 'manage:database', 'manage:storage', 'manage:functions', 'view:analytics:workspace');
                break;
            case 'service_role':
                permissions.push('api:full_access', 'bypass:rls', 'service:read:all', 'service:write:all');
                break;
        }
        // Regular user role permissions (application level)
        switch (this.role) {
            case 'admin':
                permissions.push('read:all', 'write:all', 'delete:all', 'manage:workspace', 'manage:users', 'manage:database', 'manage:storage', 'manage:functions', 'manage:auth');
                break;
            case 'editor':
                permissions.push('read:all', 'write:own', 'write:shared', 'delete:own', 'manage:own_profile');
                break;
            case 'viewer':
                permissions.push('read:own', 'read:shared', 'manage:own_profile');
                break;
            case 'authenticated':
            default:
                permissions.push('read:own', 'write:own', 'delete:own', 'manage:own_profile');
                break;
        }
        return [...new Set(permissions)]; // Remove duplicates
    }
    // Check if user is platform admin
    get isPlatformAdmin() {
        return this.adminRole === 'platform_admin';
    }
    // Check if user is workspace owner or admin
    get isWorkspaceAdmin() {
        return ['workspace_owner', 'workspace_admin', 'platform_admin'].includes(this.adminRole || '');
    }
    // Check if user can access admin interface
    get canAccessAdmin() {
        return this.isWorkspaceAdmin || this.isPlatformAdmin;
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255 }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100, select: false, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "first_name", type: "varchar", length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "firstName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "last_name", type: "varchar", length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "lastName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], User.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 50, default: 'authenticated' }),
    __metadata("design:type", String)
], User.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "admin_role", type: "varchar", length: 50, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "adminRole", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "auth_provider", type: "varchar", length: 50, default: 'email' }),
    __metadata("design:type", String)
], User.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "provider_id", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "providerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "oauth_providers", type: "jsonb", nullable: true }),
    __metadata("design:type", Object)
], User.prototype, "oauthProviders", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_email_verified", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isEmailVerified", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "email_verified", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "emailVerified", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "email_verify_token", type: "varchar", length: 500, nullable: true, select: false }),
    __metadata("design:type", String)
], User.prototype, "emailVerifyToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "password_reset_token", type: "varchar", length: 500, nullable: true, select: false }),
    __metadata("design:type", String)
], User.prototype, "passwordResetToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "password_reset_expires", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "passwordResetExpires", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "magic_link_token", type: "varchar", length: 500, nullable: true, select: false }),
    __metadata("design:type", String)
], User.prototype, "magicLinkToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "magic_link_expires", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "magicLinkExpires", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "last_sign_in", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "lastSignIn", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "last_sign_in_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "lastSignInAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sign_in_count", type: "integer", default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "signInCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_active", type: "boolean", default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_anonymous", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isAnonymous", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "jsonb", nullable: true }),
    __metadata("design:type", Object)
], User.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "raw_app_meta_data", type: "jsonb", nullable: true }),
    __metadata("design:type", Object)
], User.prototype, "rawAppMetaData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "raw_user_meta_data", type: "jsonb", nullable: true }),
    __metadata("design:type", Object)
], User.prototype, "rawUserMetaData", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], User.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "confirmed_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "confirmedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "banned_until", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "bannedUntil", void 0);
__decorate([
    (0, typeorm_1.OneToMany)("Workspace", "owner"),
    __metadata("design:type", Array)
], User.prototype, "workspaces", void 0);
__decorate([
    (0, typeorm_1.OneToMany)("RefreshToken", "user"),
    __metadata("design:type", Array)
], User.prototype, "refreshTokens", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    (0, typeorm_1.BeforeUpdate)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], User.prototype, "hashPassword", null);
User = __decorate([
    (0, typeorm_1.Entity)("users"),
    (0, typeorm_1.Index)(["email", "workspaceId"], { unique: true })
], User);
exports.User = User;
//# sourceMappingURL=user.entity.js.map