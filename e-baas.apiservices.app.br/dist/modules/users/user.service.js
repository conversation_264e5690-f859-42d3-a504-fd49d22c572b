"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class UserServices {
    constructor(userUseCases) {
        this.userUseCases = userUseCases;
    }
    async getAll(req, res) {
        const response = await this.userUseCases.getAll(req?.query);
        return res.json(response);
    }
    async getOne(req, res) {
        const response = await this.userUseCases.getOne(req?.params?.id);
        return res.json(response);
    }
    async create(req, res) {
        const data = req.body;
        const response = await this.userUseCases.create(data);
        return res.json(response);
    }
    async update(req, res) {
        const data = req.body;
        const userId = req.params.id;
        const response = await this.userUseCases.update(userId, data);
        return res.status(200).json(response);
    }
    async delete(req, res) {
        const response = await this.userUseCases.delete(req?.params?.id);
        return res.status(204).json(response);
    }
}
exports.default = UserServices;
//# sourceMappingURL=user.service.js.map