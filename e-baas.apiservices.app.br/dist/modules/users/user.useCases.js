"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const repository_1 = require("../../infra/repository");
const errorHandlers_1 = require("../../infra/errorHandlers");
class UserUseCases {
    constructor() { }
    async getAll() {
        try {
            return await repository_1.userRepository.find();
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async getOne(id) {
        try {
            const user = await repository_1.userRepository.findOneBy({ id });
            if (!user) {
                throw errorHandlers_1.ErrorHandler.NotFound("User not found");
            }
            return user;
        }
        catch (error) {
            if (error instanceof Error && error.message === "User not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async create(data) {
        try {
            return await repository_1.userRepository.save(data);
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async update(id, data) {
        try {
            await this.getOne(id);
            await repository_1.userRepository.update(id, data);
            return await this.getOne(id);
        }
        catch (error) {
            if (error instanceof Error && error.message === "User not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async delete(id) {
        try {
            await this.getOne(id);
            await repository_1.userRepository.delete(id);
        }
        catch (error) {
            if (error instanceof Error && error.message === "User not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
}
exports.default = UserUseCases;
//# sourceMappingURL=user.useCases.js.map