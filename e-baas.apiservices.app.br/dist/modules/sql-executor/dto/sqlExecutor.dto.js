"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SqlQueryHistoryDto = exports.SqlValidationDto = exports.SqlExecutorDto = exports.SqlQueryType = void 0;
const class_validator_1 = require("class-validator");
var SqlQueryType;
(function (SqlQueryType) {
    SqlQueryType["SELECT"] = "SELECT";
    SqlQueryType["INSERT"] = "INSERT";
    SqlQueryType["UPDATE"] = "UPDATE";
    SqlQueryType["DELETE"] = "DELETE";
    SqlQueryType["CREATE"] = "CREATE";
    SqlQueryType["ALTER"] = "ALTER";
    SqlQueryType["DROP"] = "DROP";
    SqlQueryType["TRUNCATE"] = "TRUNCATE";
})(SqlQueryType = exports.SqlQueryType || (exports.SqlQueryType = {}));
class SqlExecutorDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SqlExecutorDto.prototype, "query", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SqlExecutorDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], SqlExecutorDto.prototype, "params", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SqlExecutorDto.prototype, "readonly", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SqlExecutorDto.prototype, "timeout", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SqlExecutorDto.prototype, "explainPlan", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SqlExecutorDto.prototype, "userId", void 0);
exports.SqlExecutorDto = SqlExecutorDto;
class SqlValidationDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SqlValidationDto.prototype, "query", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SqlValidationDto.prototype, "strict", void 0);
exports.SqlValidationDto = SqlValidationDto;
class SqlQueryHistoryDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SqlQueryHistoryDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SqlQueryHistoryDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SqlQueryHistoryDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SqlQueryHistoryDto.prototype, "offset", void 0);
exports.SqlQueryHistoryDto = SqlQueryHistoryDto;
//# sourceMappingURL=sqlExecutor.dto.js.map