"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const admin_service_1 = require("./admin.service");
const admin_auth_middleware_1 = require("../../infra/middlewares/admin-auth.middleware");
const admin_roles_dto_1 = require("../auth/dto/admin-roles.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const backup_controller_1 = __importDefault(require("../backup/backup.controller"));
const adminRouter = (0, express_1.Router)();
const adminService = new admin_service_1.AdminService();
// Helper function for validation
const validateDto = async (DtoClass, data) => {
    const dto = (0, class_transformer_1.plainToClass)(DtoClass, data);
    const errors = await (0, class_validator_1.validate)(dto);
    if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
    }
    return dto;
};
// ==== PLATFORM ADMIN ROUTES ====
// Get all workspaces (Platform Admin only)
adminRouter.get("/platform/workspaces", admin_auth_middleware_1.requirePlatformAdmin, async (req, res) => {
    try {
        const workspaces = await adminService.getAllWorkspaces();
        return res.status(200).json({ workspaces });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get global analytics (Platform Admin only)
adminRouter.get("/platform/analytics", admin_auth_middleware_1.requirePlatformAdmin, async (req, res) => {
    try {
        const { period = '30d' } = req.query;
        const analytics = await adminService.getGlobalAnalytics(period);
        return res.status(200).json(analytics);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Assign admin roles (Platform Admin only)
adminRouter.post("/platform/assign-admin-role", admin_auth_middleware_1.requirePlatformAdmin, async (req, res) => {
    try {
        const assignDto = await validateDto(admin_roles_dto_1.AssignAdminRoleDto, req.body);
        const result = await adminService.assignAdminRole(assignDto);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get all users across all workspaces (Platform Admin only)
adminRouter.get("/platform/users", admin_auth_middleware_1.requirePlatformAdmin, async (req, res) => {
    try {
        const { page = 1, limit = 50, search } = req.query;
        const users = await adminService.getAllUsers({
            page: Number(page),
            limit: Number(limit),
            search: search
        });
        return res.status(200).json(users);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// ==== WORKSPACE ADMIN ROUTES ====
// Get workspace info (accessible to workspace admins and platform admins)
adminRouter.get("/workspace", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const workspaceId = req.user?.workspaceId;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID required' });
        }
        const workspace = await adminService.getWorkspaceDetails(workspaceId);
        return res.status(200).json(workspace);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get workspace users
adminRouter.get("/workspace/users", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const workspaceId = req.user?.workspaceId;
        const { page = 1, limit = 50, search } = req.query;
        const users = await adminService.getWorkspaceUsers(workspaceId, {
            page: Number(page),
            limit: Number(limit),
            search: search
        });
        return res.status(200).json(users);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get workspace analytics
adminRouter.get("/workspace/analytics", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const workspaceId = req.user?.workspaceId;
        const { period = '30d' } = req.query;
        const analytics = await adminService.getWorkspaceAnalytics(workspaceId, period);
        return res.status(200).json(analytics);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get workspace API keys
adminRouter.get("/workspace/api-keys", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const workspaceId = req.user?.workspaceId;
        const apiKeys = await adminService.getWorkspaceApiKeys(workspaceId);
        return res.status(200).json({ apiKeys });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Create workspace API key
adminRouter.post("/workspace/api-keys", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const workspaceId = req.user?.workspaceId;
        const { name, permissions, expiresAt } = req.body;
        const apiKey = await adminService.createWorkspaceApiKey(workspaceId, {
            name,
            permissions,
            expiresAt,
            createdBy: req.user?.id
        });
        return res.status(201).json(apiKey);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get workspace database schemas
adminRouter.get("/workspace/database/schemas", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const workspaceId = req.user?.workspaceId;
        const schemas = await adminService.getWorkspaceDatabaseSchemas(workspaceId);
        return res.status(200).json({ schemas });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get workspace storage usage
adminRouter.get("/workspace/storage/usage", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const workspaceId = req.user?.workspaceId;
        const usage = await adminService.getWorkspaceStorageUsage(workspaceId);
        return res.status(200).json(usage);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// ==== COMMON ADMIN ROUTES ====
// Get current admin user info
adminRouter.get("/me", admin_auth_middleware_1.requireAdmin, async (req, res) => {
    try {
        const user = req.user;
        return res.status(200).json({
            user: {
                ...user,
                adminCapabilities: {
                    canManageWorkspaces: user?.isPlatformAdmin,
                    canManageUsers: user?.isWorkspaceAdmin,
                    canViewGlobalAnalytics: user?.isPlatformAdmin,
                    canManageApiKeys: user?.isWorkspaceAdmin,
                    canManageDatabase: user?.isWorkspaceAdmin,
                    canManageStorage: user?.isWorkspaceAdmin
                }
            }
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Check admin permissions
adminRouter.post("/check-permission", admin_auth_middleware_1.requireAdmin, async (req, res) => {
    try {
        const { permission } = req.body;
        const hasPermission = req.user?.permissions.includes(permission) || req.user?.isPlatformAdmin;
        return res.status(200).json({
            hasPermission,
            permission,
            userPermissions: req.user?.permissions,
            isPlatformAdmin: req.user?.isPlatformAdmin,
            isWorkspaceAdmin: req.user?.isWorkspaceAdmin
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// ==== BACKUP ROUTES ====
adminRouter.use(backup_controller_1.default);
exports.default = adminRouter;
//# sourceMappingURL=admin.controller.js.map