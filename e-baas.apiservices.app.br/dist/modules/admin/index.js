"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = exports.adminController = void 0;
var admin_controller_1 = require("./admin.controller");
Object.defineProperty(exports, "adminController", { enumerable: true, get: function () { return __importDefault(admin_controller_1).default; } });
var admin_service_1 = require("./admin.service");
Object.defineProperty(exports, "AdminService", { enumerable: true, get: function () { return admin_service_1.AdminService; } });
__exportStar(require("./dto/admin.dto"), exports);
//# sourceMappingURL=index.js.map