"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const repository_1 = require("../../infra/repository");
const errorHandlers_1 = require("../../infra/errorHandlers");
class SqlExecutorUseCases {
    constructor() { }
    async getAll() {
        try {
            return await repository_1.sqlExecutorRepository.find();
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async getOne(id) {
        try {
            const sqlExecutor = await repository_1.sqlExecutorRepository.findOneBy({ id });
            if (!sqlExecutor) {
                throw errorHandlers_1.ErrorHandler.NotFound("SqlExecutor not found");
            }
            return sqlExecutor;
        }
        catch (error) {
            if (error instanceof Error && error.message === "SqlExecutor not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async create(data) {
        try {
            return await repository_1.sqlExecutorRepository.save(data);
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async update(id, data) {
        try {
            const sqlExecutor = await this.getOne(id);
            await repository_1.sqlExecutorRepository.update(id, data);
            return { ...sqlExecutor, ...data };
        }
        catch (error) {
            if (error instanceof Error && error.message === "SqlExecutor not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async delete(id) {
        try {
            await this.getOne(id);
            await repository_1.sqlExecutorRepository.delete(id);
        }
        catch (error) {
            if (error instanceof Error && error.message === "SqlExecutor not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
}
exports.default = SqlExecutorUseCases;
//# sourceMappingURL=sqlExecutor.useCases.js.map