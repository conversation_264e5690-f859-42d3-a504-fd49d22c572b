{"version": 3, "file": "sqlExecutor.useCases.js", "sourceRoot": "", "sources": ["../../../src/modules/sqlExecutor/sqlExecutor.useCases.ts"], "names": [], "mappings": ";;AACA,uDAA+D;AAE/D,6DAAyD;AAEzD,MAAqB,mBAAmB;IAGtC,gBAAe,CAAC;IAEhB,KAAK,CAAC,MAAM;QACV,IAAI;YACF,OAAO,MAAM,kCAAqB,CAAC,IAAI,EAAE,CAAC;SAC3C;QAAC,OAAO,KAAc,EAAE;YACvB,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,kCAAqB,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAClE,IAAI,CAAC,WAAW,EAAE;gBAChB,MAAM,4BAAY,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;aACtD;YACD,OAAO,WAAW,CAAC;SACpB;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,uBAAuB,EAAE;gBACvE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAA0B;QACrC,IAAI;YACF,OAAO,MAAM,kCAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC/C;QAAC,OAAO,KAAc,EAAE;YACvB,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,IAA0B;QACjD,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,kCAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAC7C,OAAO,EAAE,GAAG,WAAW,EAAE,GAAG,IAAI,EAAE,CAAC;SACpC;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,uBAAuB,EAAE;gBACvE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,IAAI;YACF,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACtB,MAAM,kCAAqB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACxC;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,uBAAuB,EAAE;gBACvE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;CACF;AA5DD,sCA4DC"}