{"version": 3, "file": "sqlExecutor.service.js", "sourceRoot": "", "sources": ["../../../src/modules/sqlExecutor/sqlExecutor.service.ts"], "names": [], "mappings": ";;;AACA,kEAA0E;AAC1E,yEAAyH;AAuBzH,MAAa,kBAAkB;IAA/B;QACU,iBAAY,GAAmC,IAAI,GAAG,EAAE,CAAC;QAChD,oBAAe,GAAG,KAAK,CAAC,CAAC,aAAa;QACtC,aAAQ,GAAG,KAAK,CAAC,CAAC,yBAAyB;IAyQ9D,CAAC;IAvQS,KAAK,CAAC,aAAa,CAAC,WAAmB;QAC7C,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,UAAmB;YACzB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;YACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC;YAC7C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;YAC/C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;YAC/C,QAAQ,EAAE,aAAa,WAAW,EAAE;YACpC,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,OAAO,MAAM,IAAA,oCAAsB,EAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAsB;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,UAAU,GAAsB,IAAI,CAAC;QAEzC,IAAI;YACF,2BAA2B;YAC3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;gBACrB,MAAM,IAAI,KAAK,CAAC,gBAAgB,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aAClE;YAED,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC1D,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAC;YAEnD,IAAI;gBACF,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;gBAE5B,2BAA2B;gBAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC;gBAEvD,8DAA8D;gBAC9D,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBACzD,MAAM,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;oBACrD,MAAM,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;iBACtD;gBAED,IAAI,MAAW,CAAC;gBAEhB,IAAI,MAAM,CAAC,WAAW,EAAE;oBACtB,qCAAqC;oBACrC,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CACpC,WAAW,CAAC,KAAK,CAAC,2CAA2C,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAC3F,OAAO,CACR,CAAC;iBACH;qBAAM;oBACL,2BAA2B;oBAC3B,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CACpC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAC9C,OAAO,CACR,CAAC;iBACH;gBAED,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAElD,uCAAuC;gBACvC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;oBAC1D,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACzC;gBAED,MAAM,eAAe,GAAuB;oBAC1C,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC;oBACvE,aAAa;oBACb,SAAS;oBACT,QAAQ,EAAE,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;iBACrG,CAAC;gBAEF,uBAAuB;gBACvB,MAAM,IAAI,CAAC,eAAe,CAAC;oBACzB,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,WAAW,EAAE,MAAM,CAAC,WAAW;oBAC/B,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,aAAa;oBACb,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;gBAEH,OAAO,eAAe,CAAC;aAExB;oBAAS;gBACR,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;aAC7B;SAEF;QAAC,OAAO,KAAU,EAAE;YACnB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,8BAA8B;YAC9B,MAAM,IAAI,CAAC,eAAe,CAAC;gBACzB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,aAAa;gBACb,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,aAAa;gBACb,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC;SACH;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,aAA+B;QACjD,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,iCAAiC;QACjC,MAAM,iBAAiB,GAAG;YACxB,sCAAsC;YACtC,iBAAiB;YACjB,wBAAwB;YACxB,QAAQ;YACR,YAAY;YACZ,cAAc;YACd,gBAAgB;SACjB,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE;YACvC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACvB,MAAM,CAAC,IAAI,CAAC,+CAA+C,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;aAC9E;SACF;QAED,8CAA8C;QAC9C,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YAChD,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;SACxD;QAED,4BAA4B;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACnC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;SAC/C,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAA8B;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAE7E,IAAI,eAAe,GAAG,gBAAgB,CAAC;QAEvC,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC;SAC/E;QAED,6CAA6C;QAC7C,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAEhF,mBAAmB;QACnB,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC;QACtC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC;QAErC,OAAO,eAAe,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;IACvD,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAI,OAAmB,EAAE,OAAe;QACtE,MAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;YACtD,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,uBAAuB,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;IACjD,CAAC;IAEO,eAAe,CAAC,KAAa;QACnC,MAAM,eAAe,GAAG,kDAAkD,CAAC;QAC3E,OAAO,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5C,CAAC;IAEO,YAAY,CAAC,KAAa;QAChC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE3C,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;YAAE,OAAO,8BAAY,CAAC,MAAM,CAAC;QAC3F,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO,8BAAY,CAAC,MAAM,CAAC;QAC7D,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO,8BAAY,CAAC,MAAM,CAAC;QAC7D,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO,8BAAY,CAAC,MAAM,CAAC;QAC7D,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO,8BAAY,CAAC,MAAM,CAAC;QAC7D,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;YAAE,OAAO,8BAAY,CAAC,KAAK,CAAC;QAC3D,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;YAAE,OAAO,8BAAY,CAAC,IAAI,CAAC;QACzD,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC;YAAE,OAAO,8BAAY,CAAC,QAAQ,CAAC;QAEjE,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,KAAa;QACpC,8BAA8B;QAC9B,MAAM,WAAW,GAAG,+FAA+F,CAAC;QACpH,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,WAO7B;QACC,MAAM,YAAY,GAAoB;YACpC,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9D,GAAG,WAAW;YACd,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;YACnD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;SACpD;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,CAAE,CAAC;QACzE,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEpC,4CAA4C;QAC5C,IAAI,gBAAgB,CAAC,MAAM,GAAG,IAAI,EAAE;YAClC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;SAC5D;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAsB;QACvC,MAAM,UAAU,GAAG,EAAE,GAAG,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;QACpD,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,WAAmB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAEzD,MAAM,KAAK,GAAG;YACZ,YAAY,EAAE,OAAO,CAAC,MAAM;YAC5B,iBAAiB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;YACxD,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;YACrD,oBAAoB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;gBACtC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM;gBACvE,CAAC,CAAC,CAAC;YACL,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC;YAC7D,YAAY,EAAE,OAAO;iBAClB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;iBACvB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;iBACZ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5E,CAAC;QAEF,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,wBAAwB,CAAC,OAA0B;QACzD,MAAM,YAAY,GAA2B,EAAE,CAAC;QAEhD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACxC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AA5QD,gDA4QC"}