"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const sqlExecutor_service_1 = require("./sqlExecutor.service");
const sqlExecutor_dto_1 = require("../sql-executor/dto/sqlExecutor.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const sqlExecutorRouter = (0, express_1.Router)();
const sqlExecutorService = new sqlExecutor_service_1.SqlExecutorService();
// POST /execute - Execute SQL query
sqlExecutorRouter.post("/execute", async (req, res) => {
    try {
        const { query, workspaceId, params, readonly, timeout, userId } = req.body;
        const sqlDto = (0, class_transformer_1.plainToClass)(sqlExecutor_dto_1.SqlExecutorDto, {
            query,
            workspaceId,
            params,
            readonly,
            timeout,
            userId
        });
        const errors = await (0, class_validator_1.validate)(sqlDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await sqlExecutorService.executeQuery(sqlDto);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// POST /validate - Validate SQL query without executing
sqlExecutorRouter.post("/validate", async (req, res) => {
    try {
        const { query, strict } = req.body;
        const validationDto = (0, class_transformer_1.plainToClass)(sqlExecutor_dto_1.SqlValidationDto, {
            query,
            strict
        });
        const errors = await (0, class_validator_1.validate)(validationDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await sqlExecutorService.validateQuery(validationDto);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// POST /explain - Explain query execution plan
sqlExecutorRouter.post("/explain", async (req, res) => {
    try {
        const { query, workspaceId, params, userId } = req.body;
        const sqlDto = (0, class_transformer_1.plainToClass)(sqlExecutor_dto_1.SqlExecutorDto, {
            query,
            workspaceId,
            params,
            explainPlan: true,
            readonly: true,
            userId
        });
        const errors = await (0, class_validator_1.validate)(sqlDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await sqlExecutorService.explainQuery(sqlDto);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// GET /history - Get query execution history
sqlExecutorRouter.get("/history", async (req, res) => {
    try {
        const { workspaceId, userId, limit, offset } = req.query;
        const historyDto = (0, class_transformer_1.plainToClass)(sqlExecutor_dto_1.SqlQueryHistoryDto, {
            workspaceId,
            userId,
            limit: limit ? parseInt(limit) : undefined,
            offset: offset ? parseInt(offset) : undefined
        });
        const errors = await (0, class_validator_1.validate)(historyDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await sqlExecutorService.getQueryHistory(historyDto);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// GET /stats/:workspaceId - Get query execution statistics
sqlExecutorRouter.get("/stats/:workspaceId", async (req, res) => {
    try {
        const { workspaceId } = req.params;
        if (!workspaceId) {
            return res.status(400).json({ error: "Workspace ID is required" });
        }
        const result = await sqlExecutorService.getQueryStats(workspaceId);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// POST /batch - Execute multiple queries in batch
sqlExecutorRouter.post("/batch", async (req, res) => {
    try {
        const { queries, workspaceId, userId } = req.body;
        if (!Array.isArray(queries)) {
            return res.status(400).json({ error: "Queries must be an array" });
        }
        const results = [];
        for (const queryData of queries) {
            const sqlDto = (0, class_transformer_1.plainToClass)(sqlExecutor_dto_1.SqlExecutorDto, {
                query: queryData.query,
                workspaceId,
                params: queryData.params,
                readonly: queryData.readonly,
                timeout: queryData.timeout,
                userId
            });
            const errors = await (0, class_validator_1.validate)(sqlDto);
            if (errors.length > 0) {
                results.push({ error: "Validation failed", details: errors });
                continue;
            }
            try {
                const result = await sqlExecutorService.executeQuery(sqlDto);
                results.push(result);
            }
            catch (error) {
                results.push({ error: error.message });
            }
        }
        return res.json({ results });
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
exports.default = sqlExecutorRouter;
//# sourceMappingURL=sqlExecutor.controller.js.map