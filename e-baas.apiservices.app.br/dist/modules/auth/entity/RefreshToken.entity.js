"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefreshToken = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../../modules/users/entity/user.entity");
const uuid_1 = require("uuid");
let RefreshToken = class RefreshToken {
    generateToken() {
        this.token = this.generateSecureToken();
    }
    generateSecureToken() {
        // Generate a more secure token combining UUID and timestamp
        const uuid = (0, uuid_1.v4)().replace(/-/g, '');
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2);
        return `${uuid}${timestamp}${random}`;
    }
    isExpired() {
        return new Date() > this.expiresAt;
    }
    isValid() {
        return !this.isRevoked && !this.isExpired();
    }
    revoke() {
        this.isRevoked = true;
        this.revokedAt = new Date();
    }
    updateLastUsed() {
        this.lastUsedAt = new Date();
    }
    // Check if token is close to expiry (within 10 minutes)
    isNearExpiry() {
        const tenMinutes = 10 * 60 * 1000;
        return (this.expiresAt.getTime() - Date.now()) < tenMinutes;
    }
    // Get remaining time in milliseconds
    getRemainingTime() {
        return Math.max(0, this.expiresAt.getTime() - Date.now());
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], RefreshToken.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "token", type: "varchar", length: 500, unique: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "token", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], RefreshToken.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "expires_at", type: "timestamp" }),
    __metadata("design:type", Date)
], RefreshToken.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_revoked", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], RefreshToken.prototype, "isRevoked", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "revoked_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], RefreshToken.prototype, "revokedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "parent_id", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "session_id", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "sessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "ip_address", type: "varchar", length: 45, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "user_agent", type: "text", nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], RefreshToken.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], RefreshToken.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "last_used_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], RefreshToken.prototype, "lastUsedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "user_id" }),
    __metadata("design:type", String)
], RefreshToken.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.refreshTokens),
    (0, typeorm_1.JoinColumn)({ name: "user_id" }),
    __metadata("design:type", user_entity_1.User)
], RefreshToken.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RefreshToken.prototype, "generateToken", null);
RefreshToken = __decorate([
    (0, typeorm_1.Entity)("refresh_tokens"),
    (0, typeorm_1.Index)(["token"], { unique: true }),
    (0, typeorm_1.Index)(["userId", "workspaceId"])
], RefreshToken);
exports.RefreshToken = RefreshToken;
//# sourceMappingURL=RefreshToken.entity.js.map