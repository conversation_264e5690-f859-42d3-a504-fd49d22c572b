"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_service_1 = require("./auth.service");
const auth_dto_1 = require("./dto/auth.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const authRouter = (0, express_1.Router)();
const authService = new auth_service_1.AuthService();
// Helper function for validation
const validateDto = async (DtoClass, data) => {
    const dto = (0, class_transformer_1.plainToClass)(DtoClass, data);
    const errors = await (0, class_validator_1.validate)(dto);
    if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
    }
    return dto;
};
// Sign up
authRouter.post("/signup", async (req, res) => {
    try {
        const signUpDto = await validateDto(auth_dto_1.SignUpDto, req.body);
        const result = await authService.signUp(signUpDto);
        return res.status(201).json(result);
    }
    catch (error) {
        if (error.message.includes('already exists')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Sign in
authRouter.post("/signin", async (req, res) => {
    try {
        const signInDto = await validateDto(auth_dto_1.SignInDto, req.body);
        const result = await authService.signIn(signInDto);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('credentials') || error.message.includes('deactivated') || error.message.includes('banned')) {
            return res.status(401).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Refresh token
authRouter.post("/token/refresh", async (req, res) => {
    try {
        const refreshTokenDto = await validateDto(auth_dto_1.RefreshTokenDto, req.body);
        const result = await authService.refreshToken(refreshTokenDto);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('Invalid') || error.message.includes('expired')) {
            return res.status(401).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Sign out
authRouter.post("/signout", async (req, res) => {
    try {
        const { refreshToken } = req.body;
        if (!refreshToken) {
            return res.status(400).json({ error: 'Refresh token is required' });
        }
        const result = await authService.signOut(refreshToken);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Sign out from all devices
authRouter.post("/signout/all", async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        const result = await authService.signOutAll(userId);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Reset password request
authRouter.post("/recover", async (req, res) => {
    try {
        const resetPasswordDto = await validateDto(auth_dto_1.ResetPasswordDto, req.body);
        const result = await authService.resetPassword(resetPasswordDto);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Update password with reset token
authRouter.post("/recover/verify", async (req, res) => {
    try {
        const updatePasswordDto = await validateDto(auth_dto_1.UpdatePasswordDto, req.body);
        const result = await authService.updatePassword(updatePasswordDto);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('Invalid') || error.message.includes('expired')) {
            return res.status(401).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Verify email
authRouter.post("/verify", async (req, res) => {
    try {
        const verifyEmailDto = await validateDto(auth_dto_1.VerifyEmailDto, req.body);
        const result = await authService.verifyEmail(verifyEmailDto);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('Invalid')) {
            return res.status(401).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Send magic link
authRouter.post("/magiclink", async (req, res) => {
    try {
        const magicLinkDto = await validateDto(auth_dto_1.MagicLinkDto, req.body);
        const result = await authService.sendMagicLink(magicLinkDto);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Verify magic link
authRouter.post("/magiclink/verify", async (req, res) => {
    try {
        const { token } = req.body;
        if (!token) {
            return res.status(400).json({ error: 'Token is required' });
        }
        const result = await authService.verifyMagicLink(token);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get current user
authRouter.get("/user", async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        const user = await authService.getUser(userId);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        return res.status(200).json({ user });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Update user
authRouter.put("/user", async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        const updateUserDto = await validateDto(auth_dto_1.UpdateUserDto, req.body);
        const user = await authService.updateUser(userId, updateUserDto);
        return res.status(200).json({ user });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Admin endpoints
authRouter.post("/admin/users", async (req, res) => {
    try {
        // Check if user has admin permissions
        const userRole = req.user?.role;
        if (userRole !== 'service_role') {
            return res.status(403).json({ error: 'Admin access required' });
        }
        const signUpDto = await validateDto(auth_dto_1.SignUpDto, req.body);
        const result = await authService.signUp(signUpDto);
        return res.status(201).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Token verification endpoint
authRouter.post("/verify-token", async (req, res) => {
    try {
        const { token } = req.body;
        if (!token) {
            return res.status(400).json({ error: 'Token is required' });
        }
        const payload = authService.verifyAccessToken(token);
        return res.status(200).json({ valid: true, payload });
    }
    catch (error) {
        return res.status(401).json({ valid: false, error: error.message });
    }
});
// OAuth Authentication Endpoints
/**
 * @swagger
 * /auth/v1/oauth/providers:
 *   get:
 *     summary: Get enabled OAuth providers
 *     tags: [Authentication]
 *     responses:
 *       200:
 *         description: List of enabled OAuth providers
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 providers:
 *                   type: array
 *                   items:
 *                     type: string
 *                     enum: [google, github, facebook, twitter]
 */
authRouter.get("/oauth/providers", async (req, res) => {
    try {
        const providers = authService.getEnabledOAuthProviders();
        return res.status(200).json({ providers });
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
/**
 * @swagger
 * /auth/v1/oauth/authorize:
 *   post:
 *     summary: Get OAuth authorization URL
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - provider
 *             properties:
 *               provider:
 *                 type: string
 *                 enum: [google, github, facebook, twitter]
 *               state:
 *                 type: string
 *                 description: Optional state parameter
 *               workspaceId:
 *                 type: string
 *                 description: Workspace ID
 *     responses:
 *       200:
 *         description: OAuth authorization URL
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                 state:
 *                   type: string
 *                 provider:
 *                   type: string
 */
authRouter.post("/oauth/authorize", async (req, res) => {
    try {
        const { provider, state, workspaceId } = req.body;
        if (!provider) {
            return res.status(400).json({ error: 'Provider is required' });
        }
        // Generate state if not provided
        const oauthState = state || authService.generateOAuthState();
        // Store state with workspace info for later validation
        const stateData = JSON.stringify({ state: oauthState, workspaceId });
        const encodedState = Buffer.from(stateData).toString('base64');
        const url = authService.getOAuthAuthUrl(provider, encodedState);
        return res.status(200).json({
            url,
            state: oauthState,
            provider
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /auth/v1/oauth/callback:
 *   post:
 *     summary: OAuth callback - exchange code for tokens
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - provider
 *               - code
 *             properties:
 *               provider:
 *                 type: string
 *                 enum: [google, github, facebook, twitter]
 *               code:
 *                 type: string
 *                 description: Authorization code from OAuth provider
 *               state:
 *                 type: string
 *                 description: State parameter for validation
 *     responses:
 *       200:
 *         description: Authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   type: object
 *                 accessToken:
 *                   type: string
 *                 refreshToken:
 *                   type: string
 *                 expiresIn:
 *                   type: number
 *                 tokenType:
 *                   type: string
 */
authRouter.post("/oauth/callback", async (req, res) => {
    try {
        const { provider, code, state } = req.body;
        if (!provider || !code) {
            return res.status(400).json({ error: 'Provider and code are required' });
        }
        // Decode and validate state
        let workspaceId = 'default';
        if (state) {
            try {
                const stateData = JSON.parse(Buffer.from(state, 'base64').toString());
                workspaceId = stateData.workspaceId || 'default';
            }
            catch (error) {
                console.warn('Failed to decode state parameter:', error);
            }
        }
        const oauthDto = await validateDto(auth_dto_1.OAuthDto, {
            provider,
            code,
            state,
            workspaceId
        });
        const result = await authService.signInWithOAuth(oauthDto);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /auth/v1/oauth/callback/{provider}:
 *   get:
 *     summary: OAuth callback endpoint (GET version for redirect-based flows)
 *     tags: [Authentication]
 *     parameters:
 *       - in: path
 *         name: provider
 *         required: true
 *         schema:
 *           type: string
 *           enum: [google, github, facebook, twitter]
 *       - in: query
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: state
 *         schema:
 *           type: string
 *       - in: query
 *         name: error
 *         schema:
 *           type: string
 *     responses:
 *       302:
 *         description: Redirect to frontend with auth result
 */
authRouter.get("/oauth/callback/:provider", async (req, res) => {
    try {
        const { provider } = req.params;
        const { code, state, error } = req.query;
        if (error) {
            // OAuth provider returned an error
            return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/error?error=${encodeURIComponent(error)}`);
        }
        if (!code) {
            return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/error?error=missing_code`);
        }
        // Decode state
        let workspaceId = 'default';
        if (state) {
            try {
                const stateData = JSON.parse(Buffer.from(state, 'base64').toString());
                workspaceId = stateData.workspaceId || 'default';
            }
            catch (error) {
                console.warn('Failed to decode state parameter:', error);
            }
        }
        const oauthDto = await validateDto(auth_dto_1.OAuthDto, {
            provider,
            code: code,
            state: state,
            workspaceId
        });
        const result = await authService.signInWithOAuth(oauthDto);
        // Redirect to frontend with tokens
        const redirectUrl = new URL(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/callback`);
        redirectUrl.searchParams.set('access_token', result.accessToken);
        redirectUrl.searchParams.set('refresh_token', result.refreshToken);
        redirectUrl.searchParams.set('expires_in', result.expiresIn.toString());
        redirectUrl.searchParams.set('token_type', result.tokenType);
        return res.redirect(redirectUrl.toString());
    }
    catch (error) {
        console.error('OAuth callback error:', error);
        return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/error?error=${encodeURIComponent(error.message)}`);
    }
});
/**
 * @swagger
 * /auth/v1/oauth/link:
 *   post:
 *     summary: Link OAuth provider to existing account
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - provider
 *               - code
 *             properties:
 *               provider:
 *                 type: string
 *                 enum: [google, github, facebook, twitter]
 *               code:
 *                 type: string
 *     responses:
 *       200:
 *         description: OAuth provider linked successfully
 */
authRouter.post("/oauth/link", async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        const oauthDto = await validateDto(auth_dto_1.OAuthDto, req.body);
        const result = await authService.linkOAuthProvider(userId, oauthDto);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /auth/v1/oauth/unlink:
 *   post:
 *     summary: Unlink OAuth provider from account
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - provider
 *             properties:
 *               provider:
 *                 type: string
 *                 enum: [google, github, facebook, twitter]
 *     responses:
 *       200:
 *         description: OAuth provider unlinked successfully
 */
authRouter.post("/oauth/unlink", async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        const { provider } = req.body;
        if (!provider) {
            return res.status(400).json({ error: 'Provider is required' });
        }
        const result = await authService.unlinkOAuthProvider(userId, provider);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
exports.default = authRouter;
//# sourceMappingURL=auth.controller.js.map