"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuthService = void 0;
const axios_1 = __importDefault(require("axios"));
const auth_dto_1 = require("./dto/auth.dto");
const config_1 = __importDefault(require("../../infra/config"));
class OAuthService {
    constructor() {
        this.providers = new Map();
        this.initializeProviders();
    }
    initializeProviders() {
        // Google OAuth configuration
        if (config_1.default.oauth?.google?.clientId) {
            this.providers.set(auth_dto_1.AuthProvider.GOOGLE, {
                clientId: config_1.default.oauth.google.clientId,
                clientSecret: config_1.default.oauth.google.clientSecret,
                redirectUri: config_1.default.oauth.google.redirectUri || `${config_1.default.app.baseUrl}/auth/v1/callback/google`,
                scope: ['openid', 'email', 'profile']
            });
        }
        // GitHub OAuth configuration
        if (config_1.default.oauth?.github?.clientId) {
            this.providers.set(auth_dto_1.AuthProvider.GITHUB, {
                clientId: config_1.default.oauth.github.clientId,
                clientSecret: config_1.default.oauth.github.clientSecret,
                redirectUri: config_1.default.oauth.github.redirectUri || `${config_1.default.app.baseUrl}/auth/v1/callback/github`,
                scope: ['user:email', 'read:user']
            });
        }
        // Facebook OAuth configuration
        if (config_1.default.oauth?.facebook?.clientId) {
            this.providers.set(auth_dto_1.AuthProvider.FACEBOOK, {
                clientId: config_1.default.oauth.facebook.clientId,
                clientSecret: config_1.default.oauth.facebook.clientSecret,
                redirectUri: config_1.default.oauth.facebook.redirectUri || `${config_1.default.app.baseUrl}/auth/v1/callback/facebook`,
                scope: ['email', 'public_profile']
            });
        }
    }
    getAuthUrl(provider, state) {
        const providerConfig = this.providers.get(provider);
        if (!providerConfig) {
            throw new Error(`OAuth provider ${provider} is not configured`);
        }
        const baseUrls = {
            [auth_dto_1.AuthProvider.GOOGLE]: 'https://accounts.google.com/o/oauth2/v2/auth',
            [auth_dto_1.AuthProvider.GITHUB]: 'https://github.com/login/oauth/authorize',
            [auth_dto_1.AuthProvider.FACEBOOK]: 'https://www.facebook.com/v18.0/dialog/oauth',
            [auth_dto_1.AuthProvider.EMAIL]: '',
            [auth_dto_1.AuthProvider.TWITTER]: 'https://api.twitter.com/oauth/authorize'
        };
        const params = new URLSearchParams({
            client_id: providerConfig.clientId,
            response_type: 'code',
            scope: providerConfig.scope.join(' '),
            redirect_uri: providerConfig.redirectUri,
            ...(state && { state })
        });
        return `${baseUrls[provider]}?${params.toString()}`;
    }
    async exchangeCodeForToken(provider, code) {
        const providerConfig = this.providers.get(provider);
        if (!providerConfig) {
            throw new Error(`OAuth provider ${provider} is not configured`);
        }
        const tokenUrls = {
            [auth_dto_1.AuthProvider.GOOGLE]: 'https://oauth2.googleapis.com/token',
            [auth_dto_1.AuthProvider.GITHUB]: 'https://github.com/login/oauth/access_token',
            [auth_dto_1.AuthProvider.FACEBOOK]: 'https://graph.facebook.com/v18.0/oauth/access_token',
            [auth_dto_1.AuthProvider.EMAIL]: '',
            [auth_dto_1.AuthProvider.TWITTER]: ''
        };
        try {
            const response = await axios_1.default.post(tokenUrls[provider], {
                client_id: providerConfig.clientId,
                client_secret: providerConfig.clientSecret,
                code,
                redirect_uri: providerConfig.redirectUri,
                grant_type: 'authorization_code'
            }, {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });
            if (response.data.access_token) {
                return response.data.access_token;
            }
            else {
                throw new Error('No access token received from OAuth provider');
            }
        }
        catch (error) {
            console.error(`Failed to exchange code for token with ${provider}:`, error.response?.data || error.message);
            throw new Error(`OAuth token exchange failed: ${error.message}`);
        }
    }
    async getUserInfo(provider, accessToken) {
        try {
            switch (provider) {
                case auth_dto_1.AuthProvider.GOOGLE:
                    return await this.getGoogleUserInfo(accessToken);
                case auth_dto_1.AuthProvider.GITHUB:
                    return await this.getGitHubUserInfo(accessToken);
                case auth_dto_1.AuthProvider.FACEBOOK:
                    return await this.getFacebookUserInfo(accessToken);
                default:
                    throw new Error(`OAuth provider ${provider} is not supported`);
            }
        }
        catch (error) {
            console.error(`Failed to get user info from ${provider}:`, error.message);
            throw new Error(`Failed to get user information from ${provider}: ${error.message}`);
        }
    }
    async getGoogleUserInfo(accessToken) {
        const response = await axios_1.default.get('https://www.googleapis.com/oauth2/v2/userinfo', {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        const data = response.data;
        return {
            id: data.id,
            email: data.email,
            name: data.name,
            firstName: data.given_name,
            lastName: data.family_name,
            avatar: data.picture,
            provider: auth_dto_1.AuthProvider.GOOGLE,
            providerId: data.id,
            metadata: {
                locale: data.locale,
                verified_email: data.verified_email
            }
        };
    }
    async getGitHubUserInfo(accessToken) {
        const headers = {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': 'application/vnd.github.v3+json'
        };
        // Get user basic info
        const userResponse = await axios_1.default.get('https://api.github.com/user', { headers });
        const userData = userResponse.data;
        // Get user emails
        const emailsResponse = await axios_1.default.get('https://api.github.com/user/emails', { headers });
        const emails = emailsResponse.data;
        const primaryEmail = emails.find((email) => email.primary)?.email || userData.email;
        return {
            id: userData.id.toString(),
            email: primaryEmail,
            name: userData.name || userData.login,
            firstName: userData.name?.split(' ')[0],
            lastName: userData.name?.split(' ').slice(1).join(' '),
            avatar: userData.avatar_url,
            provider: auth_dto_1.AuthProvider.GITHUB,
            providerId: userData.id.toString(),
            metadata: {
                login: userData.login,
                bio: userData.bio,
                blog: userData.blog,
                location: userData.location,
                public_repos: userData.public_repos,
                followers: userData.followers,
                following: userData.following
            }
        };
    }
    async getFacebookUserInfo(accessToken) {
        const response = await axios_1.default.get('https://graph.facebook.com/v18.0/me', {
            params: {
                fields: 'id,name,email,first_name,last_name,picture.type(large)',
                access_token: accessToken
            }
        });
        const data = response.data;
        return {
            id: data.id,
            email: data.email,
            name: data.name,
            firstName: data.first_name,
            lastName: data.last_name,
            avatar: data.picture?.data?.url,
            provider: auth_dto_1.AuthProvider.FACEBOOK,
            providerId: data.id,
            metadata: {
                picture: data.picture
            }
        };
    }
    isProviderEnabled(provider) {
        return this.providers.has(provider);
    }
    getEnabledProviders() {
        return Array.from(this.providers.keys());
    }
    validateState(receivedState, expectedState) {
        if (!expectedState)
            return true;
        return receivedState === expectedState;
    }
    generateState() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
    }
}
exports.OAuthService = OAuthService;
//# sourceMappingURL=oauth.service.js.map