"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssignAdminRoleDto = exports.AdminRoleDto = exports.UserRole = exports.AdminRole = void 0;
const class_validator_1 = require("class-validator");
var AdminRole;
(function (AdminRole) {
    AdminRole["PLATFORM_ADMIN"] = "platform_admin";
    AdminRole["WORKSPACE_ADMIN"] = "workspace_admin";
    AdminRole["WORKSPACE_OWNER"] = "workspace_owner";
    AdminRole["SERVICE_ROLE"] = "service_role"; // Role de serviço (API keys)
})(AdminRole = exports.AdminRole || (exports.AdminRole = {}));
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["EDITOR"] = "editor";
    UserRole["VIEWER"] = "viewer";
    UserRole["AUTHENTICATED"] = "authenticated";
})(UserRole = exports.UserRole || (exports.UserRole = {}));
class AdminRoleDto {
}
__decorate([
    (0, class_validator_1.IsEnum)(AdminRole),
    __metadata("design:type", String)
], AdminRoleDto.prototype, "adminRole", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(UserRole),
    __metadata("design:type", String)
], AdminRoleDto.prototype, "userRole", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AdminRoleDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AdminRoleDto.prototype, "permissions", void 0);
exports.AdminRoleDto = AdminRoleDto;
class AssignAdminRoleDto {
}
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], AssignAdminRoleDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(AdminRole),
    __metadata("design:type", String)
], AssignAdminRoleDto.prototype, "adminRole", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AssignAdminRoleDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AssignAdminRoleDto.prototype, "customPermissions", void 0);
exports.AssignAdminRoleDto = AssignAdminRoleDto;
//# sourceMappingURL=admin-roles.dto.js.map