"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoFAService = void 0;
const crypto_1 = __importDefault(require("crypto"));
const data_source_1 = require("../../infra/database/data-source");
const user_entity_1 = require("../users/entity/user.entity");
class TwoFAService {
    constructor() {
        this.secrets = new Map();
        this.userRepository = data_source_1.AppDataSource.getRepository(user_entity_1.User);
    }
    /**
     * Gera um novo secret para 2FA
     */
    generateSecret(userId) {
        // Gerar secret aleatório (32 caracteres base32)
        const secret = this.generateBase32Secret();
        // Gerar códigos de backup
        const backupCodes = this.generateBackupCodes();
        // Criar URL para QR Code (compatível com Google Authenticator)
        const qrCodeUrl = this.generateQRCodeUrl(secret, userId);
        // Armazenar temporariamente (será ativado após verificação)
        const twoFASecret = {
            id: crypto_1.default.randomUUID(),
            userId,
            secret,
            backupCodes,
            isActive: false,
            createdAt: new Date()
        };
        this.secrets.set(userId, twoFASecret);
        return {
            secret,
            qrCodeUrl,
            backupCodes
        };
    }
    /**
     * Verifica um código TOTP
     */
    async verifyTOTP(userId, token) {
        const userSecret = this.secrets.get(userId);
        if (!userSecret) {
            return false;
        }
        // Verificar código atual e adjacentes (para compensar diferenças de tempo)
        const timeWindow = Math.floor(Date.now() / 30000);
        for (let i = -1; i <= 1; i++) {
            const expectedToken = this.generateTOTP(userSecret.secret, timeWindow + i);
            if (token === expectedToken) {
                return true;
            }
        }
        return false;
    }
    /**
     * Verifica um código de backup
     */
    async verifyBackupCode(userId, backupCode) {
        const userSecret = this.secrets.get(userId);
        if (!userSecret) {
            return false;
        }
        const codeIndex = userSecret.backupCodes.indexOf(backupCode);
        if (codeIndex === -1) {
            return false;
        }
        // Remover o código usado
        userSecret.backupCodes.splice(codeIndex, 1);
        userSecret.lastUsedAt = new Date();
        this.secrets.set(userId, userSecret);
        return true;
    }
    /**
     * Ativa 2FA para um usuário após verificação
     */
    async enable2FA(userId, token) {
        const isValid = await this.verifyTOTP(userId, token);
        if (!isValid) {
            return { success: false };
        }
        const userSecret = this.secrets.get(userId);
        if (!userSecret) {
            return { success: false };
        }
        // Ativar 2FA
        userSecret.isActive = true;
        this.secrets.set(userId, userSecret);
        // Atualizar usuário no banco
        await this.userRepository.update(userId, {
            rawAppMetaData: {
                twoFactorEnabled: true,
                twoFactorEnabledAt: new Date().toISOString()
            }
        });
        return {
            success: true,
            backupCodes: userSecret.backupCodes
        };
    }
    /**
     * Desativa 2FA para um usuário
     */
    async disable2FA(userId) {
        // Remover secret
        this.secrets.delete(userId);
        // Atualizar usuário no banco
        await this.userRepository.update(userId, {
            rawAppMetaData: {
                twoFactorEnabled: false,
                twoFactorDisabledAt: new Date().toISOString()
            }
        });
        return { success: true };
    }
    /**
     * Verifica se 2FA está ativo para um usuário
     */
    async is2FAEnabled(userId) {
        const userSecret = this.secrets.get(userId);
        return userSecret?.isActive || false;
    }
    /**
     * Gera novos códigos de backup
     */
    async generateNewBackupCodes(userId) {
        const userSecret = this.secrets.get(userId);
        if (!userSecret || !userSecret.isActive) {
            throw new Error('2FA is not enabled for this user');
        }
        const newBackupCodes = this.generateBackupCodes();
        userSecret.backupCodes = newBackupCodes;
        this.secrets.set(userId, userSecret);
        return newBackupCodes;
    }
    /**
     * Obtém informações sobre 2FA do usuário
     */
    async get2FAInfo(userId) {
        const userSecret = this.secrets.get(userId);
        return {
            enabled: userSecret?.isActive || false,
            backupCodesRemaining: userSecret?.backupCodes.length || 0,
            lastUsedAt: userSecret?.lastUsedAt
        };
    }
    /**
     * Gera secret base32 para TOTP
     */
    generateBase32Secret() {
        const buffer = crypto_1.default.randomBytes(20);
        return buffer.toString('base64').replace(/[^A-Z2-7]/gi, '').substring(0, 32);
    }
    /**
     * Gera códigos de backup
     */
    generateBackupCodes() {
        const codes = [];
        for (let i = 0; i < 10; i++) {
            codes.push(crypto_1.default.randomBytes(4).toString('hex').toUpperCase());
        }
        return codes;
    }
    /**
     * Gera URL para QR Code
     */
    generateQRCodeUrl(secret, userId) {
        const issuer = 'E-BaaS';
        const accountName = `${issuer}:${userId}`;
        return `otpauth://totp/${encodeURIComponent(accountName)}?secret=${secret}&issuer=${encodeURIComponent(issuer)}`;
    }
    /**
     * Gera código TOTP para um timestamp específico
     */
    generateTOTP(secret, timeCounter) {
        // Converter secret base32 para buffer
        const secretBuffer = Buffer.from(secret, 'base64');
        // Converter time counter para buffer de 8 bytes
        const timeBuffer = Buffer.alloc(8);
        timeBuffer.writeUInt32BE(Math.floor(timeCounter / 0x100000000), 0);
        timeBuffer.writeUInt32BE(timeCounter & 0xffffffff, 4);
        // Calcular HMAC-SHA1
        const hmac = crypto_1.default.createHmac('sha1', secretBuffer);
        hmac.update(timeBuffer);
        const hash = hmac.digest();
        // Extrair código de 6 dígitos
        const offset = hash[hash.length - 1] & 0xf;
        const code = ((hash[offset] & 0x7f) << 24) |
            ((hash[offset + 1] & 0xff) << 16) |
            ((hash[offset + 2] & 0xff) << 8) |
            (hash[offset + 3] & 0xff);
        return (code % 1000000).toString().padStart(6, '0');
    }
    /**
     * Valida formato de código TOTP
     */
    isValidTOTPFormat(token) {
        return /^\d{6}$/.test(token);
    }
    /**
     * Valida formato de código de backup
     */
    isValidBackupCodeFormat(code) {
        return /^[A-F0-9]{8}$/.test(code.toUpperCase());
    }
}
exports.TwoFAService = TwoFAService;
//# sourceMappingURL=twofa.service.js.map