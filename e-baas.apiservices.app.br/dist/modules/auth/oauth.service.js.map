{"version": 3, "file": "oauth.service.js", "sourceRoot": "", "sources": ["../../../src/modules/auth/oauth.service.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,6CAA8C;AAC9C,gEAAwC;AAqBxC,MAAa,YAAY;IAGvB;QAFQ,cAAS,GAAmC,IAAI,GAAG,EAAE,CAAC;QAG5D,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACzB,6BAA6B;QAC7B,IAAI,gBAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;YAClC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAY,CAAC,MAAM,EAAE;gBACtC,QAAQ,EAAE,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ;gBACtC,YAAY,EAAE,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY;gBAC9C,WAAW,EAAE,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,gBAAM,CAAC,GAAG,CAAC,OAAO,0BAA0B;gBAC/F,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;aACtC,CAAC,CAAC;SACJ;QAED,6BAA6B;QAC7B,IAAI,gBAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;YAClC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAY,CAAC,MAAM,EAAE;gBACtC,QAAQ,EAAE,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ;gBACtC,YAAY,EAAE,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY;gBAC9C,WAAW,EAAE,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,gBAAM,CAAC,GAAG,CAAC,OAAO,0BAA0B;gBAC/F,KAAK,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;aACnC,CAAC,CAAC;SACJ;QAED,+BAA+B;QAC/B,IAAI,gBAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;YACpC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAY,CAAC,QAAQ,EAAE;gBACxC,QAAQ,EAAE,gBAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ;gBACxC,YAAY,EAAE,gBAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY;gBAChD,WAAW,EAAE,gBAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,IAAI,GAAG,gBAAM,CAAC,GAAG,CAAC,OAAO,4BAA4B;gBACnG,KAAK,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC;aACnC,CAAC,CAAC;SACJ;IACH,CAAC;IAED,UAAU,CAAC,QAAsB,EAAE,KAAc;QAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,kBAAkB,QAAQ,oBAAoB,CAAC,CAAC;SACjE;QAED,MAAM,QAAQ,GAAG;YACf,CAAC,uBAAY,CAAC,MAAM,CAAC,EAAE,8CAA8C;YACrE,CAAC,uBAAY,CAAC,MAAM,CAAC,EAAE,0CAA0C;YACjE,CAAC,uBAAY,CAAC,QAAQ,CAAC,EAAE,6CAA6C;YACtE,CAAC,uBAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACxB,CAAC,uBAAY,CAAC,OAAO,CAAC,EAAE,yCAAyC;SAClE,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;YACjC,SAAS,EAAE,cAAc,CAAC,QAAQ;YAClC,aAAa,EAAE,MAAM;YACrB,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;YACrC,YAAY,EAAE,cAAc,CAAC,WAAW;YACxC,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,CAAC;SACxB,CAAC,CAAC;QAEH,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,QAAsB,EAAE,IAAY;QAC7D,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,kBAAkB,QAAQ,oBAAoB,CAAC,CAAC;SACjE;QAED,MAAM,SAAS,GAAG;YAChB,CAAC,uBAAY,CAAC,MAAM,CAAC,EAAE,qCAAqC;YAC5D,CAAC,uBAAY,CAAC,MAAM,CAAC,EAAE,6CAA6C;YACpE,CAAC,uBAAY,CAAC,QAAQ,CAAC,EAAE,qDAAqD;YAC9E,CAAC,uBAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACxB,CAAC,uBAAY,CAAC,OAAO,CAAC,EAAE,EAAE;SAC3B,CAAC;QAEF,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;gBACrD,SAAS,EAAE,cAAc,CAAC,QAAQ;gBAClC,aAAa,EAAE,cAAc,CAAC,YAAY;gBAC1C,IAAI;gBACJ,YAAY,EAAE,cAAc,CAAC,WAAW;gBACxC,UAAU,EAAE,oBAAoB;aACjC,EAAE;gBACD,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;oBAC5B,cAAc,EAAE,mCAAmC;iBACpD;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE;gBAC9B,OAAO,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;aACnC;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;aACjE;SACF;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO,CAAC,KAAK,CAAC,0CAA0C,QAAQ,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5G,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAClE;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,QAAsB,EAAE,WAAmB;QAC3D,IAAI;YACF,QAAQ,QAAQ,EAAE;gBAChB,KAAK,uBAAY,CAAC,MAAM;oBACtB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACnD,KAAK,uBAAY,CAAC,MAAM;oBACtB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACnD,KAAK,uBAAY,CAAC,QAAQ;oBACxB,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;gBACrD;oBACE,MAAM,IAAI,KAAK,CAAC,kBAAkB,QAAQ,mBAAmB,CAAC,CAAC;aAClE;SACF;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO,CAAC,KAAK,CAAC,gCAAgC,QAAQ,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,uCAAuC,QAAQ,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtF;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,WAAmB;QACjD,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,+CAA+C,EAAE;YAChF,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,WAAW,EAAE;aACzC;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC3B,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,QAAQ,EAAE,IAAI,CAAC,WAAW;YAC1B,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,QAAQ,EAAE,uBAAY,CAAC,MAAM;YAC7B,UAAU,EAAE,IAAI,CAAC,EAAE;YACnB,QAAQ,EAAE;gBACR,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,cAAc,EAAE,IAAI,CAAC,cAAc;aACpC;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,WAAmB;QACjD,MAAM,OAAO,GAAG;YACd,eAAe,EAAE,UAAU,WAAW,EAAE;YACxC,QAAQ,EAAE,gCAAgC;SAC3C,CAAC;QAEF,sBAAsB;QACtB,MAAM,YAAY,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,6BAA6B,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QACjF,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC;QAEnC,kBAAkB;QAClB,MAAM,cAAc,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,oCAAoC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1F,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC;QACnC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;QAEzF,OAAO;YACL,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC1B,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK;YACrC,SAAS,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACvC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACtD,MAAM,EAAE,QAAQ,CAAC,UAAU;YAC3B,QAAQ,EAAE,uBAAY,CAAC,MAAM;YAC7B,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE;YAClC,QAAQ,EAAE;gBACR,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,GAAG,EAAE,QAAQ,CAAC,GAAG;gBACjB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,YAAY,EAAE,QAAQ,CAAC,YAAY;gBACnC,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QACnD,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,qCAAqC,EAAE;YACtE,MAAM,EAAE;gBACN,MAAM,EAAE,wDAAwD;gBAChE,YAAY,EAAE,WAAW;aAC1B;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC3B,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG;YAC/B,QAAQ,EAAE,uBAAY,CAAC,QAAQ;YAC/B,UAAU,EAAE,IAAI,CAAC,EAAE;YACnB,QAAQ,EAAE;gBACR,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB;SACF,CAAC;IACJ,CAAC;IAED,iBAAiB,CAAC,QAAsB;QACtC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,mBAAmB;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,aAAa,CAAC,aAAqB,EAAE,aAAsB;QACzD,IAAI,CAAC,aAAa;YAAE,OAAO,IAAI,CAAC;QAChC,OAAO,aAAa,KAAK,aAAa,CAAC;IACzC,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrD,CAAC;CACF;AA9ND,oCA8NC"}