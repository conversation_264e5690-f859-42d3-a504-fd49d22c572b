{"version": 3, "file": "twofa.service.js", "sourceRoot": "", "sources": ["../../../src/modules/auth/twofa.service.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAE5B,kEAAiE;AACjE,6DAAmD;AAYnD,MAAa,YAAY;IAIvB;QAFQ,YAAO,GAA6B,IAAI,GAAG,EAAE,CAAC;QAGpD,IAAI,CAAC,cAAc,GAAG,2BAAa,CAAC,aAAa,CAAC,kBAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc;QAC3B,gDAAgD;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE3C,0BAA0B;QAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE/C,+DAA+D;QAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEzD,4DAA4D;QAC5D,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,gBAAM,CAAC,UAAU,EAAE;YACvB,MAAM;YACN,MAAM;YACN,WAAW;YACX,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAEtC,OAAO;YACL,MAAM;YACN,SAAS;YACT,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,KAAa;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,KAAK,CAAC;SACd;QAED,2EAA2E;QAC3E,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC;QAElD,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;YAC3E,IAAI,KAAK,KAAK,aAAa,EAAE;gBAC3B,OAAO,IAAI,CAAC;aACb;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,UAAkB;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,KAAK,CAAC;SACd;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC7D,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC;SACd;QAED,yBAAyB;QACzB,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAC5C,UAAU,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc,EAAE,KAAa;QAC3C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;SAC3B;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;SAC3B;QAED,aAAa;QACb,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAErC,6BAA6B;QAC7B,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE;YACvC,cAAc,EAAE;gBACd,gBAAgB,EAAE,IAAI;gBACtB,kBAAkB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aAC7C;SACF,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,UAAU,CAAC,WAAW;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,MAAc;QAC7B,iBAAiB;QACjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE5B,6BAA6B;QAC7B,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE;YACvC,cAAc,EAAE;gBACd,gBAAgB,EAAE,KAAK;gBACvB,mBAAmB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aAC9C;SACF,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,UAAU,EAAE,QAAQ,IAAI,KAAK,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,MAAc;QACzC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACrD;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAClD,UAAU,CAAC,WAAW,GAAG,cAAc,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAErC,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,MAAc;QAK7B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE5C,OAAO;YACL,OAAO,EAAE,UAAU,EAAE,QAAQ,IAAI,KAAK;YACtC,oBAAoB,EAAE,UAAU,EAAE,WAAW,CAAC,MAAM,IAAI,CAAC;YACzD,UAAU,EAAE,UAAU,EAAE,UAAU;SACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,MAAM,GAAG,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YAC3B,KAAK,CAAC,IAAI,CAAC,gBAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;SACjE;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAc,EAAE,MAAc;QACtD,MAAM,MAAM,GAAG,QAAQ,CAAC;QACxB,MAAM,WAAW,GAAG,GAAG,MAAM,IAAI,MAAM,EAAE,CAAC;QAC1C,OAAO,kBAAkB,kBAAkB,CAAC,WAAW,CAAC,WAAW,MAAM,WAAW,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC;IACnH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,MAAc,EAAE,WAAmB;QACtD,sCAAsC;QACtC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAEnD,gDAAgD;QAChD,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;QACnE,UAAU,CAAC,aAAa,CAAC,WAAW,GAAG,UAAU,EAAE,CAAC,CAAC,CAAC;QAEtD,qBAAqB;QACrB,MAAM,IAAI,GAAG,gBAAM,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE3B,8BAA8B;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QAC3C,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAEvC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,KAAa;QAC7B,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,IAAY;QAClC,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAClD,CAAC;CACF;AAnPD,oCAmPC"}