"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const data_source_1 = require("../../infra/database/data-source");
const user_entity_1 = require("../users/entity/user.entity");
const RefreshToken_entity_1 = require("./entity/RefreshToken.entity");
const auth_dto_1 = require("./dto/auth.dto");
const oauth_service_1 = require("./oauth.service");
const email_service_1 = require("./email.service");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("config"));
const uuid_1 = require("uuid");
const crypto_1 = __importDefault(require("crypto"));
class AuthService {
    constructor() {
        this.sessions = new Map();
        this.userRepository = data_source_1.AppDataSource.getRepository(user_entity_1.User);
        this.refreshTokenRepository = data_source_1.AppDataSource.getRepository(RefreshToken_entity_1.RefreshToken);
        this.oauthService = new oauth_service_1.OAuthService();
        this.emailService = new email_service_1.EmailService();
    }
    async signUp(signUpDto) {
        const { email, password, workspaceId = 'default', firstName, lastName, metadata, emailConfirm = false } = signUpDto;
        // Check if user already exists in workspace
        const existingUser = await this.userRepository.findOne({
            where: { email, workspaceId }
        });
        if (existingUser) {
            throw new Error('User already exists in this workspace');
        }
        // Create new user
        const user = this.userRepository.create({
            email,
            password,
            firstName,
            lastName,
            workspaceId,
            role: auth_dto_1.UserRole.AUTHENTICATED,
            authProvider: auth_dto_1.AuthProvider.EMAIL,
            emailVerified: emailConfirm,
            confirmedAt: emailConfirm ? new Date() : undefined,
            rawUserMetaData: metadata,
            signInCount: 0,
            isActive: true
        });
        // Generate email verification token if needed
        if (!emailConfirm) {
            user.emailVerifyToken = this.generateSecureToken();
        }
        await this.userRepository.save(user);
        // Generate tokens
        const { accessToken, refreshToken, expiresIn, expiresAt } = await this.generateTokens(user);
        return {
            user: user.toSafeObject(),
            accessToken,
            refreshToken,
            expiresIn,
            expiresAt,
            tokenType: 'Bearer'
        };
    }
    async signIn(signInDto) {
        const { email, password, workspaceId = 'default' } = signInDto;
        // Find user with password
        const user = await this.userRepository.findOne({
            where: { email, workspaceId },
            select: ['id', 'email', 'password', 'firstName', 'lastName', 'workspaceId', 'role', 'emailVerified', 'isActive', 'isBanned']
        });
        if (!user) {
            throw new Error('Invalid credentials');
        }
        // Check if user is active and not banned
        if (!user.isActive) {
            throw new Error('Account is deactivated');
        }
        if (user.isBanned) {
            throw new Error('Account is temporarily banned');
        }
        // Verify password
        const isValidPassword = await user.comparePassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid credentials');
        }
        // Update login stats
        user.lastSignIn = new Date();
        user.signInCount += 1;
        await this.userRepository.save(user);
        // Generate tokens
        const { accessToken, refreshToken, expiresIn, expiresAt } = await this.generateTokens(user);
        return {
            user: user.toSafeObject(),
            accessToken,
            refreshToken,
            expiresIn,
            expiresAt,
            tokenType: 'Bearer'
        };
    }
    async refreshToken(refreshTokenDto) {
        const { refreshToken: token, workspaceId = 'default' } = refreshTokenDto;
        // Find refresh token
        const refreshTokenEntity = await this.refreshTokenRepository.findOne({
            where: { token, workspaceId },
            relations: ['user']
        });
        if (!refreshTokenEntity || !refreshTokenEntity.isValid()) {
            throw new Error('Invalid or expired refresh token');
        }
        // Update last used
        refreshTokenEntity.updateLastUsed();
        await this.refreshTokenRepository.save(refreshTokenEntity);
        // Check if user is still active
        if (!refreshTokenEntity.user.isActive) {
            throw new Error('User account is deactivated');
        }
        // Generate new tokens (refresh token rotation)
        const { accessToken, refreshToken: newRefreshToken, expiresIn, expiresAt } = await this.generateTokens(refreshTokenEntity.user);
        // Revoke old refresh token
        refreshTokenEntity.revoke();
        await this.refreshTokenRepository.save(refreshTokenEntity);
        return {
            accessToken,
            refreshToken: newRefreshToken,
            expiresIn,
            expiresAt,
            tokenType: 'Bearer'
        };
    }
    async resetPassword(resetPasswordDto) {
        const { email, workspaceId = 'default' } = resetPasswordDto;
        const user = await this.userRepository.findOne({
            where: { email, workspaceId }
        });
        if (!user) {
            // Don't reveal if user exists for security
            return { message: 'If the email exists, a password reset link has been sent' };
        }
        // Generate reset token
        const resetToken = this.generateSecureToken();
        const resetExpires = new Date(Date.now() + 3600000); // 1 hour
        user.passwordResetToken = resetToken;
        user.passwordResetExpires = resetExpires;
        await this.userRepository.save(user);
        // Send email with reset link
        try {
            await this.emailService.sendPasswordReset(user.email, resetToken);
        }
        catch (error) {
            console.error('Failed to send password reset email:', error);
            // Don't throw error to avoid revealing if user exists
        }
        return { message: 'If the email exists, a password reset link has been sent' };
    }
    async updatePassword(updatePasswordDto) {
        const { token, newPassword } = updatePasswordDto;
        const user = await this.userRepository.findOne({
            where: {
                passwordResetToken: token,
            },
            select: ['id', 'passwordResetExpires', 'passwordResetToken']
        });
        if (!user || !user.passwordResetExpires || user.passwordResetExpires < new Date()) {
            throw new Error('Invalid or expired reset token');
        }
        // Update password
        user.password = newPassword;
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        await this.userRepository.save(user);
        // Revoke all refresh tokens for this user
        await this.refreshTokenRepository.update({ userId: user.id }, { isRevoked: true, revokedAt: new Date() });
        return { message: 'Password updated successfully' };
    }
    async verifyEmail(verifyEmailDto) {
        const { token, workspaceId = 'default' } = verifyEmailDto;
        const user = await this.userRepository.findOne({
            where: { emailVerifyToken: token, workspaceId },
            select: ['id', 'emailVerifyToken', 'emailVerified']
        });
        if (!user) {
            throw new Error('Invalid verification token');
        }
        user.emailVerified = true;
        user.confirmedAt = new Date();
        user.emailVerifyToken = undefined;
        await this.userRepository.save(user);
        return {
            user: user.toSafeObject(),
            message: 'Email verified successfully'
        };
    }
    async sendMagicLink(magicLinkDto) {
        const { email, workspaceId = 'default', redirectTo } = magicLinkDto;
        let user = await this.userRepository.findOne({
            where: { email, workspaceId }
        });
        if (!user) {
            // Create new user for magic link
            user = this.userRepository.create({
                email,
                workspaceId,
                role: auth_dto_1.UserRole.AUTHENTICATED,
                authProvider: auth_dto_1.AuthProvider.EMAIL,
                emailVerified: true,
                confirmedAt: new Date(),
                isActive: true
            });
            await this.userRepository.save(user);
        }
        // Generate magic link token
        const magicToken = this.generateSecureToken();
        const magicExpires = new Date(Date.now() + 3600000); // 1 hour
        // Store magic link token with expiration
        user.magicLinkToken = magicToken;
        user.magicLinkExpires = magicExpires;
        await this.userRepository.save(user);
        // Send email with magic link
        try {
            await this.emailService.sendMagicLink(email, magicToken, redirectTo);
        }
        catch (error) {
            console.error('Failed to send magic link email:', error);
            // Don't throw error to avoid revealing if user exists
        }
        return { message: 'Magic link sent to your email' };
    }
    async verifyMagicLink(token) {
        const user = await this.userRepository.findOne({
            where: {
                magicLinkToken: token,
            },
            select: ['id', 'email', 'magicLinkToken', 'magicLinkExpires', 'workspaceId', 'role', 'adminRole']
        });
        if (!user || !user.magicLinkExpires || user.magicLinkExpires < new Date()) {
            throw new Error('Invalid or expired magic link token');
        }
        // Clear magic link token
        user.magicLinkToken = undefined;
        user.magicLinkExpires = undefined;
        user.lastSignIn = new Date();
        user.signInCount = (user.signInCount || 0) + 1;
        await this.userRepository.save(user);
        // Generate JWT tokens
        const tokens = await this.generateTokens(user);
        return {
            user: user.toSafeObject(),
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            expiresIn: tokens.expiresIn,
            expiresAt: tokens.expiresAt,
            tokenType: 'Bearer'
        };
    }
    async updateUser(userId, updateUserDto) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new Error('User not found');
        }
        // Update fields
        if (updateUserDto.firstName !== undefined)
            user.firstName = updateUserDto.firstName;
        if (updateUserDto.lastName !== undefined)
            user.lastName = updateUserDto.lastName;
        if (updateUserDto.email !== undefined)
            user.email = updateUserDto.email;
        if (updateUserDto.password !== undefined)
            user.password = updateUserDto.password;
        if (updateUserDto.metadata !== undefined)
            user.rawUserMetaData = updateUserDto.metadata;
        await this.userRepository.save(user);
        return user.toSafeObject();
    }
    async getUser(userId) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        return user ? user.toSafeObject() : null;
    }
    async signOut(refreshToken) {
        // Revoke refresh token
        await this.refreshTokenRepository.update({ token: refreshToken }, { isRevoked: true, revokedAt: new Date() });
        return { message: 'Signed out successfully' };
    }
    async signOutAll(userId) {
        // Revoke all refresh tokens for user
        await this.refreshTokenRepository.update({ userId }, { isRevoked: true, revokedAt: new Date() });
        return { message: 'Signed out from all devices' };
    }
    async generateTokens(user) {
        const jwtSecret = config_1.default.jwt?.secret || 'your_jwt_secret_key';
        const jwtExpiresIn = config_1.default.jwt?.expiresIn || '1h';
        const refreshExpiresIn = config_1.default.jwt?.refreshExpiresIn || '7d';
        // Generate access token
        const payload = user.getJwtPayload();
        const accessToken = jsonwebtoken_1.default.sign(payload, jwtSecret, { expiresIn: jwtExpiresIn });
        // Calculate expiration times
        const expiresIn = 3600; // 1 hour in seconds
        const expiresAt = Math.floor(Date.now() / 1000) + expiresIn;
        // Generate refresh token
        const refreshTokenEntity = this.refreshTokenRepository.create({
            userId: user.id,
            workspaceId: user.workspaceId,
            expiresAt: new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)) // 7 days
        });
        await this.refreshTokenRepository.save(refreshTokenEntity);
        return {
            accessToken,
            refreshToken: refreshTokenEntity.token,
            expiresIn,
            expiresAt
        };
    }
    generateSecureToken() {
        return crypto_1.default.randomBytes(32).toString('hex');
    }
    // JWT utilities
    verifyAccessToken(token) {
        try {
            const jwtSecret = config_1.default.jwt?.secret || 'your_jwt_secret_key';
            return jsonwebtoken_1.default.verify(token, jwtSecret);
        }
        catch (error) {
            throw new Error('Invalid access token');
        }
    }
    // Session management
    async createSession(sessionDto) {
        const sessionId = (0, uuid_1.v4)();
        const { accessToken, refreshToken } = await this.generateTokens(await this.userRepository.findOne({ where: { id: sessionDto.userId } }));
        const session = {
            id: sessionId,
            userId: sessionDto.userId,
            workspaceId: sessionDto.workspaceId,
            accessToken,
            refreshToken,
            expiresAt: new Date(Date.now() + 3600000),
            ipAddress: sessionDto.ipAddress,
            userAgent: sessionDto.userAgent,
            isActive: true,
            createdAt: new Date(),
            lastAccessedAt: new Date()
        };
        this.sessions.set(sessionId, session);
        return session;
    }
    async getSession(sessionId) {
        return this.sessions.get(sessionId) || null;
    }
    async revokeSession(sessionId) {
        this.sessions.delete(sessionId);
    }
    async getUserSessions(userId) {
        return Array.from(this.sessions.values()).filter(session => session.userId === userId);
    }
    async getUserById(userId) {
        return await this.userRepository.findOne({
            where: { id: userId }
        });
    }
    async getUserByEmail(email, workspaceId) {
        const whereClause = { email };
        if (workspaceId) {
            whereClause.workspaceId = workspaceId;
        }
        return await this.userRepository.findOne({
            where: whereClause
        });
    }
    // OAuth Methods
    getOAuthAuthUrl(provider, state) {
        try {
            return this.oauthService.getAuthUrl(provider, state);
        }
        catch (error) {
            throw new Error(`Failed to generate OAuth URL for ${provider}: ${error.message}`);
        }
    }
    async signInWithOAuth(oauthDto) {
        const { provider, code, state, workspaceId = 'default' } = oauthDto;
        try {
            // Validate provider is enabled
            if (!this.oauthService.isProviderEnabled(provider)) {
                throw new Error(`OAuth provider ${provider} is not enabled`);
            }
            // Exchange code for access token
            const accessToken = await this.oauthService.exchangeCodeForToken(provider, code);
            // Get user info from OAuth provider
            const oauthUserInfo = await this.oauthService.getUserInfo(provider, accessToken);
            // Find or create user
            let user = await this.findOrCreateOAuthUser(oauthUserInfo, workspaceId);
            // Update user with latest OAuth info
            user = await this.updateUserOAuthInfo(user, oauthUserInfo);
            // Generate tokens
            const { accessToken: authToken, refreshToken } = await this.generateTokens(user);
            // Create session
            await this.createSession({
                userId: user.id,
                workspaceId,
                ipAddress: undefined,
                userAgent: undefined
            });
            return {
                user,
                accessToken: authToken,
                refreshToken,
                expiresIn: 3600,
                expiresAt: Date.now() + 3600000,
                tokenType: 'Bearer'
            };
        }
        catch (error) {
            console.error(`OAuth sign-in failed for ${provider}:`, error.message);
            throw new Error(`OAuth authentication failed: ${error.message}`);
        }
    }
    async findOrCreateOAuthUser(oauthUserInfo, workspaceId) {
        // First, try to find user by email
        let user = await this.userRepository.findOne({
            where: { email: oauthUserInfo.email, workspaceId }
        });
        if (user) {
            // User exists, update OAuth provider info if needed
            if (!user.oauthProviders) {
                user.oauthProviders = {};
            }
            user.oauthProviders[oauthUserInfo.provider] = {
                providerId: oauthUserInfo.providerId,
                lastSignIn: new Date()
            };
            return await this.userRepository.save(user);
        }
        // Create new user
        user = this.userRepository.create({
            email: oauthUserInfo.email,
            firstName: oauthUserInfo.firstName,
            lastName: oauthUserInfo.lastName,
            workspaceId,
            provider: oauthUserInfo.provider,
            isEmailVerified: true,
            oauthProviders: {
                [oauthUserInfo.provider]: {
                    providerId: oauthUserInfo.providerId,
                    lastSignIn: new Date()
                }
            },
            metadata: {
                ...oauthUserInfo.metadata,
                avatar: oauthUserInfo.avatar,
                signUpMethod: 'oauth',
                oauthProvider: oauthUserInfo.provider
            },
            role: auth_dto_1.UserRole.AUTHENTICATED
        });
        return await this.userRepository.save(user);
    }
    async updateUserOAuthInfo(user, oauthUserInfo) {
        // Update user info with latest from OAuth provider
        if (oauthUserInfo.name && !user.firstName && !user.lastName) {
            user.firstName = oauthUserInfo.firstName;
            user.lastName = oauthUserInfo.lastName;
        }
        if (oauthUserInfo.avatar) {
            if (!user.metadata)
                user.metadata = {};
            user.metadata.avatar = oauthUserInfo.avatar;
        }
        // Update OAuth provider info
        if (!user.oauthProviders) {
            user.oauthProviders = {};
        }
        user.oauthProviders[oauthUserInfo.provider] = {
            providerId: oauthUserInfo.providerId,
            lastSignIn: new Date()
        };
        user.lastSignInAt = new Date();
        return await this.userRepository.save(user);
    }
    async linkOAuthProvider(userId, oauthDto) {
        const { provider, code } = oauthDto;
        try {
            // Get current user
            const user = await this.userRepository.findOne({ where: { id: userId } });
            if (!user) {
                throw new Error('User not found');
            }
            // Exchange code for access token
            const accessToken = await this.oauthService.exchangeCodeForToken(provider, code);
            // Get user info from OAuth provider
            const oauthUserInfo = await this.oauthService.getUserInfo(provider, accessToken);
            // Check if this OAuth account is already linked to another user
            const existingUser = await this.userRepository.findOne({
                where: { email: oauthUserInfo.email, workspaceId: user.workspaceId }
            });
            if (existingUser && existingUser.id !== userId) {
                throw new Error('This OAuth account is already linked to another user');
            }
            // Link OAuth provider to current user
            if (!user.oauthProviders) {
                user.oauthProviders = {};
            }
            user.oauthProviders[provider] = {
                providerId: oauthUserInfo.providerId,
                lastSignIn: new Date()
            };
            const updatedUser = await this.userRepository.save(user);
            return {
                message: `Successfully linked ${provider} account`,
                user: updatedUser
            };
        }
        catch (error) {
            console.error(`Failed to link OAuth provider ${provider}:`, error.message);
            throw new Error(`Failed to link OAuth account: ${error.message}`);
        }
    }
    async unlinkOAuthProvider(userId, provider) {
        try {
            const user = await this.userRepository.findOne({ where: { id: userId } });
            if (!user) {
                throw new Error('User not found');
            }
            if (!user.oauthProviders || !user.oauthProviders[provider]) {
                throw new Error(`${provider} account is not linked to this user`);
            }
            // Check if user has password or other OAuth providers
            const hasPassword = !!user.password;
            const hasOtherProviders = Object.keys(user.oauthProviders).length > 1;
            if (!hasPassword && !hasOtherProviders) {
                throw new Error('Cannot unlink the only authentication method. Please set a password first.');
            }
            // Remove OAuth provider
            delete user.oauthProviders[provider];
            const updatedUser = await this.userRepository.save(user);
            return {
                message: `Successfully unlinked ${provider} account`,
                user: updatedUser
            };
        }
        catch (error) {
            console.error(`Failed to unlink OAuth provider ${provider}:`, error.message);
            throw error;
        }
    }
    getEnabledOAuthProviders() {
        return this.oauthService.getEnabledProviders();
    }
    generateOAuthState() {
        return this.oauthService.generateState();
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=auth.service.js.map