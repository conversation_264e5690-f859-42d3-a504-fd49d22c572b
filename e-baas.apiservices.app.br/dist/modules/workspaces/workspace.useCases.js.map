{"version": 3, "file": "workspace.useCases.js", "sourceRoot": "", "sources": ["../../../src/modules/workspaces/workspace.useCases.ts"], "names": [], "mappings": ";;AAEA,uDAGgC;AAChC,6DAAyD;AACzD,4FAAgF;AAChF,kEAG0C;AAO1C,MAAqB,iBAAiB;IACpC,gBAAe,CAAC;IAEhB,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,IAAI;YACF,MAAM,UAAU,GAAG,MAAM,gCAAmB,CAAC,IAAI,CAAC;gBAChD,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;gBAC1B,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;SACxE;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,WAAmB,EACnB,MAAc;QAEd,IAAI;YACF,MAAM,SAAS,GAAG,MAAM,gCAAmB,CAAC,OAAO,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE;aAC5C,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,4BAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;aACpD;YAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;SACzC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACrE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CACV,IAAkB,EAClB,MAAc;QAEd,IAAI;YACF,MAAM,SAAS,GAAG,gCAAmB,CAAC,MAAM,CAAC;gBAC3C,GAAG,IAAI;gBACP,OAAO,EAAE,MAAM;aAChB,CAAC,CAAC;YAEH,MAAM,gCAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE1C,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;SACzC;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CACV,WAAmB,EACnB,IAAkB,EAClB,MAAc;QAEd,IAAI;YACF,MAAM,SAAS,GAAG,MAAM,gCAAmB,CAAC,OAAO,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE;aAC5C,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,4BAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;aACpD;YAED,MAAM,gBAAgB,GAAG;gBACvB,GAAG,SAAS;gBACZ,GAAG,IAAI;aACR,CAAC;YAEF,MAAM,gCAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEjD,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;SAChD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACrE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,WAAmB,EAAE,MAAc;QAC9C,IAAI;YACF,MAAM,SAAS,GAAG,MAAM,gCAAmB,CAAC,OAAO,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE;aAC5C,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,4BAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;aACpD;YAED,MAAM,gCAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACrE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,WAAmB,EAAE,MAAc;QACvD,IAAI;YACF,MAAM,SAAS,GAAG,MAAM,gCAAmB,CAAC,OAAO,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE;aAC5C,CAAC,CAAC;YAEH,OAAO,CAAC,CAAC,SAAS,CAAC;SACpB;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CACd,WAAmB,EACnB,YAAoB,EACpB,KAAa;QAEb,IAAI;YACF,8BAA8B;YAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;gBAChC,MAAM,4BAAY,CAAC,UAAU,CAC3B,sFAAsF,CACvF,CAAC;aACH;YAED,wCAAwC;YACxC,MAAM,QAAQ,GAAG,MAAM,qCAAwB,CAAC,OAAO,CAAC;gBACtD,KAAK,EAAE;oBACL,WAAW;oBACX,YAAY,EAAE,YAA4B;oBAC1C,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE;gBACb,MAAM,4BAAY,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC;aAC1D;YAED,+CAA+C;YAC/C,IAAI,YAAY,KAAK,oCAAY,CAAC,OAAO,EAAE;gBACzC,MAAM,4BAAY,CAAC,UAAU,CAC3B,iEAAiE,CAClE,CAAC;aACH;iBAAM;gBACL,0BAA0B;gBAC1B,MAAM,UAAU,GAAG,MAAM,IAAA,oCAAsB,EAAC,WAAW,EAAE;oBAC3D,IAAI,EAAE,YAAmB;oBACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B,CAAC,CAAC;gBAEH,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAE7C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBACpD,CAAC;aACH;SACF;QAAC,OAAO,KAAK,EAAE;YACd,IACE,KAAK,YAAY,KAAK;gBACtB,CAAC,KAAK,CAAC,OAAO,KAAK,2BAA2B;oBAC5C,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC,EACrD;gBACA,MAAM,KAAK,CAAC;aACb;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC;SACH;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CACf,WAAmB,EACnB,YAAoB,EACpB,IAAwB;QAExB,IAAI;YACF,wBAAwB;YACxB,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;gBAC5D,MAAM,4BAAY,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC;aACxD;YAED,wCAAwC;YACxC,MAAM,QAAQ,GAAG,MAAM,qCAAwB,CAAC,OAAO,CAAC;gBACtD,KAAK,EAAE;oBACL,WAAW;oBACX,YAAY,EAAE,YAA4B;oBAC1C,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE;gBACb,MAAM,4BAAY,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC;aAC1D;YAED,6CAA6C;YAC7C,IAAI,YAAY,KAAK,oCAAY,CAAC,OAAO,EAAE;gBACzC,kDAAkD;gBAClD,MAAM,WAAW,GAAG,MAAM,IAAA,gCAAkB,EAAC,WAAW,EAAE;oBACxD,GAAG,EACD,QAAQ,CAAC,gBAAgB;wBACzB,aAAa,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,QAAQ,EAAE;iBAC/G,CAAC,CAAC;gBAEH,MAAM,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAE7C,4CAA4C;gBAC5C,MAAM,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAErC,kDAAkD;gBAClD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO;qBAC5B,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC;qBAC1C,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;oBACb,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBACtB,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,EAAE;oBACxD,MAAM,EAAE,IAAI;iBACb,CAAC,CAAC,CAAC;gBAEN,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzB,MAAM,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC5C,MAAM,OAAO,CAAC,GAAG,CACf,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CACtB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE;wBAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,MAAM,EAAE,IAAI;qBACb,CAAC,CACH,CACF,CAAC;iBACH;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,uBAAuB,IAAI,CAAC,IAAI,wBAAwB;iBAClE,CAAC;aACH;iBAAM;gBACL,0BAA0B;gBAC1B,MAAM,UAAU,GAAG,MAAM,IAAA,oCAAsB,EAAC,WAAW,EAAE;oBAC3D,IAAI,EAAE,YAAmB;oBACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B,CAAC,CAAC;gBAEH,8BAA8B;gBAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;gBAEvE,MAAM,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAEvC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,UAAU,IAAI,CAAC,IAAI,wBAAwB;iBACrD,CAAC;aACH;SACF;QAAC,OAAO,KAAK,EAAE;YACd,IACE,KAAK,YAAY,KAAK;gBACtB,CAAC,KAAK,CAAC,OAAO,KAAK,2BAA2B;oBAC5C,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC,EAClD;gBACA,MAAM,KAAK,CAAC;aACb;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC;SACH;IACH,CAAC;IAEO,gBAAgB,CAAC,SAAoB;QAC3C,OAAO;YACL,EAAE,EAAE,SAAS,CAAC,EAAE;YAChB,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,SAAS,EAAE,SAAS,CAAC,SAAS;SAC/B,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,KAAa;QACpC,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAEnD,+BAA+B;QAC/B,IACE,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC;YACtC,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC;YACzC,eAAe,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAC1C;YACA,OAAO,IAAI,CAAC;SACb;QAED,6BAA6B;QAC7B,IACE,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC;YACvC,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAClC;YACA,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,sBAAsB,CAC5B,eAAmC,EACnC,YAAoB;QAEpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC;QAE1C,IAAI,iBAAiB,GAAG,OAAO;aAC5B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACd,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAE7B,uCAAuC;YACvC,IAAI,YAAY,KAAK,UAAU,EAAE;gBAC/B,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ;oBAAE,UAAU,GAAG,cAAc,CAAC;gBAC1D,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ;oBAAE,UAAU,GAAG,SAAS,CAAC;gBACrD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS;oBAAE,UAAU,GAAG,SAAS,CAAC;gBACtD,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;oBAAE,UAAU,GAAG,WAAW,CAAC;gBAC1D,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM;oBAAE,UAAU,GAAG,MAAM,CAAC;aACjD;iBAAM,IAAI,YAAY,KAAK,OAAO,EAAE;gBACnC,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ;oBAAE,UAAU,GAAG,cAAc,CAAC;gBAC1D,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ;oBAAE,UAAU,GAAG,KAAK,CAAC;gBACjD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS;oBAAE,UAAU,GAAG,YAAY,CAAC;gBACzD,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;oBAAE,UAAU,GAAG,UAAU,CAAC;gBACzD,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM;oBAAE,UAAU,GAAG,aAAa,CAAC;aACxD;YAED,IAAI,UAAU,GAAG,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAElD,IAAI,MAAM,CAAC,OAAO;gBAAE,UAAU,IAAI,cAAc,CAAC;YACjD,IAAI,MAAM,CAAC,MAAM;gBAAE,UAAU,IAAI,SAAS,CAAC;YAC3C,IAAI,MAAM,CAAC,QAAQ,KAAK,KAAK;gBAAE,UAAU,IAAI,WAAW,CAAC;YACzD,IAAI,MAAM,CAAC,OAAO;gBAAE,UAAU,IAAI,YAAY,MAAM,CAAC,OAAO,EAAE,CAAC;YAE/D,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,OAAO,iBAAiB,IAAI,MAAM,iBAAiB,GAAG,CAAC;IACzD,CAAC;CACF;AApWD,oCAoWC"}