{"version": 3, "file": "workspace.service.js", "sourceRoot": "", "sources": ["../../../src/modules/workspaces/workspace.service.ts"], "names": [], "mappings": ";;AAOA,MAAqB,gBAAgB;IAGnC,YAAY,iBAAoC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAgB,EAAE,GAAa;QAC1C,IAAI;YACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;aACxD;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACvE,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC7B;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACvD;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAgB,EAAE,GAAa;QAC1C,IAAI;YACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;aACxD;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAChE,WAAW,EACX,MAAM,CACP,CAAC;YACF,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5B;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;oBAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBACvD;gBACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACvD;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAgB,EAAE,GAAa;QAC1C,IAAI;YACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;aACxD;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,GAAG,CAAC,IAAoB,CAAC;YACtC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACpE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACxC;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACvD;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAgB,EAAE,GAAa;QAC1C,IAAI;YACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;aACxD;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,GAAG,GAAG,CAAC,IAAoB,CAAC;YACtC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CACnD,WAAW,EACX,IAAI,EACJ,MAAM,CACP,CAAC;YACF,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5B;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;oBAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBACvD;gBACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACvD;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAgB,EAAE,GAAa;QAC1C,IAAI;YACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;aACxD;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACzD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;SAC/B;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;oBAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBACvD;gBACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACvD;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,GAAgB,EAAE,GAAa;QAC9C,IAAI;YACF,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAmB,CAAC;YAExD,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;gBAC/B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;aACxD;YAED,qFAAqF;YACrF,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;gBAC9B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAC5D,WAAW,EACX,MAAM,CACP,CAAC;gBACF,IAAI,CAAC,SAAS,EAAE;oBACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;iBACrD;aACF;YAED,6EAA6E;YAC7E,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,KAAK,WAAW,EAAE;gBACrD,OAAO,GAAG;qBACP,MAAM,CAAC,GAAG,CAAC;qBACX,IAAI,CAAC,EAAE,KAAK,EAAE,2CAA2C,EAAE,CAAC,CAAC;aACjE;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CACpD,WAAW,EACX,YAAY,EACZ,KAAK,CACN,CAAC;YACF,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,IACE,KAAK,CAAC,OAAO,KAAK,qBAAqB;oBACvC,KAAK,CAAC,OAAO,KAAK,2BAA2B,EAC7C;oBACA,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBACvD;gBACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,0BAA0B,CAAC,EAAE;oBACtD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBACvD;gBACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACvD;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAAgB,EAAE,GAAa;QAC/C,IAAI;YACF,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,MAAM,IAAI,GAAG,GAAG,CAAC,IAA0B,CAAC;YAE5C,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;gBAC/B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;aACxD;YAED,qFAAqF;YACrF,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;gBAC9B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAC5D,WAAW,EACX,MAAM,CACP,CAAC;gBACF,IAAI,CAAC,SAAS,EAAE;oBACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;iBACrD;aACF;YAED,6EAA6E;YAC7E,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,KAAK,WAAW,EAAE;gBACrD,OAAO,GAAG;qBACP,MAAM,CAAC,GAAG,CAAC;qBACX,IAAI,CAAC,EAAE,KAAK,EAAE,2CAA2C,EAAE,CAAC,CAAC;aACjE;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACrD,WAAW,EACX,YAAY,EACZ,IAAI,CACL,CAAC;YACF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACrC;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,IACE,KAAK,CAAC,OAAO,KAAK,qBAAqB;oBACvC,KAAK,CAAC,OAAO,KAAK,2BAA2B,EAC7C;oBACA,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBACvD;gBACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE;oBACnD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBACvD;gBACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACvD;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;IACH,CAAC;CACF;AAvND,mCAuNC"}