"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Workspace = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
let Workspace = class Workspace {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], Workspace.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], Workspace.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Workspace.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_active", type: "boolean", default: true }),
    __metadata("design:type", Boolean)
], Workspace.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], Workspace.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], Workspace.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "owner_id" }),
    __metadata("design:type", String)
], Workspace.prototype, "ownerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)("User", "workspaces"),
    (0, typeorm_1.JoinColumn)({ name: "owner_id" }),
    __metadata("design:type", Object)
], Workspace.prototype, "owner", void 0);
__decorate([
    (0, typeorm_1.OneToMany)("ApiKey", "workspace"),
    __metadata("design:type", Array)
], Workspace.prototype, "apiKeys", void 0);
__decorate([
    (0, typeorm_1.OneToMany)("DatabaseConfig", "workspace"),
    __metadata("design:type", Array)
], Workspace.prototype, "databaseConfigs", void 0);
Workspace = __decorate([
    (0, typeorm_1.Entity)("workspaces")
], Workspace);
exports.Workspace = Workspace;
//# sourceMappingURL=Workspace.entity.js.map