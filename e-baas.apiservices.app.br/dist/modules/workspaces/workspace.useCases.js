"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const repository_1 = require("../../infra/repository");
const errorHandlers_1 = require("../../infra/errorHandlers");
const DatabaseConfig_entity_1 = require("../database-configs/entity/DatabaseConfig.entity");
const data_source_1 = require("../../infra/database/data-source");
class WorkspaceUseCases {
    constructor() { }
    async getAllByUserId(userId) {
        try {
            const workspaces = await repository_1.workspaceRepository.find({
                where: { ownerId: userId },
                order: { createdAt: "DESC" },
            });
            return workspaces.map((workspace) => this.mapToResponseDto(workspace));
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async getOneByIdAndUserId(workspaceId, userId) {
        try {
            const workspace = await repository_1.workspaceRepository.findOne({
                where: { id: workspaceId, ownerId: userId },
            });
            if (!workspace) {
                throw errorHandlers_1.ErrorHandler.NotFound("Workspace not found");
            }
            return this.mapToResponseDto(workspace);
        }
        catch (error) {
            if (error instanceof Error && error.message === "Workspace not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async create(data, userId) {
        try {
            const workspace = repository_1.workspaceRepository.create({
                ...data,
                ownerId: userId,
            });
            await repository_1.workspaceRepository.save(workspace);
            return this.mapToResponseDto(workspace);
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async update(workspaceId, data, userId) {
        try {
            const workspace = await repository_1.workspaceRepository.findOne({
                where: { id: workspaceId, ownerId: userId },
            });
            if (!workspace) {
                throw errorHandlers_1.ErrorHandler.NotFound("Workspace not found");
            }
            const updatedWorkspace = {
                ...workspace,
                ...data,
            };
            await repository_1.workspaceRepository.save(updatedWorkspace);
            return this.mapToResponseDto(updatedWorkspace);
        }
        catch (error) {
            if (error instanceof Error && error.message === "Workspace not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async delete(workspaceId, userId) {
        try {
            const workspace = await repository_1.workspaceRepository.findOne({
                where: { id: workspaceId, ownerId: userId },
            });
            if (!workspace) {
                throw errorHandlers_1.ErrorHandler.NotFound("Workspace not found");
            }
            await repository_1.workspaceRepository.remove(workspace);
        }
        catch (error) {
            if (error instanceof Error && error.message === "Workspace not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async checkUserAccess(workspaceId, userId) {
        try {
            const workspace = await repository_1.workspaceRepository.findOne({
                where: { id: workspaceId, ownerId: userId },
            });
            return !!workspace;
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async executeSql(workspaceId, databaseType, query) {
        try {
            // Verificar queries perigosas
            if (this.isDangerousQuery(query)) {
                throw errorHandlers_1.ErrorHandler.BadRequest("Dangerous query detected. Please avoid using DROP, TRUNCATE or DELETE without WHERE.");
            }
            // Buscar configuração de banco de dados
            const dbConfig = await repository_1.databaseConfigRepository.findOne({
                where: {
                    workspaceId,
                    databaseType: databaseType,
                    isActive: true,
                },
            });
            if (!dbConfig) {
                throw errorHandlers_1.ErrorHandler.NotFound("Database config not found");
            }
            // Executar query de acordo com o tipo de banco
            if (databaseType === DatabaseConfig_entity_1.DatabaseType.MONGODB) {
                throw errorHandlers_1.ErrorHandler.BadRequest("MongoDB queries should be executed through a different endpoint");
            }
            else {
                // Para PostgreSQL e MySQL
                const connection = await (0, data_source_1.getWorkspaceConnection)(workspaceId, {
                    type: databaseType,
                    host: dbConfig.host,
                    port: dbConfig.port,
                    username: dbConfig.username,
                    password: dbConfig.password,
                    database: dbConfig.database,
                });
                const result = await connection.query(query);
                return {
                    success: true,
                    data: result,
                    rowCount: Array.isArray(result) ? result.length : 0,
                };
            }
        }
        catch (error) {
            if (error instanceof Error &&
                (error.message === "Database config not found" ||
                    error.message.includes("Dangerous query detected"))) {
                throw error;
            }
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }
    async createTable(workspaceId, databaseType, data) {
        try {
            // Validar tipo de banco
            if (!["postgres", "mysql", "mongodb"].includes(databaseType)) {
                throw errorHandlers_1.ErrorHandler.BadRequest("Invalid database type");
            }
            // Buscar configuração de banco de dados
            const dbConfig = await repository_1.databaseConfigRepository.findOne({
                where: {
                    workspaceId,
                    databaseType: databaseType,
                    isActive: true,
                },
            });
            if (!dbConfig) {
                throw errorHandlers_1.ErrorHandler.NotFound("Database config not found");
            }
            // Criar tabela de acordo com o tipo de banco
            if (databaseType === DatabaseConfig_entity_1.DatabaseType.MONGODB) {
                // Para MongoDB, criamos uma coleção com validação
                const mongoClient = await (0, data_source_1.getMongoConnection)(workspaceId, {
                    url: dbConfig.connectionString ||
                        `mongodb://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`,
                });
                const db = mongoClient.db(dbConfig.database);
                // Criar coleção com validação se necessário
                await db.createCollection(data.name);
                // Criar índices para campos com unique ou primary
                const indexSpecs = data.columns
                    .filter((col) => col.unique || col.primary)
                    .map((col) => ({
                    key: { [col.name]: 1 },
                    name: `${col.name}_${col.unique ? "unique" : "primary"}`,
                    unique: true,
                }));
                if (indexSpecs.length > 0) {
                    const collection = db.collection(data.name);
                    await Promise.all(indexSpecs.map((spec) => collection.createIndex(spec.key, {
                        name: spec.name,
                        unique: true,
                    })));
                }
                return {
                    success: true,
                    message: `MongoDB collection '${data.name}' created successfully`,
                };
            }
            else {
                // Para PostgreSQL e MySQL
                const connection = await (0, data_source_1.getWorkspaceConnection)(workspaceId, {
                    type: databaseType,
                    host: dbConfig.host,
                    port: dbConfig.port,
                    username: dbConfig.username,
                    password: dbConfig.password,
                    database: dbConfig.database,
                });
                // Gerar SQL para criar tabela
                const createTableSQL = this.generateCreateTableSQL(data, databaseType);
                await connection.query(createTableSQL);
                return {
                    success: true,
                    message: `Table '${data.name}' created successfully`,
                };
            }
        }
        catch (error) {
            if (error instanceof Error &&
                (error.message === "Database config not found" ||
                    error.message.includes("Invalid database type"))) {
                throw error;
            }
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }
    mapToResponseDto(workspace) {
        return {
            id: workspace.id,
            name: workspace.name,
            description: workspace.description,
            ownerId: workspace.ownerId,
            isActive: workspace.isActive,
            createdAt: workspace.createdAt,
            updatedAt: workspace.updatedAt,
        };
    }
    isDangerousQuery(query) {
        const normalizedQuery = query.toUpperCase().trim();
        // Verificar comandos perigosos
        if (normalizedQuery.includes("DROP TABLE") ||
            normalizedQuery.includes("DROP DATABASE") ||
            normalizedQuery.includes("TRUNCATE TABLE")) {
            return true;
        }
        // Verificar DELETE sem WHERE
        if (normalizedQuery.includes("DELETE FROM") &&
            !normalizedQuery.includes("WHERE")) {
            return true;
        }
        return false;
    }
    generateCreateTableSQL(tableDefinition, databaseType) {
        const { name, columns } = tableDefinition;
        let columnDefinitions = columns
            .map((column) => {
            let columnType = column.type;
            // Mapear tipos para o banco específico
            if (databaseType === "postgres") {
                if (column.type === "string")
                    columnType = "VARCHAR(255)";
                if (column.type === "number")
                    columnType = "INTEGER";
                if (column.type === "boolean")
                    columnType = "BOOLEAN";
                if (column.type === "timestamp")
                    columnType = "TIMESTAMP";
                if (column.type === "uuid")
                    columnType = "UUID";
            }
            else if (databaseType === "mysql") {
                if (column.type === "string")
                    columnType = "VARCHAR(255)";
                if (column.type === "number")
                    columnType = "INT";
                if (column.type === "boolean")
                    columnType = "TINYINT(1)";
                if (column.type === "timestamp")
                    columnType = "DATETIME";
                if (column.type === "uuid")
                    columnType = "VARCHAR(36)";
            }
            let definition = `"${column.name}" ${columnType}`;
            if (column.primary)
                definition += " PRIMARY KEY";
            if (column.unique)
                definition += " UNIQUE";
            if (column.nullable === false)
                definition += " NOT NULL";
            if (column.default)
                definition += ` DEFAULT ${column.default}`;
            return definition;
        })
            .join(", ");
        return `CREATE TABLE "${name}" (${columnDefinitions})`;
    }
}
exports.default = WorkspaceUseCases;
//# sourceMappingURL=workspace.useCases.js.map