"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MODULE_INFO = exports.EdgeFunction = exports.EdgeFunctionsUseCases = exports.EdgeFunctionsService = exports.edgeFunctionsRouter = void 0;
// Edge Functions Module
var edge_functions_controller_1 = require("./edge-functions.controller");
Object.defineProperty(exports, "edgeFunctionsRouter", { enumerable: true, get: function () { return __importDefault(edge_functions_controller_1).default; } });
var edge_functions_service_1 = require("./edge-functions.service");
Object.defineProperty(exports, "EdgeFunctionsService", { enumerable: true, get: function () { return edge_functions_service_1.EdgeFunctionsService; } });
var edge_functions_useCases_1 = require("./edge-functions.useCases");
Object.defineProperty(exports, "EdgeFunctionsUseCases", { enumerable: true, get: function () { return edge_functions_useCases_1.EdgeFunctionsUseCases; } });
var EdgeFunction_entity_1 = require("./entity/EdgeFunction.entity");
Object.defineProperty(exports, "EdgeFunction", { enumerable: true, get: function () { return EdgeFunction_entity_1.EdgeFunction; } });
__exportStar(require("./dto/edge-functions.dto"), exports);
// Module information
exports.MODULE_INFO = {
    name: "edge-functions",
    version: "1.0.0",
    description: "Edge Functions with Deno Runtime - Serverless functions execution",
    endpoints: [
        "POST /functions/v1 - Create function",
        "GET /functions/v1 - List functions",
        "GET /functions/v1/:slug - Get function",
        "PUT /functions/v1/:slug - Update function",
        "DELETE /functions/v1/:slug - Delete function",
        "POST /functions/v1/:slug/deploy - Deploy function",
        "POST /functions/v1/invoke/:slug - Execute function (authenticated)",
        "GET /functions/v1/public/:slug - Execute public function (GET)",
        "POST /functions/v1/public/:slug - Execute public function (POST)",
        "GET /functions/v1/:slug/metrics - Get function metrics",
        "GET /functions/v1/:slug/logs - Get function logs",
        "GET /functions/v1/health - Health check"
    ],
    features: [
        "Deno Runtime Integration",
        "TypeScript Support",
        "HTTP/CRON/Database/Event Triggers",
        "Public and Authenticated Execution",
        "Environment Variables and Secrets",
        "Real-time Metrics and Logging",
        "Function Deployment and Versioning",
        "CORS Support",
        "Timeout and Memory Management"
    ]
};
//# sourceMappingURL=index.js.map