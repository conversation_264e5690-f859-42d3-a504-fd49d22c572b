"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EdgeFunction = void 0;
const typeorm_1 = require("typeorm");
const edge_functions_dto_1 = require("../dto/edge-functions.dto");
let EdgeFunction = class EdgeFunction {
    generateSourceCodeHash() {
        const crypto = require('crypto');
        this.sourceCodeHash = crypto
            .createHash('sha256')
            .update(this.sourceCode)
            .digest('hex');
    }
    // Helper methods
    isHTTPFunction() {
        return this.trigger === edge_functions_dto_1.FunctionTrigger.HTTP;
    }
    isCronFunction() {
        return this.trigger === edge_functions_dto_1.FunctionTrigger.CRON && !!this.cronSchedule;
    }
    isDatabaseFunction() {
        return this.trigger === edge_functions_dto_1.FunctionTrigger.DATABASE && !!this.databaseTrigger;
    }
    isEventFunction() {
        return this.trigger === edge_functions_dto_1.FunctionTrigger.EVENT && !!this.eventTypes?.length;
    }
    isActive() {
        return this.status === edge_functions_dto_1.FunctionStatus.ACTIVE;
    }
    canExecute() {
        return this.isActive() && !!this.sourceCode;
    }
    getTimeout() {
        return this.config?.timeoutMs || 10000;
    }
    getMemoryLimit() {
        return this.config?.memoryMB || 128;
    }
    getAllowedMethods() {
        return this.config?.allowedMethods || ['GET', 'POST'];
    }
    getAllowedOrigins() {
        return this.config?.allowedOrigins || ['*'];
    }
    isCorsEnabled() {
        return this.config?.enableCors ?? true;
    }
    incrementInvocation() {
        this.invocationCount += 1;
        this.lastInvokedAt = new Date();
    }
    incrementError() {
        this.errorCount += 1;
    }
    updateExecutionTime(executionTime) {
        this.totalExecutionTime += executionTime;
        this.averageExecutionTime = this.totalExecutionTime / this.invocationCount;
    }
    getErrorRate() {
        if (this.invocationCount === 0)
            return 0;
        return (this.errorCount / this.invocationCount) * 100;
    }
    toSafeObject() {
        const { secrets, ...safe } = this;
        return {
            ...safe,
            hasSecrets: !!secrets && Object.keys(secrets).length > 0
        };
    }
    getExecutionUrl(baseUrl) {
        if (this.isPublic) {
            return `${baseUrl}/functions/v1/public/${this.slug}`;
        }
        return `${baseUrl}/functions/v1/invoke/${this.slug}`;
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], EdgeFunction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], EdgeFunction.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, unique: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], EdgeFunction.prototype, "slug", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", length: 36 }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], EdgeFunction.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "text", nullable: true }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "source_code", type: "text" }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "sourceCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "source_code_hash", length: 64 }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "sourceCodeHash", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: edge_functions_dto_1.FunctionRuntime,
        default: edge_functions_dto_1.FunctionRuntime.DENO
    }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "runtime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: edge_functions_dto_1.FunctionTrigger,
        default: edge_functions_dto_1.FunctionTrigger.HTTP
    }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "trigger", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: edge_functions_dto_1.FunctionStatus,
        default: edge_functions_dto_1.FunctionStatus.ACTIVE
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], EdgeFunction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "jsonb", nullable: true }),
    __metadata("design:type", edge_functions_dto_1.FunctionConfigDto)
], EdgeFunction.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "environment_variables", type: "jsonb", nullable: true }),
    __metadata("design:type", Object)
], EdgeFunction.prototype, "environmentVariables", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "jsonb", nullable: true }),
    __metadata("design:type", Object)
], EdgeFunction.prototype, "secrets", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_public", default: false }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Boolean)
], EdgeFunction.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "cron_schedule", nullable: true }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "cronSchedule", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "database_trigger", type: "jsonb", nullable: true }),
    __metadata("design:type", Object)
], EdgeFunction.prototype, "databaseTrigger", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "event_types", type: "text", array: true, nullable: true }),
    __metadata("design:type", Array)
], EdgeFunction.prototype, "eventTypes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "created_by", nullable: true }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "updated_by", nullable: true }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "last_deployed_at", nullable: true }),
    __metadata("design:type", Date)
], EdgeFunction.prototype, "lastDeployedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "last_invoked_at", nullable: true }),
    __metadata("design:type", Date)
], EdgeFunction.prototype, "lastInvokedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "invocation_count", default: 0 }),
    __metadata("design:type", Number)
], EdgeFunction.prototype, "invocationCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "error_count", default: 0 }),
    __metadata("design:type", Number)
], EdgeFunction.prototype, "errorCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "total_execution_time", type: "bigint", default: 0 }),
    __metadata("design:type", Number)
], EdgeFunction.prototype, "totalExecutionTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "average_execution_time", type: "float", default: 0 }),
    __metadata("design:type", Number)
], EdgeFunction.prototype, "averageExecutionTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "version", default: "1.0.0" }),
    __metadata("design:type", String)
], EdgeFunction.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "deployment_size", type: "bigint", default: 0 }),
    __metadata("design:type", Number)
], EdgeFunction.prototype, "deploymentSize", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], EdgeFunction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], EdgeFunction.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    (0, typeorm_1.BeforeUpdate)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], EdgeFunction.prototype, "generateSourceCodeHash", null);
EdgeFunction = __decorate([
    (0, typeorm_1.Entity)("edge_functions"),
    (0, typeorm_1.Index)(["workspaceId", "slug"], { unique: true }),
    (0, typeorm_1.Index)(["workspaceId", "status"]),
    (0, typeorm_1.Index)(["status", "isPublic"])
], EdgeFunction);
exports.EdgeFunction = EdgeFunction;
//# sourceMappingURL=EdgeFunction.entity.js.map