"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionLogsDto = exports.ListFunctionsDto = exports.DeployFunctionDto = exports.FunctionInvocationDto = exports.ExecuteFunctionDto = exports.UpdateFunctionDto = exports.CreateFunctionDto = exports.FunctionConfigDto = exports.FunctionTrigger = exports.HttpMethod = exports.FunctionStatus = exports.FunctionRuntime = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var FunctionRuntime;
(function (FunctionRuntime) {
    FunctionRuntime["DENO"] = "deno";
    FunctionRuntime["DENO_DEPLOY"] = "deno-deploy";
})(FunctionRuntime = exports.FunctionRuntime || (exports.FunctionRuntime = {}));
var FunctionStatus;
(function (FunctionStatus) {
    FunctionStatus["ACTIVE"] = "active";
    FunctionStatus["INACTIVE"] = "inactive";
    FunctionStatus["DEPLOYING"] = "deploying";
    FunctionStatus["ERROR"] = "error";
})(FunctionStatus = exports.FunctionStatus || (exports.FunctionStatus = {}));
var HttpMethod;
(function (HttpMethod) {
    HttpMethod["GET"] = "GET";
    HttpMethod["POST"] = "POST";
    HttpMethod["PUT"] = "PUT";
    HttpMethod["DELETE"] = "DELETE";
    HttpMethod["PATCH"] = "PATCH";
    HttpMethod["HEAD"] = "HEAD";
    HttpMethod["OPTIONS"] = "OPTIONS";
})(HttpMethod = exports.HttpMethod || (exports.HttpMethod = {}));
var FunctionTrigger;
(function (FunctionTrigger) {
    FunctionTrigger["HTTP"] = "http";
    FunctionTrigger["CRON"] = "cron";
    FunctionTrigger["DATABASE"] = "database";
    FunctionTrigger["EVENT"] = "event";
})(FunctionTrigger = exports.FunctionTrigger || (exports.FunctionTrigger = {}));
class FunctionConfigDto {
    constructor() {
        this.timeoutMs = 10000;
        this.memoryMB = 128;
        this.allowedMethods = [HttpMethod.GET, HttpMethod.POST];
        this.allowedOrigins = ['*'];
        this.enableCors = true;
    }
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(60000),
    __metadata("design:type", Number)
], FunctionConfigDto.prototype, "timeoutMs", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(64),
    (0, class_validator_1.Max)(512),
    __metadata("design:type", Number)
], FunctionConfigDto.prototype, "memoryMB", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(HttpMethod, { each: true }),
    __metadata("design:type", Array)
], FunctionConfigDto.prototype, "allowedMethods", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], FunctionConfigDto.prototype, "allowedOrigins", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FunctionConfigDto.prototype, "enableCors", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], FunctionConfigDto.prototype, "headers", void 0);
exports.FunctionConfigDto = FunctionConfigDto;
class CreateFunctionDto {
    constructor() {
        this.runtime = FunctionRuntime.DENO;
        this.trigger = FunctionTrigger.HTTP;
        this.isPublic = false;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFunctionDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFunctionDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFunctionDto.prototype, "slug", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFunctionDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFunctionDto.prototype, "sourceCode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(FunctionRuntime),
    __metadata("design:type", String)
], CreateFunctionDto.prototype, "runtime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(FunctionTrigger),
    __metadata("design:type", String)
], CreateFunctionDto.prototype, "trigger", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FunctionConfigDto),
    __metadata("design:type", FunctionConfigDto)
], CreateFunctionDto.prototype, "config", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateFunctionDto.prototype, "environmentVariables", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateFunctionDto.prototype, "secrets", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateFunctionDto.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFunctionDto.prototype, "cronSchedule", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateFunctionDto.prototype, "databaseTrigger", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateFunctionDto.prototype, "eventTypes", void 0);
exports.CreateFunctionDto = CreateFunctionDto;
class UpdateFunctionDto {
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFunctionDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFunctionDto.prototype, "sourceCode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FunctionConfigDto),
    __metadata("design:type", FunctionConfigDto)
], UpdateFunctionDto.prototype, "config", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateFunctionDto.prototype, "environmentVariables", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateFunctionDto.prototype, "secrets", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateFunctionDto.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(FunctionStatus),
    __metadata("design:type", String)
], UpdateFunctionDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFunctionDto.prototype, "cronSchedule", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateFunctionDto.prototype, "databaseTrigger", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UpdateFunctionDto.prototype, "eventTypes", void 0);
exports.UpdateFunctionDto = UpdateFunctionDto;
class ExecuteFunctionDto {
    constructor() {
        this.method = HttpMethod.POST;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ExecuteFunctionDto.prototype, "functionSlug", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ExecuteFunctionDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(HttpMethod),
    __metadata("design:type", String)
], ExecuteFunctionDto.prototype, "method", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ExecuteFunctionDto.prototype, "headers", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], ExecuteFunctionDto.prototype, "body", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ExecuteFunctionDto.prototype, "queryParams", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExecuteFunctionDto.prototype, "path", void 0);
exports.ExecuteFunctionDto = ExecuteFunctionDto;
class FunctionInvocationDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], FunctionInvocationDto.prototype, "functionId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], FunctionInvocationDto.prototype, "payload", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], FunctionInvocationDto.prototype, "context", void 0);
exports.FunctionInvocationDto = FunctionInvocationDto;
class DeployFunctionDto {
    constructor() {
        this.force = false;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DeployFunctionDto.prototype, "functionSlug", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DeployFunctionDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeployFunctionDto.prototype, "version", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DeployFunctionDto.prototype, "force", void 0);
exports.DeployFunctionDto = DeployFunctionDto;
class ListFunctionsDto {
    constructor() {
        this.limit = 20;
        this.offset = 0;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ListFunctionsDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(FunctionStatus),
    __metadata("design:type", String)
], ListFunctionsDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(FunctionRuntime),
    __metadata("design:type", String)
], ListFunctionsDto.prototype, "runtime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListFunctionsDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ListFunctionsDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListFunctionsDto.prototype, "offset", void 0);
exports.ListFunctionsDto = ListFunctionsDto;
class FunctionLogsDto {
    constructor() {
        this.limit = 100;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], FunctionLogsDto.prototype, "functionSlug", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], FunctionLogsDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], FunctionLogsDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionLogsDto.prototype, "since", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionLogsDto.prototype, "until", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionLogsDto.prototype, "level", void 0);
exports.FunctionLogsDto = FunctionLogsDto;
//# sourceMappingURL=edge-functions.dto.js.map