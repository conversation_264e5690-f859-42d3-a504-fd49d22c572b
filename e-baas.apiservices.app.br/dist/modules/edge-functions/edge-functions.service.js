"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EdgeFunctionsService = void 0;
const data_source_1 = require("../../infra/database/data-source");
const EdgeFunction_entity_1 = require("./entity/EdgeFunction.entity");
const edge_functions_dto_1 = require("./dto/edge-functions.dto");
const child_process_1 = require("child_process");
const fs_1 = require("fs");
const path_1 = require("path");
const crypto_1 = require("crypto");
class EdgeFunctionsService {
    constructor() {
        this.functionRepository = data_source_1.AppDataSource.getRepository(EdgeFunction_entity_1.EdgeFunction);
        this.functionsDir = (0, path_1.join)(process.cwd(), 'functions');
        this.denoPath = this.getDenoPath();
        this.ensureFunctionsDirectory();
    }
    getDenoPath() {
        try {
            // Try to find Deno in PATH
            const denoPath = (0, child_process_1.execSync)('which deno', { encoding: 'utf8' }).trim();
            return denoPath;
        }
        catch (error) {
            console.warn('Deno not found in PATH, using default');
            return 'deno';
        }
    }
    ensureFunctionsDirectory() {
        if (!(0, fs_1.existsSync)(this.functionsDir)) {
            (0, fs_1.mkdirSync)(this.functionsDir, { recursive: true });
        }
    }
    generateSlug(name) {
        return name
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    getFunctionPath(workspaceId, slug) {
        return (0, path_1.join)(this.functionsDir, workspaceId, slug);
    }
    prepareFunctionCode(sourceCode, context) {
        const wrapperCode = `
// E-BaaS Edge Function Runtime
const FUNCTION_CONTEXT = ${JSON.stringify(context)};

// Global context for the function
globalThis.context = FUNCTION_CONTEXT;
globalThis.env = FUNCTION_CONTEXT.environment;

// Enhanced logging
const originalConsole = globalThis.console;
const logs = [];

globalThis.console = {
  ...originalConsole,
  log: (...args) => {
    logs.push({ level: 'info', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsole.log('[INFO]', ...args);
  },
  warn: (...args) => {
    logs.push({ level: 'warn', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsole.warn('[WARN]', ...args);
  },
  error: (...args) => {
    logs.push({ level: 'error', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsole.error('[ERROR]', ...args);
  },
  debug: (...args) => {
    logs.push({ level: 'debug', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsole.debug('[DEBUG]', ...args);
  }
};

// Function timeout handler
const timeoutMs = ${context.config.timeoutMs || 10000};
let timeoutId;

// Performance monitoring
const startTime = Date.now();
let memoryUsed = 0;

// Wrap the user function
async function executeUserFunction() {
  try {
    // User function code
    ${sourceCode}
  } catch (error) {
    console.error('Function execution error:', error);
    throw error;
  }
}

// Main execution wrapper
async function main() {
  return new Promise(async (resolve, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error('Function execution timeout'));
    }, timeoutMs);

    try {
      const result = await executeUserFunction();
      
      // Calculate memory usage (approximation)
      memoryUsed = Math.round((process.memoryUsage().heapUsed - process.memoryUsage().heapTotal) / 1024 / 1024);
      
      const executionTime = Date.now() - startTime;
      
      clearTimeout(timeoutId);
      resolve({
        result,
        executionTime,
        memoryUsed,
        logs
      });
    } catch (error) {
      clearTimeout(timeoutId);
      reject(error);
    }
  });
}

if (import.meta.main) {
  main().then(result => {
    console.log('__EBAAS_RESULT__', JSON.stringify(result));
    Deno.exit(0);
  }).catch(error => {
    console.error('__EBAAS_ERROR__', error.message);
    Deno.exit(1);
  });
}
`;
        return wrapperCode;
    }
    // CRUD Operations
    async createFunction(createFunctionDto, createdBy) {
        const slug = this.generateSlug(createFunctionDto.slug || createFunctionDto.name);
        // Check if function with slug already exists
        const existingFunction = await this.functionRepository.findOne({
            where: { workspaceId: createFunctionDto.workspaceId, slug }
        });
        if (existingFunction) {
            throw new Error(`Function with slug '${slug}' already exists`);
        }
        const functionEntity = this.functionRepository.create({
            ...createFunctionDto,
            slug,
            createdBy,
            status: edge_functions_dto_1.FunctionStatus.INACTIVE
        });
        const savedFunction = await this.functionRepository.save(functionEntity);
        // Create function directory and write initial code
        const functionPath = this.getFunctionPath(savedFunction.workspaceId, savedFunction.slug);
        (0, fs_1.mkdirSync)(functionPath, { recursive: true });
        (0, fs_1.writeFileSync)((0, path_1.join)(functionPath, 'index.ts'), savedFunction.sourceCode);
        return savedFunction;
    }
    async updateFunction(functionSlug, workspaceId, updateFunctionDto, updatedBy) {
        const functionEntity = await this.functionRepository.findOne({
            where: { slug: functionSlug, workspaceId }
        });
        if (!functionEntity) {
            throw new Error('Function not found');
        }
        Object.assign(functionEntity, {
            ...updateFunctionDto,
            updatedBy
        });
        const updatedFunction = await this.functionRepository.save(functionEntity);
        // Update source code file if changed
        if (updateFunctionDto.sourceCode) {
            const functionPath = this.getFunctionPath(updatedFunction.workspaceId, updatedFunction.slug);
            (0, fs_1.writeFileSync)((0, path_1.join)(functionPath, 'index.ts'), updatedFunction.sourceCode);
        }
        return updatedFunction;
    }
    async getFunction(functionSlug, workspaceId) {
        return await this.functionRepository.findOne({
            where: { slug: functionSlug, workspaceId }
        });
    }
    async listFunctions(listFunctionsDto) {
        const queryBuilder = this.functionRepository.createQueryBuilder('func');
        queryBuilder.where('func.workspaceId = :workspaceId', { workspaceId: listFunctionsDto.workspaceId });
        if (listFunctionsDto.status) {
            queryBuilder.andWhere('func.status = :status', { status: listFunctionsDto.status });
        }
        if (listFunctionsDto.runtime) {
            queryBuilder.andWhere('func.runtime = :runtime', { runtime: listFunctionsDto.runtime });
        }
        if (listFunctionsDto.search) {
            queryBuilder.andWhere('(func.name ILIKE :search OR func.description ILIKE :search)', { search: `%${listFunctionsDto.search}%` });
        }
        queryBuilder.orderBy('func.createdAt', 'DESC');
        if (listFunctionsDto.limit) {
            queryBuilder.limit(listFunctionsDto.limit);
        }
        if (listFunctionsDto.offset) {
            queryBuilder.offset(listFunctionsDto.offset);
        }
        const [functions, total] = await queryBuilder.getManyAndCount();
        return { functions, total };
    }
    async deleteFunction(functionSlug, workspaceId) {
        const functionEntity = await this.functionRepository.findOne({
            where: { slug: functionSlug, workspaceId }
        });
        if (!functionEntity) {
            throw new Error('Function not found');
        }
        // Remove function directory
        const functionPath = this.getFunctionPath(functionEntity.workspaceId, functionEntity.slug);
        if ((0, fs_1.existsSync)(functionPath)) {
            (0, fs_1.rmSync)(functionPath, { recursive: true, force: true });
        }
        await this.functionRepository.remove(functionEntity);
        return { message: `Function '${functionSlug}' deleted successfully` };
    }
    // Deployment and Execution
    async deployFunction(deployFunctionDto) {
        const functionEntity = await this.functionRepository.findOne({
            where: { slug: deployFunctionDto.functionSlug, workspaceId: deployFunctionDto.workspaceId }
        });
        if (!functionEntity) {
            throw new Error('Function not found');
        }
        functionEntity.status = edge_functions_dto_1.FunctionStatus.DEPLOYING;
        await this.functionRepository.save(functionEntity);
        try {
            const functionPath = this.getFunctionPath(functionEntity.workspaceId, functionEntity.slug);
            const buildLogs = [];
            // Validate Deno syntax
            try {
                const checkResult = (0, child_process_1.execSync)(`${this.denoPath} check ${(0, path_1.join)(functionPath, 'index.ts')}`, { encoding: 'utf8', timeout: 30000 });
                buildLogs.push('✅ TypeScript syntax validation passed');
            }
            catch (error) {
                buildLogs.push(`❌ TypeScript validation failed: ${error.message}`);
                throw new Error('Function validation failed');
            }
            // Update function status and deployment info
            const version = deployFunctionDto.version || `${Date.now()}`;
            functionEntity.status = edge_functions_dto_1.FunctionStatus.ACTIVE;
            functionEntity.version = version;
            functionEntity.lastDeployedAt = new Date();
            // Calculate deployment size
            const stats = require('fs').statSync((0, path_1.join)(functionPath, 'index.ts'));
            functionEntity.deploymentSize = stats.size;
            await this.functionRepository.save(functionEntity);
            return {
                version,
                deployedAt: new Date(),
                status: 'success',
                buildLogs,
                size: functionEntity.deploymentSize
            };
        }
        catch (error) {
            functionEntity.status = edge_functions_dto_1.FunctionStatus.ERROR;
            await this.functionRepository.save(functionEntity);
            return {
                version: 'failed',
                deployedAt: new Date(),
                status: 'failed',
                buildLogs: [`❌ Deployment failed: ${error.message}`],
                size: 0
            };
        }
    }
    async executeFunction(executeFunctionDto) {
        const functionEntity = await this.functionRepository.findOne({
            where: { slug: executeFunctionDto.functionSlug, workspaceId: executeFunctionDto.workspaceId }
        });
        if (!functionEntity) {
            throw new Error('Function not found');
        }
        if (!functionEntity.canExecute()) {
            throw new Error('Function is not executable');
        }
        const requestId = (0, crypto_1.randomUUID)();
        const context = {
            functionId: functionEntity.id,
            workspaceId: functionEntity.workspaceId,
            requestId,
            timestamp: new Date(),
            environment: {
                ...functionEntity.environmentVariables,
                EBAAS_FUNCTION_ID: functionEntity.id,
                EBAAS_WORKSPACE_ID: functionEntity.workspaceId,
                EBAAS_REQUEST_ID: requestId
            },
            secrets: functionEntity.secrets || {},
            config: functionEntity.config || {}
        };
        const functionPath = this.getFunctionPath(functionEntity.workspaceId, functionEntity.slug);
        const wrappedCode = this.prepareFunctionCode(functionEntity.sourceCode, context);
        const tempFile = (0, path_1.join)(functionPath, `temp_${requestId}.ts`);
        try {
            // Write wrapped code to temporary file
            (0, fs_1.writeFileSync)(tempFile, wrappedCode);
            const startTime = Date.now();
            // Execute with Deno
            const result = await new Promise((resolve, reject) => {
                const denoProcess = (0, child_process_1.spawn)(this.denoPath, [
                    'run',
                    '--allow-all',
                    '--no-check',
                    tempFile
                ], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    timeout: functionEntity.getTimeout()
                });
                let stdout = '';
                let stderr = '';
                denoProcess.stdout?.on('data', (data) => {
                    stdout += data.toString();
                });
                denoProcess.stderr?.on('data', (data) => {
                    stderr += data.toString();
                });
                denoProcess.on('close', (code) => {
                    const executionTime = Date.now() - startTime;
                    try {
                        // Parse result from stdout
                        const resultMatch = stdout.match(/__EBAAS_RESULT__\s+(.+)/);
                        const errorMatch = stderr.match(/__EBAAS_ERROR__\s+(.+)/);
                        if (errorMatch) {
                            reject(new Error(errorMatch[1]));
                            return;
                        }
                        if (resultMatch) {
                            const executionResult = JSON.parse(resultMatch[1]);
                            resolve({
                                statusCode: 200,
                                headers: { 'Content-Type': 'application/json' },
                                body: executionResult.result,
                                executionTime: executionResult.executionTime || executionTime,
                                memoryUsed: executionResult.memoryUsed || 0,
                                logs: executionResult.logs || []
                            });
                        }
                        else {
                            resolve({
                                statusCode: 200,
                                headers: { 'Content-Type': 'text/plain' },
                                body: stdout || 'Function executed successfully',
                                executionTime,
                                memoryUsed: 0,
                                logs: []
                            });
                        }
                    }
                    catch (error) {
                        reject(new Error(`Failed to parse function result: ${error.message}`));
                    }
                });
                denoProcess.on('error', (error) => {
                    reject(new Error(`Function execution failed: ${error.message}`));
                });
                // Send request data to function if needed
                if (executeFunctionDto.body) {
                    denoProcess.stdin?.write(JSON.stringify(executeFunctionDto.body));
                }
                denoProcess.stdin?.end();
            });
            // Update function statistics
            functionEntity.incrementInvocation();
            functionEntity.updateExecutionTime(result.executionTime);
            await this.functionRepository.save(functionEntity);
            return result;
        }
        catch (error) {
            // Update error statistics
            functionEntity.incrementInvocation();
            functionEntity.incrementError();
            await this.functionRepository.save(functionEntity);
            throw new Error(`Function execution failed: ${error.message}`);
        }
        finally {
            // Clean up temporary file
            if ((0, fs_1.existsSync)(tempFile)) {
                (0, fs_1.rmSync)(tempFile);
            }
        }
    }
    // Monitoring and Analytics
    async getFunctionMetrics(functionSlug, workspaceId, period) {
        const functionEntity = await this.functionRepository.findOne({
            where: { slug: functionSlug, workspaceId }
        });
        if (!functionEntity) {
            throw new Error('Function not found');
        }
        return {
            invocations: functionEntity.invocationCount,
            errors: functionEntity.errorCount,
            averageExecutionTime: functionEntity.averageExecutionTime,
            averageMemoryUsage: 0,
            totalExecutionTime: functionEntity.totalExecutionTime,
            period
        };
    }
    async getFunctionLogs(functionLogsDto) {
        // For now, return empty logs since we'd need a proper logging system
        // In a real implementation, you'd store logs in a database or log service
        return [];
    }
    // Health and Status
    async healthCheck() {
        try {
            // Check if Deno is available
            let denoAvailable = false;
            try {
                (0, child_process_1.execSync)(`${this.denoPath} --version`, { timeout: 5000 });
                denoAvailable = true;
            }
            catch {
                denoAvailable = false;
            }
            const [functionsCount, activeFunctions] = await Promise.all([
                this.functionRepository.count(),
                this.functionRepository.count({ where: { status: edge_functions_dto_1.FunctionStatus.ACTIVE } })
            ]);
            const status = denoAvailable ? 'healthy' : 'degraded';
            return {
                status,
                denoAvailable,
                functionsCount,
                activeFunctions
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                denoAvailable: false,
                functionsCount: 0,
                activeFunctions: 0
            };
        }
    }
}
exports.EdgeFunctionsService = EdgeFunctionsService;
//# sourceMappingURL=edge-functions.service.js.map