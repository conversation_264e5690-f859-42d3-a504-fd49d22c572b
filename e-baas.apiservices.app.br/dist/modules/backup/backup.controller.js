"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const backup_service_1 = require("./backup.service");
const admin_auth_middleware_1 = require("../../infra/middlewares/admin-auth.middleware");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const backup_dto_1 = require("./dto/backup.dto");
const path = __importStar(require("path"));
const backupRouter = (0, express_1.Router)();
const backupService = new backup_service_1.BackupService();
// Helper function for validation
const validateDto = async (DtoClass, data) => {
    const dto = (0, class_transformer_1.plainToClass)(DtoClass, data);
    const errors = await (0, class_validator_1.validate)(dto);
    if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
    }
    return dto;
};
// ==== PLATFORM ADMIN BACKUP ROUTES ====
/**
 * @swagger
 * /admin/v1/platform/backups:
 *   post:
 *     summary: Create platform backup (Platform Admin only)
 *     tags: [Platform Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               adminOnly:
 *                 type: boolean
 *                 description: Backup only admin data, not client workspaces
 *               includeDatabase:
 *                 type: boolean
 *                 default: true
 *               includeStorage:
 *                 type: boolean
 *                 default: true
 *               includeUsers:
 *                 type: boolean
 *                 default: true
 *               includeWorkspaces:
 *                 type: boolean
 *                 default: true
 *               compressionLevel:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 9
 *                 default: 6
 */
backupRouter.post("/platform/backups", admin_auth_middleware_1.requirePlatformAdmin, async (req, res) => {
    try {
        const createBackupDto = await validateDto(backup_dto_1.CreateBackupDto, req.body);
        const options = {
            adminOnly: createBackupDto.adminOnly || false,
            includeDatabase: createBackupDto.includeDatabase,
            includeStorage: createBackupDto.includeStorage,
            includeUsers: createBackupDto.includeUsers,
            includeWorkspaces: createBackupDto.includeWorkspaces,
            compressionLevel: createBackupDto.compressionLevel
        };
        const backup = await backupService.createPlatformBackup(options);
        return res.status(201).json({
            message: 'Platform backup created successfully',
            backup: {
                id: backup.id,
                filename: backup.filename,
                size: backup.size,
                type: backup.type,
                createdAt: backup.createdAt,
                metadata: backup.metadata
            }
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /admin/v1/platform/backups:
 *   get:
 *     summary: List all platform backups (Platform Admin only)
 *     tags: [Platform Admin - Backups]
 *     security:
 *       - bearerAuth: []
 */
backupRouter.get("/platform/backups", admin_auth_middleware_1.requirePlatformAdmin, async (req, res) => {
    try {
        const backups = await backupService.listBackups();
        return res.status(200).json({
            backups: backups.map(backup => ({
                id: backup.id,
                filename: backup.filename,
                size: backup.size,
                type: backup.type,
                workspaceId: backup.workspaceId,
                createdAt: backup.createdAt,
                metadata: backup.metadata
            }))
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /admin/v1/platform/backups/{backupId}/download:
 *   get:
 *     summary: Download platform backup (Platform Admin only)
 *     tags: [Platform Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: backupId
 *         required: true
 *         schema:
 *           type: string
 */
backupRouter.get("/platform/backups/:backupId/download", admin_auth_middleware_1.requirePlatformAdmin, async (req, res) => {
    try {
        const { backupId } = req.params;
        const backupPath = await backupService.downloadBackup(backupId);
        const filename = path.basename(backupPath);
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Type', 'application/zip');
        return res.download(backupPath, filename);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /admin/v1/platform/backups/{backupId}:
 *   delete:
 *     summary: Delete platform backup (Platform Admin only)
 *     tags: [Platform Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: backupId
 *         required: true
 *         schema:
 *           type: string
 */
backupRouter.delete("/platform/backups/:backupId", admin_auth_middleware_1.requirePlatformAdmin, async (req, res) => {
    try {
        const { backupId } = req.params;
        await backupService.deleteBackup(backupId);
        return res.status(200).json({
            message: 'Backup deleted successfully'
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// ==== WORKSPACE ADMIN BACKUP ROUTES ====
/**
 * @swagger
 * /admin/v1/workspace/backups:
 *   post:
 *     summary: Create workspace backup (Workspace Admin only)
 *     tags: [Workspace Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               includeDatabase:
 *                 type: boolean
 *                 default: true
 *               includeStorage:
 *                 type: boolean
 *                 default: true
 *               includeUsers:
 *                 type: boolean
 *                 default: true
 *               compressionLevel:
 *                 type: number
 *                 minimum: 1
 *                 maximum: 9
 *                 default: 6
 */
backupRouter.post("/workspace/backups", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const createBackupDto = await validateDto(backup_dto_1.CreateBackupDto, req.body);
        const workspaceId = req.user?.workspaceId;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID required' });
        }
        const options = {
            workspaceId,
            includeDatabase: createBackupDto.includeDatabase,
            includeStorage: createBackupDto.includeStorage,
            includeUsers: createBackupDto.includeUsers,
            includeWorkspaces: createBackupDto.includeWorkspaces,
            compressionLevel: createBackupDto.compressionLevel
        };
        const backup = await backupService.createWorkspaceBackup(options);
        return res.status(201).json({
            message: 'Workspace backup created successfully',
            backup: {
                id: backup.id,
                filename: backup.filename,
                size: backup.size,
                type: backup.type,
                workspaceId: backup.workspaceId,
                createdAt: backup.createdAt,
                metadata: backup.metadata
            }
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /admin/v1/workspace/backups:
 *   get:
 *     summary: List workspace backups (Workspace Admin only)
 *     tags: [Workspace Admin - Backups]
 *     security:
 *       - bearerAuth: []
 */
backupRouter.get("/workspace/backups", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const workspaceId = req.user?.workspaceId;
        const backups = await backupService.listBackups(workspaceId);
        return res.status(200).json({
            backups: backups.map(backup => ({
                id: backup.id,
                filename: backup.filename,
                size: backup.size,
                type: backup.type,
                createdAt: backup.createdAt,
                metadata: backup.metadata
            }))
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /admin/v1/workspace/backups/{backupId}/download:
 *   get:
 *     summary: Download workspace backup (Workspace Admin only)
 *     tags: [Workspace Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: backupId
 *         required: true
 *         schema:
 *           type: string
 */
backupRouter.get("/workspace/backups/:backupId/download", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const { backupId } = req.params;
        const workspaceId = req.user?.workspaceId;
        const backupPath = await backupService.downloadBackup(backupId, workspaceId);
        const filename = path.basename(backupPath);
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Type', 'application/zip');
        return res.download(backupPath, filename);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /admin/v1/workspace/backups/{backupId}:
 *   delete:
 *     summary: Delete workspace backup (Workspace Admin only)
 *     tags: [Workspace Admin - Backups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: backupId
 *         required: true
 *         schema:
 *           type: string
 */
backupRouter.delete("/workspace/backups/:backupId", admin_auth_middleware_1.requireWorkspaceAdmin, admin_auth_middleware_1.scopeToWorkspace, async (req, res) => {
    try {
        const { backupId } = req.params;
        const workspaceId = req.user?.workspaceId;
        await backupService.deleteBackup(backupId, workspaceId);
        return res.status(200).json({
            message: 'Backup deleted successfully'
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// ==== BACKUP STATUS & INFO ROUTES ====
/**
 * @swagger
 * /admin/v1/backups/status:
 *   get:
 *     summary: Get backup system status
 *     tags: [Admin - Backups]
 *     security:
 *       - bearerAuth: []
 */
backupRouter.get("/backups/status", admin_auth_middleware_1.requireAdmin, async (req, res) => {
    try {
        const workspaceId = req.user?.isPlatformAdmin ? undefined : req.user?.workspaceId;
        const backups = await backupService.listBackups(workspaceId);
        const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
        const lastBackup = backups[0];
        return res.status(200).json({
            totalBackups: backups.length,
            totalSize,
            lastBackup: lastBackup ? {
                id: lastBackup.id,
                type: lastBackup.type,
                createdAt: lastBackup.createdAt,
                size: lastBackup.size
            } : null,
            systemStatus: 'operational',
            backupDirectory: process.env.BACKUP_DIRECTORY || './backups'
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
exports.default = backupRouter;
//# sourceMappingURL=backup.controller.js.map