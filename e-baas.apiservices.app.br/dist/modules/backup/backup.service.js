"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackupService = void 0;
const data_source_1 = require("../../infra/database/data-source");
const user_entity_1 = require("../users/entity/user.entity");
const Workspace_entity_1 = require("../workspaces/entity/Workspace.entity");
const StorageFile_entity_1 = require("../storage/entity/StorageFile.entity");
const Bucket_entity_1 = require("../storage/entity/Bucket.entity");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const archiver = __importStar(require("archiver"));
const fs_1 = require("fs");
class BackupService {
    constructor() {
        this.userRepository = data_source_1.AppDataSource.getRepository(user_entity_1.User);
        this.workspaceRepository = data_source_1.AppDataSource.getRepository(Workspace_entity_1.Workspace);
        this.storageFileRepository = data_source_1.AppDataSource.getRepository(StorageFile_entity_1.StorageFile);
        this.bucketRepository = data_source_1.AppDataSource.getRepository(Bucket_entity_1.Bucket);
        this.backupDirectory = process.env.BACKUP_DIRECTORY || './backups';
        // Ensure backup directory exists
        if (!fs.existsSync(this.backupDirectory)) {
            fs.mkdirSync(this.backupDirectory, { recursive: true });
        }
    }
    // ==== PLATFORM ADMIN BACKUPS ====
    async createPlatformBackup(options) {
        const backupId = this.generateBackupId('platform');
        const backupPath = path.join(this.backupDirectory, `${backupId}.zip`);
        console.log('Starting platform backup...', { backupId, options });
        const archive = archiver('zip', {
            zlib: { level: options.compressionLevel || 6 }
        });
        const output = (0, fs_1.createWriteStream)(backupPath);
        archive.pipe(output);
        let metadata = {
            databaseTables: [],
            storageFiles: 0,
            userCount: 0,
            compressionRatio: 0
        };
        try {
            // 1. Backup Database
            if (options.includeDatabase) {
                const dbBackup = await this.createDatabaseBackup(options.adminOnly ? 'admin' : 'full');
                archive.append(JSON.stringify(dbBackup, null, 2), { name: 'database/full_backup.json' });
                metadata.databaseTables = dbBackup.tables.map(t => t.name);
            }
            // 2. Backup Users
            if (options.includeUsers) {
                const users = options.adminOnly
                    ? await this.getAdminUsers()
                    : await this.getAllUsers();
                archive.append(JSON.stringify(users, null, 2), { name: 'users/users.json' });
                metadata.userCount = users.length;
            }
            // 3. Backup Workspaces
            if (options.includeWorkspaces && !options.adminOnly) {
                const workspaces = await this.getAllWorkspaces();
                archive.append(JSON.stringify(workspaces, null, 2), { name: 'workspaces/workspaces.json' });
            }
            // 4. Backup Storage
            if (options.includeStorage) {
                const storageBackup = options.adminOnly
                    ? await this.createAdminStorageBackup(archive)
                    : await this.createFullStorageBackup(archive);
                metadata.storageFiles = storageBackup.fileCount;
            }
            // 5. Backup System Configuration
            const systemConfig = await this.getSystemConfiguration();
            archive.append(JSON.stringify(systemConfig, null, 2), { name: 'system/configuration.json' });
            // 6. Create backup manifest
            const manifest = {
                backupId,
                createdAt: new Date().toISOString(),
                type: options.adminOnly ? 'admin_only' : 'platform',
                options,
                metadata
            };
            archive.append(JSON.stringify(manifest, null, 2), { name: 'manifest.json' });
            await this.finalizeArchive(archive);
            const stats = fs.statSync(backupPath);
            metadata.compressionRatio = this.calculateCompressionRatio(metadata);
            return {
                id: backupId,
                filename: `${backupId}.zip`,
                size: stats.size,
                path: backupPath,
                createdAt: new Date(),
                type: options.adminOnly ? 'admin_only' : 'platform',
                metadata
            };
        }
        catch (error) {
            // Cleanup failed backup
            if (fs.existsSync(backupPath)) {
                fs.unlinkSync(backupPath);
            }
            throw new Error(`Platform backup failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    // ==== WORKSPACE ADMIN BACKUPS ====
    async createWorkspaceBackup(options) {
        const workspace = await this.workspaceRepository.findOne({
            where: { id: options.workspaceId },
            relations: ['owner']
        });
        if (!workspace) {
            throw new Error('Workspace not found');
        }
        const backupId = this.generateBackupId('workspace', options.workspaceId);
        const backupPath = path.join(this.backupDirectory, `${backupId}.zip`);
        console.log('Starting workspace backup...', { backupId, workspaceId: options.workspaceId, options });
        const archive = archiver('zip', {
            zlib: { level: options.compressionLevel || 6 }
        });
        const output = (0, fs_1.createWriteStream)(backupPath);
        archive.pipe(output);
        let metadata = {
            databaseTables: [],
            storageFiles: 0,
            userCount: 0,
            compressionRatio: 0
        };
        try {
            // 1. Backup Workspace Database Data
            if (options.includeDatabase) {
                const dbBackup = await this.createWorkspaceDatabaseBackup(options.workspaceId);
                archive.append(JSON.stringify(dbBackup, null, 2), { name: 'database/workspace_data.json' });
                metadata.databaseTables = dbBackup.tables.map(t => t.name);
            }
            // 2. Backup Workspace Users
            if (options.includeUsers) {
                const users = await this.getWorkspaceUsers(options.workspaceId);
                archive.append(JSON.stringify(users, null, 2), { name: 'users/workspace_users.json' });
                metadata.userCount = users.length;
            }
            // 3. Backup Workspace Info
            const workspaceInfo = {
                ...workspace,
                owner: workspace.owner ? {
                    id: workspace.owner.id,
                    email: workspace.owner.email,
                    fullName: workspace.owner.fullName
                } : null
            };
            archive.append(JSON.stringify(workspaceInfo, null, 2), { name: 'workspace/info.json' });
            // 4. Backup Workspace Storage
            if (options.includeStorage) {
                const storageBackup = await this.createWorkspaceStorageBackup(options.workspaceId, archive);
                metadata.storageFiles = storageBackup.fileCount;
            }
            // 5. Backup Workspace Configuration
            const workspaceConfig = await this.getWorkspaceConfiguration(options.workspaceId);
            archive.append(JSON.stringify(workspaceConfig, null, 2), { name: 'configuration/workspace_settings.json' });
            // 6. Create backup manifest
            const manifest = {
                backupId,
                createdAt: new Date().toISOString(),
                type: 'workspace',
                workspaceId: options.workspaceId,
                workspaceName: workspace.name,
                options,
                metadata
            };
            archive.append(JSON.stringify(manifest, null, 2), { name: 'manifest.json' });
            await this.finalizeArchive(archive);
            const stats = fs.statSync(backupPath);
            metadata.compressionRatio = this.calculateCompressionRatio(metadata);
            return {
                id: backupId,
                filename: `${backupId}.zip`,
                size: stats.size,
                path: backupPath,
                createdAt: new Date(),
                type: 'workspace',
                workspaceId: options.workspaceId,
                metadata
            };
        }
        catch (error) {
            // Cleanup failed backup
            if (fs.existsSync(backupPath)) {
                fs.unlinkSync(backupPath);
            }
            throw new Error(`Workspace backup failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    // ==== BACKUP LISTING & MANAGEMENT ====
    async listBackups(workspaceId) {
        const backupFiles = fs.readdirSync(this.backupDirectory)
            .filter(file => file.endsWith('.zip'))
            .map(file => {
            const filepath = path.join(this.backupDirectory, file);
            const stats = fs.statSync(filepath);
            // Extract backup info from filename
            const parts = file.replace('.zip', '').split('_');
            const type = parts[1];
            const extractedWorkspaceId = type === 'workspace' ? parts[2] : undefined;
            return {
                id: file.replace('.zip', ''),
                filename: file,
                size: stats.size,
                path: filepath,
                createdAt: stats.birthtime,
                type: type,
                workspaceId: extractedWorkspaceId,
                metadata: {
                    databaseTables: [],
                    storageFiles: 0,
                    userCount: 0,
                    compressionRatio: 0
                }
            };
        });
        // Filter by workspace if specified
        if (workspaceId) {
            return backupFiles.filter(backup => backup.workspaceId === workspaceId);
        }
        return backupFiles.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
    async deleteBackup(backupId, requesterWorkspaceId) {
        const backupPath = path.join(this.backupDirectory, `${backupId}.zip`);
        if (!fs.existsSync(backupPath)) {
            throw new Error('Backup not found');
        }
        // Security check: workspace admins can only delete their own backups
        if (requesterWorkspaceId) {
            const parts = backupId.split('_');
            if (parts[1] === 'workspace' && parts[2] !== requesterWorkspaceId) {
                throw new Error('Cannot delete backups from other workspaces');
            }
            if (parts[1] === 'platform' || parts[1] === 'admin') {
                throw new Error('Cannot delete platform/admin backups');
            }
        }
        fs.unlinkSync(backupPath);
    }
    async downloadBackup(backupId, requesterWorkspaceId) {
        const backupPath = path.join(this.backupDirectory, `${backupId}.zip`);
        if (!fs.existsSync(backupPath)) {
            throw new Error('Backup not found');
        }
        // Security check: workspace admins can only download their own backups
        if (requesterWorkspaceId) {
            const parts = backupId.split('_');
            if (parts[1] === 'workspace' && parts[2] !== requesterWorkspaceId) {
                throw new Error('Cannot download backups from other workspaces');
            }
            if (parts[1] === 'platform' || parts[1] === 'admin') {
                throw new Error('Cannot download platform/admin backups');
            }
        }
        return backupPath;
    }
    // ==== PRIVATE HELPER METHODS ====
    generateBackupId(type, workspaceId) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const random = Math.random().toString(36).substring(2, 8);
        if (type === 'workspace' && workspaceId) {
            return `backup_workspace_${workspaceId}_${timestamp}_${random}`;
        }
        return `backup_${type}_${timestamp}_${random}`;
    }
    async createDatabaseBackup(scope) {
        // This would connect to database and export schema + data
        // For now, mock implementation
        const tables = scope === 'admin'
            ? ['users', 'workspaces', 'api_keys', 'system_settings']
            : ['users', 'workspaces', 'api_keys', 'storage_files', 'buckets', 'storage_policies'];
        return {
            scope,
            tables: tables.map(name => ({
                name,
                schema: `CREATE TABLE ${name} (...)`,
                data: [] // Mock data
            })),
            exportedAt: new Date().toISOString()
        };
    }
    async createWorkspaceDatabaseBackup(workspaceId) {
        // Export only data related to specific workspace
        const tables = ['users', 'workspace_specific_tables'];
        return {
            workspaceId,
            tables: tables.map(name => ({
                name,
                schema: `CREATE TABLE ${name} (...)`,
                data: [] // Data filtered by workspace_id
            })),
            exportedAt: new Date().toISOString()
        };
    }
    async getAdminUsers() {
        return await this.userRepository.find({
            where: [
                { adminRole: 'platform_admin' },
                { adminRole: 'service_role' }
            ],
            select: ['id', 'email', 'role', 'adminRole', 'createdAt']
        });
    }
    async getAllUsers() {
        return await this.userRepository.find({
            select: ['id', 'email', 'role', 'adminRole', 'workspaceId', 'createdAt']
        });
    }
    async getWorkspaceUsers(workspaceId) {
        return await this.userRepository.find({
            where: { workspaceId },
            select: ['id', 'email', 'role', 'adminRole', 'createdAt']
        });
    }
    async getAllWorkspaces() {
        return await this.workspaceRepository.find({
            relations: ['owner'],
            select: ['id', 'name', 'description', 'isActive', 'createdAt']
        });
    }
    async createAdminStorageBackup(archive) {
        // Only backup system/admin files, not user content
        return { fileCount: 0 };
    }
    async createFullStorageBackup(archive) {
        // Backup all storage files
        const files = await this.storageFileRepository.find();
        for (const file of files.slice(0, 10)) { // Limit for demo
            // In real implementation, would copy actual files
            archive.append(`Mock file content for ${file.name}`, {
                name: `storage/${file.bucket_id}/${file.path}`
            });
        }
        return { fileCount: files.length };
    }
    async createWorkspaceStorageBackup(workspaceId, archive) {
        // Backup only files belonging to workspace
        const buckets = await this.bucketRepository.find({
            where: { workspaceId } // Assuming buckets have workspaceId
        });
        let fileCount = 0;
        for (const bucket of buckets) {
            const files = await this.storageFileRepository.find({
                where: { bucket_id: bucket.id }
            });
            for (const file of files) {
                archive.append(`Mock file content for ${file.name}`, {
                    name: `storage/${bucket.name}/${file.path}`
                });
                fileCount++;
            }
        }
        return { fileCount };
    }
    async getSystemConfiguration() {
        return {
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'production',
            databaseUrl: process.env.DATABASE_URL ? '[REDACTED]' : null,
            features: {
                storage: true,
                realtime: true,
                functions: true,
                auth: true
            }
        };
    }
    async getWorkspaceConfiguration(workspaceId) {
        // Get workspace-specific settings
        return {
            workspaceId,
            settings: {
                allowPublicSignup: false,
                emailVerificationRequired: true,
                passwordMinLength: 8
            },
            integrations: [],
            apiKeys: [] // Without actual keys for security
        };
    }
    async finalizeArchive(archive) {
        return new Promise((resolve, reject) => {
            archive.on('error', reject);
            archive.on('end', resolve);
            archive.finalize();
        });
    }
    calculateCompressionRatio(metadata) {
        // Mock calculation
        return Math.random() * 0.3 + 0.5; // 50-80% compression
    }
}
exports.BackupService = BackupService;
//# sourceMappingURL=backup.service.js.map