"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CDNService = exports.CustomCDNProvider = exports.AWSCloudFrontCDNProvider = exports.CloudflareCDNProvider = exports.CDNProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const config_1 = __importDefault(require("../../infra/config"));
class CDNProvider {
    constructor(baseUrl) {
        this.baseUrl = baseUrl || "";
        this.httpClient = axios_1.default.create({
            timeout: 30000,
            headers: {
                'User-Agent': 'E-BaaS-Storage/1.0'
            }
        });
    }
}
exports.CDNProvider = CDNProvider;
class CloudflareCDNProvider extends CDNProvider {
    constructor() {
        super(config_1.default.cdn.baseUrl);
        this.apiToken = config_1.default.cdn.cloudflare.apiToken;
        this.zoneId = config_1.default.cdn.cloudflare.zoneId;
        this.accountId = config_1.default.cdn.cloudflare.accountId;
        this.httpClient.defaults.headers.common['Authorization'] = `Bearer ${this.apiToken}`;
        this.httpClient.defaults.baseURL = 'https://api.cloudflare.com/v4';
    }
    validateConfiguration() {
        return !!(this.apiToken && this.zoneId && this.baseUrl);
    }
    generateCdnUrl(path) {
        if (!this.baseUrl)
            return path;
        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
        return `${this.baseUrl}/${cleanPath}`;
    }
    async purgeCache(options) {
        try {
            if (!this.validateConfiguration()) {
                throw new Error('Cloudflare CDN not properly configured');
            }
            const payload = {};
            if (options.purgeAll) {
                payload.purge_everything = true;
            }
            else {
                if (options.urls) {
                    payload.files = options.urls.map(url => url.startsWith('http') ? url : this.generateCdnUrl(url));
                }
                if (options.prefixes) {
                    payload.prefixes = options.prefixes;
                }
                if (options.tags) {
                    payload.tags = options.tags;
                }
            }
            const response = await this.httpClient.post(`/zones/${this.zoneId}/purge_cache`, payload);
            if (response.data.success) {
                return {
                    success: true,
                    purgeId: response.data.result?.id,
                    message: 'Cache purged successfully'
                };
            }
            else {
                throw new Error(response.data.errors?.[0]?.message || 'Unknown error');
            }
        }
        catch (error) {
            console.error('Cloudflare cache purge failed:', error);
            return {
                success: false,
                error: error.message || 'Cache purge failed'
            };
        }
    }
    async getMetrics(startDate, endDate) {
        try {
            const since = startDate || new Date(Date.now() - 24 * 60 * 60 * 1000); // 24h ago
            const until = endDate || new Date();
            const params = {
                since: since.toISOString(),
                until: until.toISOString(),
                dimensions: 'responseContentType,clientCountryName',
                metrics: 'requests,bytes,cachedRequests,cachedBytes',
                sort: '-requests'
            };
            const response = await this.httpClient.get(`/zones/${this.zoneId}/analytics/dashboard`, { params });
            if (response.data.success) {
                const data = response.data.result;
                const totalRequests = data.totals.requests || 0;
                const cachedRequests = data.totals.cachedRequests || 0;
                return {
                    requests: totalRequests,
                    bandwidth: data.totals.bytes || 0,
                    cacheHitRatio: totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0,
                    topUrls: [] // Cloudflare doesn't provide URL-level data in free tier
                };
            }
            else {
                throw new Error('Failed to fetch metrics');
            }
        }
        catch (error) {
            console.error('Failed to fetch Cloudflare metrics:', error);
            return {
                requests: 0,
                bandwidth: 0,
                cacheHitRatio: 0,
                topUrls: []
            };
        }
    }
}
exports.CloudflareCDNProvider = CloudflareCDNProvider;
class AWSCloudFrontCDNProvider extends CDNProvider {
    constructor() {
        super(config_1.default.cdn.baseUrl);
        this.accessKeyId = config_1.default.cdn.aws.accessKeyId;
        this.secretAccessKey = config_1.default.cdn.aws.secretAccessKey;
        this.region = config_1.default.cdn.aws.region;
        this.distributionId = config_1.default.cdn.aws.distributionId;
    }
    validateConfiguration() {
        return !!(this.accessKeyId && this.secretAccessKey && this.distributionId && this.baseUrl);
    }
    generateCdnUrl(path) {
        if (!this.baseUrl)
            return path;
        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
        return `${this.baseUrl}/${cleanPath}`;
    }
    async purgeCache(options) {
        try {
            if (!this.validateConfiguration()) {
                throw new Error('AWS CloudFront CDN not properly configured');
            }
            // Note: This is a simplified implementation
            // In production, you would use AWS SDK to create invalidation
            const paths = options.urls || ['/*'];
            console.log(`Would create CloudFront invalidation for paths: ${paths.join(', ')}`);
            return {
                success: true,
                message: 'CloudFront invalidation created (mock implementation)'
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message || 'CloudFront purge failed'
            };
        }
    }
    async getMetrics(startDate, endDate) {
        // Mock implementation - would use CloudWatch in production
        return {
            requests: 0,
            bandwidth: 0,
            cacheHitRatio: 0,
            topUrls: []
        };
    }
}
exports.AWSCloudFrontCDNProvider = AWSCloudFrontCDNProvider;
class CustomCDNProvider extends CDNProvider {
    constructor() {
        super(config_1.default.cdn.baseUrl);
        this.apiEndpoint = config_1.default.cdn.custom.apiEndpoint;
        this.apiKey = config_1.default.cdn.custom.apiKey;
        this.purgeEndpoint = config_1.default.cdn.custom.purgeEndpoint;
        if (this.apiKey) {
            this.httpClient.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
        }
    }
    validateConfiguration() {
        return !!(this.apiEndpoint && this.apiKey && this.baseUrl);
    }
    generateCdnUrl(path) {
        if (!this.baseUrl)
            return path;
        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
        return `${this.baseUrl}/${cleanPath}`;
    }
    async purgeCache(options) {
        try {
            if (!this.validateConfiguration()) {
                throw new Error('Custom CDN not properly configured');
            }
            const response = await this.httpClient.post(this.purgeEndpoint, {
                urls: options.urls,
                prefixes: options.prefixes,
                tags: options.tags,
                purgeAll: options.purgeAll
            });
            return {
                success: true,
                purgeId: response.data.id,
                message: response.data.message || 'Cache purged successfully'
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message || 'Custom CDN purge failed'
            };
        }
    }
    async getMetrics(startDate, endDate) {
        try {
            const response = await this.httpClient.get(`${this.apiEndpoint}/metrics`, {
                params: {
                    start: startDate?.toISOString(),
                    end: endDate?.toISOString()
                }
            });
            return response.data;
        }
        catch (error) {
            return {
                requests: 0,
                bandwidth: 0,
                cacheHitRatio: 0,
                topUrls: []
            };
        }
    }
}
exports.CustomCDNProvider = CustomCDNProvider;
class CDNService {
    constructor() {
        this.provider = null;
        this.initializeProvider();
    }
    initializeProvider() {
        if (!config_1.default.cdn.enabled) {
            return;
        }
        switch (config_1.default.cdn.provider) {
            case 'cloudflare':
                this.provider = new CloudflareCDNProvider();
                break;
            case 'aws':
                this.provider = new AWSCloudFrontCDNProvider();
                break;
            case 'custom':
                this.provider = new CustomCDNProvider();
                break;
            default:
                console.warn(`Unknown CDN provider: ${config_1.default.cdn.provider}`);
        }
        if (this.provider && !this.provider.validateConfiguration()) {
            console.warn('CDN provider configuration is invalid');
            this.provider = null;
        }
    }
    isEnabled() {
        return config_1.default.cdn.enabled && this.provider !== null;
    }
    getProvider() {
        return this.isEnabled() ? config_1.default.cdn.provider : null;
    }
    generateFileUrl(bucketName, filePath) {
        if (!this.isEnabled()) {
            // Return original API URL if CDN is not enabled
            const baseUrl = config_1.default.app.baseUrl;
            return `${baseUrl}/storage/v1/object/public/${bucketName}/${filePath}`;
        }
        const cdnPath = `storage/${bucketName}/${filePath}`;
        return this.provider.generateCdnUrl(cdnPath);
    }
    async purgeFile(bucketName, filePath) {
        if (!this.isEnabled()) {
            return { success: true, message: 'CDN not enabled' };
        }
        const cdnPath = `storage/${bucketName}/${filePath}`;
        const cdnUrl = this.provider.generateCdnUrl(cdnPath);
        return await this.provider.purgeCache({
            urls: [cdnUrl]
        });
    }
    async purgeBucket(bucketName) {
        if (!this.isEnabled()) {
            return { success: true, message: 'CDN not enabled' };
        }
        const prefix = `storage/${bucketName}/`;
        return await this.provider.purgeCache({
            prefixes: [prefix]
        });
    }
    async purgeAll() {
        if (!this.isEnabled()) {
            return { success: true, message: 'CDN not enabled' };
        }
        return await this.provider.purgeCache({
            purgeAll: true
        });
    }
    async getMetrics(startDate, endDate) {
        if (!this.isEnabled()) {
            return null;
        }
        return await this.provider.getMetrics(startDate, endDate);
    }
    getCacheHeaders(maxAge) {
        if (!config_1.default.cdn.cacheEnabled) {
            return {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            };
        }
        const cacheMaxAge = maxAge || config_1.default.cdn.cacheMaxAge;
        return {
            'Cache-Control': `public, max-age=${cacheMaxAge}, s-maxage=${cacheMaxAge}`,
            'Vary': 'Accept-Encoding',
            'X-CDN-Cache': 'MISS'
        };
    }
    async handleFileUpdate(bucketName, filePath) {
        if (!this.isEnabled() || !config_1.default.cdn.purgeOnUpdate) {
            return;
        }
        try {
            const result = await this.purgeFile(bucketName, filePath);
            if (result.success) {
                console.log(`✅ CDN cache purged for file: ${bucketName}/${filePath}`);
            }
            else {
                console.error(`❌ Failed to purge CDN cache for file: ${bucketName}/${filePath}`, result.error);
            }
        }
        catch (error) {
            console.error('Error purging CDN cache on file update:', error);
        }
    }
    async handleFileDelete(bucketName, filePath) {
        if (!this.isEnabled()) {
            return;
        }
        try {
            const result = await this.purgeFile(bucketName, filePath);
            if (result.success) {
                console.log(`✅ CDN cache purged for deleted file: ${bucketName}/${filePath}`);
            }
            else {
                console.error(`❌ Failed to purge CDN cache for deleted file: ${bucketName}/${filePath}`, result.error);
            }
        }
        catch (error) {
            console.error('Error purging CDN cache on file delete:', error);
        }
    }
}
exports.CDNService = CDNService;
//# sourceMappingURL=cdn.service.js.map