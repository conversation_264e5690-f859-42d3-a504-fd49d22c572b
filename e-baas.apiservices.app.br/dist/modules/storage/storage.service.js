"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageService = void 0;
const data_source_1 = require("../../infra/database/data-source");
const Bucket_entity_1 = require("./entity/Bucket.entity");
const StorageFile_entity_1 = require("./entity/StorageFile.entity");
const storage_dto_1 = require("./dto/storage.dto");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("../../infra/config"));
const sharp_1 = __importDefault(require("sharp"));
const cdn_service_1 = require("./cdn.service");
const versioning_service_1 = require("./versioning.service");
const rls_service_1 = require("./rls.service");
const rls_dto_1 = require("./dto/rls.dto");
class StorageService {
    constructor() {
        this.bucketRepository = data_source_1.AppDataSource.getRepository(Bucket_entity_1.Bucket);
        this.fileRepository = data_source_1.AppDataSource.getRepository(StorageFile_entity_1.StorageFile);
        this.storageBasePath = process.env.STORAGE_PATH || './storage';
        this.cdnService = new cdn_service_1.CDNService();
        this.versioningService = new versioning_service_1.VersioningService();
        this.rlsService = new rls_service_1.StorageRLSService();
        this.ensureStorageDirectoryExists();
    }
    async ensureStorageDirectoryExists() {
        try {
            await promises_1.default.access(this.storageBasePath);
        }
        catch {
            await promises_1.default.mkdir(this.storageBasePath, { recursive: true });
        }
    }
    // Bucket Management
    async createBucket(createBucketDto, createdBy) {
        // Check if bucket already exists
        const existingBucket = await this.bucketRepository.findOne({
            where: {
                name: createBucketDto.name,
                workspaceId: createBucketDto.workspaceId
            }
        });
        if (existingBucket) {
            throw new Error(`Bucket '${createBucketDto.name}' already exists in this workspace`);
        }
        // Create bucket
        const bucket = this.bucketRepository.create({
            ...createBucketDto,
            createdBy,
            filesCount: 0,
            totalSize: 0
        });
        await this.bucketRepository.save(bucket);
        // Create physical directory
        const bucketPath = path_1.default.join(this.storageBasePath, bucket.getStoragePath());
        await promises_1.default.mkdir(bucketPath, { recursive: true });
        return bucket.toSafeObject();
    }
    async getBucket(bucketName, workspaceId) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: bucketName, workspaceId, isActive: true }
        });
        return bucket ? bucket.toSafeObject() : null;
    }
    async listBuckets(workspaceId) {
        const buckets = await this.bucketRepository.find({
            where: { workspaceId, isActive: true },
            order: { createdAt: 'DESC' }
        });
        return buckets.map(bucket => bucket.toSafeObject());
    }
    async updateBucket(bucketName, workspaceId, updateDto) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: bucketName, workspaceId, isActive: true }
        });
        if (!bucket) {
            throw new Error('Bucket not found');
        }
        // Update bucket properties
        Object.assign(bucket, updateDto);
        await this.bucketRepository.save(bucket);
        return bucket.toSafeObject();
    }
    async deleteBucket(bucketName, workspaceId, force = false) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: bucketName, workspaceId, isActive: true }
        });
        if (!bucket) {
            throw new Error('Bucket not found');
        }
        // Check if bucket has files
        const fileCount = await this.fileRepository.count({
            where: { bucketId: bucket.id, isDeleted: false }
        });
        if (fileCount > 0 && !force) {
            throw new Error('Bucket is not empty. Use force=true to delete all files');
        }
        // Delete all files in bucket if force is true
        if (force && fileCount > 0) {
            await this.fileRepository.update({ bucketId: bucket.id }, { isDeleted: true, deletedAt: new Date() });
        }
        // Soft delete bucket
        bucket.isActive = false;
        await this.bucketRepository.save(bucket);
        // Optionally remove physical directory
        try {
            const bucketPath = path_1.default.join(this.storageBasePath, bucket.getStoragePath());
            await promises_1.default.rm(bucketPath, { recursive: true, force: true });
        }
        catch (error) {
            console.warn(`Failed to remove bucket directory: ${error}`);
        }
        return { message: `Bucket '${bucketName}' deleted successfully` };
    }
    // RLS Integration Methods
    async checkBucketAccess(bucketName, workspaceId, userId, action) {
        if (!userId) {
            return; // Skip RLS for unauthenticated requests (will be handled by bucket visibility)
        }
        const accessResult = await this.rlsService.checkBucketAccess(bucketName, workspaceId, userId, action);
        if (!accessResult.allowed) {
            throw new Error(`Access denied: ${accessResult.reason}`);
        }
    }
    async checkFileAccess(fileId, userId, action) {
        if (!userId) {
            return; // Skip RLS for unauthenticated requests (will be handled by file visibility)
        }
        const accessResult = await this.rlsService.checkFileAccess(fileId, userId, action);
        if (!accessResult.allowed) {
            throw new Error(`Access denied: ${accessResult.reason}`);
        }
    }
    // File Operations
    async uploadFile(uploadDto, fileBuffer, userId) {
        // Check RLS policies for bucket access
        if (userId) {
            await this.checkBucketAccess(uploadDto.bucketName, uploadDto.workspaceId, userId, rls_dto_1.PolicyAction.UPLOAD);
        }
        // Get bucket
        const bucket = await this.bucketRepository.findOne({
            where: { name: uploadDto.bucketName, workspaceId: uploadDto.workspaceId, isActive: true }
        });
        if (!bucket) {
            throw new Error('Bucket not found');
        }
        // Validate file type
        if (uploadDto.contentType && !bucket.isFileTypeAllowed(uploadDto.contentType)) {
            throw new Error('File type not allowed in this bucket');
        }
        // Validate file size
        if (!bucket.isFileSizeAllowed(fileBuffer.length)) {
            throw new Error(`File size exceeds bucket limit of ${bucket.maxFileSize} bytes`);
        }
        // Construct file path
        const filePath = uploadDto.path
            ? `${uploadDto.path}/${uploadDto.fileName}`.replace(/\/+/g, '/')
            : uploadDto.fileName;
        // Check if file exists and overwrite is not allowed
        const existingFile = await this.fileRepository.findOne({
            where: { bucketId: bucket.id, path: filePath, isDeleted: false }
        });
        if (existingFile && !uploadDto.overwrite) {
            throw new Error('File already exists. Use overwrite=true to replace it');
        }
        // Create or update file record
        let file;
        let isNewFile = false;
        if (existingFile && uploadDto.overwrite) {
            // Create version if versioning is enabled
            if (existingFile.hasVersioning()) {
                try {
                    await this.versioningService.createVersion(existingFile.id, fileBuffer, {
                        type: 'auto',
                        changelog: 'File updated via upload'
                    });
                }
                catch (error) {
                    console.warn('Failed to create version on file update:', error);
                }
            }
            // Update existing file
            existingFile.size = fileBuffer.length;
            existingFile.mimeType = uploadDto.contentType || 'application/octet-stream';
            existingFile.metadata = uploadDto.metadata;
            existingFile.cacheControl = uploadDto.cacheControl;
            existingFile.ownerId = userId;
            existingFile.isPublic = bucket.isPublic();
            file = existingFile;
        }
        else {
            // Create new file
            isNewFile = true;
            file = this.fileRepository.create({
                name: uploadDto.fileName,
                path: filePath,
                bucketId: bucket.id,
                workspaceId: uploadDto.workspaceId,
                size: fileBuffer.length,
                mimeType: uploadDto.contentType || 'application/octet-stream',
                metadata: uploadDto.metadata,
                cacheControl: uploadDto.cacheControl,
                ownerId: userId,
                isPublic: bucket.isPublic(),
                versioningEnabled: bucket.enableVersioning || false
            });
        }
        await this.fileRepository.save(file);
        // Save file to disk
        const fullStoragePath = path_1.default.join(this.storageBasePath, file.storagePath);
        await promises_1.default.mkdir(path_1.default.dirname(fullStoragePath), { recursive: true });
        await promises_1.default.writeFile(fullStoragePath, fileBuffer);
        // Update bucket statistics
        if (!existingFile) {
            bucket.updateStats(fileBuffer.length, 1);
        }
        else {
            bucket.updateStats(fileBuffer.length - existingFile.size, 0);
        }
        await this.bucketRepository.save(bucket);
        // Handle CDN integration
        if (existingFile && this.cdnService.isEnabled()) {
            // Purge CDN cache when updating existing file
            await this.cdnService.handleFileUpdate(uploadDto.bucketName, file.path);
        }
        // Generate CDN URL if enabled, otherwise use regular API URL
        const publicUrl = this.cdnService.isEnabled()
            ? this.cdnService.generateFileUrl(uploadDto.bucketName, file.path)
            : file.getUrlPath();
        return {
            id: file.id,
            path: file.path,
            fullPath: publicUrl,
            metadata: {
                ...file.toMetadata(),
                cdnEnabled: this.cdnService.isEnabled(),
                cdnProvider: this.cdnService.getProvider()
            },
            signedUrl: uploadDto.expiresIn ? await this.createSignedUrl({
                bucketName: uploadDto.bucketName,
                workspaceId: uploadDto.workspaceId,
                filePath: file.path,
                operation: storage_dto_1.FileOperation.DOWNLOAD,
                expiresIn: uploadDto.expiresIn
            }).then(res => res.signedUrl) : undefined
        };
    }
    async downloadFile(downloadDto, userId) {
        // Get file
        const file = await this.fileRepository.findOne({
            where: {
                bucketId: await this.getBucketId(downloadDto.bucketName, downloadDto.workspaceId),
                path: downloadDto.filePath,
                isDeleted: false
            },
            relations: ['bucket']
        });
        if (!file) {
            throw new Error('File not found');
        }
        // Check RLS policies for file access
        if (userId) {
            await this.checkFileAccess(file.id, userId, rls_dto_1.PolicyAction.DOWNLOAD);
        }
        // Check permissions
        if (!file.isPublic && !file.bucket?.isPublic()) {
            // This would be checked by middleware in real implementation
            // For now, we'll allow download if the workspace matches
        }
        // Read file from disk
        const fullStoragePath = path_1.default.join(this.storageBasePath, file.storagePath);
        let buffer;
        try {
            buffer = await promises_1.default.readFile(fullStoragePath);
        }
        catch (error) {
            throw new Error('File not found on storage');
        }
        // Apply transformations if requested and file is an image
        if (downloadDto.transform && file.isImage()) {
            try {
                const transformParams = this.parseTransformParams(downloadDto.transform);
                buffer = await this.transformImage(buffer, transformParams);
            }
            catch (error) {
                console.warn('Image transformation failed:', error);
                // Continue with original file if transformation fails
            }
        }
        // Update access statistics
        file.markAsAccessed();
        await this.fileRepository.save(file);
        return { file, buffer };
    }
    async deleteFile(deleteDto, userId) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: deleteDto.bucketName, workspaceId: deleteDto.workspaceId, isActive: true }
        });
        if (!bucket) {
            throw new Error('Bucket not found');
        }
        const file = await this.fileRepository.findOne({
            where: { bucketId: bucket.id, path: deleteDto.filePath, isDeleted: false }
        });
        if (!file) {
            throw new Error('File not found');
        }
        // Check RLS policies for file deletion
        if (userId) {
            await this.checkFileAccess(file.id, userId, rls_dto_1.PolicyAction.DELETE);
        }
        // Soft delete file
        file.softDelete();
        await this.fileRepository.save(file);
        // Update bucket statistics
        bucket.updateStats(-file.size, -1);
        await this.bucketRepository.save(bucket);
        // Handle CDN cache purging
        if (this.cdnService.isEnabled()) {
            await this.cdnService.handleFileDelete(deleteDto.bucketName, file.path);
        }
        // Optionally delete physical file
        try {
            const fullStoragePath = path_1.default.join(this.storageBasePath, file.storagePath);
            await promises_1.default.unlink(fullStoragePath);
        }
        catch (error) {
            console.warn(`Failed to delete physical file: ${error}`);
        }
        return { message: 'File deleted successfully' };
    }
    async listFiles(listDto, userId) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: listDto.bucketName, workspaceId: listDto.workspaceId, isActive: true }
        });
        if (!bucket) {
            throw new Error('Bucket not found');
        }
        // Check RLS policies for bucket listing
        if (userId) {
            await this.checkBucketAccess(listDto.bucketName, listDto.workspaceId, userId, rls_dto_1.PolicyAction.LIST);
        }
        const queryBuilder = this.fileRepository
            .createQueryBuilder('file')
            .where('file.bucketId = :bucketId', { bucketId: bucket.id })
            .andWhere('file.isDeleted = :isDeleted', { isDeleted: false });
        // Apply prefix filter (folder)
        if (listDto.prefix) {
            queryBuilder.andWhere('file.path LIKE :prefix', { prefix: `${listDto.prefix}%` });
        }
        // Apply search filter
        if (listDto.search) {
            queryBuilder.andWhere('file.name LIKE :search', { search: `%${listDto.search}%` });
        }
        // Apply sorting
        if (listDto.sortBy && listDto.sortBy.length > 0) {
            listDto.sortBy.forEach((sort, index) => {
                const [field, direction = 'ASC'] = sort.split(':');
                if (index === 0) {
                    queryBuilder.orderBy(`file.${field}`, direction.toUpperCase());
                }
                else {
                    queryBuilder.addOrderBy(`file.${field}`, direction.toUpperCase());
                }
            });
        }
        else {
            queryBuilder.orderBy('file.createdAt', 'DESC');
        }
        // Apply pagination
        const limit = Math.min(listDto.limit || 100, 1000); // Max 1000 files per request
        queryBuilder.limit(limit + 1); // Get one extra to check if there are more
        if (listDto.offset) {
            // Implement cursor-based pagination
            queryBuilder.andWhere('file.id > :offset', { offset: listDto.offset });
        }
        const files = await queryBuilder.getMany();
        const hasMore = files.length > limit;
        if (hasMore) {
            files.pop(); // Remove the extra file
        }
        const nextOffset = hasMore && files.length > 0 ? files[files.length - 1].id : undefined;
        return {
            files: files.map(file => file.toMetadata()),
            hasMore,
            nextOffset
        };
    }
    // Signed URLs
    async createSignedUrl(signedUrlDto) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: signedUrlDto.bucketName, workspaceId: signedUrlDto.workspaceId, isActive: true }
        });
        if (!bucket) {
            throw new Error('Bucket not found');
        }
        // For download operations, check if file exists
        if (signedUrlDto.operation === storage_dto_1.FileOperation.DOWNLOAD) {
            const file = await this.fileRepository.findOne({
                where: { bucketId: bucket.id, path: signedUrlDto.filePath, isDeleted: false }
            });
            if (!file) {
                throw new Error('File not found');
            }
        }
        // Generate JWT token for signed URL
        const expiresAt = new Date(Date.now() + (signedUrlDto.expiresIn * 1000));
        const token = jsonwebtoken_1.default.sign({
            bucket: signedUrlDto.bucketName,
            path: signedUrlDto.filePath,
            operation: signedUrlDto.operation,
            workspaceId: signedUrlDto.workspaceId,
            options: signedUrlDto.options,
            exp: Math.floor(expiresAt.getTime() / 1000)
        }, config_1.default.jwt.secret);
        const baseUrl = process.env.BASE_URL || 'http://localhost:3333';
        const signedUrl = `${baseUrl}/storage/v1/object/sign/${signedUrlDto.bucketName}/${signedUrlDto.filePath}?token=${token}`;
        return {
            signedUrl,
            token,
            expiresAt,
            operation: signedUrlDto.operation
        };
    }
    // Storage Usage
    async getStorageUsage(workspaceId) {
        const buckets = await this.bucketRepository.find({
            where: { workspaceId, isActive: true }
        });
        const totalBuckets = buckets.length;
        const totalFiles = buckets.reduce((sum, bucket) => sum + bucket.filesCount, 0);
        const totalSize = buckets.reduce((sum, bucket) => sum + bucket.totalSize, 0);
        return {
            workspaceId,
            totalBuckets,
            totalFiles,
            totalSize,
            buckets: buckets.map(bucket => ({
                name: bucket.name,
                filesCount: bucket.filesCount,
                size: bucket.totalSize
            }))
        };
    }
    // CDN Management methods
    async purgeCDNCache(bucketName, filePath) {
        if (!this.cdnService.isEnabled()) {
            return { success: false, message: 'CDN is not enabled' };
        }
        try {
            let result;
            if (bucketName && filePath) {
                // Purge specific file
                result = await this.cdnService.purgeFile(bucketName, filePath);
            }
            else if (bucketName) {
                // Purge entire bucket
                result = await this.cdnService.purgeBucket(bucketName);
            }
            else {
                // Purge all cache
                result = await this.cdnService.purgeAll();
            }
            return {
                success: result.success,
                message: result.message || (result.success ? 'Cache purged successfully' : result.error || 'Failed to purge cache')
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || 'Failed to purge CDN cache'
            };
        }
    }
    async getCDNMetrics(startDate, endDate) {
        if (!this.cdnService.isEnabled()) {
            return null;
        }
        return await this.cdnService.getMetrics(startDate, endDate);
    }
    getCDNInfo() {
        return {
            enabled: this.cdnService.isEnabled(),
            provider: this.cdnService.getProvider(),
            cacheEnabled: config_1.default.cdn.cacheEnabled,
            cacheMaxAge: config_1.default.cdn.cacheMaxAge,
            purgeOnUpdate: config_1.default.cdn.purgeOnUpdate
        };
    }
    // File Versioning methods
    async enableFileVersioning(fileId, policy) {
        return await this.versioningService.enableVersioning(fileId, policy);
    }
    async disableFileVersioning(fileId, keepExistingVersions = true) {
        return await this.versioningService.disableVersioning(fileId, keepExistingVersions);
    }
    async createFileVersion(fileId, fileBuffer, options = {}) {
        return await this.versioningService.createVersion(fileId, fileBuffer, options);
    }
    async getFileVersions(fileId, includeDeleted = false) {
        const versions = await this.versioningService.getVersions(fileId, includeDeleted);
        return versions.map(v => v.toVersionInfo());
    }
    async getFileVersion(fileId, versionNumber) {
        const version = await this.versioningService.getVersion(fileId, versionNumber);
        return version ? version.toVersionInfo() : null;
    }
    async downloadFileVersion(fileId, versionNumber) {
        const { version, buffer } = await this.versioningService.downloadVersion(fileId, versionNumber);
        return { version: version.toVersionInfo(), buffer };
    }
    async rollbackFileToVersion(fileId, versionNumber, options = {}) {
        const version = await this.versioningService.rollbackToVersion(fileId, versionNumber, options);
        return version.toVersionInfo();
    }
    async deleteFileVersion(fileId, versionNumber, permanent = false) {
        return await this.versioningService.deleteVersion(fileId, versionNumber, permanent);
    }
    async applyVersionRetentionPolicy(fileId, policy) {
        return await this.versioningService.applyRetentionPolicy(fileId, policy);
    }
    async cleanupExpiredVersions() {
        return await this.versioningService.cleanupExpiredVersions();
    }
    async getVersioningStats(fileId) {
        return await this.versioningService.getVersioningStats(fileId);
    }
    // Helper methods
    async getBucketId(bucketName, workspaceId) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: bucketName, workspaceId, isActive: true },
            select: ['id']
        });
        if (!bucket) {
            throw new Error('Bucket not found');
        }
        return bucket.id;
    }
    parseTransformParams(transform) {
        const params = {};
        const pairs = transform.split(',');
        for (const pair of pairs) {
            const [key, value] = pair.split('=');
            switch (key) {
                case 'w':
                case 'width':
                    params.width = parseInt(value);
                    break;
                case 'h':
                case 'height':
                    params.height = parseInt(value);
                    break;
                case 'q':
                case 'quality':
                    params.quality = parseInt(value);
                    break;
                case 'f':
                case 'format':
                    params.format = value;
                    break;
                case 'r':
                case 'resize':
                    params.resize = value;
                    break;
            }
        }
        return params;
    }
    async transformImage(buffer, transform) {
        let image = (0, sharp_1.default)(buffer);
        // Apply resize
        if (transform.width || transform.height) {
            image = image.resize(transform.width, transform.height, {
                fit: transform.resize || 'cover',
                withoutEnlargement: true
            });
        }
        // Apply format conversion
        if (transform.format) {
            switch (transform.format) {
                case 'webp':
                    image = image.webp({ quality: transform.quality || 80 });
                    break;
                case 'jpeg':
                    image = image.jpeg({ quality: transform.quality || 80, progressive: transform.progressive });
                    break;
                case 'png':
                    image = image.png({ quality: transform.quality || 80 });
                    break;
                case 'avif':
                    image = image.avif({ quality: transform.quality || 80 });
                    break;
            }
        }
        return await image.toBuffer();
    }
}
exports.StorageService = StorageService;
//# sourceMappingURL=storage.service.js.map