{"version": 3, "file": "cdn.service.js", "sourceRoot": "", "sources": ["../../../src/modules/storage/cdn.service.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAC7C,gEAAwC;AAuBxC,MAAsB,WAAW;IAI/B,YAAY,OAAgB;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,eAAK,CAAC,MAAM,CAAC;YAC7B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,YAAY,EAAE,oBAAoB;aACnC;SACF,CAAC,CAAC;IACL,CAAC;CAMF;AAlBD,kCAkBC;AAED,MAAa,qBAAsB,SAAQ,WAAW;IAKpD;QACE,KAAK,CAAC,gBAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,gBAAM,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,gBAAM,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,gBAAM,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC;QAEjD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,QAAQ,EAAE,CAAC;QACrF,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,GAAG,+BAA+B,CAAC;IACrE,CAAC;IAED,qBAAqB;QACnB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,cAAc,CAAC,IAAY;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAwB;QACvC,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBACjC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;aAC3D;YAED,MAAM,OAAO,GAAQ,EAAE,CAAC;YAExB,IAAI,OAAO,CAAC,QAAQ,EAAE;gBACpB,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;aACjC;iBAAM;gBACL,IAAI,OAAO,CAAC,IAAI,EAAE;oBAChB,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACrC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CACxD,CAAC;iBACH;gBACD,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACpB,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;iBACrC;gBACD,IAAI,OAAO,CAAC,IAAI,EAAE;oBAChB,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;iBAC7B;aACF;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CACzC,UAAU,IAAI,CAAC,MAAM,cAAc,EACnC,OAAO,CACR,CAAC;YAEF,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACzB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;oBACjC,OAAO,EAAE,2BAA2B;iBACrC,CAAC;aACH;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,eAAe,CAAC,CAAC;aACxE;SACF;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,oBAAoB;aAC7C,CAAC;SACH;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAgB,EAAE,OAAc;QAC/C,IAAI;YACF,MAAM,KAAK,GAAG,SAAS,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,UAAU;YACjF,MAAM,KAAK,GAAG,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;YAEpC,MAAM,MAAM,GAAG;gBACb,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE;gBAC1B,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE;gBAC1B,UAAU,EAAE,uCAAuC;gBACnD,OAAO,EAAE,2CAA2C;gBACpD,IAAI,EAAE,WAAW;aAClB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CACxC,UAAU,IAAI,CAAC,MAAM,sBAAsB,EAC3C,EAAE,MAAM,EAAE,CACX,CAAC;YAEF,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACzB,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBAClC,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC;gBAChD,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC;gBAEvD,OAAO;oBACL,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;oBACjC,aAAa,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC7E,OAAO,EAAE,EAAE,CAAC,yDAAyD;iBACtE,CAAC;aACH;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;aAC5C;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO;gBACL,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;gBACZ,aAAa,EAAE,CAAC;gBAChB,OAAO,EAAE,EAAE;aACZ,CAAC;SACH;IACH,CAAC;CACF;AAlHD,sDAkHC;AAED,MAAa,wBAAyB,SAAQ,WAAW;IAMvD;QACE,KAAK,CAAC,gBAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,gBAAM,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,gBAAM,CAAC,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,gBAAM,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,gBAAM,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC;IACtD,CAAC;IAED,qBAAqB;QACnB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7F,CAAC;IAED,cAAc,CAAC,IAAY;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAwB;QACvC,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBACjC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;aAC/D;YAED,4CAA4C;YAC5C,8DAA8D;YAC9D,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;YAErC,OAAO,CAAC,GAAG,CAAC,mDAAmD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEnF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,uDAAuD;aACjE,CAAC;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,yBAAyB;aAClD,CAAC;SACH;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAgB,EAAE,OAAc;QAC/C,2DAA2D;QAC3D,OAAO;YACL,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE,CAAC;YAChB,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;CACF;AAzDD,4DAyDC;AAED,MAAa,iBAAkB,SAAQ,WAAW;IAKhD;QACE,KAAK,CAAC,gBAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC;QACjD,IAAI,CAAC,MAAM,GAAG,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC;QAErD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,MAAM,EAAE,CAAC;SACpF;IACH,CAAC;IAED,qBAAqB;QACnB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,cAAc,CAAC,IAAY;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAwB;QACvC,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBACjC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;aACvD;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBAC9D,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACzB,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,2BAA2B;aAC9D,CAAC;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,yBAAyB;aAClD,CAAC;SACH;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAgB,EAAE,OAAc;QAC/C,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,UAAU,EAAE;gBACxE,MAAM,EAAE;oBACN,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE;oBAC/B,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE;iBAC5B;aACF,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC;SACtB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO;gBACL,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;gBACZ,aAAa,EAAE,CAAC;gBAChB,OAAO,EAAE,EAAE;aACZ,CAAC;SACH;IACH,CAAC;CACF;AAvED,8CAuEC;AAED,MAAa,UAAU;IAGrB;QAFQ,aAAQ,GAAuB,IAAI,CAAC;QAG1C,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,gBAAM,CAAC,GAAG,CAAC,OAAO,EAAE;YACvB,OAAO;SACR;QAED,QAAQ,gBAAM,CAAC,GAAG,CAAC,QAAQ,EAAE;YAC3B,KAAK,YAAY;gBACf,IAAI,CAAC,QAAQ,GAAG,IAAI,qBAAqB,EAAE,CAAC;gBAC5C,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,CAAC,QAAQ,GAAG,IAAI,wBAAwB,EAAE,CAAC;gBAC/C,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;gBACxC,MAAM;YACR;gBACE,OAAO,CAAC,IAAI,CAAC,yBAAyB,gBAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChE;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,EAAE;YAC3D,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;IACH,CAAC;IAED,SAAS;QACP,OAAO,gBAAM,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC;IACtD,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,gBAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;IACvD,CAAC;IAED,eAAe,CAAC,UAAkB,EAAE,QAAgB;QAClD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACrB,gDAAgD;YAChD,MAAM,OAAO,GAAG,gBAAM,CAAC,GAAG,CAAC,OAAO,CAAC;YACnC,OAAO,GAAG,OAAO,6BAA6B,UAAU,IAAI,QAAQ,EAAE,CAAC;SACxE;QAED,MAAM,OAAO,GAAG,WAAW,UAAU,IAAI,QAAQ,EAAE,CAAC;QACpD,OAAO,IAAI,CAAC,QAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,QAAgB;QAClD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACrB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;SACtD;QAED,MAAM,OAAO,GAAG,WAAW,UAAU,IAAI,QAAQ,EAAE,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAEtD,OAAO,MAAM,IAAI,CAAC,QAAS,CAAC,UAAU,CAAC;YACrC,IAAI,EAAE,CAAC,MAAM,CAAC;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,UAAkB;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACrB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;SACtD;QAED,MAAM,MAAM,GAAG,WAAW,UAAU,GAAG,CAAC;QAExC,OAAO,MAAM,IAAI,CAAC,QAAS,CAAC,UAAU,CAAC;YACrC,QAAQ,EAAE,CAAC,MAAM,CAAC;SACnB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACrB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;SACtD;QAED,OAAO,MAAM,IAAI,CAAC,QAAS,CAAC,UAAU,CAAC;YACrC,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAgB,EAAE,OAAc;QAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QAED,OAAO,MAAM,IAAI,CAAC,QAAS,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,eAAe,CAAC,MAAe;QAC7B,IAAI,CAAC,gBAAM,CAAC,GAAG,CAAC,YAAY,EAAE;YAC5B,OAAO;gBACL,eAAe,EAAE,qCAAqC;gBACtD,QAAQ,EAAE,UAAU;gBACpB,SAAS,EAAE,GAAG;aACf,CAAC;SACH;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,gBAAM,CAAC,GAAG,CAAC,WAAW,CAAC;QAErD,OAAO;YACL,eAAe,EAAE,mBAAmB,WAAW,cAAc,WAAW,EAAE;YAC1E,MAAM,EAAE,iBAAiB;YACzB,aAAa,EAAE,MAAM;SACtB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAAkB,EAAE,QAAgB;QACzD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAM,CAAC,GAAG,CAAC,aAAa,EAAE;YAClD,OAAO;SACR;QAED,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC1D,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,OAAO,CAAC,GAAG,CAAC,gCAAgC,UAAU,IAAI,QAAQ,EAAE,CAAC,CAAC;aACvE;iBAAM;gBACL,OAAO,CAAC,KAAK,CAAC,yCAAyC,UAAU,IAAI,QAAQ,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;aAChG;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;SACjE;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAAkB,EAAE,QAAgB;QACzD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACrB,OAAO;SACR;QAED,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC1D,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,OAAO,CAAC,GAAG,CAAC,wCAAwC,UAAU,IAAI,QAAQ,EAAE,CAAC,CAAC;aAC/E;iBAAM;gBACL,OAAO,CAAC,KAAK,CAAC,iDAAiD,UAAU,IAAI,QAAQ,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;aACxG;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;SACjE;IACH,CAAC;CACF;AAjJD,gCAiJC"}