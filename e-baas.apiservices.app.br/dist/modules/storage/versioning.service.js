"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersioningService = void 0;
const data_source_1 = require("../../infra/database/data-source");
const StorageFile_entity_1 = require("./entity/StorageFile.entity");
const FileVersion_entity_1 = require("./entity/FileVersion.entity");
const Bucket_entity_1 = require("./entity/Bucket.entity");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const crypto_1 = __importDefault(require("crypto"));
class VersioningService {
    constructor() {
        this.fileRepository = data_source_1.AppDataSource.getRepository(StorageFile_entity_1.StorageFile);
        this.versionRepository = data_source_1.AppDataSource.getRepository(FileVersion_entity_1.FileVersion);
        this.bucketRepository = data_source_1.AppDataSource.getRepository(Bucket_entity_1.Bucket);
        this.storageBasePath = process.env.STORAGE_PATH || './storage';
        this.versionsPath = path_1.default.join(this.storageBasePath, 'versions');
        this.ensureVersionsDirectoryExists();
    }
    async ensureVersionsDirectoryExists() {
        try {
            await promises_1.default.access(this.versionsPath);
        }
        catch {
            await promises_1.default.mkdir(this.versionsPath, { recursive: true });
        }
    }
    async enableVersioning(fileId, policy) {
        const file = await this.fileRepository.findOne({
            where: { id: fileId, isDeleted: false }
        });
        if (!file) {
            throw new Error('File not found');
        }
        file.enableVersioning();
        // Create initial version if it doesn't exist
        if (file.totalVersions === 1) {
            await this.createInitialVersion(file);
        }
        await this.fileRepository.save(file);
        console.log(`✅ Versioning enabled for file: ${file.path}`);
        return file;
    }
    async disableVersioning(fileId, keepExistingVersions = true) {
        const file = await this.fileRepository.findOne({
            where: { id: fileId, isDeleted: false }
        });
        if (!file) {
            throw new Error('File not found');
        }
        file.disableVersioning();
        if (!keepExistingVersions) {
            // Delete all versions except the latest
            await this.versionRepository.update({ fileId, isLatest: false }, { status: FileVersion_entity_1.VersionStatus.DELETED });
        }
        await this.fileRepository.save(file);
        console.log(`✅ Versioning disabled for file: ${file.path}`);
        return file;
    }
    async createVersion(fileId, fileBuffer, options = {}) {
        const file = await this.fileRepository.findOne({
            where: { id: fileId, isDeleted: false }
        });
        if (!file) {
            throw new Error('File not found');
        }
        if (!file.hasVersioning()) {
            throw new Error('Versioning is not enabled for this file');
        }
        // Generate file hash
        const etag = crypto_1.default.createHash('md5').update(fileBuffer).digest('hex');
        const checksum = crypto_1.default.createHash('sha256').update(fileBuffer).digest('hex');
        // Check if this version already exists
        const existingVersion = await this.versionRepository.findOne({
            where: { fileId, etag }
        });
        if (existingVersion) {
            throw new Error('A version with identical content already exists');
        }
        // Mark current latest as not latest
        await this.versionRepository.update({ fileId, isLatest: true }, { isLatest: false });
        // Create new version
        const newVersionNumber = file.currentVersion + 1;
        const version = this.versionRepository.create({
            fileId,
            versionNumber: newVersionNumber,
            size: fileBuffer.length,
            mimeType: file.mimeType,
            etag: `"${etag}"`,
            checksum,
            status: FileVersion_entity_1.VersionStatus.ACTIVE,
            type: options.type || FileVersion_entity_1.VersionType.AUTO,
            isLatest: true,
            metadata: options.metadata,
            changelog: options.changelog,
            createdBy: options.createdBy,
            expiresAt: options.retentionDays ?
                new Date(Date.now() + options.retentionDays * 24 * 60 * 60 * 1000) :
                undefined
        });
        const savedVersion = await this.versionRepository.save(version);
        // Save file to disk
        const versionPath = path_1.default.join(this.versionsPath, savedVersion.storagePath);
        await promises_1.default.mkdir(path_1.default.dirname(versionPath), { recursive: true });
        await promises_1.default.writeFile(versionPath, fileBuffer);
        // Update file metadata
        file.incrementVersion();
        file.size = fileBuffer.length;
        file.etag = `"${etag}"`;
        await this.fileRepository.save(file);
        console.log(`✅ Version ${newVersionNumber} created for file: ${file.path}`);
        return savedVersion;
    }
    async getVersions(fileId, includeDeleted = false) {
        const whereCondition = { fileId };
        if (!includeDeleted) {
            whereCondition.status = FileVersion_entity_1.VersionStatus.ACTIVE;
        }
        return await this.versionRepository.find({
            where: whereCondition,
            order: { versionNumber: 'DESC' }
        });
    }
    async getVersion(fileId, versionNumber) {
        return await this.versionRepository.findOne({
            where: {
                fileId,
                versionNumber,
                status: FileVersion_entity_1.VersionStatus.ACTIVE
            }
        });
    }
    async getLatestVersion(fileId) {
        return await this.versionRepository.findOne({
            where: {
                fileId,
                isLatest: true,
                status: FileVersion_entity_1.VersionStatus.ACTIVE
            }
        });
    }
    async downloadVersion(fileId, versionNumber) {
        const version = await this.getVersion(fileId, versionNumber);
        if (!version) {
            throw new Error('Version not found');
        }
        const versionPath = path_1.default.join(this.versionsPath, version.storagePath);
        try {
            const buffer = await promises_1.default.readFile(versionPath);
            return { version, buffer };
        }
        catch (error) {
            throw new Error('Version file not found on storage');
        }
    }
    async rollbackToVersion(fileId, versionNumber, options = {}) {
        const file = await this.fileRepository.findOne({
            where: { id: fileId, isDeleted: false }
        });
        if (!file) {
            throw new Error('File not found');
        }
        const targetVersion = await this.getVersion(fileId, versionNumber);
        if (!targetVersion) {
            throw new Error('Target version not found');
        }
        // Create backup of current version if requested
        if (options.createBackup) {
            const currentVersionPath = path_1.default.join(this.storageBasePath, file.storagePath);
            try {
                const currentBuffer = await promises_1.default.readFile(currentVersionPath);
                await this.createVersion(fileId, currentBuffer, {
                    type: FileVersion_entity_1.VersionType.ROLLBACK,
                    changelog: `Backup before rollback to version ${versionNumber}`,
                    createdBy: options.createdBy
                });
            }
            catch (error) {
                console.warn('Failed to create backup before rollback:', error);
            }
        }
        // Copy target version to current file location
        const targetVersionPath = path_1.default.join(this.versionsPath, targetVersion.storagePath);
        const currentFilePath = path_1.default.join(this.storageBasePath, file.storagePath);
        const versionBuffer = await promises_1.default.readFile(targetVersionPath);
        await promises_1.default.writeFile(currentFilePath, versionBuffer);
        // Update file metadata
        file.size = targetVersion.size;
        file.etag = targetVersion.etag;
        file.setCurrentVersion(versionNumber);
        await this.fileRepository.save(file);
        // Mark target version as latest
        await this.versionRepository.update({ fileId, isLatest: true }, { isLatest: false });
        targetVersion.markAsLatest();
        await this.versionRepository.save(targetVersion);
        console.log(`✅ Rolled back file ${file.path} to version ${versionNumber}`);
        return targetVersion;
    }
    async deleteVersion(fileId, versionNumber, permanent = false) {
        const version = await this.getVersion(fileId, versionNumber);
        if (!version) {
            throw new Error('Version not found');
        }
        if (version.isLatest) {
            throw new Error('Cannot delete the latest version');
        }
        if (permanent) {
            // Delete physical file
            try {
                const versionPath = path_1.default.join(this.versionsPath, version.storagePath);
                await promises_1.default.unlink(versionPath);
            }
            catch (error) {
                console.warn(`Failed to delete version file: ${error}`);
            }
            // Delete database record
            await this.versionRepository.remove(version);
        }
        else {
            // Soft delete
            version.softDelete();
            await this.versionRepository.save(version);
        }
        console.log(`✅ Version ${versionNumber} ${permanent ? 'permanently ' : ''}deleted`);
    }
    async cleanupExpiredVersions() {
        const expiredVersions = await this.versionRepository.find({
            where: {
                status: FileVersion_entity_1.VersionStatus.ACTIVE,
                isLatest: false
            }
        });
        const result = {
            deletedVersions: 0,
            freedSpace: 0,
            errors: []
        };
        for (const version of expiredVersions) {
            if (version.isExpired()) {
                try {
                    const versionPath = path_1.default.join(this.versionsPath, version.storagePath);
                    // Get file size before deletion
                    try {
                        const stats = await promises_1.default.stat(versionPath);
                        result.freedSpace += stats.size;
                    }
                    catch (error) {
                        // File might not exist
                    }
                    // Delete physical file
                    try {
                        await promises_1.default.unlink(versionPath);
                    }
                    catch (error) {
                        // File might not exist
                    }
                    // Delete database record
                    await this.versionRepository.remove(version);
                    result.deletedVersions++;
                }
                catch (error) {
                    result.errors.push(`Failed to cleanup version ${version.id}: ${error.message}`);
                }
            }
        }
        if (result.deletedVersions > 0) {
            console.log(`✅ Cleaned up ${result.deletedVersions} expired versions, freed ${(result.freedSpace / 1024 / 1024).toFixed(2)} MB`);
        }
        return result;
    }
    async applyRetentionPolicy(fileId, policy) {
        const versions = await this.getVersions(fileId, false);
        const result = {
            deletedVersions: 0,
            freedSpace: 0,
            errors: []
        };
        // Sort versions by creation date (newest first)
        const sortedVersions = versions
            .filter(v => !v.isLatest) // Never delete latest version
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        // Apply max versions limit
        if (policy.maxVersions && sortedVersions.length > policy.maxVersions) {
            const versionsToDelete = sortedVersions.slice(policy.maxVersions);
            for (const version of versionsToDelete) {
                try {
                    await this.deleteVersion(fileId, version.versionNumber, true);
                    result.deletedVersions++;
                    result.freedSpace += version.size;
                }
                catch (error) {
                    result.errors.push(`Failed to delete version ${version.versionNumber}: ${error.message}`);
                }
            }
        }
        // Apply retention days
        if (policy.retentionDays) {
            const cutoffDate = new Date(Date.now() - policy.retentionDays * 24 * 60 * 60 * 1000);
            const oldVersions = sortedVersions.filter(v => v.createdAt < cutoffDate);
            for (const version of oldVersions) {
                try {
                    await this.deleteVersion(fileId, version.versionNumber, true);
                    result.deletedVersions++;
                    result.freedSpace += version.size;
                }
                catch (error) {
                    result.errors.push(`Failed to delete old version ${version.versionNumber}: ${error.message}`);
                }
            }
        }
        return result;
    }
    async getVersioningStats(fileId) {
        const whereCondition = fileId ? { id: fileId } : {};
        const files = await this.fileRepository.find({
            where: { ...whereCondition, isDeleted: false }
        });
        const versionedFiles = files.filter(f => f.hasVersioning());
        const versionWhereCondition = fileId ? { fileId } : {};
        const versions = await this.versionRepository.find({
            where: { ...versionWhereCondition, status: FileVersion_entity_1.VersionStatus.ACTIVE }
        });
        const totalSize = versions.reduce((sum, version) => sum + version.size, 0);
        return {
            totalFiles: files.length,
            versionedFiles: versionedFiles.length,
            totalVersions: versions.length,
            totalSize,
            averageVersionsPerFile: versionedFiles.length > 0 ? versions.length / versionedFiles.length : 0
        };
    }
    async createInitialVersion(file) {
        // Read current file content
        const currentFilePath = path_1.default.join(this.storageBasePath, file.storagePath);
        let fileBuffer;
        try {
            fileBuffer = await promises_1.default.readFile(currentFilePath);
        }
        catch (error) {
            throw new Error('Current file not found on storage');
        }
        // Create initial version
        const etag = crypto_1.default.createHash('md5').update(fileBuffer).digest('hex');
        const checksum = crypto_1.default.createHash('sha256').update(fileBuffer).digest('hex');
        const version = this.versionRepository.create({
            fileId: file.id,
            versionNumber: 1,
            size: fileBuffer.length,
            mimeType: file.mimeType,
            etag: `"${etag}"`,
            checksum,
            status: FileVersion_entity_1.VersionStatus.ACTIVE,
            type: FileVersion_entity_1.VersionType.AUTO,
            isLatest: true,
            changelog: 'Initial version'
        });
        const savedVersion = await this.versionRepository.save(version);
        // Save initial version to versions directory
        const versionPath = path_1.default.join(this.versionsPath, savedVersion.storagePath);
        await promises_1.default.mkdir(path_1.default.dirname(versionPath), { recursive: true });
        await promises_1.default.writeFile(versionPath, fileBuffer);
        return savedVersion;
    }
}
exports.VersioningService = VersioningService;
//# sourceMappingURL=versioning.service.js.map