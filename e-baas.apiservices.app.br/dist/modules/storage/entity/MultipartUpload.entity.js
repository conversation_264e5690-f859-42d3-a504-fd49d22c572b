"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultipartUpload = void 0;
const typeorm_1 = require("typeorm");
let MultipartUpload = class MultipartUpload {
    generateUploadId() {
        if (!this.uploadId) {
            this.uploadId = this.generateRandomUploadId();
        }
        // Set expiration to 24 hours from now
        if (!this.expiresAt) {
            this.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
        }
    }
    generateRandomUploadId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 15);
        return `multipart_${timestamp}_${random}`;
    }
    // Helper methods
    isExpired() {
        return new Date() > this.expiresAt;
    }
    isActive() {
        return this.status === 'initiated' || this.status === 'in_progress';
    }
    isCompleted() {
        return this.status === 'completed';
    }
    isAborted() {
        return this.status === 'aborted';
    }
    addPart(partNumber, etag, size, md5Hash) {
        // Remove existing part with same number if exists
        this.uploadedParts = this.uploadedParts.filter(part => part.partNumber !== partNumber);
        // Add new part
        this.uploadedParts.push({
            partNumber,
            etag,
            size,
            uploadedAt: new Date(),
            md5Hash
        });
        // Sort parts by part number
        this.uploadedParts.sort((a, b) => a.partNumber - b.partNumber);
        // Update uploaded size
        this.uploadedSize = this.uploadedParts.reduce((total, part) => total + part.size, 0);
        // Update status
        if (this.status === 'initiated') {
            this.status = 'in_progress';
        }
    }
    removePart(partNumber) {
        const partIndex = this.uploadedParts.findIndex(part => part.partNumber === partNumber);
        if (partIndex !== -1) {
            this.uploadedParts.splice(partIndex, 1);
            // Recalculate uploaded size
            this.uploadedSize = this.uploadedParts.reduce((total, part) => total + part.size, 0);
        }
    }
    getPart(partNumber) {
        return this.uploadedParts.find(part => part.partNumber === partNumber);
    }
    getPartsCount() {
        return this.uploadedParts.length;
    }
    getExpectedPartsCount() {
        if (!this.totalSize)
            return 0;
        return Math.ceil(this.totalSize / this.chunkSize);
    }
    getUploadProgress() {
        if (!this.totalSize)
            return 0;
        return (this.uploadedSize / this.totalSize) * 100;
    }
    validateCompleteness() {
        if (!this.totalSize)
            return false;
        const expectedParts = this.getExpectedPartsCount();
        if (this.uploadedParts.length !== expectedParts)
            return false;
        // Check for consecutive part numbers starting from 1
        for (let i = 1; i <= expectedParts; i++) {
            if (!this.uploadedParts.find(part => part.partNumber === i)) {
                return false;
            }
        }
        return this.uploadedSize === this.totalSize;
    }
    complete() {
        this.status = 'completed';
        this.completedAt = new Date();
    }
    abort(reason) {
        this.status = 'aborted';
        this.abortedAt = new Date();
        if (reason) {
            this.failureReason = reason;
        }
    }
    getFullPath() {
        return this.path ? `${this.path}/${this.fileName}` : this.fileName;
    }
    getDurationMinutes() {
        const endTime = this.completedAt || this.abortedAt || new Date();
        return Math.round((endTime.getTime() - this.createdAt.getTime()) / (1000 * 60));
    }
    toSafeObject() {
        return {
            id: this.id,
            uploadId: this.uploadId,
            bucketName: this.bucketName,
            workspaceId: this.workspaceId,
            fileName: this.fileName,
            path: this.path,
            fullPath: this.getFullPath(),
            contentType: this.contentType,
            metadata: this.metadata,
            totalSize: this.totalSize,
            uploadedSize: this.uploadedSize,
            chunkSize: this.chunkSize,
            status: this.status,
            partsCount: this.getPartsCount(),
            expectedPartsCount: this.getExpectedPartsCount(),
            uploadProgress: this.getUploadProgress(),
            expiresAt: this.expiresAt,
            isExpired: this.isExpired(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            completedAt: this.completedAt,
            abortedAt: this.abortedAt,
            durationMinutes: this.getDurationMinutes()
        };
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], MultipartUpload.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "upload_id", unique: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], MultipartUpload.prototype, "uploadId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bucket_name" }),
    __metadata("design:type", String)
], MultipartUpload.prototype, "bucketName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id" }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], MultipartUpload.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "file_name" }),
    __metadata("design:type", String)
], MultipartUpload.prototype, "fileName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "file_path" }),
    __metadata("design:type", String)
], MultipartUpload.prototype, "path", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "content_type", nullable: true }),
    __metadata("design:type", String)
], MultipartUpload.prototype, "contentType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "jsonb", nullable: true }),
    __metadata("design:type", Object)
], MultipartUpload.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "cache_control", nullable: true }),
    __metadata("design:type", String)
], MultipartUpload.prototype, "cacheControl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "total_size", type: "bigint", nullable: true }),
    __metadata("design:type", Number)
], MultipartUpload.prototype, "totalSize", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "chunk_size", type: "integer", default: 5242880 }) // 5MB
    ,
    __metadata("design:type", Number)
], MultipartUpload.prototype, "chunkSize", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "uploaded_size", type: "bigint", default: 0 }),
    __metadata("design:type", Number)
], MultipartUpload.prototype, "uploadedSize", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: ["initiated", "in_progress", "completed", "aborted"],
        default: "initiated"
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], MultipartUpload.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "uploaded_parts", type: "jsonb", default: [] }),
    __metadata("design:type", Array)
], MultipartUpload.prototype, "uploadedParts", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "expires_at" }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Date)
], MultipartUpload.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "created_by", nullable: true }),
    __metadata("design:type", String)
], MultipartUpload.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "completed_at", nullable: true }),
    __metadata("design:type", Date)
], MultipartUpload.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "aborted_at", nullable: true }),
    __metadata("design:type", Date)
], MultipartUpload.prototype, "abortedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "failure_reason", nullable: true }),
    __metadata("design:type", String)
], MultipartUpload.prototype, "failureReason", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], MultipartUpload.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], MultipartUpload.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MultipartUpload.prototype, "generateUploadId", null);
MultipartUpload = __decorate([
    (0, typeorm_1.Entity)("multipart_uploads"),
    (0, typeorm_1.Index)(["workspaceId", "bucketName"]),
    (0, typeorm_1.Index)(["status", "expiresAt"]),
    (0, typeorm_1.Index)(["uploadId"], { unique: true })
], MultipartUpload);
exports.MultipartUpload = MultipartUpload;
//# sourceMappingURL=MultipartUpload.entity.js.map