"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileVersion = exports.VersionType = exports.VersionStatus = void 0;
const typeorm_1 = require("typeorm");
const StorageFile_entity_1 = require("./StorageFile.entity");
const crypto_1 = __importDefault(require("crypto"));
var VersionStatus;
(function (VersionStatus) {
    VersionStatus["ACTIVE"] = "active";
    VersionStatus["ARCHIVED"] = "archived";
    VersionStatus["DELETED"] = "deleted";
})(VersionStatus = exports.VersionStatus || (exports.VersionStatus = {}));
var VersionType;
(function (VersionType) {
    VersionType["MANUAL"] = "manual";
    VersionType["AUTO"] = "auto";
    VersionType["ROLLBACK"] = "rollback";
})(VersionType = exports.VersionType || (exports.VersionType = {}));
let FileVersion = class FileVersion {
    generateVersionMetadata() {
        // Generate version hash if not provided
        if (!this.versionHash) {
            this.versionHash = this.generateVersionHash();
        }
        // Generate storage path for this version
        if (!this.storagePath) {
            this.storagePath = this.generateVersionStoragePath();
        }
    }
    generateVersionHash() {
        const data = `${this.fileId}-${this.versionNumber}-${Date.now()}-${Math.random()}`;
        return crypto_1.default.createHash('sha256').update(data).digest('hex');
    }
    generateVersionStoragePath() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `versions/${this.fileId}/${year}/${month}/${day}/v${this.versionNumber}-${this.versionHash}`;
    }
    // Get human readable file size
    getHumanFileSize() {
        const bytes = this.size;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0)
            return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    // Get compression ratio
    getCompressionRatio() {
        if (!this.originalSize || this.originalSize === 0)
            return 1;
        return this.size / this.originalSize;
    }
    // Check if this version is expired
    isExpired() {
        if (!this.expiresAt)
            return false;
        return new Date() > this.expiresAt;
    }
    // Mark version as latest
    markAsLatest() {
        this.isLatest = true;
    }
    // Mark version as archived
    archive() {
        this.status = VersionStatus.ARCHIVED;
        this.isLatest = false;
    }
    // Mark version as deleted
    softDelete() {
        this.status = VersionStatus.DELETED;
        this.isLatest = false;
    }
    // Restore version
    restore() {
        this.status = VersionStatus.ACTIVE;
    }
    // Calculate age in days
    getAgeInDays() {
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - this.createdAt.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    // Get version info for API response
    toVersionInfo() {
        return {
            id: this.id,
            fileId: this.fileId,
            versionNumber: this.versionNumber,
            versionHash: this.versionHash,
            size: this.size,
            humanSize: this.getHumanFileSize(),
            mimeType: this.mimeType,
            etag: this.etag,
            status: this.status,
            type: this.type,
            isLatest: this.isLatest,
            metadata: this.metadata,
            changelog: this.changelog,
            createdBy: this.createdBy,
            checksum: this.checksum,
            compressionType: this.compressionType,
            originalSize: this.originalSize,
            compressionRatio: this.getCompressionRatio(),
            ageInDays: this.getAgeInDays(),
            isExpired: this.isExpired(),
            expiresAt: this.expiresAt,
            createdAt: this.createdAt
        };
    }
    // Convert to safe object (without sensitive paths)
    toSafeObject() {
        const { storagePath, ...safeObject } = this;
        return {
            ...safeObject,
            humanSize: this.getHumanFileSize(),
            compressionRatio: this.getCompressionRatio(),
            ageInDays: this.getAgeInDays()
        };
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], FileVersion.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "file_id", type: "varchar", length: 255 }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], FileVersion.prototype, "fileId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "version_number", type: "integer" }),
    __metadata("design:type", Number)
], FileVersion.prototype, "versionNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "version_hash", type: "varchar", length: 64, unique: true }),
    __metadata("design:type", String)
], FileVersion.prototype, "versionHash", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "bigint" }),
    __metadata("design:type", Number)
], FileVersion.prototype, "size", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "mime_type", type: "varchar", length: 100 }),
    __metadata("design:type", String)
], FileVersion.prototype, "mimeType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], FileVersion.prototype, "etag", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "storage_path", type: "varchar", length: 1000 }),
    __metadata("design:type", String)
], FileVersion.prototype, "storagePath", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: VersionStatus,
        default: VersionStatus.ACTIVE
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], FileVersion.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: VersionType,
        default: VersionType.AUTO
    }),
    __metadata("design:type", String)
], FileVersion.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_latest", type: "boolean", default: false }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Boolean)
], FileVersion.prototype, "isLatest", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "json", nullable: true }),
    __metadata("design:type", Object)
], FileVersion.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "text", nullable: true }),
    __metadata("design:type", String)
], FileVersion.prototype, "changelog", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "created_by", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], FileVersion.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "checksum", type: "varchar", length: 128, nullable: true }),
    __metadata("design:type", String)
], FileVersion.prototype, "checksum", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "compression_type", type: "varchar", length: 50, nullable: true }),
    __metadata("design:type", String)
], FileVersion.prototype, "compressionType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "original_size", type: "bigint", nullable: true }),
    __metadata("design:type", Number)
], FileVersion.prototype, "originalSize", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "expires_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], FileVersion.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], FileVersion.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => StorageFile_entity_1.StorageFile, file => file.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: "file_id" }),
    __metadata("design:type", StorageFile_entity_1.StorageFile)
], FileVersion.prototype, "file", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], FileVersion.prototype, "generateVersionMetadata", null);
FileVersion = __decorate([
    (0, typeorm_1.Entity)("file_versions"),
    (0, typeorm_1.Index)(["fileId", "versionNumber"], { unique: true }),
    (0, typeorm_1.Index)(["fileId", "status"]),
    (0, typeorm_1.Index)(["fileId", "isLatest"]),
    (0, typeorm_1.Index)(["createdAt"]),
    (0, typeorm_1.Index)(["size"])
], FileVersion);
exports.FileVersion = FileVersion;
//# sourceMappingURL=FileVersion.entity.js.map