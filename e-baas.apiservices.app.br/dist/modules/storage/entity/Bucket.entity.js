"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bucket = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const storage_dto_1 = require("../dto/storage.dto");
const StorageFile_entity_1 = require("./StorageFile.entity");
let Bucket = class Bucket {
    validateBucket() {
        // Normalize bucket name
        this.name = this.name.toLowerCase().replace(/[^a-z0-9-]/g, '-');
        // Validate bucket name format
        if (!/^[a-z0-9][a-z0-9-]*[a-z0-9]$/.test(this.name) && this.name.length > 1) {
            throw new Error('Bucket name must contain only lowercase letters, numbers, and hyphens, and must start and end with alphanumeric characters');
        }
        if (this.name.length < 3 || this.name.length > 63) {
            throw new Error('Bucket name must be between 3 and 63 characters long');
        }
        // Validate CORS origins
        if (this.enableCors && this.corsOrigins) {
            this.corsOrigins = this.corsOrigins.filter(origin => origin.trim() !== '');
        }
        // Set default CORS if enabled but no origins specified
        if (this.enableCors && (!this.corsOrigins || this.corsOrigins.length === 0)) {
            this.corsOrigins = ['*'];
        }
    }
    // Check if bucket is public
    isPublic() {
        return this.visibility === storage_dto_1.BucketVisibility.PUBLIC;
    }
    // Check if file type is allowed
    isFileTypeAllowed(mimeType) {
        if (!this.allowedMimeTypes || this.allowedMimeTypes.length === 0) {
            return true; // No restrictions
        }
        return this.allowedMimeTypes.some(allowedType => {
            if (allowedType.includes('*')) {
                const pattern = allowedType.replace(/\*/g, '.*');
                return new RegExp(`^${pattern}$`).test(mimeType);
            }
            return allowedType === mimeType;
        });
    }
    // Check if file size is within limits
    isFileSizeAllowed(fileSize) {
        if (!this.maxFileSize) {
            return true; // No size limit
        }
        return fileSize <= this.maxFileSize;
    }
    // Check if origin is allowed (CORS)
    isOriginAllowed(origin) {
        if (!this.enableCors) {
            return false;
        }
        if (!this.corsOrigins || this.corsOrigins.includes('*')) {
            return true;
        }
        return this.corsOrigins.some(allowedOrigin => {
            if (allowedOrigin.includes('*')) {
                const pattern = allowedOrigin.replace(/\*/g, '.*');
                return new RegExp(`^${pattern}$`).test(origin);
            }
            return allowedOrigin === origin;
        });
    }
    // Update statistics
    updateStats(fileSizeDelta, fileCountDelta) {
        this.totalSize = Math.max(0, this.totalSize + fileSizeDelta);
        this.filesCount = Math.max(0, this.filesCount + fileCountDelta);
    }
    // Get storage path for this bucket
    getStoragePath() {
        return `${this.workspaceId}/${this.name}`;
    }
    // Get URL prefix for files in this bucket
    getUrlPrefix(baseUrl) {
        if (this.isPublic()) {
            return `${baseUrl}/storage/v1/object/public/${this.name}`;
        }
        return `${baseUrl}/storage/v1/object/authenticated/${this.name}`;
    }
    // Convert to safe object (without sensitive data)
    toSafeObject() {
        const { ...safeObject } = this;
        return {
            ...safeObject,
            storageUsed: this.totalSize,
            fileCount: this.filesCount
        };
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], Bucket.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], Bucket.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], Bucket.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: storage_dto_1.BucketVisibility,
        default: storage_dto_1.BucketVisibility.PRIVATE
    }),
    __metadata("design:type", String)
], Bucket.prototype, "visibility", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "text", nullable: true }),
    __metadata("design:type", String)
], Bucket.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "allowed_mime_types",
        type: "simple-array",
        nullable: true
    }),
    __metadata("design:type", Array)
], Bucket.prototype, "allowedMimeTypes", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "max_file_size",
        type: "bigint",
        nullable: true
    }),
    __metadata("design:type", Number)
], Bucket.prototype, "maxFileSize", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "enable_versioning",
        type: "boolean",
        default: false
    }),
    __metadata("design:type", Boolean)
], Bucket.prototype, "enableVersioning", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "enable_cors",
        type: "boolean",
        default: true
    }),
    __metadata("design:type", Boolean)
], Bucket.prototype, "enableCors", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "cors_origins",
        type: "simple-array",
        nullable: true
    }),
    __metadata("design:type", Array)
], Bucket.prototype, "corsOrigins", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "files_count",
        type: "integer",
        default: 0
    }),
    __metadata("design:type", Number)
], Bucket.prototype, "filesCount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "total_size",
        type: "bigint",
        default: 0
    }),
    __metadata("design:type", Number)
], Bucket.prototype, "totalSize", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "created_by",
        type: "varchar",
        length: 255,
        nullable: true
    }),
    __metadata("design:type", String)
], Bucket.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "is_active",
        type: "boolean",
        default: true
    }),
    __metadata("design:type", Boolean)
], Bucket.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], Bucket.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], Bucket.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => StorageFile_entity_1.StorageFile, file => file.bucket),
    __metadata("design:type", Array)
], Bucket.prototype, "files", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    (0, typeorm_1.BeforeUpdate)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Bucket.prototype, "validateBucket", null);
Bucket = __decorate([
    (0, typeorm_1.Entity)("storage_buckets"),
    (0, typeorm_1.Index)(["workspaceId", "name"], { unique: true }),
    (0, typeorm_1.Index)(["workspaceId"])
], Bucket);
exports.Bucket = Bucket;
//# sourceMappingURL=Bucket.entity.js.map