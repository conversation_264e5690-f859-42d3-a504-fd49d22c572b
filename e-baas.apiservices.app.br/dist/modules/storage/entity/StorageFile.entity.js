"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageFile = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const Bucket_entity_1 = require("./Bucket.entity");
const FileVersion_entity_1 = require("./FileVersion.entity");
const crypto_1 = __importDefault(require("crypto"));
let StorageFile = class StorageFile {
    generateFileMetadata() {
        // Generate ETag if not provided
        if (!this.etag) {
            this.etag = this.generateETag();
        }
        // Set version if versioning is enabled
        if (!this.version) {
            this.version = this.generateVersion();
        }
        // Normalize path
        this.path = this.normalizePath(this.path);
        // Generate storage path
        this.storagePath = this.generateStoragePath();
    }
    updateMetadata() {
        // Update ETag on content change
        if (this.size && this.etag) {
            this.etag = this.generateETag();
        }
    }
    generateETag() {
        // Generate a unique ETag based on file properties
        const data = `${this.name}-${this.size}-${Date.now()}`;
        return crypto_1.default.createHash('md5').update(data).digest('hex');
    }
    generateVersion() {
        return crypto_1.default.randomUUID();
    }
    normalizePath(path) {
        // Remove leading/trailing slashes and normalize
        return path
            .replace(/^\/+/, '')
            .replace(/\/+$/, '')
            .replace(/\/+/g, '/');
    }
    generateStoragePath() {
        // Generate file system path
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${this.workspaceId}/${this.bucketId}/${year}/${month}/${day}/${this.id}`;
    }
    // Get file extension
    getExtension() {
        const lastDot = this.name.lastIndexOf('.');
        return lastDot > 0 ? this.name.substring(lastDot + 1).toLowerCase() : '';
    }
    // Check if file is an image
    isImage() {
        return this.mimeType.startsWith('image/');
    }
    // Check if file is a video
    isVideo() {
        return this.mimeType.startsWith('video/');
    }
    // Check if file is an audio
    isAudio() {
        return this.mimeType.startsWith('audio/');
    }
    // Check if file is a document
    isDocument() {
        const documentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv'
        ];
        return documentTypes.includes(this.mimeType);
    }
    // Get human readable file size
    getHumanFileSize() {
        const bytes = this.size;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0)
            return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    // Get file URL path
    getUrlPath() {
        return `${this.bucketId}/${this.path}`;
    }
    // Mark file as accessed
    markAsAccessed() {
        this.lastAccessedAt = new Date();
        this.accessCount += 1;
    }
    // Soft delete file
    softDelete() {
        this.isDeleted = true;
        this.deletedAt = new Date();
    }
    // Restore soft deleted file
    restore() {
        this.isDeleted = false;
        this.deletedAt = undefined;
    }
    // Check if file is expired (for temporary files)
    isExpired(expirationHours = 24) {
        if (!this.createdAt)
            return false;
        const expirationTime = new Date(this.createdAt.getTime() + (expirationHours * 60 * 60 * 1000));
        return new Date() > expirationTime;
    }
    // Get file metadata for API response
    toMetadata() {
        return {
            id: this.id,
            name: this.name,
            path: this.path,
            bucket: this.bucketId,
            size: this.size,
            mimeType: this.mimeType,
            etag: this.etag,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            metadata: this.metadata,
            version: this.version,
            cacheControl: this.cacheControl,
            owner: this.ownerId,
            isPublic: this.isPublic,
            lastAccessed: this.lastAccessedAt,
            accessCount: this.accessCount,
            humanSize: this.getHumanFileSize(),
            extension: this.getExtension(),
            isImage: this.isImage(),
            isVideo: this.isVideo(),
            isAudio: this.isAudio(),
            isDocument: this.isDocument()
        };
    }
    // Versioning methods
    enableVersioning() {
        this.versioningEnabled = true;
    }
    disableVersioning() {
        this.versioningEnabled = false;
    }
    incrementVersion() {
        this.currentVersion += 1;
        this.totalVersions += 1;
    }
    setCurrentVersion(version) {
        this.currentVersion = version;
    }
    hasVersioning() {
        return this.versioningEnabled;
    }
    // Get version info
    getVersionInfo() {
        return {
            enabled: this.versioningEnabled,
            current: this.currentVersion,
            total: this.totalVersions,
            hasVersions: this.totalVersions > 1
        };
    }
    // Convert to safe object (without sensitive paths)
    toSafeObject() {
        const { storagePath, ...safeObject } = this;
        return {
            ...safeObject,
            humanSize: this.getHumanFileSize(),
            extension: this.getExtension(),
            urlPath: this.getUrlPath(),
            versionInfo: this.getVersionInfo()
        };
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], StorageFile.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255 }),
    __metadata("design:type", String)
], StorageFile.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 500 }),
    __metadata("design:type", String)
], StorageFile.prototype, "path", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bucket_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], StorageFile.prototype, "bucketId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], StorageFile.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "bigint" }),
    __metadata("design:type", Number)
], StorageFile.prototype, "size", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "mime_type", type: "varchar", length: 100 }),
    __metadata("design:type", String)
], StorageFile.prototype, "mimeType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], StorageFile.prototype, "etag", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "cache_control", type: "varchar", length: 100, nullable: true }),
    __metadata("design:type", String)
], StorageFile.prototype, "cacheControl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "json", nullable: true }),
    __metadata("design:type", Object)
], StorageFile.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 50, nullable: true }),
    __metadata("design:type", String)
], StorageFile.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "storage_path", type: "varchar", length: 1000 }),
    __metadata("design:type", String)
], StorageFile.prototype, "storagePath", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_public", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], StorageFile.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "owner_id", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], StorageFile.prototype, "ownerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "last_accessed_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], StorageFile.prototype, "lastAccessedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "access_count", type: "integer", default: 0 }),
    __metadata("design:type", Number)
], StorageFile.prototype, "accessCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_deleted", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], StorageFile.prototype, "isDeleted", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "deleted_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], StorageFile.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "versioning_enabled", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], StorageFile.prototype, "versioningEnabled", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "current_version", type: "integer", default: 1 }),
    __metadata("design:type", Number)
], StorageFile.prototype, "currentVersion", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "total_versions", type: "integer", default: 1 }),
    __metadata("design:type", Number)
], StorageFile.prototype, "totalVersions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], StorageFile.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], StorageFile.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Bucket_entity_1.Bucket, bucket => bucket.files),
    (0, typeorm_1.JoinColumn)({ name: "bucket_id" }),
    __metadata("design:type", Bucket_entity_1.Bucket)
], StorageFile.prototype, "bucket", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => FileVersion_entity_1.FileVersion, version => version.file, { cascade: true }),
    __metadata("design:type", Array)
], StorageFile.prototype, "versions", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], StorageFile.prototype, "generateFileMetadata", null);
__decorate([
    (0, typeorm_1.BeforeUpdate)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], StorageFile.prototype, "updateMetadata", null);
StorageFile = __decorate([
    (0, typeorm_1.Entity)("storage_files"),
    (0, typeorm_1.Index)(["bucketId", "path"], { unique: true }),
    (0, typeorm_1.Index)(["bucketId"]),
    (0, typeorm_1.Index)(["workspaceId"]),
    (0, typeorm_1.Index)(["mimeType"]),
    (0, typeorm_1.Index)(["createdAt"])
], StorageFile);
exports.StorageFile = StorageFile;
//# sourceMappingURL=StorageFile.entity.js.map