"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoragePolicy = void 0;
const typeorm_1 = require("typeorm");
const rls_dto_1 = require("../dto/rls.dto");
let StoragePolicy = class StoragePolicy {
    // Helper methods
    isApplicableToAction(action) {
        return this.actions.includes(action) || this.actions.includes(rls_dto_1.PolicyAction.ALL);
    }
    isApplicableToBucket(bucketName) {
        return !this.bucketName || this.bucketName === bucketName;
    }
    isGlobalPolicy() {
        return !this.bucketName;
    }
    getConditionByField(field) {
        return this.conditions.find(condition => condition.field === field);
    }
    hasCondition(field) {
        return this.conditions.some(condition => condition.field === field);
    }
    toSafeObject() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            effect: this.effect,
            actions: this.actions,
            conditions: this.conditions,
            workspaceId: this.workspaceId,
            bucketName: this.bucketName,
            priority: this.priority,
            isActive: this.isActive,
            createdBy: this.createdBy,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }
    toAuditLog() {
        return {
            policyId: this.id,
            name: this.name,
            effect: this.effect,
            actions: this.actions,
            workspaceId: this.workspaceId,
            bucketName: this.bucketName,
            isActive: this.isActive
        };
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], StoragePolicy.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255 }),
    __metadata("design:type", String)
], StoragePolicy.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "text", nullable: true }),
    __metadata("design:type", String)
], StoragePolicy.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: rls_dto_1.PolicyEffect,
        default: rls_dto_1.PolicyEffect.DENY
    }),
    __metadata("design:type", String)
], StoragePolicy.prototype, "effect", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "json",
        transformer: {
            to: (value) => JSON.stringify(value),
            from: (value) => value ? JSON.parse(value) : []
        }
    }),
    __metadata("design:type", Array)
], StoragePolicy.prototype, "actions", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "json",
        transformer: {
            to: (value) => JSON.stringify(value),
            from: (value) => value ? JSON.parse(value) : []
        }
    }),
    __metadata("design:type", Array)
], StoragePolicy.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], StoragePolicy.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bucket_name", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], StoragePolicy.prototype, "bucketName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "integer", default: 0 }),
    __metadata("design:type", Number)
], StoragePolicy.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_active", type: "boolean", default: true }),
    __metadata("design:type", Boolean)
], StoragePolicy.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "created_by", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], StoragePolicy.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], StoragePolicy.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], StoragePolicy.prototype, "updatedAt", void 0);
StoragePolicy = __decorate([
    (0, typeorm_1.Entity)("storage_policies"),
    (0, typeorm_1.Index)("IDX_storage_policies_workspace_id", ["workspaceId"]),
    (0, typeorm_1.Index)("IDX_storage_policies_bucket_name", ["bucketName"]),
    (0, typeorm_1.Index)("IDX_storage_policies_active", ["isActive"]),
    (0, typeorm_1.Index)("IDX_storage_policies_priority", ["priority"])
], StoragePolicy);
exports.StoragePolicy = StoragePolicy;
//# sourceMappingURL=StoragePolicy.entity.js.map