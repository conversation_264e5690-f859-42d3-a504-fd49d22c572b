"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const storage_service_1 = require("./storage.service");
const multipart_service_1 = require("./multipart.service");
const cdn_service_1 = require("./cdn.service");
const rls_service_1 = require("./rls.service");
const storage_dto_1 = require("./dto/storage.dto");
const rls_dto_1 = require("./dto/rls.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const apiKeyAuth_middleware_1 = require("../../infra/middlewares/apiKeyAuth.middleware");
const multer_1 = __importDefault(require("multer"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("../../infra/config"));
const storageRouter = (0, express_1.Router)();
const storageService = new storage_service_1.StorageService();
const multipartUploadService = new multipart_service_1.MultipartUploadService();
const cdnService = new cdn_service_1.CDNService();
const rlsService = new rls_service_1.StorageRLSService();
// Configure multer for file uploads
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB default limit
    }
});
// Helper function for validation
const validateDto = async (DtoClass, data) => {
    const dto = (0, class_transformer_1.plainToClass)(DtoClass, data);
    const errors = await (0, class_validator_1.validate)(dto);
    if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
    }
    return dto;
};
// Middleware to validate signed URL tokens
const validateSignedUrl = async (req, res, next) => {
    try {
        const token = req.query.token;
        if (!token) {
            return res.status(401).json({ error: 'Signed URL token required' });
        }
        const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwt.secret);
        req.signedUrlContext = decoded;
        next();
    }
    catch (error) {
        return res.status(401).json({ error: 'Invalid or expired signed URL' });
    }
};
// BUCKET MANAGEMENT ROUTES
// Create bucket
storageRouter.post("/buckets", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const createBucketDto = await validateDto(storage_dto_1.CreateBucketDto, req.body);
        const createdBy = req.user?.id;
        const bucket = await storageService.createBucket(createBucketDto, createdBy);
        return res.status(201).json(bucket);
    }
    catch (error) {
        if (error.message.includes('already exists')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Get bucket info
storageRouter.get("/buckets/:bucketName", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { bucketName } = req.params;
        const { workspaceId } = req.query;
        const bucket = await storageService.getBucket(bucketName, workspaceId);
        if (!bucket) {
            return res.status(404).json({ error: 'Bucket not found' });
        }
        return res.status(200).json(bucket);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// List buckets
storageRouter.get("/buckets", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId } = req.query;
        const buckets = await storageService.listBuckets(workspaceId);
        return res.status(200).json(buckets);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Update bucket
storageRouter.put("/buckets/:bucketName", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const { bucketName } = req.params;
        const { workspaceId } = req.query;
        const updateBucketDto = await validateDto(storage_dto_1.UpdateBucketDto, req.body);
        const bucket = await storageService.updateBucket(bucketName, workspaceId, updateBucketDto);
        return res.status(200).json(bucket);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Delete bucket
storageRouter.delete("/buckets/:bucketName", apiKeyAuth_middleware_1.requireDeleteAccess, async (req, res) => {
    try {
        const { bucketName } = req.params;
        const { workspaceId, force } = req.query;
        const result = await storageService.deleteBucket(bucketName, workspaceId, force === 'true');
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('not empty')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// FILE OPERATION ROUTES
// Upload file
storageRouter.post("/object/:bucketName/*", apiKeyAuth_middleware_1.requireWriteAccess, upload.single('file'), async (req, res) => {
    try {
        const { bucketName } = req.params;
        const filePath = req.params[0]; // Everything after bucketName
        const { workspaceId } = req.query;
        if (!req.file) {
            return res.status(400).json({ error: 'No file provided' });
        }
        const uploadDto = {
            bucketName,
            workspaceId,
            fileName: req.file.originalname,
            path: filePath ? filePath.replace(/\/[^\/]*$/, '') : undefined,
            contentType: req.file.mimetype,
            metadata: req.body.metadata ? JSON.parse(req.body.metadata) : undefined,
            overwrite: req.body.overwrite === 'true',
            cacheControl: req.body.cacheControl,
            expiresIn: req.body.expiresIn ? parseInt(req.body.expiresIn) : undefined
        };
        const userId = req.user?.id;
        const result = await storageService.uploadFile(uploadDto, req.file.buffer, userId);
        return res.status(201).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('already exists')) {
            return res.status(409).json({ error: error.message });
        }
        if (error.message.includes('not allowed') || error.message.includes('exceeds')) {
            return res.status(413).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Download file (public access)
storageRouter.get("/object/public/:bucketName/*", async (req, res) => {
    try {
        const { bucketName } = req.params;
        const filePath = req.params[0];
        const { workspaceId, transform } = req.query;
        const downloadDto = {
            bucketName,
            workspaceId,
            filePath,
            transform
        };
        const { file, buffer } = await storageService.downloadFile(downloadDto);
        // Set appropriate headers
        res.setHeader('Content-Type', file.mimeType);
        res.setHeader('Content-Length', buffer.length);
        res.setHeader('ETag', file.etag);
        res.setHeader('Last-Modified', file.updatedAt.toUTCString());
        // Set CDN cache headers
        const cacheHeaders = cdnService.getCacheHeaders(file.cacheControl ? parseInt(file.cacheControl.split('=')[1]) : undefined);
        Object.entries(cacheHeaders).forEach(([key, value]) => {
            res.setHeader(key, value);
        });
        // Set content disposition for downloads
        const disposition = req.query.download === 'true' ? 'attachment' : 'inline';
        res.setHeader('Content-Disposition', `${disposition}; filename="${file.name}"`);
        return res.send(buffer);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Download file (authenticated access)
storageRouter.get("/object/authenticated/:bucketName/*", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { bucketName } = req.params;
        const filePath = req.params[0];
        const { workspaceId, transform } = req.query;
        const downloadDto = {
            bucketName,
            workspaceId,
            filePath,
            transform
        };
        const { file, buffer } = await storageService.downloadFile(downloadDto);
        // Set appropriate headers
        res.setHeader('Content-Type', file.mimeType);
        res.setHeader('Content-Length', buffer.length);
        res.setHeader('ETag', file.etag);
        res.setHeader('Last-Modified', file.updatedAt.toUTCString());
        // Set CDN cache headers
        const cacheHeaders = cdnService.getCacheHeaders(file.cacheControl ? parseInt(file.cacheControl.split('=')[1]) : undefined);
        Object.entries(cacheHeaders).forEach(([key, value]) => {
            res.setHeader(key, value);
        });
        // Set content disposition for downloads
        const disposition = req.query.download === 'true' ? 'attachment' : 'inline';
        res.setHeader('Content-Disposition', `${disposition}; filename="${file.name}"`);
        return res.send(buffer);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Download file with signed URL
storageRouter.get("/object/sign/:bucketName/*", validateSignedUrl, async (req, res) => {
    try {
        const context = req.signedUrlContext;
        const filePath = req.params[0];
        if (context.operation !== storage_dto_1.FileOperation.DOWNLOAD) {
            return res.status(403).json({ error: 'Invalid operation for this signed URL' });
        }
        const downloadDto = {
            bucketName: context.bucket,
            workspaceId: context.workspaceId,
            filePath: context.path,
            transform: req.query.transform
        };
        const { file, buffer } = await storageService.downloadFile(downloadDto);
        // Set appropriate headers
        res.setHeader('Content-Type', file.mimeType);
        res.setHeader('Content-Length', buffer.length);
        res.setHeader('ETag', file.etag);
        res.setHeader('Last-Modified', file.updatedAt.toUTCString());
        if (file.cacheControl) {
            res.setHeader('Cache-Control', file.cacheControl);
        }
        return res.send(buffer);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// List files in bucket
storageRouter.get("/object/list/:bucketName", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { bucketName } = req.params;
        const { workspaceId, prefix, limit, offset, search, sortBy } = req.query;
        const listDto = {
            bucketName,
            workspaceId,
            prefix,
            limit: limit ? parseInt(limit) : undefined,
            offset,
            search,
            sortBy: sortBy ? sortBy.split(',') : undefined
        };
        const result = await storageService.listFiles(listDto);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Delete file
storageRouter.delete("/object/:bucketName/*", apiKeyAuth_middleware_1.requireDeleteAccess, async (req, res) => {
    try {
        const { bucketName } = req.params;
        const filePath = req.params[0];
        const { workspaceId } = req.query;
        const deleteDto = {
            bucketName,
            workspaceId,
            filePath
        };
        const result = await storageService.deleteFile(deleteDto);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// SIGNED URL ROUTES
// Create signed URL
storageRouter.post("/object/sign", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const signedUrlDto = await validateDto(storage_dto_1.CreateSignedUrlDto, req.body);
        const result = await storageService.createSignedUrl(signedUrlDto);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// UTILITY ROUTES
// Get storage usage statistics
storageRouter.get("/usage", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId } = req.query;
        const usage = await storageService.getStorageUsage(workspaceId);
        return res.status(200).json(usage);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// CDN MANAGEMENT ROUTES
// Get CDN info and status
storageRouter.get("/cdn/info", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const cdnInfo = storageService.getCDNInfo();
        return res.status(200).json(cdnInfo);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Purge CDN cache
storageRouter.post("/cdn/purge", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const { bucketName, filePath } = req.body;
        const result = await storageService.purgeCDNCache(bucketName, filePath);
        if (result.success) {
            return res.status(200).json(result);
        }
        else {
            return res.status(400).json(result);
        }
    }
    catch (error) {
        return res.status(400).json({
            success: false,
            message: error.message || 'Failed to purge CDN cache'
        });
    }
});
// Get CDN metrics
storageRouter.get("/cdn/metrics", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        const metrics = await storageService.getCDNMetrics(start, end);
        if (metrics) {
            return res.status(200).json(metrics);
        }
        else {
            return res.status(400).json({ error: 'CDN is not enabled or metrics not available' });
        }
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// FILE VERSIONING ROUTES
// Enable versioning for a file
storageRouter.post("/versioning/enable", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const enableDto = await validateDto(storage_dto_1.EnableVersioningDto, req.body);
        const file = await storageService.enableFileVersioning(enableDto.fileId, {
            maxVersions: enableDto.maxVersions,
            retentionDays: enableDto.retentionDays,
            autoCleanup: enableDto.autoCleanup,
            excludeExtensions: enableDto.excludeExtensions
        });
        return res.status(200).json({
            success: true,
            file: file.toSafeObject(),
            message: 'Versioning enabled successfully'
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Disable versioning for a file
storageRouter.post("/versioning/disable", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const { fileId, keepExistingVersions = true } = req.body;
        if (!fileId) {
            return res.status(400).json({ error: 'fileId is required' });
        }
        const file = await storageService.disableFileVersioning(fileId, keepExistingVersions);
        return res.status(200).json({
            success: true,
            file: file.toSafeObject(),
            message: 'Versioning disabled successfully'
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Create a new version of a file
storageRouter.post("/versioning/create", apiKeyAuth_middleware_1.requireWriteAccess, upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file provided' });
        }
        const createDto = await validateDto(storage_dto_1.CreateVersionDto, {
            fileId: req.body.fileId,
            changelog: req.body.changelog,
            type: req.body.type,
            metadata: req.body.metadata ? JSON.parse(req.body.metadata) : undefined,
            retentionDays: req.body.retentionDays ? parseInt(req.body.retentionDays) : undefined
        });
        const userId = req.user?.id;
        const version = await storageService.createFileVersion(createDto.fileId, req.file.buffer, {
            ...createDto,
            createdBy: userId
        });
        return res.status(201).json({
            success: true,
            version,
            message: 'Version created successfully'
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('not enabled')) {
            return res.status(400).json({ error: error.message });
        }
        if (error.message.includes('identical content')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// List versions of a file
storageRouter.get("/versioning/:fileId/versions", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { fileId } = req.params;
        const { includeDeleted, limit, offset } = req.query;
        const listDto = await validateDto(storage_dto_1.ListVersionsDto, {
            fileId,
            includeDeleted: includeDeleted === 'true',
            limit: limit ? parseInt(limit) : 50,
            offset: offset ? parseInt(offset) : 0
        });
        const versions = await storageService.getFileVersions(listDto.fileId, listDto.includeDeleted);
        // Apply pagination
        const paginatedVersions = versions.slice(listDto.offset, listDto.offset + listDto.limit);
        return res.status(200).json({
            success: true,
            versions: paginatedVersions,
            pagination: {
                total: versions.length,
                limit: listDto.limit,
                offset: listDto.offset,
                hasMore: versions.length > listDto.offset + listDto.limit
            }
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get specific version info
storageRouter.get("/versioning/:fileId/versions/:versionNumber", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { fileId, versionNumber } = req.params;
        const version = await storageService.getFileVersion(fileId, parseInt(versionNumber));
        if (!version) {
            return res.status(404).json({ error: 'Version not found' });
        }
        return res.status(200).json({
            success: true,
            version
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Download specific version
storageRouter.get("/versioning/:fileId/versions/:versionNumber/download", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { fileId, versionNumber } = req.params;
        const { version, buffer } = await storageService.downloadFileVersion(fileId, parseInt(versionNumber));
        // Set appropriate headers
        res.setHeader('Content-Type', version.mimeType);
        res.setHeader('Content-Length', buffer.length);
        res.setHeader('ETag', version.etag);
        res.setHeader('X-Version-Number', version.versionNumber);
        res.setHeader('X-Version-Hash', version.versionHash);
        // Set CDN cache headers
        const cacheHeaders = cdnService.getCacheHeaders();
        Object.entries(cacheHeaders).forEach(([key, value]) => {
            res.setHeader(key, value);
        });
        // Set content disposition for downloads
        const disposition = req.query.download === 'true' ? 'attachment' : 'inline';
        res.setHeader('Content-Disposition', `${disposition}; filename="v${version.versionNumber}-${version.fileId}"`);
        return res.send(buffer);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Rollback to specific version
storageRouter.post("/versioning/rollback", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const rollbackDto = await validateDto(storage_dto_1.RollbackVersionDto, req.body);
        const userId = req.user?.id;
        const version = await storageService.rollbackFileToVersion(rollbackDto.fileId, rollbackDto.versionNumber, {
            createBackup: rollbackDto.createBackup,
            changelog: rollbackDto.changelog || `Rollback to version ${rollbackDto.versionNumber}`,
            createdBy: userId
        });
        return res.status(200).json({
            success: true,
            version,
            message: `Successfully rolled back to version ${rollbackDto.versionNumber}`
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Delete specific version
storageRouter.delete("/versioning/:fileId/versions/:versionNumber", apiKeyAuth_middleware_1.requireDeleteAccess, async (req, res) => {
    try {
        const { fileId, versionNumber } = req.params;
        const { permanent } = req.query;
        await storageService.deleteFileVersion(fileId, parseInt(versionNumber), permanent === 'true');
        return res.status(200).json({
            success: true,
            message: `Version ${versionNumber} ${permanent === 'true' ? 'permanently ' : ''}deleted successfully`
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('Cannot delete')) {
            return res.status(400).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Apply retention policy
storageRouter.post("/versioning/retention", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const retentionDto = await validateDto(storage_dto_1.ApplyRetentionPolicyDto, req.body);
        const result = await storageService.applyVersionRetentionPolicy(retentionDto.fileId, {
            maxVersions: retentionDto.maxVersions,
            retentionDays: retentionDto.retentionDays,
            autoCleanup: retentionDto.autoCleanup
        });
        return res.status(200).json({
            success: true,
            result,
            message: 'Retention policy applied successfully'
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Cleanup expired versions
storageRouter.post("/versioning/cleanup", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const result = await storageService.cleanupExpiredVersions();
        return res.status(200).json({
            success: true,
            result,
            message: `Cleaned up ${result.deletedVersions} expired versions, freed ${(result.freedSpace / 1024 / 1024).toFixed(2)} MB`
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get versioning statistics
storageRouter.get("/versioning/stats", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { fileId } = req.query;
        const stats = await storageService.getVersioningStats(fileId);
        return res.status(200).json({
            success: true,
            stats
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// MULTIPART UPLOAD ROUTES
/**
 * @swagger
 * /storage/v1/multipart/initiate:
 *   post:
 *     summary: Initiate multipart upload
 *     tags: [Storage]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bucketName
 *               - workspaceId
 *               - fileName
 *             properties:
 *               bucketName:
 *                 type: string
 *               workspaceId:
 *                 type: string
 *               fileName:
 *                 type: string
 *               path:
 *                 type: string
 *               contentType:
 *                 type: string
 *               metadata:
 *                 type: object
 *               totalSize:
 *                 type: number
 *               chunkSize:
 *                 type: number
 *                 default: 5242880
 *     responses:
 *       200:
 *         description: Multipart upload initiated successfully
 */
storageRouter.post("/multipart/initiate", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const initiateDto = await validateDto(storage_dto_1.InitiateMultipartUploadDto, req.body);
        const createdBy = req.user?.id;
        const result = await multipartUploadService.initiateMultipartUpload(initiateDto, createdBy);
        return res.status(200).json({
            success: true,
            upload: result
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('already exists') || error.message.includes('not allowed')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /storage/v1/multipart/upload:
 *   post:
 *     summary: Upload a part of multipart upload
 *     tags: [Storage]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: uploadId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: bucketName
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: partNumber
 *         required: true
 *         schema:
 *           type: integer
 *       - in: query
 *         name: md5Hash
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/octet-stream:
 *           schema:
 *             type: string
 *             format: binary
 *     responses:
 *       200:
 *         description: Part uploaded successfully
 */
storageRouter.post("/multipart/upload", apiKeyAuth_middleware_1.requireWriteAccess, upload.single('part'), async (req, res) => {
    try {
        const { uploadId, bucketName, workspaceId, partNumber, md5Hash } = req.query;
        if (!uploadId || !bucketName || !workspaceId || !partNumber) {
            return res.status(400).json({ error: 'Missing required parameters: uploadId, bucketName, workspaceId, partNumber' });
        }
        let partData;
        if (req.file) {
            partData = req.file.buffer;
        }
        else if (req.body && Buffer.isBuffer(req.body)) {
            partData = req.body;
        }
        else {
            return res.status(400).json({ error: 'No part data provided' });
        }
        const uploadPartDto = await validateDto(storage_dto_1.UploadPartDto, {
            uploadId,
            bucketName,
            workspaceId,
            partNumber: parseInt(partNumber),
            md5Hash,
            contentLength: partData.length
        });
        const result = await multipartUploadService.uploadPart(uploadPartDto, partData);
        return res.status(200).json({
            success: true,
            part: result
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('expired') || error.message.includes('mismatch')) {
            return res.status(410).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /storage/v1/multipart/complete:
 *   post:
 *     summary: Complete multipart upload
 *     tags: [Storage]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - uploadId
 *               - bucketName
 *               - workspaceId
 *               - parts
 *             properties:
 *               uploadId:
 *                 type: string
 *               bucketName:
 *                 type: string
 *               workspaceId:
 *                 type: string
 *               parts:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     partNumber:
 *                       type: integer
 *                     etag:
 *                       type: string
 *                     size:
 *                       type: integer
 *     responses:
 *       200:
 *         description: Multipart upload completed successfully
 */
storageRouter.post("/multipart/complete", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const completeDto = await validateDto(storage_dto_1.CompleteMultipartUploadDto, req.body);
        const result = await multipartUploadService.completeMultipartUpload(completeDto);
        return res.status(200).json({
            success: true,
            file: result
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('expired') || error.message.includes('Missing part')) {
            return res.status(410).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /storage/v1/multipart/abort:
 *   post:
 *     summary: Abort multipart upload
 *     tags: [Storage]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - uploadId
 *               - bucketName
 *               - workspaceId
 *             properties:
 *               uploadId:
 *                 type: string
 *               bucketName:
 *                 type: string
 *               workspaceId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Multipart upload aborted successfully
 */
storageRouter.post("/multipart/abort", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const abortDto = await validateDto(storage_dto_1.AbortMultipartUploadDto, req.body);
        const result = await multipartUploadService.abortMultipartUpload(abortDto);
        return res.status(200).json({
            success: true,
            message: result.message
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /storage/v1/multipart/uploads:
 *   get:
 *     summary: List active multipart uploads
 *     tags: [Storage]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: bucketName
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: prefix
 *         schema:
 *           type: string
 *       - in: query
 *         name: maxUploads
 *         schema:
 *           type: integer
 *           maximum: 1000
 *     responses:
 *       200:
 *         description: List of active multipart uploads
 */
storageRouter.get("/multipart/uploads", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const listDto = await validateDto(storage_dto_1.ListMultipartUploadsDto, req.query);
        const result = await multipartUploadService.listMultipartUploads(listDto);
        return res.status(200).json({
            success: true,
            uploads: result.uploads,
            hasMore: result.hasMore,
            nextKeyMarker: result.nextKeyMarker,
            nextUploadIdMarker: result.nextUploadIdMarker
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /storage/v1/multipart/{uploadId}/parts:
 *   get:
 *     summary: List uploaded parts
 *     tags: [Storage]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: uploadId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: bucketName
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: maxParts
 *         schema:
 *           type: integer
 *           maximum: 1000
 *       - in: query
 *         name: partNumberMarker
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: List of uploaded parts
 */
storageRouter.get("/multipart/:uploadId/parts", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { uploadId } = req.params;
        const listDto = await validateDto(storage_dto_1.ListPartsDto, {
            uploadId,
            ...req.query
        });
        const result = await multipartUploadService.listParts(listDto);
        return res.status(200).json({
            success: true,
            parts: result.parts,
            hasMore: result.hasMore,
            nextPartNumberMarker: result.nextPartNumberMarker,
            maxParts: result.maxParts
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
/**
 * @swagger
 * /storage/v1/multipart/{uploadId}/status:
 *   get:
 *     summary: Get multipart upload status
 *     tags: [Storage]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: uploadId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Multipart upload status
 */
storageRouter.get("/multipart/:uploadId/status", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { uploadId } = req.params;
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'workspaceId is required' });
        }
        const upload = await multipartUploadService.getMultipartUpload(uploadId, workspaceId);
        if (!upload) {
            return res.status(404).json({ error: 'Multipart upload not found' });
        }
        return res.status(200).json({
            success: true,
            upload: upload.toSafeObject()
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// RLS POLICY ROUTES
// Create storage policy
storageRouter.post("/policies", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const createDto = await validateDto(rls_dto_1.CreatePolicyDto, req.body);
        const createdBy = req.user?.id;
        const policy = await rlsService.createPolicy(createDto.name, createDto.effect, createDto.actions, createDto.conditions, createDto.workspaceId, createDto.bucketName, createDto.description);
        return res.status(201).json({
            success: true,
            policy: policy.toSafeObject(),
            message: 'Policy created successfully'
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Update storage policy
storageRouter.put("/policies/:policyId", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const { policyId } = req.params;
        const updateDto = await validateDto(rls_dto_1.UpdatePolicyDto, req.body);
        const policy = await rlsService.updatePolicy(policyId, updateDto);
        return res.status(200).json({
            success: true,
            policy: policy.toSafeObject(),
            message: 'Policy updated successfully'
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Delete storage policy
storageRouter.delete("/policies/:policyId", apiKeyAuth_middleware_1.requireDeleteAccess, async (req, res) => {
    try {
        const { policyId } = req.params;
        await rlsService.deletePolicy(policyId);
        return res.status(200).json({
            success: true,
            message: 'Policy deleted successfully'
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Get storage policy
storageRouter.get("/policies/:policyId", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { policyId } = req.params;
        const policy = await rlsService.getPolicy(policyId);
        if (!policy) {
            return res.status(404).json({ error: 'Policy not found' });
        }
        return res.status(200).json({
            success: true,
            policy: policy.toSafeObject()
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// List storage policies
storageRouter.get("/policies", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId, bucketName } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'workspaceId is required' });
        }
        const policies = await rlsService.getPolicies(workspaceId, bucketName);
        return res.status(200).json({
            success: true,
            policies: policies.map(p => p.toSafeObject()),
            total: policies.length
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Check file access
storageRouter.post("/access/check", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const checkDto = await validateDto(rls_dto_1.CheckAccessDto, req.body);
        const userId = checkDto.userId || req.user?.id;
        if (!userId) {
            return res.status(400).json({ error: 'userId is required' });
        }
        const startTime = Date.now();
        let result;
        if (checkDto.fileId) {
            result = await rlsService.checkFileAccess(checkDto.fileId, userId, checkDto.action, checkDto.additionalContext);
        }
        else if (checkDto.bucketName) {
            result = await rlsService.checkBucketAccess(checkDto.bucketName, checkDto.workspaceId, userId, checkDto.action, checkDto.additionalContext);
        }
        else {
            return res.status(400).json({ error: 'Either fileId or bucketName is required' });
        }
        const evaluationTime = Date.now() - startTime;
        return res.status(200).json({
            success: true,
            access: {
                allowed: result.allowed,
                reason: result.reason,
                matchedPolicies: result.matchedPolicies.map(p => ({
                    id: p.id,
                    name: p.name,
                    effect: p.effect,
                    priority: p.priority
                })),
                evaluationTime
            }
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Test policy
storageRouter.post("/policies/test", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const testDto = await validateDto(rls_dto_1.TestPolicyDto, req.body);
        const result = await rlsService.testPolicy(testDto.policyId, testDto.testContext || {});
        return res.status(200).json({
            success: true,
            test: {
                passed: result.allowed,
                reason: result.reason,
                conditionsEvaluated: result.matchedPolicies.length > 0 ?
                    result.matchedPolicies[0].conditions.map(condition => ({
                        field: condition.field,
                        operator: condition.operator,
                        expectedValue: condition.value,
                        actualValue: 'N/A',
                        result: result.allowed
                    })) : []
            }
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Create policy from template
storageRouter.post("/policies/templates", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const templateDto = await validateDto(rls_dto_1.PolicyTemplateDto, req.body);
        let policy;
        switch (templateDto.template) {
            case 'owner_only':
                policy = await rlsService.createOwnerOnlyPolicy(templateDto.workspaceId, templateDto.bucketName);
                break;
            case 'public_read':
                policy = await rlsService.createPublicReadPolicy(templateDto.workspaceId, templateDto.bucketName);
                break;
            case 'authenticated_only':
                policy = await rlsService.createAuthenticatedOnlyPolicy(templateDto.workspaceId, templateDto.bucketName);
                break;
            case 'workspace_team':
                policy = await rlsService.createWorkspaceTeamPolicy(templateDto.workspaceId, templateDto.bucketName);
                break;
            case 'time_based':
                if (!templateDto.parameters?.startTime || !templateDto.parameters?.endTime) {
                    return res.status(400).json({ error: 'startTime and endTime parameters are required for time_based template' });
                }
                policy = await rlsService.createTimeBasedPolicy(templateDto.workspaceId, new Date(templateDto.parameters.startTime), new Date(templateDto.parameters.endTime), templateDto.bucketName);
                break;
            case 'ip_restricted':
                if (!templateDto.parameters?.allowedCIDRs) {
                    return res.status(400).json({ error: 'allowedCIDRs parameter is required for ip_restricted template' });
                }
                policy = await rlsService.createIPRestrictedPolicy(templateDto.workspaceId, templateDto.parameters.allowedCIDRs, templateDto.bucketName);
                break;
            default:
                return res.status(400).json({ error: 'Invalid template type' });
        }
        return res.status(201).json({
            success: true,
            policy: policy.toSafeObject(),
            message: `Policy created from ${templateDto.template} template`
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Bulk policy operations
storageRouter.post("/policies/bulk", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const bulkDto = await validateDto(rls_dto_1.BulkPolicyOperationDto, req.body);
        const results = [];
        for (const policyId of bulkDto.policyIds) {
            try {
                let result;
                switch (bulkDto.operation) {
                    case 'activate':
                        result = await rlsService.updatePolicy(policyId, { isActive: true });
                        break;
                    case 'deactivate':
                        result = await rlsService.updatePolicy(policyId, { isActive: false });
                        break;
                    case 'delete':
                        await rlsService.deletePolicy(policyId);
                        result = { id: policyId, deleted: true };
                        break;
                }
                results.push({ policyId, success: true, result });
            }
            catch (error) {
                results.push({ policyId, success: false, error: error.message });
            }
        }
        const successCount = results.filter(r => r.success).length;
        return res.status(200).json({
            success: true,
            operation: bulkDto.operation,
            results,
            summary: {
                total: bulkDto.policyIds.length,
                successful: successCount,
                failed: bulkDto.policyIds.length - successCount
            }
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Health check
storageRouter.get("/health", async (req, res) => {
    return res.status(200).json({
        status: 'healthy',
        service: 'storage',
        timestamp: new Date().toISOString()
    });
});
exports.default = storageRouter;
//# sourceMappingURL=storage.controller.js.map