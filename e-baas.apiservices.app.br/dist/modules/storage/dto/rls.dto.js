"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonConditions = exports.PolicyAuditDto = exports.PolicyTemplateDto = exports.BulkPolicyOperationDto = exports.TestPolicyDto = exports.CheckAccessDto = exports.UpdatePolicyDto = exports.PolicyConditionDto = exports.CreatePolicyDto = exports.PolicyOperator = exports.PolicyAction = exports.PolicyEffect = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
// Enums
var PolicyEffect;
(function (PolicyEffect) {
    PolicyEffect["ALLOW"] = "allow";
    PolicyEffect["DENY"] = "deny";
})(PolicyEffect = exports.PolicyEffect || (exports.PolicyEffect = {}));
var PolicyAction;
(function (PolicyAction) {
    PolicyAction["ALL"] = "*";
    PolicyAction["CREATE"] = "create";
    PolicyAction["READ"] = "read";
    PolicyAction["UPDATE"] = "update";
    PolicyAction["DELETE"] = "delete";
    PolicyAction["LIST"] = "list";
    PolicyAction["UPLOAD"] = "upload";
    PolicyAction["DOWNLOAD"] = "download";
    PolicyAction["COPY"] = "copy";
    PolicyAction["MOVE"] = "move";
})(PolicyAction = exports.PolicyAction || (exports.PolicyAction = {}));
var PolicyOperator;
(function (PolicyOperator) {
    PolicyOperator["EQ"] = "eq";
    PolicyOperator["NE"] = "ne";
    PolicyOperator["IN"] = "in";
    PolicyOperator["NOT_IN"] = "not_in";
    PolicyOperator["STARTS_WITH"] = "starts_with";
    PolicyOperator["ENDS_WITH"] = "ends_with";
    PolicyOperator["CONTAINS"] = "contains";
    PolicyOperator["GT"] = "gt";
    PolicyOperator["GTE"] = "gte";
    PolicyOperator["LT"] = "lt";
    PolicyOperator["LTE"] = "lte";
    PolicyOperator["REGEX"] = "regex";
    PolicyOperator["EXISTS"] = "exists";
    PolicyOperator["TIME_BETWEEN"] = "time_between";
    PolicyOperator["IP_IN_RANGE"] = "ip_in_range";
})(PolicyOperator = exports.PolicyOperator || (exports.PolicyOperator = {}));
// DTOs
class CreatePolicyDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePolicyDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePolicyDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(PolicyEffect),
    __metadata("design:type", String)
], CreatePolicyDto.prototype, "effect", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(PolicyAction, { each: true }),
    __metadata("design:type", Array)
], CreatePolicyDto.prototype, "actions", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PolicyConditionDto),
    __metadata("design:type", Array)
], CreatePolicyDto.prototype, "conditions", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePolicyDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePolicyDto.prototype, "bucketName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePolicyDto.prototype, "priority", void 0);
exports.CreatePolicyDto = CreatePolicyDto;
class PolicyConditionDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyConditionDto.prototype, "field", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyConditionDto.prototype, "operator", void 0);
exports.PolicyConditionDto = PolicyConditionDto;
class UpdatePolicyDto {
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdatePolicyDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdatePolicyDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(PolicyEffect),
    __metadata("design:type", String)
], UpdatePolicyDto.prototype, "effect", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(PolicyAction, { each: true }),
    __metadata("design:type", Array)
], UpdatePolicyDto.prototype, "actions", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PolicyConditionDto),
    __metadata("design:type", Array)
], UpdatePolicyDto.prototype, "conditions", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdatePolicyDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdatePolicyDto.prototype, "isActive", void 0);
exports.UpdatePolicyDto = UpdatePolicyDto;
class CheckAccessDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CheckAccessDto.prototype, "fileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CheckAccessDto.prototype, "bucketName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CheckAccessDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(PolicyAction),
    __metadata("design:type", String)
], CheckAccessDto.prototype, "action", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CheckAccessDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CheckAccessDto.prototype, "additionalContext", void 0);
exports.CheckAccessDto = CheckAccessDto;
class TestPolicyDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TestPolicyDto.prototype, "policyId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], TestPolicyDto.prototype, "testContext", void 0);
exports.TestPolicyDto = TestPolicyDto;
class BulkPolicyOperationDto {
}
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], BulkPolicyOperationDto.prototype, "policyIds", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(['activate', 'deactivate', 'delete']),
    __metadata("design:type", String)
], BulkPolicyOperationDto.prototype, "operation", void 0);
exports.BulkPolicyOperationDto = BulkPolicyOperationDto;
class PolicyTemplateDto {
}
__decorate([
    (0, class_validator_1.IsEnum)(['owner_only', 'public_read', 'authenticated_only', 'workspace_team', 'time_based', 'ip_restricted']),
    __metadata("design:type", String)
], PolicyTemplateDto.prototype, "template", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyTemplateDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyTemplateDto.prototype, "bucketName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], PolicyTemplateDto.prototype, "parameters", void 0);
exports.PolicyTemplateDto = PolicyTemplateDto;
class PolicyAuditDto {
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyAuditDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyAuditDto.prototype, "bucketName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyAuditDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(PolicyAction),
    __metadata("design:type", String)
], PolicyAuditDto.prototype, "action", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyAuditDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyAuditDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PolicyAuditDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PolicyAuditDto.prototype, "offset", void 0);
exports.PolicyAuditDto = PolicyAuditDto;
// Predefined condition templates
exports.CommonConditions = {
    // User-based conditions
    OWNER_ONLY: (userId) => ({
        field: 'file.ownerId',
        operator: 'eq',
        value: userId
    }),
    AUTHENTICATED_USER: () => ({
        field: 'user.id',
        operator: 'exists',
        value: true
    }),
    WORKSPACE_MEMBER: (workspaceId) => ({
        field: 'user.workspaceId',
        operator: 'eq',
        value: workspaceId
    }),
    // Time-based conditions
    BUSINESS_HOURS: () => ({
        field: 'timestamp',
        operator: 'time_between',
        value: ['09:00', '17:00']
    }),
    WEEKDAYS_ONLY: () => ({
        field: 'timestamp.getDay()',
        operator: 'in',
        value: [1, 2, 3, 4, 5]
    }),
    // File-based conditions
    FILE_SIZE_LIMIT: (maxSize) => ({
        field: 'file.size',
        operator: 'lte',
        value: maxSize
    }),
    FILE_TYPE_WHITELIST: (allowedTypes) => ({
        field: 'file.mimeType',
        operator: 'in',
        value: allowedTypes
    }),
    FILE_PATH_PREFIX: (prefix) => ({
        field: 'file.path',
        operator: 'starts_with',
        value: prefix
    }),
    // Network-based conditions
    IP_WHITELIST: (allowedIPs) => ({
        field: 'ipAddress',
        operator: 'in',
        value: allowedIPs
    }),
    INTERNAL_NETWORK: () => ({
        field: 'ipAddress',
        operator: 'ip_in_range',
        value: '10.0.0.0/8'
    })
};
//# sourceMappingURL=rls.dto.js.map