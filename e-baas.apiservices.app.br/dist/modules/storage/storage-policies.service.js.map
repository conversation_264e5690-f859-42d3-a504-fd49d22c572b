{"version": 3, "file": "storage-policies.service.js", "sourceRoot": "", "sources": ["../../../src/modules/storage/storage-policies.service.ts"], "names": [], "mappings": ";;;AACA,kEAAiE;AACjE,0DAAgD;AAChD,oEAA0D;AAsB1D,MAAa,sBAAsB;IAIjC;QACE,IAAI,CAAC,gBAAgB,GAAG,2BAAa,CAAC,aAAa,CAAC,sBAAM,CAAC,CAAC;QAC5D,IAAI,CAAC,cAAc,GAAG,2BAAa,CAAC,aAAa,CAAC,gCAAW,CAAC,CAAC;IACjE,CAAC;IAED,kCAAkC;IAClC,KAAK,CAAC,iBAAiB,CACrB,UAAkB,EAClB,WAAmB,EACnB,SAAsC,EACtC,WAIC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE;SACzD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,KAAK,CAAC;SACd;QAED,+CAA+C;QAC/C,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,SAAS,KAAK,MAAM,EAAE;YAC7C,OAAO,IAAI,CAAC;SACb;QAED,yCAAyC;QACzC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,WAAW,IAAI,WAAW,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO,KAAK,CAAC;SACd;QAED,yBAAyB;QACzB,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE;YACnC,+BAA+B;YAC/B,IAAI,WAAW,CAAC,IAAI,KAAK,cAAc,EAAE;gBACvC,OAAO,IAAI,CAAC;aACb;YAED,gEAAgE;YAChE,IAAI,WAAW,CAAC,IAAI,KAAK,eAAe,EAAE;gBACxC,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,qCAAqC;aACrE;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,yCAAyC;IACzC,KAAK,CAAC,eAAe,CACnB,UAAkB,EAClB,WAAmB,EACnB,QAAgB,EAChB,SAAsC,EACtC,WAIC;QAED,4BAA4B;QAC5B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;QACnG,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE;gBACL,QAAQ,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC;gBACzD,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,KAAK;aACjB;YACD,SAAS,EAAE,CAAC,QAAQ,CAAC;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE;YACT,4DAA4D;YAC5D,OAAO,SAAS,KAAK,OAAO,CAAC;SAC9B;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,KAAK,MAAM,EAAE;YACzC,OAAO,IAAI,CAAC;SACb;QAED,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,6BAA6B;YAC7B,IAAI,WAAW,EAAE,EAAE,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW,CAAC,EAAE,EAAE;gBACtD,OAAO,IAAI,CAAC;aACb;YAED,+BAA+B;YAC/B,IAAI,WAAW,EAAE,IAAI,KAAK,cAAc,EAAE;gBACxC,OAAO,IAAI,CAAC;aACb;YAED,+DAA+D;YAC/D,IAAI,WAAW,EAAE,IAAI,KAAK,eAAe,EAAE;gBACzC,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,4CAA4C;aAC5E;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gDAAgD;IAChD,KAAK,CAAC,4BAA4B,CAAC,WAAmB;QACpD,4DAA4D;QAC5D,6DAA6D;QAE7D,MAAM,QAAQ,GAAG;YACf;gBACE,IAAI,EAAE,wBAAwB,WAAW,EAAE;gBAC3C,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,mBAAmB,WAAW,gGAAgG;aACvI;YACD;gBACE,IAAI,EAAE,wBAAwB,WAAW,EAAE;gBAC3C,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,mBAAmB,WAAW,uEAAuE;aAC9G;YACD;gBACE,IAAI,EAAE,wBAAwB,WAAW,EAAE;gBAC3C,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,mBAAmB,WAAW,iEAAiE;aACxG;YACD;gBACE,IAAI,EAAE,wBAAwB,WAAW,EAAE;gBAC3C,KAAK,EAAE,iBAAiB;gBACxB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,mBAAmB,WAAW,iEAAiE;aACxG;YACD;gBACE,IAAI,EAAE,sBAAsB,WAAW,EAAE;gBACzC,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,mBAAmB,WAAW,mFAAmF;aAC1H;YACD;gBACE,IAAI,EAAE,sBAAsB,WAAW,EAAE;gBACzC,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,mBAAmB,WAAW,uEAAuE;aAC9G;YACD;gBACE,IAAI,EAAE,sBAAsB,WAAW,EAAE;gBACzC,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,mBAAmB,WAAW,+DAA+D;aACtG;YACD;gBACE,IAAI,EAAE,sBAAsB,WAAW,EAAE;gBACzC,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,mBAAmB,WAAW,+DAA+D;aACtG;SACF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,mDAAmD,WAAW,GAAG,EAAE,QAAQ,CAAC,CAAC;IAC3F,CAAC;IAED,4CAA4C;IAC5C,mBAAmB,CACjB,KAA0C,EAC1C,SAAoD,EACpD,WAAmB;QAEnB,MAAM,aAAa,GAAG,mBAAmB,WAAW,GAAG,CAAC;QAExD,QAAQ,KAAK,EAAE;YACb,KAAK,iBAAiB;gBACpB,QAAQ,SAAS,EAAE;oBACjB,KAAK,QAAQ;wBACX,OAAO,GAAG,aAAa,+FAA+F,CAAC;oBACzH,KAAK,QAAQ;wBACX,OAAO,GAAG,aAAa,sEAAsE,CAAC;oBAChG,KAAK,QAAQ;wBACX,OAAO,GAAG,aAAa,gEAAgE,CAAC;oBAC1F,KAAK,QAAQ;wBACX,OAAO,GAAG,aAAa,gEAAgE,CAAC;iBAC3F;gBACD,MAAM;YAER,KAAK,eAAe;gBAClB,QAAQ,SAAS,EAAE;oBACjB,KAAK,QAAQ;wBACX,OAAO,GAAG,aAAa,kFAAkF,CAAC;oBAC5G,KAAK,QAAQ;wBACX,OAAO,GAAG,aAAa,sEAAsE,CAAC;oBAChG,KAAK,QAAQ;wBACX,OAAO,GAAG,aAAa,8DAA8D,CAAC;oBACxF,KAAK,QAAQ;wBACX,OAAO,GAAG,aAAa,8DAA8D,CAAC;iBACzF;gBACD,MAAM;SACT;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,mCAAmC;IACnC,KAAK,CAAC,yBAAyB,CAC7B,UAAkB,EAClB,WAAmB,EACnB,QAAgB,EAChB,WAIC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE;SACzD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;SACvD;QAED,4BAA4B;QAC5B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QACjG,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,2BAA2B,EAAE,CAAC;SAChE;QAED,+BAA+B;QAC/B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE;gBACL,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,KAAK;aACjB;SACF,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE;YAChB,4CAA4C;YAC5C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YACvG,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,0CAA0C,EAAE,CAAC;aAC/E;SACF;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED,qCAAqC;IACrC,KAAK,CAAC,2BAA2B,CAC/B,UAAkB,EAClB,WAAmB,EACnB,QAAgB,EAChB,WAIC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAEtG,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,wBAAwB,EAAE,CAAC;SAC7D;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED,qCAAqC;IACrC,KAAK,CAAC,yBAAyB,CAC7B,UAAkB,EAClB,WAAmB,EACnB,QAAgB,EAChB,WAIC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QAExG,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,0BAA0B,EAAE,CAAC;SAC/D;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED,0BAA0B;IAClB,KAAK,CAAC,WAAW,CAAC,UAAkB,EAAE,WAAmB;QAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE;YACxD,MAAM,EAAE,CAAC,IAAI,CAAC;SACf,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACrC;QAED,OAAO,MAAM,CAAC,EAAE,CAAC;IACnB,CAAC;IAED,wCAAwC;IACxC,2BAA2B,CAAC,WAAmB;QAK7C,OAAO;YACL,cAAc,EAAE;gBACd;oBACE,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,+EAA+E;oBAC5F,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,QAAQ,EAAE,WAAW,CAAC;iBAC3E;gBACD;oBACE,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,4DAA4D;oBACzE,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,QAAQ,EAAE,WAAW,CAAC;iBAC3E;gBACD;oBACE,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,2DAA2D;oBACxE,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,QAAQ,EAAE,WAAW,CAAC;iBAC3E;gBACD;oBACE,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,2DAA2D;oBACxE,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,QAAQ,EAAE,WAAW,CAAC;iBAC3E;aACF;YACD,YAAY,EAAE;gBACZ;oBACE,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,mDAAmD;oBAChE,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,CAAC;iBACzE;gBACD;oBACE,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,6DAA6D;oBAC1E,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,CAAC;iBACzE;gBACD;oBACE,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,qDAAqD;oBAClE,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,CAAC;iBACzE;gBACD;oBACE,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,qDAAqD;oBAClE,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,CAAC;iBACzE;aACF;YACD,QAAQ,EAAE;gBACR;oBACE,WAAW,EAAE,4CAA4C;oBACzD,KAAK,EAAE,eAAe;oBACtB,MAAM,EAAE,mDAAmD;iBAC5D;gBACD;oBACE,WAAW,EAAE,yCAAyC;oBACtD,KAAK,EAAE,eAAe;oBACtB,MAAM,EAAE,iDAAiD;iBAC1D;gBACD;oBACE,WAAW,EAAE,0CAA0C;oBACvD,KAAK,EAAE,eAAe;oBACtB,MAAM,EAAE,oDAAoD;iBAC7D;aACF;SACF,CAAC;IACJ,CAAC;CACF;AA3XD,wDA2XC"}