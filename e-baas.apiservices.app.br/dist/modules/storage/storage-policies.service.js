"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoragePoliciesService = void 0;
const data_source_1 = require("../../infra/database/data-source");
const Bucket_entity_1 = require("./entity/Bucket.entity");
const StorageFile_entity_1 = require("./entity/StorageFile.entity");
class StoragePoliciesService {
    constructor() {
        this.bucketRepository = data_source_1.AppDataSource.getRepository(Bucket_entity_1.Bucket);
        this.fileRepository = data_source_1.AppDataSource.getRepository(StorageFile_entity_1.StorageFile);
    }
    // Check if user can access bucket
    async checkBucketAccess(bucketName, workspaceId, operation, userContext) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: bucketName, workspaceId, isActive: true }
        });
        if (!bucket) {
            return false;
        }
        // Public buckets allow read access to everyone
        if (bucket.isPublic() && operation === 'read') {
            return true;
        }
        // Private buckets require authentication
        if (!bucket.isPublic() && (!userContext || userContext.isAnonymous)) {
            return false;
        }
        // Check workspace access
        if (userContext && userContext.role) {
            // Service role has full access
            if (userContext.role === 'service_role') {
                return true;
            }
            // Authenticated users have read/write access to their workspace
            if (userContext.role === 'authenticated') {
                return operation !== 'delete'; // Delete requires special permission
            }
        }
        return false;
    }
    // Check if user can access specific file
    async checkFileAccess(bucketName, workspaceId, filePath, operation, userContext) {
        // First check bucket access
        const bucketAccess = await this.checkBucketAccess(bucketName, workspaceId, operation, userContext);
        if (!bucketAccess) {
            return false;
        }
        const file = await this.fileRepository.findOne({
            where: {
                bucketId: await this.getBucketId(bucketName, workspaceId),
                path: filePath,
                isDeleted: false
            },
            relations: ['bucket']
        });
        if (!file) {
            // For write operations (upload), file doesn't need to exist
            return operation === 'write';
        }
        // Public files allow read access
        if (file.isPublic && operation === 'read') {
            return true;
        }
        // Check file ownership for private files
        if (!file.isPublic) {
            // File owner has full access
            if (userContext?.id && file.ownerId === userContext.id) {
                return true;
            }
            // Service role has full access
            if (userContext?.role === 'service_role') {
                return true;
            }
            // For authenticated users, check if they have workspace access
            if (userContext?.role === 'authenticated') {
                return operation !== 'delete'; // Delete requires ownership or service role
            }
        }
        return false;
    }
    // Create default storage policies for workspace
    async createDefaultStoragePolicies(workspaceId) {
        // This would create RLS policies in a real PostgreSQL setup
        // For now, we'll just log the policies that would be created
        const policies = [
            {
                name: `bucket_select_policy_${workspaceId}`,
                table: 'storage_buckets',
                operation: 'SELECT',
                policy: `workspace_id = '${workspaceId}' AND (visibility = 'public' OR auth.role() = 'service_role' OR auth.role() = 'authenticated')`
            },
            {
                name: `bucket_insert_policy_${workspaceId}`,
                table: 'storage_buckets',
                operation: 'INSERT',
                policy: `workspace_id = '${workspaceId}' AND (auth.role() = 'service_role' OR auth.role() = 'authenticated')`
            },
            {
                name: `bucket_update_policy_${workspaceId}`,
                table: 'storage_buckets',
                operation: 'UPDATE',
                policy: `workspace_id = '${workspaceId}' AND (auth.role() = 'service_role' OR created_by = auth.uid())`
            },
            {
                name: `bucket_delete_policy_${workspaceId}`,
                table: 'storage_buckets',
                operation: 'DELETE',
                policy: `workspace_id = '${workspaceId}' AND (auth.role() = 'service_role' OR created_by = auth.uid())`
            },
            {
                name: `file_select_policy_${workspaceId}`,
                table: 'storage_files',
                operation: 'SELECT',
                policy: `workspace_id = '${workspaceId}' AND (is_public = true OR auth.role() = 'service_role' OR owner_id = auth.uid())`
            },
            {
                name: `file_insert_policy_${workspaceId}`,
                table: 'storage_files',
                operation: 'INSERT',
                policy: `workspace_id = '${workspaceId}' AND (auth.role() = 'service_role' OR auth.role() = 'authenticated')`
            },
            {
                name: `file_update_policy_${workspaceId}`,
                table: 'storage_files',
                operation: 'UPDATE',
                policy: `workspace_id = '${workspaceId}' AND (auth.role() = 'service_role' OR owner_id = auth.uid())`
            },
            {
                name: `file_delete_policy_${workspaceId}`,
                table: 'storage_files',
                operation: 'DELETE',
                policy: `workspace_id = '${workspaceId}' AND (auth.role() = 'service_role' OR owner_id = auth.uid())`
            }
        ];
        console.log(`Storage policies would be created for workspace ${workspaceId}:`, policies);
    }
    // Get storage policy for specific operation
    getStoragePolicySQL(table, operation, workspaceId) {
        const baseCondition = `workspace_id = '${workspaceId}'`;
        switch (table) {
            case 'storage_buckets':
                switch (operation) {
                    case 'SELECT':
                        return `${baseCondition} AND (visibility = 'public' OR auth.role() = 'service_role' OR auth.role() = 'authenticated')`;
                    case 'INSERT':
                        return `${baseCondition} AND (auth.role() = 'service_role' OR auth.role() = 'authenticated')`;
                    case 'UPDATE':
                        return `${baseCondition} AND (auth.role() = 'service_role' OR created_by = auth.uid())`;
                    case 'DELETE':
                        return `${baseCondition} AND (auth.role() = 'service_role' OR created_by = auth.uid())`;
                }
                break;
            case 'storage_files':
                switch (operation) {
                    case 'SELECT':
                        return `${baseCondition} AND (is_public = true OR auth.role() = 'service_role' OR owner_id = auth.uid())`;
                    case 'INSERT':
                        return `${baseCondition} AND (auth.role() = 'service_role' OR auth.role() = 'authenticated')`;
                    case 'UPDATE':
                        return `${baseCondition} AND (auth.role() = 'service_role' OR owner_id = auth.uid())`;
                    case 'DELETE':
                        return `${baseCondition} AND (auth.role() = 'service_role' OR owner_id = auth.uid())`;
                }
                break;
        }
        return baseCondition;
    }
    // Validate file upload permissions
    async validateUploadPermissions(bucketName, workspaceId, filePath, userContext) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: bucketName, workspaceId, isActive: true }
        });
        if (!bucket) {
            return { allowed: false, reason: 'Bucket not found' };
        }
        // Check bucket write access
        const bucketAccess = await this.checkBucketAccess(bucketName, workspaceId, 'write', userContext);
        if (!bucketAccess) {
            return { allowed: false, reason: 'No write access to bucket' };
        }
        // Check if file already exists
        const existingFile = await this.fileRepository.findOne({
            where: {
                bucketId: bucket.id,
                path: filePath,
                isDeleted: false
            }
        });
        if (existingFile) {
            // Check if user can overwrite existing file
            const fileAccess = await this.checkFileAccess(bucketName, workspaceId, filePath, 'write', userContext);
            if (!fileAccess) {
                return { allowed: false, reason: 'No permission to overwrite existing file' };
            }
        }
        return { allowed: true };
    }
    // Validate file download permissions
    async validateDownloadPermissions(bucketName, workspaceId, filePath, userContext) {
        const fileAccess = await this.checkFileAccess(bucketName, workspaceId, filePath, 'read', userContext);
        if (!fileAccess) {
            return { allowed: false, reason: 'No read access to file' };
        }
        return { allowed: true };
    }
    // Validate file deletion permissions
    async validateDeletePermissions(bucketName, workspaceId, filePath, userContext) {
        const fileAccess = await this.checkFileAccess(bucketName, workspaceId, filePath, 'delete', userContext);
        if (!fileAccess) {
            return { allowed: false, reason: 'No delete access to file' };
        }
        return { allowed: true };
    }
    // Helper to get bucket ID
    async getBucketId(bucketName, workspaceId) {
        const bucket = await this.bucketRepository.findOne({
            where: { name: bucketName, workspaceId, isActive: true },
            select: ['id']
        });
        if (!bucket) {
            throw new Error('Bucket not found');
        }
        return bucket.id;
    }
    // Generate storage policy documentation
    generatePolicyDocumentation(workspaceId) {
        return {
            bucketPolicies: [
                {
                    operation: 'SELECT',
                    description: 'Users can view buckets in their workspace. Public buckets are visible to all.',
                    policy: this.getStoragePolicySQL('storage_buckets', 'SELECT', workspaceId)
                },
                {
                    operation: 'INSERT',
                    description: 'Authenticated users can create buckets in their workspace.',
                    policy: this.getStoragePolicySQL('storage_buckets', 'INSERT', workspaceId)
                },
                {
                    operation: 'UPDATE',
                    description: 'Only bucket creators and service role can update buckets.',
                    policy: this.getStoragePolicySQL('storage_buckets', 'UPDATE', workspaceId)
                },
                {
                    operation: 'DELETE',
                    description: 'Only bucket creators and service role can delete buckets.',
                    policy: this.getStoragePolicySQL('storage_buckets', 'DELETE', workspaceId)
                }
            ],
            filePolicies: [
                {
                    operation: 'SELECT',
                    description: 'Users can access public files or their own files.',
                    policy: this.getStoragePolicySQL('storage_files', 'SELECT', workspaceId)
                },
                {
                    operation: 'INSERT',
                    description: 'Authenticated users can upload files to accessible buckets.',
                    policy: this.getStoragePolicySQL('storage_files', 'INSERT', workspaceId)
                },
                {
                    operation: 'UPDATE',
                    description: 'Only file owners and service role can update files.',
                    policy: this.getStoragePolicySQL('storage_files', 'UPDATE', workspaceId)
                },
                {
                    operation: 'DELETE',
                    description: 'Only file owners and service role can delete files.',
                    policy: this.getStoragePolicySQL('storage_files', 'DELETE', workspaceId)
                }
            ],
            examples: [
                {
                    description: 'Allow public read access to profile images',
                    table: 'storage_files',
                    policy: `bucket_id = 'profile-images' AND is_public = true`
                },
                {
                    description: 'Allow users to upload their own avatars',
                    table: 'storage_files',
                    policy: `bucket_id = 'avatars' AND owner_id = auth.uid()`
                },
                {
                    description: 'Admin-only access to sensitive documents',
                    table: 'storage_files',
                    policy: `bucket_id = 'admin-docs' AND auth.role() = 'admin'`
                }
            ]
        };
    }
}
exports.StoragePoliciesService = StoragePoliciesService;
//# sourceMappingURL=storage-policies.service.js.map