"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketServer = void 0;
const socket_io_1 = require("socket.io");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("../../infra/config"));
const realtime_service_1 = require("./realtime.service");
const api_key_service_1 = require("../api-keys/api-key.service");
const database_change_streams_service_1 = require("./database-change-streams.service");
const realtime_dto_1 = require("./dto/realtime.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class WebSocketServer {
    constructor(httpServer) {
        this.authenticatedSockets = new Map();
        this.realtimeService = new realtime_service_1.RealtimeService();
        this.apiKeyService = new api_key_service_1.ApiKeyService();
        this.databaseChangeStreams = new database_change_streams_service_1.DatabaseChangeStreamsService();
        this.io = new socket_io_1.Server(httpServer, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            },
            path: "/realtime/socket.io",
            transports: ["websocket", "polling"],
            pingTimeout: 60000,
            pingInterval: 25000
        });
        this.setupMiddleware();
        this.setupEventHandlers();
        this.setupRealtimeServiceEvents();
        this.initializeDatabaseStreams();
    }
    setupMiddleware() {
        // Authentication middleware
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
                const apiKey = socket.handshake.auth.apikey || socket.handshake.headers.apikey;
                let authInfo = {
                    id: socket.id
                };
                // Try JWT authentication first
                if (token && !token.startsWith('ebaas_')) {
                    try {
                        const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwt.secret);
                        authInfo.userId = decoded.sub;
                        authInfo.workspaceId = decoded.workspace_id || decoded.workspaceId;
                        authInfo.role = decoded.role || 'authenticated';
                    }
                    catch (jwtError) {
                        console.warn('JWT verification failed:', jwtError);
                    }
                }
                // Try API key authentication
                if (apiKey || (token && token.startsWith('ebaas_'))) {
                    const keyToValidate = apiKey || token;
                    const validKey = await this.apiKeyService.validateApiKey(keyToValidate);
                    if (validKey) {
                        authInfo.workspaceId = validKey.workspaceId;
                        authInfo.apiKeyType = validKey.type;
                        authInfo.role = validKey.type === 'service_role' ? 'service_role' : 'authenticated';
                    }
                }
                // Require authentication
                if (!authInfo.workspaceId) {
                    return next(new Error('Authentication required'));
                }
                // Store authentication info
                this.authenticatedSockets.set(socket.id, authInfo);
                socket.auth = authInfo;
                // Register connection with realtime service
                this.realtimeService.registerConnection(socket.id, authInfo.userId, authInfo.workspaceId, {
                    userAgent: socket.handshake.headers['user-agent'],
                    ip: socket.handshake.address,
                    transport: socket.conn.transport.name
                });
                next();
            }
            catch (error) {
                console.error('WebSocket authentication error:', error);
                next(new Error('Authentication failed'));
            }
        });
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            const auth = socket.auth;
            console.log(`Client connected: ${socket.id} (workspace: ${auth.workspaceId}, user: ${auth.userId})`);
            // Handle channel subscription
            socket.on('subscribe', async (data, callback) => {
                try {
                    const subscribeDto = await this.validateDto(realtime_dto_1.SubscribeChannelDto, data);
                    // Ensure workspace matches
                    if (subscribeDto.workspaceId !== auth.workspaceId) {
                        throw new Error('Workspace mismatch');
                    }
                    const result = await this.realtimeService.subscribeToChannel(subscribeDto, socket.id, auth.userId);
                    // Join socket room
                    socket.join(result.channel.getTopic());
                    callback?.({ success: true, channel: result.channel.toSafeObject() });
                }
                catch (error) {
                    console.error('Subscribe error:', error);
                    callback?.({ success: false, error: error.message });
                }
            });
            // Handle channel unsubscription
            socket.on('unsubscribe', async (data, callback) => {
                try {
                    const unsubscribeDto = await this.validateDto(realtime_dto_1.UnsubscribeChannelDto, data);
                    // Ensure workspace matches
                    if (unsubscribeDto.workspaceId !== auth.workspaceId) {
                        throw new Error('Workspace mismatch');
                    }
                    const result = await this.realtimeService.unsubscribeFromChannel(unsubscribeDto, socket.id);
                    // Leave socket room
                    const channel = await this.realtimeService.getChannel(unsubscribeDto.channel, unsubscribeDto.workspaceId);
                    if (channel) {
                        socket.leave(channel.getTopic());
                    }
                    callback?.({ success: true });
                }
                catch (error) {
                    console.error('Unsubscribe error:', error);
                    callback?.({ success: false, error: error.message });
                }
            });
            // Handle broadcast messages
            socket.on('broadcast', async (data, callback) => {
                try {
                    const broadcastDto = await this.validateDto(realtime_dto_1.BroadcastMessageDto, data);
                    // Ensure workspace matches
                    if (broadcastDto.workspaceId !== auth.workspaceId) {
                        throw new Error('Workspace mismatch');
                    }
                    const result = await this.realtimeService.broadcastMessage(broadcastDto, socket.id);
                    callback?.({ success: true, messageId: result.messageId });
                }
                catch (error) {
                    console.error('Broadcast error:', error);
                    callback?.({ success: false, error: error.message });
                }
            });
            // Handle presence updates
            socket.on('presence', async (data, callback) => {
                try {
                    const presenceDto = await this.validateDto(realtime_dto_1.PresenceUpdateDto, data);
                    // Ensure workspace matches
                    if (presenceDto.workspaceId !== auth.workspaceId) {
                        throw new Error('Workspace mismatch');
                    }
                    const presenceState = await this.realtimeService.updatePresence(presenceDto, socket.id);
                    callback?.({ success: true, presenceState });
                }
                catch (error) {
                    console.error('Presence error:', error);
                    callback?.({ success: false, error: error.message });
                }
            });
            // Handle database subscriptions
            socket.on('database_subscribe', async (data, callback) => {
                try {
                    const subscriptionData = {
                        ...data,
                        workspaceId: auth.workspaceId
                    };
                    const result = await this.realtimeService.subscribeToDatabaseChanges(data.channel || 'database_changes', auth.workspaceId, data, socket.id, auth.userId);
                    // Join socket room for database changes
                    socket.join(result.channel.getTopic());
                    callback?.({
                        success: true,
                        channel: result.channel.toSafeObject(),
                        subscription: result.subscription.id
                    });
                }
                catch (error) {
                    console.error('Database subscribe error:', error);
                    callback?.({ success: false, error: error.message });
                }
            });
            // Handle heartbeat
            socket.on('heartbeat', (callback) => {
                // Update connection activity
                const connection = this.authenticatedSockets.get(socket.id);
                if (connection) {
                    connection.metadata = {
                        ...connection.metadata,
                        lastHeartbeat: new Date().toISOString()
                    };
                }
                callback?.({ success: true, timestamp: Date.now() });
            });
            // Handle disconnection
            socket.on('disconnect', (reason) => {
                console.log(`Client disconnected: ${socket.id} (reason: ${reason})`);
                // Unregister connection
                this.realtimeService.unregisterConnection(socket.id);
                this.authenticatedSockets.delete(socket.id);
            });
            // Handle connection errors
            socket.on('error', (error) => {
                console.error(`Socket error for ${socket.id}:`, error);
            });
        });
    }
    setupRealtimeServiceEvents() {
        // Handle broadcast messages
        this.realtimeService.on('message:broadcast', (data) => {
            const { message, targetClients, workspaceId } = data;
            if (targetClients && targetClients.length > 0) {
                // Send to specific clients
                targetClients.forEach((clientId) => {
                    this.io.to(clientId).emit('message', {
                        id: message.id,
                        event: message.event,
                        payload: message.payload,
                        timestamp: message.timestamp,
                        channel: message.channel
                    });
                });
            }
            else {
                // Broadcast to all clients in workspace
                const channel = `${workspaceId}:${message.channel}`;
                this.io.to(channel).emit('message', {
                    id: message.id,
                    event: message.event,
                    payload: message.payload,
                    timestamp: message.timestamp,
                    channel: message.channel
                });
            }
        });
        // Handle database change notifications
        this.realtimeService.on('message:database', (data) => {
            const { message, targetClient } = data;
            this.io.to(targetClient).emit('database_change', {
                id: message.id,
                event: message.event,
                payload: message.payload,
                timestamp: message.timestamp,
                channel: message.channel
            });
        });
        // Handle subscription events
        this.realtimeService.on('subscription:created', (data) => {
            const { channelId, clientId, workspaceId } = data;
            this.io.to(clientId).emit('subscription_success', {
                channelId,
                workspaceId,
                timestamp: new Date().toISOString()
            });
        });
        this.realtimeService.on('subscription:removed', (data) => {
            const { channelId, clientId, workspaceId } = data;
            this.io.to(clientId).emit('subscription_removed', {
                channelId,
                workspaceId,
                timestamp: new Date().toISOString()
            });
        });
        // Handle channel events
        this.realtimeService.on('channel:deleted', (data) => {
            const { workspaceId, channelName } = data;
            const channelTopic = `${workspaceId}:${channelName}`;
            this.io.to(channelTopic).emit('channel_deleted', {
                channel: channelName,
                workspaceId,
                timestamp: new Date().toISOString()
            });
        });
    }
    async validateDto(DtoClass, data) {
        const dto = (0, class_transformer_1.plainToClass)(DtoClass, data);
        const errors = await (0, class_validator_1.validate)(dto);
        if (errors.length > 0) {
            throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
        }
        return dto;
    }
    // Admin methods
    getConnectedClients() {
        return Array.from(this.authenticatedSockets.entries()).map(([socketId, auth]) => ({
            socketId,
            auth
        }));
    }
    getConnectionsForWorkspace(workspaceId) {
        return Array.from(this.authenticatedSockets.entries())
            .filter(([_, auth]) => auth.workspaceId === workspaceId)
            .map(([socketId]) => socketId);
    }
    broadcastToWorkspace(workspaceId, event, data) {
        const connections = this.getConnectionsForWorkspace(workspaceId);
        connections.forEach(socketId => {
            this.io.to(socketId).emit(event, data);
        });
    }
    disconnectClient(socketId, reason = 'admin_disconnect') {
        const socket = this.io.sockets.sockets.get(socketId);
        if (socket) {
            socket.disconnect(true);
        }
    }
    // Get server statistics
    getServerStats() {
        return {
            connectedClients: this.authenticatedSockets.size,
            totalRooms: this.io.sockets.adapter.rooms.size,
            serverUptime: process.uptime(),
            memoryUsage: process.memoryUsage()
        };
    }
    // Initialize database change streams
    async initializeDatabaseStreams() {
        try {
            await this.databaseChangeStreams.startChangeStreams();
            console.log('✅ Database change streams initialized');
        }
        catch (error) {
            console.error('❌ Failed to initialize database change streams:', error);
        }
    }
    // Public method to get database streams health
    async getDatabaseStreamsHealth() {
        return await this.databaseChangeStreams.healthCheck();
    }
    // Public method to get subscription stats
    getDatabaseSubscriptionStats() {
        return this.databaseChangeStreams.getSubscriptionStats();
    }
}
exports.WebSocketServer = WebSocketServer;
exports.default = WebSocketServer;
//# sourceMappingURL=websocket.server.js.map