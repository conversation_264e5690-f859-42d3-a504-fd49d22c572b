"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelSubscription = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const Channel_entity_1 = require("./Channel.entity");
let ChannelSubscription = class ChannelSubscription {
    setDefaults() {
        this.lastActivity = new Date();
    }
    // Update activity timestamp
    updateActivity() {
        this.lastActivity = new Date();
    }
    // Check if subscription matches filter
    matchesFilter(data) {
        if (!this.subscriptionConfig?.filter) {
            return true; // No filter means match all
        }
        try {
            // Simple filter implementation
            // In a real implementation, you'd want a more sophisticated filter parser
            const filter = this.subscriptionConfig.filter;
            // Handle simple equality filters like "column=value"
            if (filter.includes('=')) {
                const [column, value] = filter.split('=');
                return data[column?.trim()] === value?.trim();
            }
            // Handle IN filters like "column.in.(value1,value2)"
            if (filter.includes('.in.')) {
                const [column, values] = filter.split('.in.');
                const valueArray = values.replace(/[()]/g, '').split(',').map(v => v.trim());
                return valueArray.includes(String(data[column?.trim()]));
            }
            // Handle NOT filters like "column.not.value"
            if (filter.includes('.not.')) {
                const [column, value] = filter.split('.not.');
                return data[column?.trim()] !== value?.trim();
            }
            return true;
        }
        catch (error) {
            console.warn('Filter parsing error:', error);
            return true; // Default to match if filter parsing fails
        }
    }
    // Check if event is subscribed
    isEventSubscribed(event) {
        if (!this.subscriptionConfig?.events || this.subscriptionConfig.events.length === 0) {
            return true; // No event filter means subscribe to all
        }
        return this.subscriptionConfig.events.includes(event) ||
            this.subscriptionConfig.events.includes('*');
    }
    // Check if subscription is for database changes
    isDatabaseSubscription() {
        return this.channel?.type === 'database';
    }
    // Check if subscription is for presence
    isPresenceSubscription() {
        return this.channel?.type === 'presence';
    }
    // Check if subscription is for broadcast
    isBroadcastSubscription() {
        return this.channel?.type === 'broadcast';
    }
    // Get presence state
    getPresenceState() {
        if (!this.isPresenceSubscription() || !this.presenceKey) {
            return null;
        }
        return {
            key: this.presenceKey,
            metadata: this.presenceMetadata || {}
        };
    }
    // Update presence metadata
    updatePresence(key, metadata) {
        this.presenceKey = key;
        this.presenceMetadata = metadata;
        this.updateActivity();
    }
    // Check if subscription is stale (inactive for too long)
    isStale(maxInactiveMinutes = 30) {
        const now = new Date();
        const inactiveTime = now.getTime() - this.lastActivity.getTime();
        const maxInactiveTime = maxInactiveMinutes * 60 * 1000;
        return inactiveTime > maxInactiveTime;
    }
    // Convert to safe object
    toSafeObject() {
        return {
            id: this.id,
            channelId: this.channelId,
            clientId: this.clientId,
            userId: this.userId,
            workspaceId: this.workspaceId,
            subscriptionConfig: this.subscriptionConfig,
            presenceKey: this.presenceKey,
            presenceMetadata: this.presenceMetadata,
            lastActivity: this.lastActivity,
            isActive: this.isActive,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], ChannelSubscription.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "channel_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], ChannelSubscription.prototype, "channelId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "client_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], ChannelSubscription.prototype, "clientId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "user_id", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], ChannelSubscription.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], ChannelSubscription.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "subscription_config",
        type: "json",
        nullable: true
    }),
    __metadata("design:type", Object)
], ChannelSubscription.prototype, "subscriptionConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "presence_key",
        type: "varchar",
        length: 255,
        nullable: true
    }),
    __metadata("design:type", String)
], ChannelSubscription.prototype, "presenceKey", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "presence_metadata",
        type: "json",
        nullable: true
    }),
    __metadata("design:type", Object)
], ChannelSubscription.prototype, "presenceMetadata", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "connection_metadata",
        type: "json",
        nullable: true
    }),
    __metadata("design:type", Object)
], ChannelSubscription.prototype, "connectionMetadata", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "last_activity",
        type: "timestamp",
        default: () => "CURRENT_TIMESTAMP"
    }),
    __metadata("design:type", Date)
], ChannelSubscription.prototype, "lastActivity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "is_active",
        type: "boolean",
        default: true
    }),
    __metadata("design:type", Boolean)
], ChannelSubscription.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], ChannelSubscription.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], ChannelSubscription.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Channel_entity_1.Channel, channel => channel.subscriptions),
    (0, typeorm_1.JoinColumn)({ name: "channel_id" }),
    __metadata("design:type", Channel_entity_1.Channel)
], ChannelSubscription.prototype, "channel", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ChannelSubscription.prototype, "setDefaults", null);
ChannelSubscription = __decorate([
    (0, typeorm_1.Entity)("realtime_subscriptions"),
    (0, typeorm_1.Index)(["channelId", "clientId"], { unique: true }),
    (0, typeorm_1.Index)(["channelId"]),
    (0, typeorm_1.Index)(["clientId"]),
    (0, typeorm_1.Index)(["userId"]),
    (0, typeorm_1.Index)(["workspaceId"])
], ChannelSubscription);
exports.ChannelSubscription = ChannelSubscription;
//# sourceMappingURL=ChannelSubscription.entity.js.map