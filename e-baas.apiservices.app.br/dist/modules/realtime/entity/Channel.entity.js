"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Channel = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const realtime_dto_1 = require("../dto/realtime.dto");
const ChannelSubscription_entity_1 = require("./ChannelSubscription.entity");
let Channel = class Channel {
    validateChannel() {
        // Normalize channel name
        this.name = this.name.toLowerCase().replace(/[^a-z0-9-_:]/g, '-');
        // Validate channel name format
        if (!/^[a-z0-9][a-z0-9-_:]*[a-z0-9]$/.test(this.name) && this.name.length > 1) {
            throw new Error('Channel name must contain only lowercase letters, numbers, hyphens, underscores, and colons');
        }
        if (this.name.length < 3 || this.name.length > 100) {
            throw new Error('Channel name must be between 3 and 100 characters long');
        }
        // Update last activity
        this.lastActivity = new Date();
    }
    // Check if channel can accept new connections
    canAcceptConnection() {
        if (!this.isActive) {
            return false;
        }
        if (this.maxConnections && this.connectionCount >= this.maxConnections) {
            return false;
        }
        return true;
    }
    // Check if event is allowed in this channel
    isEventAllowed(event) {
        if (!this.allowedEvents || this.allowedEvents.length === 0) {
            return true; // No restrictions
        }
        return this.allowedEvents.includes(event) || this.allowedEvents.includes('*');
    }
    // Get channel topic (used for routing)
    getTopic() {
        return `${this.workspaceId}:${this.name}`;
    }
    // Get channel path for WebSocket subscription
    getChannelPath() {
        return `realtime/${this.workspaceId}/${this.name}`;
    }
    // Update connection count
    updateConnectionCount(delta) {
        this.connectionCount = Math.max(0, this.connectionCount + delta);
        this.lastActivity = new Date();
    }
    // Update message count
    incrementMessageCount() {
        this.messageCount += 1;
        this.lastActivity = new Date();
    }
    // Check if channel is database subscription
    isDatabaseChannel() {
        return this.type === realtime_dto_1.ChannelType.DATABASE;
    }
    // Check if channel is broadcast
    isBroadcastChannel() {
        return this.type === realtime_dto_1.ChannelType.BROADCAST;
    }
    // Check if channel is presence
    isPresenceChannel() {
        return this.type === realtime_dto_1.ChannelType.PRESENCE;
    }
    // Get database subscription config
    getDatabaseConfig() {
        if (!this.isDatabaseChannel() || !this.config) {
            return {};
        }
        return {
            table: this.config.table,
            schema: this.config.schema || 'public',
            filter: this.config.filter,
            event: this.config.event || '*'
        };
    }
    // Check if user has access to channel
    hasAccess(userId, role) {
        // Public channels are accessible to everyone
        if (!this.isPrivate) {
            return true;
        }
        // Private channels require authentication
        if (!userId && !role) {
            return false;
        }
        // Service role has access to all channels
        if (role === 'service_role') {
            return true;
        }
        // Check if user is creator
        if (userId && this.createdBy === userId) {
            return true;
        }
        // Additional access control could be implemented here
        // based on workspace permissions, etc.
        return false;
    }
    // Convert to safe object
    toSafeObject() {
        return {
            id: this.id,
            name: this.name,
            workspaceId: this.workspaceId,
            type: this.type,
            description: this.description,
            isPrivate: this.isPrivate,
            maxConnections: this.maxConnections,
            connectionCount: this.connectionCount,
            messageCount: this.messageCount,
            lastActivity: this.lastActivity,
            isActive: this.isActive,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            topic: this.getTopic(),
            channelPath: this.getChannelPath()
        };
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], Channel.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], Channel.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], Channel.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: realtime_dto_1.ChannelType,
        default: realtime_dto_1.ChannelType.BROADCAST
    }),
    __metadata("design:type", String)
], Channel.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "text", nullable: true }),
    __metadata("design:type", String)
], Channel.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "is_private",
        type: "boolean",
        default: false
    }),
    __metadata("design:type", Boolean)
], Channel.prototype, "isPrivate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "max_connections",
        type: "integer",
        nullable: true
    }),
    __metadata("design:type", Number)
], Channel.prototype, "maxConnections", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "json",
        nullable: true
    }),
    __metadata("design:type", Object)
], Channel.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "allowed_events",
        type: "simple-array",
        nullable: true
    }),
    __metadata("design:type", Array)
], Channel.prototype, "allowedEvents", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "connection_count",
        type: "integer",
        default: 0
    }),
    __metadata("design:type", Number)
], Channel.prototype, "connectionCount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "message_count",
        type: "bigint",
        default: 0
    }),
    __metadata("design:type", Number)
], Channel.prototype, "messageCount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "last_activity",
        type: "timestamp",
        nullable: true
    }),
    __metadata("design:type", Date)
], Channel.prototype, "lastActivity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "is_active",
        type: "boolean",
        default: true
    }),
    __metadata("design:type", Boolean)
], Channel.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "created_by",
        type: "varchar",
        length: 255,
        nullable: true
    }),
    __metadata("design:type", String)
], Channel.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], Channel.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], Channel.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ChannelSubscription_entity_1.ChannelSubscription, subscription => subscription.channel),
    __metadata("design:type", Array)
], Channel.prototype, "subscriptions", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    (0, typeorm_1.BeforeUpdate)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Channel.prototype, "validateChannel", null);
Channel = __decorate([
    (0, typeorm_1.Entity)("realtime_channels"),
    (0, typeorm_1.Index)(["workspaceId", "name"], { unique: true }),
    (0, typeorm_1.Index)(["workspaceId"]),
    (0, typeorm_1.Index)(["type"])
], Channel);
exports.Channel = Channel;
//# sourceMappingURL=Channel.entity.js.map