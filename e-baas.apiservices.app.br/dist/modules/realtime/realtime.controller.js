"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const realtime_service_1 = require("./realtime.service");
const realtime_dto_1 = require("./dto/realtime.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const apiKeyAuth_middleware_1 = require("../../infra/middlewares/apiKeyAuth.middleware");
const realtimeRouter = (0, express_1.Router)();
const realtimeService = new realtime_service_1.RealtimeService();
// Helper function for validation
const validateDto = async (DtoClass, data) => {
    const dto = (0, class_transformer_1.plainToClass)(DtoClass, data);
    const errors = await (0, class_validator_1.validate)(dto);
    if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
    }
    return dto;
};
// CHANNEL MANAGEMENT
// Create channel
realtimeRouter.post("/channels", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const createChannelDto = await validateDto(realtime_dto_1.CreateChannelDto, req.body);
        const createdBy = req.user?.id;
        const channel = await realtimeService.createChannel(createChannelDto, createdBy);
        return res.status(201).json(channel.toSafeObject());
    }
    catch (error) {
        if (error.message.includes('already exists')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Get channel info
realtimeRouter.get("/channels/:channelName", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { channelName } = req.params;
        const { workspaceId } = req.query;
        const channel = await realtimeService.getChannel(channelName, workspaceId);
        if (!channel) {
            return res.status(404).json({ error: 'Channel not found' });
        }
        return res.status(200).json(channel.toSafeObject());
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// List channels
realtimeRouter.get("/channels", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId, type } = req.query;
        const channels = await realtimeService.listChannels(workspaceId, type);
        return res.status(200).json(channels.map(channel => channel.toSafeObject()));
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Delete channel
realtimeRouter.delete("/channels/:channelName", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const { channelName } = req.params;
        const { workspaceId } = req.query;
        const result = await realtimeService.deleteChannel(channelName, workspaceId);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// MESSAGE BROADCASTING
// Broadcast message
realtimeRouter.post("/broadcast", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const broadcastDto = await validateDto(realtime_dto_1.BroadcastMessageDto, req.body);
        const senderId = req.user?.id || req.apiKey?.id;
        const result = await realtimeService.broadcastMessage(broadcastDto, senderId);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('not allowed')) {
            return res.status(403).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// PRESENCE MANAGEMENT
// Update presence
realtimeRouter.post("/presence", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const presenceDto = await validateDto(realtime_dto_1.PresenceUpdateDto, req.body);
        const clientId = req.headers['x-client-id'] || 'unknown';
        const presenceState = await realtimeService.updatePresence(presenceDto, clientId);
        return res.status(200).json(presenceState);
    }
    catch (error) {
        if (error.message.includes('Invalid presence channel')) {
            return res.status(400).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Get presence state
realtimeRouter.get("/presence/:channelName", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { channelName } = req.params;
        const { workspaceId } = req.query;
        const presenceState = await realtimeService.getPresenceState(channelName, workspaceId);
        return res.status(200).json(presenceState);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// SUBSCRIPTION MANAGEMENT (for testing/debugging)
// Subscribe to channel (REST endpoint for testing)
realtimeRouter.post("/subscribe", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const subscribeDto = await validateDto(realtime_dto_1.SubscribeChannelDto, req.body);
        const clientId = req.headers['x-client-id'] || 'rest-client';
        const userId = req.user?.id;
        const result = await realtimeService.subscribeToChannel(subscribeDto, clientId, userId);
        return res.status(200).json({
            success: result.success,
            channel: result.channel.toSafeObject(),
            clientId
        });
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        if (error.message.includes('Access denied')) {
            return res.status(403).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Unsubscribe from channel (REST endpoint for testing)
realtimeRouter.post("/unsubscribe", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const unsubscribeDto = await validateDto(realtime_dto_1.UnsubscribeChannelDto, req.body);
        const clientId = req.headers['x-client-id'] || 'rest-client';
        const result = await realtimeService.unsubscribeFromChannel(unsubscribeDto, clientId);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// MONITORING AND STATISTICS
// Get channel statistics
realtimeRouter.get("/stats/channels", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId } = req.query;
        const stats = await realtimeService.getChannelStats(workspaceId);
        return res.status(200).json(stats);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get active connections
realtimeRouter.get("/stats/connections", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId } = req.query;
        let connections = realtimeService.getActiveConnections();
        if (workspaceId) {
            connections = realtimeService.getConnectionsForWorkspace(workspaceId);
        }
        return res.status(200).json({
            totalConnections: connections.length,
            connections: connections.map(conn => ({
                id: conn.id,
                userId: conn.userId,
                workspaceId: conn.workspaceId,
                channelCount: conn.channels.length,
                connectedAt: conn.connectedAt,
                lastActivity: conn.lastActivity
            }))
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// WEBHOOK ENDPOINTS for Database Changes
// Notify database change (internal endpoint)
realtimeRouter.post("/internal/database-change", async (req, res) => {
    try {
        // This endpoint would be called by database triggers or change data capture
        const { table, schema, eventType, old, new: newRecord, columns, workspaceId } = req.body;
        // Validate internal access (could be secured with internal API key)
        const internalKey = req.headers['x-internal-key'];
        const expectedKey = process.env.INTERNAL_API_KEY;
        if (!expectedKey || internalKey !== expectedKey) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        await realtimeService.notifyDatabaseChange({
            table,
            schema: schema || 'public',
            eventType,
            old,
            new: newRecord,
            columns: columns || [],
            timestamp: new Date(),
            commitTimestamp: new Date()
        }, workspaceId);
        return res.status(200).json({ success: true });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Health check
realtimeRouter.get("/health", async (req, res) => {
    const activeConnections = realtimeService.getActiveConnections().length;
    return res.status(200).json({
        status: 'healthy',
        service: 'realtime',
        activeConnections,
        timestamp: new Date().toISOString()
    });
});
// WEBSOCKET INFO (for client libraries)
// Get WebSocket connection info
realtimeRouter.get("/connection-info", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId } = req.query;
        const baseUrl = process.env.BASE_URL || 'http://localhost:3333';
        const wsUrl = baseUrl.replace('http', 'ws');
        return res.status(200).json({
            wsUrl: `${wsUrl}/realtime`,
            authRequired: true,
            supportedTransports: ['websocket', 'polling'],
            heartbeatInterval: 30000,
            connectionTimeout: 60000,
            maxChannelsPerConnection: 100,
            workspaceId
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
exports.default = realtimeRouter;
//# sourceMappingURL=realtime.controller.js.map