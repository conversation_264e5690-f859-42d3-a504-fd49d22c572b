"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateChannelDto = exports.DatabaseSubscriptionDto = exports.PresenceUpdateDto = exports.BroadcastMessageDto = exports.UnsubscribeChannelDto = exports.SubscribeChannelDto = exports.BroadcastEvent = exports.PresenceEvent = exports.DatabaseEvent = exports.ChannelType = void 0;
const class_validator_1 = require("class-validator");
var ChannelType;
(function (ChannelType) {
    ChannelType["DATABASE"] = "database";
    ChannelType["BROADCAST"] = "broadcast";
    ChannelType["PRESENCE"] = "presence";
})(ChannelType = exports.ChannelType || (exports.ChannelType = {}));
var DatabaseEvent;
(function (DatabaseEvent) {
    DatabaseEvent["INSERT"] = "INSERT";
    DatabaseEvent["UPDATE"] = "UPDATE";
    DatabaseEvent["DELETE"] = "DELETE";
    DatabaseEvent["ALL"] = "*";
})(DatabaseEvent = exports.DatabaseEvent || (exports.DatabaseEvent = {}));
var PresenceEvent;
(function (PresenceEvent) {
    PresenceEvent["JOIN"] = "presence_join";
    PresenceEvent["LEAVE"] = "presence_leave";
    PresenceEvent["SYNC"] = "presence_sync";
})(PresenceEvent = exports.PresenceEvent || (exports.PresenceEvent = {}));
var BroadcastEvent;
(function (BroadcastEvent) {
    BroadcastEvent["MESSAGE"] = "broadcast";
    BroadcastEvent["CUSTOM"] = "custom";
})(BroadcastEvent = exports.BroadcastEvent || (exports.BroadcastEvent = {}));
class SubscribeChannelDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubscribeChannelDto.prototype, "channel", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubscribeChannelDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(ChannelType),
    __metadata("design:type", String)
], SubscribeChannelDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], SubscribeChannelDto.prototype, "config", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], SubscribeChannelDto.prototype, "presence", void 0);
exports.SubscribeChannelDto = SubscribeChannelDto;
class UnsubscribeChannelDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UnsubscribeChannelDto.prototype, "channel", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UnsubscribeChannelDto.prototype, "workspaceId", void 0);
exports.UnsubscribeChannelDto = UnsubscribeChannelDto;
class BroadcastMessageDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BroadcastMessageDto.prototype, "channel", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BroadcastMessageDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BroadcastMessageDto.prototype, "event", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], BroadcastMessageDto.prototype, "payload", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BroadcastMessageDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], BroadcastMessageDto.prototype, "excludeClientIds", void 0);
exports.BroadcastMessageDto = BroadcastMessageDto;
class PresenceUpdateDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PresenceUpdateDto.prototype, "channel", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PresenceUpdateDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PresenceUpdateDto.prototype, "key", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], PresenceUpdateDto.prototype, "metadata", void 0);
exports.PresenceUpdateDto = PresenceUpdateDto;
class DatabaseSubscriptionDto {
    constructor() {
        this.schema = 'public';
        this.event = DatabaseEvent.ALL;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseSubscriptionDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseSubscriptionDto.prototype, "table", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseSubscriptionDto.prototype, "schema", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(DatabaseEvent),
    __metadata("design:type", String)
], DatabaseSubscriptionDto.prototype, "event", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DatabaseSubscriptionDto.prototype, "filter", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], DatabaseSubscriptionDto.prototype, "columns", void 0);
exports.DatabaseSubscriptionDto = DatabaseSubscriptionDto;
class CreateChannelDto {
    constructor() {
        this.isPrivate = false;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateChannelDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateChannelDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(ChannelType),
    __metadata("design:type", String)
], CreateChannelDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateChannelDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateChannelDto.prototype, "isPrivate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateChannelDto.prototype, "maxConnections", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateChannelDto.prototype, "config", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateChannelDto.prototype, "allowedEvents", void 0);
exports.CreateChannelDto = CreateChannelDto;
//# sourceMappingURL=realtime.dto.js.map