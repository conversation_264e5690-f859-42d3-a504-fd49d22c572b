{"version": 3, "file": "database-change-streams.service.js", "sourceRoot": "", "sources": ["../../../src/modules/realtime/database-change-streams.service.ts"], "names": [], "mappings": ";;;AAAA,kEAAiE;AACjE,mCAAsC;AACtC,yDAAqD;AAsBrD,MAAa,4BAA6B,SAAQ,qBAAY;IAQ5D;QACE,KAAK,EAAE,CAAC;QAPF,gBAAW,GAAG,KAAK,CAAC;QACpB,wBAAmB,GAAsC,IAAI,GAAG,EAAE,CAAC;QACnE,eAAU,GAAG,2BAAa,CAAC,OAAO,CAAC,UAAU,CAAC;QAE9C,YAAO,GAAkB,IAAI,CAAC;QAIpC,IAAI,CAAC,eAAe,GAAG,IAAI,kCAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YACzD,OAAO;SACR;QAED,IAAI;YACF,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACpC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;SAC/D;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACR;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;SAClC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,WAAmB,EACnB,SAAiB,EACjB,MAA0B;QAE1B,IAAI;YACF,6BAA6B;YAC7B,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAEtD,yBAAyB;YACzB,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACpE,aAAa,CAAC,IAAI,CAAC;gBACjB,GAAG,MAAM;gBACT,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,QAAQ;aAClC,CAAC,CAAC;YACH,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAEvD,yCAAyC;YACzC,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAE5D,OAAO,CAAC,GAAG,CAAC,yBAAyB,MAAM,CAAC,KAAK,gBAAgB,SAAS,EAAE,CAAC,CAAC;SAC/E;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,kCAAkC,MAAM,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,SAAiB,EAAE,SAAkB;QAC9D,IAAI,SAAS,EAAE;YACb,kCAAkC;YAClC,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACpE,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC;YAEtE,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;aACnD;iBAAM;gBACL,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,4CAA4C;YAC5C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC5C;QAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,SAAS,IAAI,KAAK,gBAAgB,SAAS,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACH,oBAAoB;QAKlB,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;QACpD,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,MAAM,cAAc,GAA2B,EAAE,CAAC;QAElD,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE;YAC7D,kBAAkB,IAAI,aAAa,CAAC,MAAM,CAAC;YAE3C,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE;gBAC/B,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,IAAI,QAAQ,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;gBACrD,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;aACtD;SACF;QAED,OAAO;YACL,aAAa;YACb,kBAAkB;YAClB,cAAc;SACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,IAAI;YACF,uDAAuD;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAC/D,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,SAAS,KAAK,SAAS,EAAE;gBACxC,OAAO,CAAC,IAAI,CAAC,yFAAyF,CAAC,CAAC;aACzG;YAED,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,qBAAqB,CAAC;YACvC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;OAEjD,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEf,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9B,IAAI;oBACF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;WAE3B,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;iBACnD;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;iBAC9F;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,oEAAoE,EAAE,KAAK,CAAC,CAAC;SAC3F;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,IAAI;YACF,4CAA4C;YAC5C,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;OAE3B,CAAC,CAAC;YAEH,uCAAuC;YACvC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;OAc3B,CAAC,CAAC;YAEH,iCAAiC;YACjC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;OAG3B,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;OAG3B,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+E3B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;SAC5D;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,MAAM,GAAG,QAAQ;QACpE,IAAI;YACF,MAAM,WAAW,GAAG,YAAY,SAAS,UAAU,CAAC;YAEpD,mCAAmC;YACnC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;OAGnD,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;YAErC,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChC,gBAAgB;gBAChB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;2BACT,WAAW;gDACU,MAAM,IAAI,SAAS;;SAE1D,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CAAC,+BAA+B,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC;aACnE;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,wCAAwC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC5C,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACzC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,oBAAoB;QAE9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB;QACrC,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;OAK3C,CAAC,CAAC;YAEH,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;gBAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAEjC,yBAAyB;gBACzB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;SAI3B,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;aACjB;YAED,6CAA6C;YAC7C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;SAI3B,CAAC,CAAC;aACJ;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;SAC9D;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,MAAW;QACrC,MAAM,cAAc,GAAmB;YACrC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE;YACxB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,KAAK,EAAE,MAAM,CAAC,UAAU;YACxB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,SAAS,EAAE,MAAM,CAAC,UAAU;YAC5B,SAAS,EAAE,MAAM,CAAC,UAAU;YAC5B,OAAO,EAAE,MAAM,CAAC,eAAe,IAAI,EAAE;YACrC,eAAe,EAAE,MAAM,CAAC,gBAAgB;YACxC,WAAW,EAAE,MAAM,CAAC,YAAY;SACjC,CAAC;QAEF,yDAAyD;QACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,CAAC;QAExE,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE;YACxC,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;SAC/E;QAED,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,MAAsB;QACtD,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,KAAK,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACjE,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;gBACxC,IAAI,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE;oBAC1D,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACjC,MAAM,CAAC,6CAA6C;iBACrD;aACF;SACF;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,MAAsB,EAAE,YAAgC;QAC1F,mBAAmB;QACnB,IAAI,YAAY,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,EAAE;YACvC,OAAO,KAAK,CAAC;SACd;QAED,mBAAmB;QACnB,IAAI,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;YAChE,OAAO,KAAK,CAAC;SACd;QAED,qBAAqB;QACrB,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,IAAI,YAAY,CAAC,KAAK,KAAK,MAAM,CAAC,SAAS,EAAE;YAC/F,OAAO,KAAK,CAAC;SACd;QAED,2CAA2C;QAC3C,IAAI,YAAY,CAAC,MAAM,EAAE;YACvB,yDAAyD;YACzD,uEAAuE;SACxE;QAED,gCAAgC;QAChC,IAAI,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3D,MAAM,iBAAiB,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACxD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC7B,CAAC;YACF,IAAI,CAAC,iBAAiB,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,EAAE;gBACvD,OAAO,KAAK,CAAC;aACd;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,MAAM,GAAG,QAAQ;QAC9D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;KAG1C,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;QAExB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,IAAI,SAAS,iBAAiB,CAAC,CAAC;SAChE;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;YAC7D,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YACnC,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;gBAC1B,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;aAClE;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;YAC7D,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YACnC,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;gBAC1B,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;aAC1D;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QAMf,IAAI;YACF,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,IAAI;gBACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;SAE1C,CAAC,CAAC;gBACH,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,CAAC,CAAC;aACpD;YAAC,OAAO,KAAK,EAAE;gBACd,gCAAgC;aACjC;YAED,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;iBACrE,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAE/C,OAAO;gBACL,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;gBACjD,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,mBAAmB,EAAE,kBAAkB;gBACvC,cAAc;aACf,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,OAAO;gBACL,MAAM,EAAE,WAAW;gBACnB,WAAW,EAAE,KAAK;gBAClB,mBAAmB,EAAE,CAAC;gBACtB,cAAc,EAAE,CAAC;aAClB,CAAC;SACH;IACH,CAAC;CACF;AA1gBD,oEA0gBC"}