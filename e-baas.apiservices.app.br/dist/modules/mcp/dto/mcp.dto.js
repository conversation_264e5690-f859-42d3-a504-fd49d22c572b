"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCP_TOOLS = exports.PublishEventDto = exports.AuthManagementDto = exports.StorageManagementDto = exports.TableManagementDto = exports.QueryDto = exports.ExecuteCommandDto = void 0;
const class_validator_1 = require("class-validator");
class ExecuteCommandDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExecuteCommandDto.prototype, "command", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ExecuteCommandDto.prototype, "args", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ExecuteCommandDto.prototype, "context", void 0);
exports.ExecuteCommandDto = ExecuteCommandDto;
class QueryDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryDto.prototype, "query", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], QueryDto.prototype, "params", void 0);
exports.QueryDto = QueryDto;
class TableManagementDto {
}
__decorate([
    (0, class_validator_1.IsEnum)(['create', 'drop', 'describe', 'list']),
    __metadata("design:type", String)
], TableManagementDto.prototype, "action", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TableManagementDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], TableManagementDto.prototype, "schema", void 0);
exports.TableManagementDto = TableManagementDto;
class StorageManagementDto {
}
__decorate([
    (0, class_validator_1.IsEnum)(['upload', 'download', 'delete', 'list']),
    __metadata("design:type", String)
], StorageManagementDto.prototype, "action", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StorageManagementDto.prototype, "bucketName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StorageManagementDto.prototype, "fileName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], StorageManagementDto.prototype, "data", void 0);
exports.StorageManagementDto = StorageManagementDto;
class AuthManagementDto {
}
__decorate([
    (0, class_validator_1.IsEnum)(['create_user', 'delete_user', 'get_user', 'list_users', 'update_permissions']),
    __metadata("design:type", String)
], AuthManagementDto.prototype, "action", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AuthManagementDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], AuthManagementDto.prototype, "userData", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], AuthManagementDto.prototype, "permissions", void 0);
exports.AuthManagementDto = AuthManagementDto;
class PublishEventDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PublishEventDto.prototype, "eventType", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], PublishEventDto.prototype, "data", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], PublishEventDto.prototype, "options", void 0);
exports.PublishEventDto = PublishEventDto;
exports.MCP_TOOLS = [
    {
        name: 'execute_sql',
        description: 'Execute SQL query on workspace database',
        inputSchema: {
            type: 'object',
            properties: {
                workspaceId: { type: 'string', description: 'Workspace ID' },
                query: { type: 'string', description: 'SQL query to execute' },
                params: { type: 'array', description: 'Query parameters' }
            },
            required: ['workspaceId', 'query']
        }
    },
    {
        name: 'get_schema',
        description: 'Get database schema for workspace',
        inputSchema: {
            type: 'object',
            properties: {
                workspaceId: { type: 'string', description: 'Workspace ID' }
            },
            required: ['workspaceId']
        }
    },
    {
        name: 'create_table',
        description: 'Create new table in workspace database',
        inputSchema: {
            type: 'object',
            properties: {
                workspaceId: { type: 'string', description: 'Workspace ID' },
                tableName: { type: 'string', description: 'Name of the table' },
                schema: { type: 'object', description: 'Table schema definition' }
            },
            required: ['workspaceId', 'tableName', 'schema']
        }
    },
    {
        name: 'upload_file',
        description: 'Upload file to workspace storage',
        inputSchema: {
            type: 'object',
            properties: {
                workspaceId: { type: 'string', description: 'Workspace ID' },
                bucketName: { type: 'string', description: 'Storage bucket name' },
                fileName: { type: 'string', description: 'File name' },
                data: { type: 'string', description: 'File data (base64 encoded)' }
            },
            required: ['workspaceId', 'bucketName', 'fileName', 'data']
        }
    },
    {
        name: 'create_user',
        description: 'Create new user in workspace',
        inputSchema: {
            type: 'object',
            properties: {
                workspaceId: { type: 'string', description: 'Workspace ID' },
                email: { type: 'string', description: 'User email' },
                password: { type: 'string', description: 'User password' },
                metadata: { type: 'object', description: 'Additional user metadata' }
            },
            required: ['workspaceId', 'email', 'password']
        }
    },
    {
        name: 'publish_event',
        description: 'Publish event to workspace queue',
        inputSchema: {
            type: 'object',
            properties: {
                workspaceId: { type: 'string', description: 'Workspace ID' },
                eventType: { type: 'string', description: 'Type of event' },
                data: { type: 'object', description: 'Event data' },
                options: { type: 'object', description: 'Publishing options' }
            },
            required: ['workspaceId', 'eventType', 'data']
        }
    }
];
//# sourceMappingURL=mcp.dto.js.map