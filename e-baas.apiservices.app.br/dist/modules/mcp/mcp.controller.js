"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.McpController = void 0;
const mcp_useCases_1 = require("./mcp.useCases");
class McpController {
    constructor() {
        this.mcpUseCases = new mcp_useCases_1.McpUseCases();
    }
    async executeCommand(req, res) {
        try {
            const { command, args, context } = req.body;
            if (!command) {
                res.status(400).json({
                    error: 'Command is required'
                });
                return;
            }
            const result = await this.mcpUseCases.executeCommand(command, args, context);
            res.json({
                success: true,
                result,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to execute command',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async getDatabaseSchema(req, res) {
        try {
            const { workspaceId } = req.params;
            const schema = await this.mcpUseCases.getDatabaseSchema(workspaceId);
            res.json({
                success: true,
                schema,
                workspaceId
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to get database schema',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async executeQuery(req, res) {
        try {
            const { workspaceId } = req.params;
            const { query, params } = req.body;
            if (!query) {
                res.status(400).json({
                    error: 'Query is required'
                });
                return;
            }
            const result = await this.mcpUseCases.executeQuery(workspaceId, query, params);
            res.json({
                success: true,
                result,
                workspaceId,
                query: query.substring(0, 100) + (query.length > 100 ? '...' : '')
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to execute query',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async manageTable(req, res) {
        try {
            const { workspaceId } = req.params;
            const { action, tableName, schema } = req.body;
            if (!action || !tableName) {
                res.status(400).json({
                    error: 'Action and tableName are required'
                });
                return;
            }
            const result = await this.mcpUseCases.manageTable(workspaceId, action, tableName, schema);
            res.json({
                success: true,
                result,
                action,
                tableName,
                workspaceId
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to manage table',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async manageStorage(req, res) {
        try {
            const { workspaceId } = req.params;
            const { action, bucketName, fileName, data } = req.body;
            if (!action) {
                res.status(400).json({
                    error: 'Action is required'
                });
                return;
            }
            const result = await this.mcpUseCases.manageStorage(workspaceId, action, {
                bucketName,
                fileName,
                data
            });
            res.json({
                success: true,
                result,
                action,
                workspaceId
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to manage storage',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async manageAuth(req, res) {
        try {
            const { workspaceId } = req.params;
            const { action, userId, userData, permissions } = req.body;
            if (!action) {
                res.status(400).json({
                    error: 'Action is required'
                });
                return;
            }
            const result = await this.mcpUseCases.manageAuth(workspaceId, action, {
                userId,
                userData,
                permissions
            });
            res.json({
                success: true,
                result,
                action,
                workspaceId
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to manage authentication',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async publishEvent(req, res) {
        try {
            const { workspaceId } = req.params;
            const { eventType, data, options } = req.body;
            if (!eventType || !data) {
                res.status(400).json({
                    error: 'eventType and data are required'
                });
                return;
            }
            const result = await this.mcpUseCases.publishEvent(workspaceId, eventType, data, options);
            res.json({
                success: true,
                messageId: result,
                eventType,
                workspaceId
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to publish event',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async getSystemInfo(req, res) {
        try {
            const info = await this.mcpUseCases.getSystemInfo();
            res.json({
                success: true,
                info
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to get system info',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async healthCheck(req, res) {
        try {
            const health = await this.mcpUseCases.healthCheck();
            res.json({
                success: true,
                health,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Health check failed',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async getAvailableCommands(req, res) {
        try {
            const commands = await this.mcpUseCases.getAvailableCommands();
            res.json({
                success: true,
                commands
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to get available commands',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
}
exports.McpController = McpController;
//# sourceMappingURL=mcp.controller.js.map