"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const repository_1 = require("infra/repository");
const errorHandlers_1 = require("infra/errorHandlers");
class ApiKeyServiceUseCases {
    constructor() { }
    async getAll() {
        try {
            return await repository_1.apiKeyServiceRepository.find();
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async getOne(id) {
        try {
            const apiKeyService = await repository_1.apiKeyServiceRepository.findOneBy({ id });
            if (!apiKeyService) {
                throw errorHandlers_1.ErrorHandler.NotFound("ApiKeyService not found");
            }
            return apiKeyService;
        }
        catch (error) {
            if (error instanceof Error &&
                error.message === "ApiKeyService not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async create(data) {
        try {
            return await repository_1.apiKeyServiceRepository.save(data);
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async update(id, data) {
        try {
            const apiKeyService = await this.getOne(id);
            await repository_1.apiKeyServiceRepository.update(id, data);
            return { ...apiKeyService, ...data };
        }
        catch (error) {
            if (error instanceof Error &&
                error.message === "ApiKeyService not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async delete(id) {
        try {
            await this.getOne(id);
            await repository_1.apiKeyServiceRepository.delete(id);
        }
        catch (error) {
            if (error instanceof Error &&
                error.message === "ApiKeyService not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
}
exports.default = ApiKeyServiceUseCases;
//# sourceMappingURL=apiKeyService.useCases.js.map