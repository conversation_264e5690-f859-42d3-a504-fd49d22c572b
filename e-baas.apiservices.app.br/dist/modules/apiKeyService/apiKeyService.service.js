"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class ApiKeyServiceService {
    constructor(contractUseCases) {
        this.contractUseCases = contractUseCases;
    }
    async getAll(req, res) {
        const response = await this.contractUseCases.getAll(req?.query);
        return res.json(response);
    }
    async getOne(req, res) {
        const response = await this.contractUseCases.getOne(req?.params?.id);
        return res.json(response);
    }
    async create(req, res) {
        const data = req.body;
        const response = await this.contractUseCases.create(data);
        return res.json(response);
    }
    async update(req, res) {
        const data = req.body;
        const userId = req.params.id;
        const response = await this.contractUseCases.update(userId, data);
        return res.json(response);
    }
    async delete(req, res) {
        const response = await this.contractUseCases.delete(req?.params?.id);
        return res.status(204).json(response);
    }
}
exports.default = ApiKeyServiceService;
//# sourceMappingURL=apiKeyService.service.js.map