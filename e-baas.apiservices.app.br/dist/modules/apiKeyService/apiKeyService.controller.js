"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const infra_1 = require("infra");
const api_key_service_service_1 = __importDefault(require("modules/api-key-service/api-key-service.service"));
const api_key_service_useCases_1 = __importDefault(require("modules/api-key-service/api-key-service.useCases"));
const api_key_service_dto_1 = require("modules/api-key-service/dto/api-key-service.dto");
const controller = (0, express_1.Router)();
const apiKeyServiceUseCases = new api_key_service_useCases_1.default();
const apiKeyServiceService = new api_key_service_service_1.default(apiKeyServiceUseCases);
controller.get("/", (req, res) => apiKeyServiceService.getAll(req, res));
controller.get("/:id", (req, res) => apiKeyServiceService.getOne(req, res));
controller.post("/", (0, infra_1.validator)(api_key_service_dto_1.ApiKeyServiceDto), (req, res) => apiKeyServiceService.create(req, res));
controller.put("/:id", (0, infra_1.validator)(api_key_service_dto_1.ApiKeyServiceDto), (req, res) => apiKeyServiceService.update(req, res));
controller.delete("/:id", (req, res) => apiKeyServiceService.delete(req, res));
exports.default = controller;
//# sourceMappingURL=apiKeyService.controller.js.map