"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const infra_1 = require("infra");
const rls_policies_service_1 = __importDefault(require("modules/rls-policies/rls-policies.service"));
const rls_policies_useCases_1 = __importDefault(require("modules/rls-policies/rls-policies.useCases"));
const rls_policies_dto_1 = require("modules/rls-policies/dto/rls-policies.dto");
const controller = (0, express_1.Router)();
const rlsPoliciesUseCases = new rls_policies_useCases_1.default();
const rlsPoliciesService = new rls_policies_service_1.default(rlsPoliciesUseCases);
controller.get("/", (req, res) => rlsPoliciesService.getAll(req, res));
controller.get("/:id", (req, res) => rlsPoliciesService.getOne(req, res));
controller.post("/", (0, infra_1.validator)(rls_policies_dto_1.RlsPoliciesDto), (req, res) => rlsPoliciesService.create(req, res));
controller.put("/:id", (0, infra_1.validator)(rls_policies_dto_1.RlsPoliciesDto), (req, res) => rlsPoliciesService.update(req, res));
controller.delete("/:id", (req, res) => rlsPoliciesService.delete(req, res));
exports.default = controller;
//# sourceMappingURL=rlsPolicies.controller.js.map