"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RlsPoliciesController = void 0;
const rlsPolicies_useCases_v2_1 = require("./rlsPolicies.useCases.v2");
const rls_helpers_service_1 = require("../rls-policies/rls-helpers.service");
class RlsPoliciesController {
    constructor() {
        this.rlsPoliciesUseCases = new rlsPolicies_useCases_v2_1.RlsPoliciesUseCases();
        this.rlsHelpers = new rls_helpers_service_1.RLSHelpers();
    }
    async createPolicy(req, res) {
        try {
            const { tableName, policyName, operation, condition } = req.body;
            if (!tableName || !policyName || !operation || !condition) {
                res.status(400).json({
                    error: 'tableName, policyName, operation and condition are required'
                });
                return;
            }
            const result = await this.rlsPoliciesUseCases.createPolicy(tableName, policyName, operation, condition);
            res.status(201).json({
                success: true,
                policy: result
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to create RLS policy',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async createDefaultPolicies(req, res) {
        try {
            const { tableName, options } = req.body;
            if (!tableName) {
                res.status(400).json({
                    error: 'tableName is required'
                });
                return;
            }
            await this.rlsHelpers.createDefaultPolicies(tableName, options);
            res.json({
                success: true,
                message: `Default RLS policies created for table ${tableName}`
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to create default RLS policies',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async listPolicies(req, res) {
        try {
            const { tableName } = req.params;
            const policies = await this.rlsHelpers.listPolicies(tableName);
            res.json({
                success: true,
                policies
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to list RLS policies',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async dropPolicies(req, res) {
        try {
            const { tableName } = req.params;
            if (!tableName) {
                res.status(400).json({
                    error: 'tableName is required'
                });
                return;
            }
            await this.rlsHelpers.dropAllPolicies(tableName);
            res.json({
                success: true,
                message: `All RLS policies dropped for table ${tableName}`
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to drop RLS policies',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async testPolicy(req, res) {
        try {
            const { tableName, operation, testData, userId, workspaceId } = req.body;
            if (!tableName || !operation) {
                res.status(400).json({
                    error: 'tableName and operation are required'
                });
                return;
            }
            const result = await this.rlsHelpers.testPolicy(tableName, operation, testData, userId, workspaceId);
            res.json({
                success: true,
                testResult: result
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to test RLS policy',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async initializeHelpers(req, res) {
        try {
            await this.rlsHelpers.createRLSHelperFunctions();
            res.json({
                success: true,
                message: 'RLS helper functions initialized successfully'
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to initialize RLS helpers',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async enableRLS(req, res) {
        try {
            const { tableName } = req.params;
            if (!tableName) {
                res.status(400).json({
                    error: 'tableName is required'
                });
                return;
            }
            await this.rlsPoliciesUseCases.enableRLS(tableName);
            res.json({
                success: true,
                message: `RLS enabled for table ${tableName}`
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to enable RLS',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async disableRLS(req, res) {
        try {
            const { tableName } = req.params;
            if (!tableName) {
                res.status(400).json({
                    error: 'tableName is required'
                });
                return;
            }
            await this.rlsPoliciesUseCases.disableRLS(tableName);
            res.json({
                success: true,
                message: `RLS disabled for table ${tableName}`
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to disable RLS',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    async getRLSStatus(req, res) {
        try {
            const { tableName } = req.params;
            const status = await this.rlsPoliciesUseCases.getRLSStatus(tableName);
            res.json({
                success: true,
                status
            });
        }
        catch (error) {
            res.status(500).json({
                error: 'Failed to get RLS status',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
}
exports.RlsPoliciesController = RlsPoliciesController;
//# sourceMappingURL=rlsPolicies.controller.v2.js.map