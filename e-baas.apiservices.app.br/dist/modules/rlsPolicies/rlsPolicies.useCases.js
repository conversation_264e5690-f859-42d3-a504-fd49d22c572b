"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const repository_1 = require("infra/repository");
const errorHandlers_1 = require("infra/errorHandlers");
class RlsPoliciesUseCases {
    constructor() { }
    async getAll() {
        try {
            return await repository_1.rlsPoliciesRepository.find();
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async getOne(id) {
        try {
            const rlsPolicies = await repository_1.rlsPoliciesRepository.findOneBy({ id });
            if (!rlsPolicies) {
                throw errorHandlers_1.ErrorHandler.NotFound("RlsPolicies not found");
            }
            return rlsPolicies;
        }
        catch (error) {
            if (error instanceof Error && error.message === "RlsPolicies not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async create(data) {
        try {
            return await repository_1.rlsPoliciesRepository.save(data);
        }
        catch (error) {
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async update(id, data) {
        try {
            const rlsPolicies = await this.getOne(id);
            await repository_1.rlsPoliciesRepository.update(id, data);
            return { ...rlsPolicies, ...data };
        }
        catch (error) {
            if (error instanceof Error && error.message === "RlsPolicies not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
    async delete(id) {
        try {
            await this.getOne(id);
            await repository_1.rlsPoliciesRepository.delete(id);
        }
        catch (error) {
            if (error instanceof Error && error.message === "RlsPolicies not found") {
                throw error;
            }
            throw errorHandlers_1.ErrorHandler.InternalServerError(error);
        }
    }
}
exports.default = RlsPoliciesUseCases;
//# sourceMappingURL=rlsPolicies.useCases.js.map