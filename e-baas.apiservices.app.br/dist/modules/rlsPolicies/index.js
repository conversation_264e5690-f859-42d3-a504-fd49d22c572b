"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const rlsPolicies_controller_v2_1 = require("./rlsPolicies.controller.v2");
const auth_middleware_1 = require("../../infra/middlewares/auth.middleware");
const rls_middleware_1 = require("../../infra/middlewares/rls.middleware");
const router = (0, express_1.Router)();
const rlsPoliciesController = new rlsPolicies_controller_v2_1.RlsPoliciesController();
// Aplicar autenticação e contexto RLS em todas as rotas
router.use(auth_middleware_1.authenticateJWT);
router.use(rls_middleware_1.setupRLSContext);
/**
 * @swagger
 * components:
 *   schemas:
 *     RLSPolicy:
 *       type: object
 *       required:
 *         - tableName
 *         - policyName
 *         - operation
 *         - condition
 *       properties:
 *         tableName:
 *           type: string
 *           description: Nome da tabela
 *           example: "users"
 *         policyName:
 *           type: string
 *           description: Nome da <PERSON>ol<PERSON>
 *           example: "users_own_data"
 *         operation:
 *           type: string
 *           enum: [SELECT, INSERT, UPDATE, DELETE]
 *           description: Operação da política
 *         condition:
 *           type: string
 *           description: Condição SQL da política
 *           example: "auth.uid() = user_id"
 *     DefaultPolicyOptions:
 *       type: object
 *       properties:
 *         hasUserId:
 *           type: boolean
 *           default: true
 *         hasWorkspaceId:
 *           type: boolean
 *           default: true
 *         userIdColumn:
 *           type: string
 *           default: "user_id"
 *         workspaceIdColumn:
 *           type: string
 *           default: "workspace_id"
 *         enableSelect:
 *           type: boolean
 *           default: true
 *         enableInsert:
 *           type: boolean
 *           default: true
 *         enableUpdate:
 *           type: boolean
 *           default: true
 *         enableDelete:
 *           type: boolean
 *           default: true
 */
/**
 * @swagger
 * /api/rls/initialize:
 *   post:
 *     summary: Inicializar funções helper RLS
 *     description: Cria todas as funções helper necessárias para RLS no PostgreSQL
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Funções helper criadas com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       403:
 *         description: Permissão insuficiente
 *       500:
 *         description: Erro interno
 */
router.post('/initialize', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.initializeHelpers.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/policies:
 *   post:
 *     summary: Criar política RLS personalizada
 *     description: Cria uma política RLS personalizada para uma tabela
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RLSPolicy'
 *     responses:
 *       201:
 *         description: Política criada com sucesso
 *       400:
 *         description: Dados inválidos
 *       403:
 *         description: Permissão insuficiente
 *       500:
 *         description: Erro interno
 */
router.post('/policies', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.createPolicy.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/policies/default:
 *   post:
 *     summary: Criar políticas RLS padrão
 *     description: Cria políticas RLS padrão para uma tabela
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tableName
 *             properties:
 *               tableName:
 *                 type: string
 *               options:
 *                 $ref: '#/components/schemas/DefaultPolicyOptions'
 *     responses:
 *       200:
 *         description: Políticas padrão criadas com sucesso
 *       400:
 *         description: Dados inválidos
 *       403:
 *         description: Permissão insuficiente
 *       500:
 *         description: Erro interno
 */
router.post('/policies/default', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.createDefaultPolicies.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/policies:
 *   get:
 *     summary: Listar todas as políticas RLS
 *     description: Lista todas as políticas RLS do banco de dados
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de políticas RLS
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 policies:
 *                   type: array
 *                   items:
 *                     type: object
 */
router.get('/policies', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.listPolicies.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/policies/{tableName}:
 *   get:
 *     summary: Listar políticas de uma tabela
 *     description: Lista todas as políticas RLS de uma tabela específica
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tableName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da tabela
 *     responses:
 *       200:
 *         description: Lista de políticas da tabela
 *       403:
 *         description: Permissão insuficiente
 *       500:
 *         description: Erro interno
 */
router.get('/policies/:tableName', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.listPolicies.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/policies/{tableName}:
 *   delete:
 *     summary: Remover todas as políticas de uma tabela
 *     description: Remove todas as políticas RLS de uma tabela específica
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tableName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da tabela
 *     responses:
 *       200:
 *         description: Políticas removidas com sucesso
 *       400:
 *         description: Nome da tabela é obrigatório
 *       403:
 *         description: Permissão insuficiente
 *       500:
 *         description: Erro interno
 */
router.delete('/policies/:tableName', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.dropPolicies.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/test:
 *   post:
 *     summary: Testar política RLS
 *     description: Testa se uma política RLS está funcionando corretamente
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tableName
 *               - operation
 *             properties:
 *               tableName:
 *                 type: string
 *               operation:
 *                 type: string
 *                 enum: [SELECT, INSERT, UPDATE, DELETE]
 *               testData:
 *                 type: object
 *               userId:
 *                 type: string
 *               workspaceId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Resultado do teste
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 testResult:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                     result:
 *                       type: any
 *                     error:
 *                       type: string
 */
router.post('/test', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.testPolicy.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/enable/{tableName}:
 *   post:
 *     summary: Ativar RLS em uma tabela
 *     description: Ativa Row-Level Security em uma tabela específica
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tableName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da tabela
 *     responses:
 *       200:
 *         description: RLS ativado com sucesso
 *       400:
 *         description: Nome da tabela é obrigatório
 *       403:
 *         description: Permissão insuficiente
 *       500:
 *         description: Erro interno
 */
router.post('/enable/:tableName', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.enableRLS.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/disable/{tableName}:
 *   post:
 *     summary: Desativar RLS em uma tabela
 *     description: Desativa Row-Level Security em uma tabela específica
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tableName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da tabela
 *     responses:
 *       200:
 *         description: RLS desativado com sucesso
 *       400:
 *         description: Nome da tabela é obrigatório
 *       403:
 *         description: Permissão insuficiente
 *       500:
 *         description: Erro interno
 */
router.post('/disable/:tableName', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.disableRLS.bind(rlsPoliciesController));
/**
 * @swagger
 * /api/rls/status/{tableName}:
 *   get:
 *     summary: Verificar status RLS de uma tabela
 *     description: Verifica se RLS está ativo em uma tabela e lista suas políticas
 *     tags: [RLS]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tableName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da tabela
 *     responses:
 *       200:
 *         description: Status RLS da tabela
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 status:
 *                   type: object
 *                   properties:
 *                     tableName:
 *                       type: string
 *                     rlsEnabled:
 *                       type: boolean
 *                     policies:
 *                       type: array
 *                       items:
 *                         type: object
 */
router.get('/status/:tableName', (0, auth_middleware_1.requirePermission)('manage:database'), rlsPoliciesController.getRLSStatus.bind(rlsPoliciesController));
exports.default = router;
//# sourceMappingURL=index.js.map