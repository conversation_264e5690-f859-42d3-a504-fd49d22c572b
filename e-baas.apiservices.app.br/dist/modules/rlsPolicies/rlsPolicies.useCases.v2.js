"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RlsPoliciesUseCases = void 0;
const data_source_1 = require("../../infra/database/data-source");
const rls_helpers_service_1 = require("../rls-policies/rls-helpers.service");
class RlsPoliciesUseCases {
    constructor() {
        this.connection = data_source_1.AppDataSource.manager.connection;
        this.rlsHelpers = new rls_helpers_service_1.RLSHelpers();
    }
    async createPolicy(tableName, policyName, operation, condition) {
        try {
            // Verificar se a política já existe
            const existingPolicy = await this.connection.query(`
        SELECT 1 FROM pg_policies 
        WHERE tablename = $1 AND policyname = $2
      `, [tableName, policyName]);
            if (existingPolicy.length > 0) {
                // Atualizar política existente
                await this.connection.query(`
          DROP POLICY IF EXISTS ${policyName} ON ${tableName}
        `);
            }
            // Criar nova política
            await this.connection.query(`
        CREATE POLICY ${policyName} ON ${tableName}
        FOR ${operation}
        USING (${condition})
      `);
            console.log(`✅ RLS policy '${policyName}' created for table '${tableName}'`);
            return {
                tableName,
                policyName,
                operation,
                condition
            };
        }
        catch (error) {
            console.error(`❌ Failed to create RLS policy '${policyName}':`, error);
            throw error;
        }
    }
    async enableRLS(tableName) {
        try {
            await this.connection.query(`ALTER TABLE ${tableName} ENABLE ROW LEVEL SECURITY`);
            console.log(`✅ RLS enabled for table '${tableName}'`);
        }
        catch (error) {
            console.error(`❌ Failed to enable RLS for table '${tableName}':`, error);
            throw error;
        }
    }
    async disableRLS(tableName) {
        try {
            await this.connection.query(`ALTER TABLE ${tableName} DISABLE ROW LEVEL SECURITY`);
            console.log(`✅ RLS disabled for table '${tableName}'`);
        }
        catch (error) {
            console.error(`❌ Failed to disable RLS for table '${tableName}':`, error);
            throw error;
        }
    }
    async getRLSStatus(tableName) {
        try {
            let query = `
        SELECT 
          t.tablename,
          t.rowsecurity as rls_enabled,
          COALESCE(
            json_agg(
              json_build_object(
                'policyname', p.policyname,
                'permissive', p.permissive,
                'roles', p.roles,
                'cmd', p.cmd,
                'qual', p.qual,
                'with_check', p.with_check
              )
            ) FILTER (WHERE p.policyname IS NOT NULL), 
            '[]'::json
          ) as policies
        FROM pg_tables t
        LEFT JOIN pg_policies p ON t.tablename = p.tablename
        WHERE t.schemaname = 'public'
      `;
            const params = [];
            if (tableName) {
                query += ' AND t.tablename = $1';
                params.push(tableName);
            }
            query += ' GROUP BY t.tablename, t.rowsecurity ORDER BY t.tablename';
            const result = await this.connection.query(query, params);
            if (tableName) {
                return result[0] || {
                    tablename: tableName,
                    rls_enabled: false,
                    policies: []
                };
            }
            return result;
        }
        catch (error) {
            console.error('❌ Failed to get RLS status:', error);
            throw error;
        }
    }
    async createWorkspaceRLSPolicies() {
        try {
            // Inicializar funções helper
            await this.rlsHelpers.createRLSHelperFunctions();
            // Criar políticas para tabela users
            await this.rlsHelpers.createDefaultPolicies('users', {
                hasUserId: true,
                hasWorkspaceId: true,
                userIdColumn: 'id',
                workspaceIdColumn: 'workspace_id'
            });
            // Criar políticas para outras tabelas importantes
            const tables = [
                { name: 'workspaces', userCol: 'owner_id', workspaceCol: 'id' },
                { name: 'api_keys', userCol: 'created_by', workspaceCol: 'workspace_id' },
                { name: 'refresh_tokens', userCol: 'user_id', workspaceCol: 'workspace_id' }
            ];
            for (const table of tables) {
                try {
                    await this.rlsHelpers.createDefaultPolicies(table.name, {
                        hasUserId: true,
                        hasWorkspaceId: true,
                        userIdColumn: table.userCol,
                        workspaceIdColumn: table.workspaceCol
                    });
                }
                catch (error) {
                    console.warn(`⚠️ Could not create RLS policies for table ${table.name}:`, error);
                    // Continue com outras tabelas
                }
            }
            console.log('✅ Workspace RLS policies created successfully');
        }
        catch (error) {
            console.error('❌ Failed to create workspace RLS policies:', error);
            throw error;
        }
    }
    async testWorkspaceAccess(tableName, userId, workspaceId) {
        try {
            // Configurar contexto de teste
            await this.connection.query(`SET LOCAL app.current_user_id = '${userId}'`);
            await this.connection.query(`SET LOCAL app.current_workspace_id = '${workspaceId}'`);
            await this.connection.query('SET LOCAL row_security = on');
            // Testar leitura
            let canRead = false;
            try {
                await this.connection.query(`SELECT 1 FROM ${tableName} LIMIT 1`);
                canRead = true;
            }
            catch (error) {
                // Erro esperado se não tem permissão
            }
            // Testar escrita (simulada)
            let canWrite = false;
            try {
                // Verificar se a função can_write retorna true
                const writeResult = await this.connection.query(`
          SELECT auth.can_write('${userId}'::uuid, '${workspaceId}') as can_write
        `);
                canWrite = writeResult[0]?.can_write || false;
            }
            catch (error) {
                // Erro na verificação
            }
            // Testar exclusão (simulada)
            let canDelete = false;
            try {
                const deleteResult = await this.connection.query(`
          SELECT auth.can_delete('${userId}'::uuid, '${workspaceId}') as can_delete
        `);
                canDelete = deleteResult[0]?.can_delete || false;
            }
            catch (error) {
                // Erro na verificação
            }
            return { canRead, canWrite, canDelete };
        }
        catch (error) {
            console.error('❌ Failed to test workspace access:', error);
            throw error;
        }
    }
    async createCustomPolicy(tableName, policyName, operation, expression, withCheck) {
        try {
            let query = `
        CREATE POLICY ${policyName} ON ${tableName}
        FOR ${operation}
        USING (${expression})
      `;
            if (operation === 'INSERT' || operation === 'UPDATE') {
                const checkExpression = withCheck || expression;
                query += ` WITH CHECK (${checkExpression})`;
            }
            await this.connection.query(query);
            console.log(`✅ Custom RLS policy '${policyName}' created for table '${tableName}'`);
        }
        catch (error) {
            console.error(`❌ Failed to create custom RLS policy '${policyName}':`, error);
            throw error;
        }
    }
    async dropPolicy(tableName, policyName) {
        try {
            await this.connection.query(`DROP POLICY IF EXISTS ${policyName} ON ${tableName}`);
            console.log(`✅ RLS policy '${policyName}' dropped from table '${tableName}'`);
        }
        catch (error) {
            console.error(`❌ Failed to drop RLS policy '${policyName}':`, error);
            throw error;
        }
    }
    async getPolicyDetails(tableName, policyName) {
        try {
            const result = await this.connection.query(`
        SELECT 
          policyname,
          permissive,
          roles,
          cmd,
          qual,
          with_check
        FROM pg_policies 
        WHERE tablename = $1 AND policyname = $2
      `, [tableName, policyName]);
            return result[0] || null;
        }
        catch (error) {
            console.error(`❌ Failed to get policy details for '${policyName}':`, error);
            throw error;
        }
    }
}
exports.RlsPoliciesUseCases = RlsPoliciesUseCases;
//# sourceMappingURL=rlsPolicies.useCases.v2.js.map