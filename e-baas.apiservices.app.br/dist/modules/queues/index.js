"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const queues_controller_1 = require("./queues.controller");
const router = (0, express_1.Router)();
const queuesController = new queues_controller_1.QueuesController();
/**
 * @swagger
 * components:
 *   schemas:
 *     PublishMessage:
 *       type: object
 *       required:
 *         - eventType
 *         - data
 *       properties:
 *         eventType:
 *           type: string
 *           description: Tipo do evento da fila
 *           example: "notification"
 *         data:
 *           type: object
 *           description: Dados da mensagem
 *           example: { "userId": "123", "message": "Nova notificação" }
 *         options:
 *           type: object
 *           properties:
 *             persistent:
 *               type: boolean
 *               default: true
 *             priority:
 *               type: number
 *               minimum: 1
 *               maximum: 10
 *               default: 1
 *             attempts:
 *               type: number
 *               minimum: 1
 *               maximum: 10
 *               default: 3
 *     QueueStatus:
 *       type: object
 *       properties:
 *         waiting:
 *           type: number
 *         processing:
 *           type: number
 *         completed:
 *           type: number
 *         failed:
 *           type: number
 *         total:
 *           type: number
 *     QueueMessage:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         data:
 *           type: object
 *         status:
 *           type: string
 *           enum: [pending, processing, completed, failed]
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         attempts:
 *           type: number
 *         maxAttempts:
 *           type: number
 *         priority:
 *           type: number
 */
/**
 * @swagger
 * /api/queues/publish:
 *   post:
 *     summary: Publicar mensagem na fila
 *     tags: [Queues]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PublishMessage'
 *     responses:
 *       201:
 *         description: Mensagem publicada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 messageId:
 *                   type: string
 *       400:
 *         description: Dados inválidos
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/publish', queuesController.publishMessage.bind(queuesController));
/**
 * @swagger
 * /api/queues/status:
 *   get:
 *     summary: Obter status de todas as filas
 *     tags: [Queues]
 *     responses:
 *       200:
 *         description: Status das filas
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   additionalProperties:
 *                     $ref: '#/components/schemas/QueueStatus'
 */
router.get('/status', queuesController.getQueueStatus.bind(queuesController));
/**
 * @swagger
 * /api/queues/{queueName}/status:
 *   get:
 *     summary: Obter status de uma fila específica
 *     tags: [Queues]
 *     parameters:
 *       - in: path
 *         name: queueName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da fila
 *     responses:
 *       200:
 *         description: Status da fila
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/QueueStatus'
 */
router.get('/:queueName/status', queuesController.getQueueStatus.bind(queuesController));
/**
 * @swagger
 * /api/queues/{queueName}/messages:
 *   get:
 *     summary: Obter mensagens de uma fila
 *     tags: [Queues]
 *     parameters:
 *       - in: path
 *         name: queueName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da fila
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [all, pending, processing, completed, failed]
 *           default: all
 *         description: Status das mensagens a buscar
 *     responses:
 *       200:
 *         description: Lista de mensagens
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/QueueMessage'
 */
router.get('/:queueName/messages', queuesController.getQueueMessages.bind(queuesController));
/**
 * @swagger
 * /api/queues/{queueName}/pending:
 *   get:
 *     summary: Obter mensagens pendentes de uma fila
 *     tags: [Queues]
 *     parameters:
 *       - in: path
 *         name: queueName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da fila
 *     responses:
 *       200:
 *         description: Lista de mensagens pendentes
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/QueueMessage'
 */
router.get('/:queueName/pending', queuesController.getPendingMessages.bind(queuesController));
/**
 * @swagger
 * /api/queues/{queueName}/completed:
 *   get:
 *     summary: Obter mensagens completadas de uma fila
 *     tags: [Queues]
 *     parameters:
 *       - in: path
 *         name: queueName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da fila
 *     responses:
 *       200:
 *         description: Lista de mensagens completadas
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/QueueMessage'
 */
router.get('/:queueName/completed', queuesController.getCompletedMessages.bind(queuesController));
/**
 * @swagger
 * /api/queues/{queueName}/monitor:
 *   get:
 *     summary: Monitorar fila em tempo real (Server-Sent Events)
 *     tags: [Queues]
 *     parameters:
 *       - in: path
 *         name: queueName
 *         required: true
 *         schema:
 *           type: string
 *         description: Nome da fila
 *     responses:
 *       200:
 *         description: Stream de eventos em tempo real
 *         content:
 *           text/event-stream:
 *             schema:
 *               type: string
 */
router.get('/:queueName/monitor', queuesController.monitorQueue.bind(queuesController));
exports.default = router;
//# sourceMappingURL=index.js.map