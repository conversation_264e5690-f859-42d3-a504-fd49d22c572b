"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueuesController = void 0;
const queues_useCases_1 = require("./queues.useCases");
class QueuesController {
    constructor() {
        this.queuesUseCases = new queues_useCases_1.QueuesUseCases();
    }
    async publishMessage(req, res) {
        try {
            const { eventType, data, options } = req.body;
            if (!eventType || !data) {
                res.status(400).json({
                    error: "eventType and data are required",
                });
                return;
            }
            const result = await this.queuesUseCases.publishMessage(eventType, data, options);
            res.status(201).json({
                success: true,
                messageId: result,
            });
        }
        catch (error) {
            res.status(500).json({
                error: "Failed to publish message",
                details: error instanceof Error ? error.message : "Unknown error",
            });
        }
    }
    async getQueueStatus(req, res) {
        try {
            const { queueName } = req.params;
            const status = await this.queuesUseCases.getQueueStatus(queueName);
            res.json({
                success: true,
                data: status,
            });
        }
        catch (error) {
            res.status(500).json({
                error: "Failed to get queue status",
                details: error instanceof Error ? error.message : "Unknown error",
            });
        }
    }
    async getQueueMessages(req, res) {
        try {
            const { queueName } = req.params;
            const { status = "all" } = req.query;
            const messages = await this.queuesUseCases.getQueueMessages(queueName, status);
            res.json({
                success: true,
                data: messages,
            });
        }
        catch (error) {
            res.status(500).json({
                error: "Failed to get queue messages",
                details: error instanceof Error ? error.message : "Unknown error",
            });
        }
    }
    async getPendingMessages(req, res) {
        try {
            const { queueName } = req.params;
            const messages = await this.queuesUseCases.getPendingMessages(queueName);
            res.json({
                success: true,
                data: messages,
            });
        }
        catch (error) {
            res.status(500).json({
                error: "Failed to get pending messages",
                details: error instanceof Error ? error.message : "Unknown error",
            });
        }
    }
    async getCompletedMessages(req, res) {
        try {
            const { queueName } = req.params;
            const messages = await this.queuesUseCases.getCompletedMessages(queueName);
            res.json({
                success: true,
                data: messages,
            });
        }
        catch (error) {
            res.status(500).json({
                error: "Failed to get completed messages",
                details: error instanceof Error ? error.message : "Unknown error",
            });
        }
    }
    async monitorQueue(req, res) {
        try {
            const { queueName } = req.params;
            res.setHeader("Content-Type", "text/event-stream");
            res.setHeader("Cache-Control", "no-cache");
            res.setHeader("Connection", "keep-alive");
            res.setHeader("Access-Control-Allow-Origin", "*");
            res.setHeader("Access-Control-Allow-Headers", "Cache-Control");
            const sendUpdate = async () => {
                try {
                    const status = await this.queuesUseCases.getQueueStatus(queueName);
                    res.write(`data: ${JSON.stringify(status)}\n\n`);
                }
                catch (error) {
                    console.error("Error getting queue status for SSE:", error);
                }
            };
            // Enviar status inicial
            await sendUpdate();
            // Atualizar a cada 5 segundos
            const interval = setInterval(sendUpdate, 5000);
            // Limpar quando a conexão fechar
            req.on("close", () => {
                clearInterval(interval);
            });
        }
        catch (error) {
            res.status(500).json({
                error: "Failed to start queue monitoring",
                details: error instanceof Error ? error.message : "Unknown error",
            });
        }
    }
}
exports.QueuesController = QueuesController;
//# sourceMappingURL=queues.controller.js.map