"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueuesService = void 0;
const sdk_queues_1 = require("sdk-queues");
const config_1 = __importDefault(require("../../infra/config"));
class QueuesService {
    constructor() {
        this.consumers = new Map();
        // Configurar conexão Redis a partir do config
        const redisConfig = {
            host: config_1.default.redis?.host || process.env.REDIS_HOST || "localhost",
            port: config_1.default.redis?.port || parseInt(process.env.REDIS_PORT || "6379"),
            username: config_1.default.redis?.username || process.env.REDIS_USERNAME || "publisher",
            password: config_1.default.redis?.password || process.env.REDIS_PASSWORD || "redis123",
        };
        this.queueManager = new sdk_queues_1.QueueManager(redisConfig);
        console.log("✅ Queue Manager initialized with Redis config:", {
            host: redisConfig.host,
            port: redisConfig.port,
        });
    }
    async publish(eventType, data, options) {
        try {
            const result = await this.queueManager.publish(eventType, data, options);
            console.log(`📤 Message published to queue '${eventType}':`, {
                messageId: result,
                options,
            });
            return result;
        }
        catch (error) {
            console.error(`❌ Failed to publish message to queue '${eventType}':`, error);
            throw error;
        }
    }
    async getQueueStatus(queueName) {
        try {
            const status = await this.queueManager.getQueueStatus();
            return (status[queueName] || {
                waiting: 0,
                processing: 0,
                completed: 0,
                failed: 0,
                total: 0,
            });
        }
        catch (error) {
            console.error(`❌ Failed to get status for queue '${queueName}':`, error);
            throw error;
        }
    }
    async getAllQueuesStatus() {
        try {
            return await this.queueManager.getQueueStatus();
        }
        catch (error) {
            console.error("❌ Failed to get status for all queues:", error);
            throw error;
        }
    }
    async getQueueMessages(queueName, status) {
        try {
            const messages = await this.queueManager.getQueueMessages(queueName, status);
            return messages.map((msg) => ({
                id: msg.id,
                data: msg.data,
                status: msg.status,
                createdAt: msg.createdAt,
                updatedAt: msg.updatedAt,
                attempts: msg.attempts,
                maxAttempts: msg.maxAttempts,
                priority: msg.priority,
            }));
        }
        catch (error) {
            console.error(`❌ Failed to get messages for queue '${queueName}':`, error);
            throw error;
        }
    }
    async getPendingMessages(queueName) {
        try {
            const messages = await this.queueManager.getPendingMessages(queueName);
            return messages.map((msg) => ({
                id: msg.id,
                data: msg.data,
                status: msg.status,
                createdAt: msg.createdAt,
                updatedAt: msg.updatedAt,
                attempts: msg.attempts,
                maxAttempts: msg.maxAttempts,
                priority: msg.priority,
            }));
        }
        catch (error) {
            console.error(`❌ Failed to get pending messages for queue '${queueName}':`, error);
            throw error;
        }
    }
    async getCompletedMessages(queueName) {
        try {
            const messages = await this.queueManager.getCompletedMessages(queueName);
            return messages.map((msg) => ({
                id: msg.id,
                data: msg.data,
                status: msg.status,
                createdAt: msg.createdAt,
                updatedAt: msg.updatedAt,
                attempts: msg.attempts,
                maxAttempts: msg.maxAttempts,
                priority: msg.priority,
            }));
        }
        catch (error) {
            console.error(`❌ Failed to get completed messages for queue '${queueName}':`, error);
            throw error;
        }
    }
    async startConsumer(queueName, handler) {
        try {
            if (this.consumers.get(queueName)) {
                console.log(`⚠️ Consumer for queue '${queueName}' is already running`);
                return;
            }
            await this.queueManager.consumer(queueName, handler);
            this.consumers.set(queueName, true);
            console.log(`✅ Consumer started for queue '${queueName}'`);
        }
        catch (error) {
            console.error(`❌ Failed to start consumer for queue '${queueName}':`, error);
            throw error;
        }
    }
    async stopConsumer(queueName) {
        try {
            // O SDK não expõe um método para parar consumidores específicos
            // Por enquanto, apenas marcar como parado
            this.consumers.delete(queueName);
            console.log(`🛑 Consumer stopped for queue '${queueName}'`);
        }
        catch (error) {
            console.error(`❌ Failed to stop consumer for queue '${queueName}':`, error);
            throw error;
        }
    }
    isConsumerRunning(queueName) {
        return this.consumers.get(queueName) || false;
    }
    getActiveConsumers() {
        return Array.from(this.consumers.keys());
    }
    async healthCheck() {
        try {
            // Verificar se o Redis está acessível tentando obter status das filas
            await this.getAllQueuesStatus();
            return {
                status: "healthy",
                redis: true,
                consumers: this.getActiveConsumers(),
            };
        }
        catch (error) {
            console.error("❌ Queue service health check failed:", error);
            return {
                status: "unhealthy",
                redis: false,
                consumers: this.getActiveConsumers(),
            };
        }
    }
}
exports.QueuesService = QueuesService;
//# sourceMappingURL=queues.service.js.map