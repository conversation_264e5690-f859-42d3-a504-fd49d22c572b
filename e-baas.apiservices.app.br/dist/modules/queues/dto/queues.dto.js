"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_EVENT_TYPES = exports.QueueMonitoringDto = exports.CreateQueueEventDto = exports.GetQueueMessagesDto = exports.PublishMessageDto = void 0;
const class_validator_1 = require("class-validator");
class PublishMessageDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PublishMessageDto.prototype, "eventType", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], PublishMessageDto.prototype, "data", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], PublishMessageDto.prototype, "options", void 0);
exports.PublishMessageDto = PublishMessageDto;
class GetQueueMessagesDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetQueueMessagesDto.prototype, "queueName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetQueueMessagesDto.prototype, "status", void 0);
exports.GetQueueMessagesDto = GetQueueMessagesDto;
class CreateQueueEventDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateQueueEventDto.prototype, "eventType", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateQueueEventDto.prototype, "data", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateQueueEventDto.prototype, "options", void 0);
exports.CreateQueueEventDto = CreateQueueEventDto;
class QueueMonitoringDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueueMonitoringDto.prototype, "queueName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueueMonitoringDto.prototype, "intervalMs", void 0);
exports.QueueMonitoringDto = QueueMonitoringDto;
exports.DEFAULT_EVENT_TYPES = [
    {
        name: "email",
        description: "Envio de emails",
        defaultPriority: 2,
        maxAttempts: 3,
    },
    {
        name: "notification",
        description: "Notificações do sistema",
        defaultPriority: 1,
        maxAttempts: 3,
    },
    {
        name: "payment",
        description: "Processamento de pagamentos",
        defaultPriority: 3,
        maxAttempts: 5,
    },
    {
        name: "order",
        description: "Processamento de pedidos",
        defaultPriority: 2,
        maxAttempts: 3,
    },
    {
        name: "analytics",
        description: "Eventos de analytics",
        defaultPriority: 1,
        maxAttempts: 1,
    },
    {
        name: "webhook",
        description: "Webhooks externos",
        defaultPriority: 2,
        maxAttempts: 3,
    },
];
//# sourceMappingURL=queues.dto.js.map