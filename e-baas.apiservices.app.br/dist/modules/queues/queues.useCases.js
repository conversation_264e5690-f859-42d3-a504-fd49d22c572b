"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueuesUseCases = void 0;
const queues_service_1 = require("./queues.service");
class QueuesUseCases {
    constructor() {
        this.queuesService = new queues_service_1.QueuesService();
    }
    async publishMessage(eventType, data, options) {
        try {
            // Validar eventType
            if (!eventType || typeof eventType !== 'string') {
                throw new Error('Invalid eventType provided');
            }
            // Validar data
            if (!data) {
                throw new Error('Message data is required');
            }
            // Aplicar opções padrão
            const publishOptions = {
                persistent: options?.persistent ?? true,
                priority: options?.priority ?? 1,
                attempts: options?.attempts ?? 3,
                ...options
            };
            // Publicar mensagem
            const messageId = await this.queuesService.publish(eventType, data, publishOptions);
            return messageId;
        }
        catch (error) {
            console.error(`Failed to publish message to queue ${eventType}:`, error);
            throw error;
        }
    }
    async getQueueStatus(queueName) {
        try {
            if (queueName) {
                return await this.queuesService.getQueueStatus(queueName);
            }
            else {
                return await this.queuesService.getAllQueuesStatus();
            }
        }
        catch (error) {
            console.error(`Failed to get queue status for ${queueName || 'all queues'}:`, error);
            throw error;
        }
    }
    async getQueueMessages(queueName, status = 'all') {
        try {
            return await this.queuesService.getQueueMessages(queueName, status);
        }
        catch (error) {
            console.error(`Failed to get messages for queue ${queueName}:`, error);
            throw error;
        }
    }
    async getPendingMessages(queueName) {
        try {
            return await this.queuesService.getPendingMessages(queueName);
        }
        catch (error) {
            console.error(`Failed to get pending messages for queue ${queueName}:`, error);
            throw error;
        }
    }
    async getCompletedMessages(queueName) {
        try {
            return await this.queuesService.getCompletedMessages(queueName);
        }
        catch (error) {
            console.error(`Failed to get completed messages for queue ${queueName}:`, error);
            throw error;
        }
    }
    async startConsumer(queueName, handler) {
        try {
            await this.queuesService.startConsumer(queueName, handler);
        }
        catch (error) {
            console.error(`Failed to start consumer for queue ${queueName}:`, error);
            throw error;
        }
    }
    async stopConsumer(queueName) {
        try {
            await this.queuesService.stopConsumer(queueName);
        }
        catch (error) {
            console.error(`Failed to stop consumer for queue ${queueName}:`, error);
            throw error;
        }
    }
    async setupDefaultConsumers() {
        try {
            // Configurar consumidores padrão para diferentes tipos de eventos
            // Consumidor de notificações
            await this.startConsumer('notification', async (message) => {
                console.log('📧 Processing notification:', message);
                // Implementar lógica de envio de notificação
            });
            // Consumidor de emails
            await this.startConsumer('email', async (message) => {
                console.log('✉️ Processing email:', message);
                // Implementar lógica de envio de email
            });
            // Consumidor de pagamentos
            await this.startConsumer('payment', async (message) => {
                console.log('💳 Processing payment:', message);
                // Implementar lógica de processamento de pagamento
            });
            // Consumidor de pedidos
            await this.startConsumer('order', async (message) => {
                console.log('📦 Processing order:', message);
                // Implementar lógica de processamento de pedido
            });
            console.log('✅ Default queue consumers started successfully');
        }
        catch (error) {
            console.error('❌ Failed to setup default consumers:', error);
            throw error;
        }
    }
}
exports.QueuesUseCases = QueuesUseCases;
//# sourceMappingURL=queues.useCases.js.map