{"version": 3, "file": "postgrest.useCases.js", "sourceRoot": "", "sources": ["../../../src/modules/postgrest/postgrest.useCases.ts"], "names": [], "mappings": ";;AACA,uDAA6D;AAE7D,6DAAyD;AAEzD,MAAqB,iBAAiB;IACpC,gBAAe,CAAC;IAEhB,KAAK,CAAC,MAAM;QACV,IAAI;YACF,OAAO,MAAM,gCAAmB,CAAC,IAAI,EAAE,CAAC;SACzC;QAAC,OAAO,KAAc,EAAE;YACvB,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,IAAI;YACF,MAAM,SAAS,GAAG,MAAM,gCAAmB,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,4BAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;aACpD;YACD,OAAO,SAAS,CAAC;SAClB;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACrE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAwB;QACnC,IAAI;YACF,OAAO,MAAM,gCAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAc,EAAE;YACvB,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,IAAwB;QAC/C,IAAI;YACF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACxC,MAAM,gCAAmB,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAC3C,OAAO,EAAE,GAAG,SAAS,EAAE,GAAG,IAAI,EAAE,CAAC;SAClC;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACrE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,IAAI;YACF,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACtB,MAAM,gCAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACtC;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,qBAAqB,EAAE;gBACrE,MAAM,KAAK,CAAC;aACb;YACD,MAAM,4BAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;CACF;AA1DD,oCA0DC"}