{"version": 3, "file": "postgrest.service.js", "sourceRoot": "", "sources": ["../../../src/modules/postgrest/postgrest.service.ts"], "names": [], "mappings": ";;;AACA,kEAA0E;AAC1E,uDAA6F;AAE7F,MAAa,gBAAgB;IACnB,KAAK,CAAC,aAAa,CAAC,WAAmB;QAC7C,2DAA2D;QAC3D,qDAAqD;QACrD,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,UAAmB;YACzB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;YACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC;YAC7C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;YAC/C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;YAC/C,QAAQ,EAAE,aAAa,WAAW,EAAE;YACpC,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,OAAO,MAAM,IAAA,oCAAsB,EAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAA2B;QAC5C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAClE,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAEnD,IAAI;YACF,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAE5B,QAAQ,QAAQ,CAAC,SAAS,EAAE;gBAC1B,KAAK,kCAAkB,CAAC,MAAM;oBAC5B,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACjE,KAAK,kCAAkB,CAAC,MAAM;oBAC5B,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACjE,KAAK,kCAAkB,CAAC,MAAM;oBAC5B,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACjE,KAAK,kCAAkB,CAAC,MAAM;oBAC5B,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACjE,KAAK,kCAAkB,CAAC,MAAM;oBAC5B,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACjE;oBACE,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;aAClE;SACF;gBAAS;YACR,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;SAC7B;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAY,EAAE,QAA2B;QACnE,IAAI,KAAK,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEvF,sBAAsB;QACtB,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC5F,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SAC9B;QAED,gBAAgB;QAChB,IAAI,QAAQ,CAAC,OAAO,EAAE;YACpB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC/D,MAAM,SAAS,GAAG,QAAQ,KAAK,EAAE,CAAC;gBAClC,IAAI,KAAK,KAAK,CAAC,EAAE;oBACf,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,IAAI,GAAG,OAAO,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;iBACzF;qBAAM;oBACL,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,KAAK,IAAI,GAAG,OAAO,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;iBAC5F;YACH,CAAC,CAAC,CAAC;SACJ;QAED,iBAAiB;QACjB,IAAI,QAAQ,CAAC,KAAK,EAAE;YAClB,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACrD,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAC,KAAK,IAAI,KAAK,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SAC3G;QAED,mBAAmB;QACnB,IAAI,QAAQ,CAAC,KAAK,EAAE;YAClB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACrC;QACD,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACvC;QAED,OAAO,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAY,EAAE,QAA2B;QACnE,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACvD,cAAc;YACd,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,EAAE;iBAC9C,MAAM,EAAE;iBACR,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;iBACpB,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;iBAC1B,OAAO,EAAE,CAAC;YAEb,IAAI,QAAQ,CAAC,MAAM,KAAK,uBAAuB,EAAE;gBAC/C,8BAA8B;gBAC9B,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC3D,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE;qBACtC,MAAM,EAAE;qBACR,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC;qBACpC,UAAU,CAAC,GAAG,CAAC;qBACf,UAAU,EAAE,CAAC;aACjB;YAED,OAAO,MAAM,CAAC;SACf;aAAM,IAAI,QAAQ,CAAC,IAAI,EAAE;YACxB,gBAAgB;YAChB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,EAAE;iBAC9C,MAAM,EAAE;iBACR,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;iBACpB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;iBACrB,OAAO,EAAE,CAAC;YAEb,IAAI,QAAQ,CAAC,MAAM,KAAK,uBAAuB,EAAE;gBAC/C,MAAM,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBACrC,IAAI,EAAE,EAAE;oBACN,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE;yBACtC,MAAM,EAAE;yBACR,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC;yBACpC,KAAK,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC;yBACzB,SAAS,EAAE,CAAC;iBAChB;aACF;YAED,OAAO,MAAM,CAAC;SACf;QAED,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAY,EAAE,QAA2B;QACnE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QAED,IAAI,KAAK,GAAG,OAAO,CAAC,kBAAkB,EAAE;aACrC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;aACtB,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEtB,iCAAiC;QACjC,IAAI,QAAQ,CAAC,OAAO,EAAE;YACpB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC/D,MAAM,SAAS,GAAG,QAAQ,KAAK,EAAE,CAAC;gBAClC,IAAI,KAAK,KAAK,CAAC,EAAE;oBACf,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;iBACvE;qBAAM;oBACL,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;iBAC1E;YACH,CAAC,CAAC,CAAC;SACJ;QAED,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAErC,IAAI,QAAQ,CAAC,MAAM,KAAK,uBAAuB,EAAE;YAC/C,yBAAyB;YACzB,IAAI,WAAW,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE7F,IAAI,QAAQ,CAAC,OAAO,EAAE;gBACpB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE;oBAC/D,MAAM,SAAS,GAAG,cAAc,KAAK,EAAE,CAAC;oBACxC,IAAI,KAAK,KAAK,CAAC,EAAE;wBACf,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;qBACnF;yBAAM;wBACL,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;qBACtF;gBACH,CAAC,CAAC,CAAC;aACJ;YAED,OAAO,MAAM,WAAW,CAAC,UAAU,EAAE,CAAC;SACvC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAY,EAAE,QAA2B;QACnE,IAAI,KAAK,GAAG,OAAO,CAAC,kBAAkB,EAAE;aACrC,MAAM,EAAE;aACR,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAExB,iCAAiC;QACjC,IAAI,QAAQ,CAAC,OAAO,EAAE;YACpB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC/D,MAAM,SAAS,GAAG,QAAQ,KAAK,EAAE,CAAC;gBAClC,IAAI,KAAK,KAAK,CAAC,EAAE;oBACf,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;iBACvE;qBAAM;oBACL,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;iBAC1E;YACH,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAY,EAAE,QAA2B;QACnE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QAED,sCAAsC;QACtC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,yBAAyB;QAEpF,MAAM,cAAc,GAAG,IAAI,CAAC,CAAC,qCAAqC;QAClE,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,eAAe,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElF,MAAM,KAAK,GAAG;oBACE,QAAQ,CAAC,KAAK,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;gBACzC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;qBACvC,cAAc,mBAAmB,SAAS;QACvD,QAAQ,CAAC,MAAM,KAAK,uBAAuB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;KACnE,CAAC;QAEF,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAuB;QACtC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAEnD,IAAI;YACF,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAE5B,oCAAoC;YACpC,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAEtE,MAAM,KAAK,GAAG,iBAAiB,MAAM,CAAC,YAAY,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAEzG,OAAO,MAAM,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;SAC5D;gBAAS;YACR,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;SAC7B;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,WAAmB,EAAE,MAAM,GAAG,QAAQ;QACpD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACzD,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAEnD,IAAI;YACF,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAE5B,+BAA+B;YAC/B,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;;;;;OAK9C,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;YAEb,6BAA6B;YAC7B,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,GAAG,CACzC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAU,EAAE,EAAE;gBAC9B,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;;;;;;;;;;WAU/C,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBAE/B,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;iBACR,CAAC;YACJ,CAAC,CAAC,CACH,CAAC;YAEF,OAAO,iBAAiB,CAAC;SAC1B;gBAAS;YACR,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;SAC7B;IACH,CAAC;CACF;AAnRD,4CAmRC"}