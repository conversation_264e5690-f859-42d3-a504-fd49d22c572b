"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const postgrest_service_1 = require("./postgrest.service");
const postgrest_dto_1 = require("./dto/postgrest.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const apiKeyAuth_middleware_1 = require("../../infra/middlewares/apiKeyAuth.middleware");
const postgrestRouter = (0, express_1.Router)();
const postgrestService = new postgrest_service_1.PostgrestService();
// PostgREST-style routes
// GET /table?select=*&filter=value - SELECT
postgrestRouter.get("/:table", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { table } = req.params;
        const { workspaceId, select, order, limit, offset, ...filters } = req.query;
        const queryDto = (0, class_transformer_1.plainToClass)(postgrest_dto_1.PostgrestQueryDto, {
            table,
            workspaceId,
            select,
            order,
            limit: limit ? parseInt(limit) : undefined,
            offset: offset ? parseInt(offset) : undefined,
            filters: Object.keys(filters).length > 0 ? filters : undefined,
            operation: postgrest_dto_1.PostgrestOperation.SELECT
        });
        const errors = await (0, class_validator_1.validate)(queryDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await postgrestService.executeQuery(queryDto);
        // Set count header if requested
        res.set('Content-Range', `0-${result.length - 1}/${result.length}`);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// POST /table - INSERT
postgrestRouter.post("/:table", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const { table } = req.params;
        const { workspaceId } = req.query;
        const prefer = req.headers.prefer;
        const data = req.body;
        const isArray = Array.isArray(data);
        const queryDto = (0, class_transformer_1.plainToClass)(postgrest_dto_1.PostgrestQueryDto, {
            table,
            workspaceId,
            data: isArray ? undefined : data,
            dataArray: isArray ? data : undefined,
            operation: postgrest_dto_1.PostgrestOperation.INSERT,
            prefer
        });
        const errors = await (0, class_validator_1.validate)(queryDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await postgrestService.executeQuery(queryDto);
        return res.status(201).json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// PATCH /table?filter=value - UPDATE
postgrestRouter.patch("/:table", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const { table } = req.params;
        const { workspaceId, ...filters } = req.query;
        const prefer = req.headers.prefer;
        const data = req.body;
        const queryDto = (0, class_transformer_1.plainToClass)(postgrest_dto_1.PostgrestQueryDto, {
            table,
            workspaceId,
            data,
            filters: Object.keys(filters).length > 0 ? filters : undefined,
            operation: postgrest_dto_1.PostgrestOperation.UPDATE,
            prefer
        });
        const errors = await (0, class_validator_1.validate)(queryDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await postgrestService.executeQuery(queryDto);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// DELETE /table?filter=value - DELETE
postgrestRouter.delete("/:table", apiKeyAuth_middleware_1.requireDeleteAccess, async (req, res) => {
    try {
        const { table } = req.params;
        const { workspaceId, ...filters } = req.query;
        const queryDto = (0, class_transformer_1.plainToClass)(postgrest_dto_1.PostgrestQueryDto, {
            table,
            workspaceId,
            filters: Object.keys(filters).length > 0 ? filters : undefined,
            operation: postgrest_dto_1.PostgrestOperation.DELETE
        });
        const errors = await (0, class_validator_1.validate)(queryDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await postgrestService.executeQuery(queryDto);
        return res.status(204).json();
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// POST /rpc/function_name - RPC calls
postgrestRouter.post("/rpc/:functionName", apiKeyAuth_middleware_1.requireWriteAccess, async (req, res) => {
    try {
        const { functionName } = req.params;
        const { workspaceId } = req.query;
        const params = req.body;
        const rpcDto = (0, class_transformer_1.plainToClass)(postgrest_dto_1.PostgrestRpcDto, {
            functionName,
            workspaceId,
            params
        });
        const errors = await (0, class_validator_1.validate)(rpcDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await postgrestService.executeRpc(rpcDto);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
// GET /schema - Get schema information
postgrestRouter.get("/", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId, schema } = req.query;
        const schemaDto = (0, class_transformer_1.plainToClass)(postgrest_dto_1.PostgrestSchemaDto, {
            workspaceId,
            schema
        });
        const errors = await (0, class_validator_1.validate)(schemaDto);
        if (errors.length > 0) {
            return res.status(400).json({ error: "Validation failed", details: errors });
        }
        const result = await postgrestService.getSchema(workspaceId, schema);
        return res.json(result);
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
});
exports.default = postgrestRouter;
//# sourceMappingURL=postgrest.controller.js.map