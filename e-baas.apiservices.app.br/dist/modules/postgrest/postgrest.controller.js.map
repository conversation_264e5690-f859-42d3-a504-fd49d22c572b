{"version": 3, "file": "postgrest.controller.js", "sourceRoot": "", "sources": ["../../../src/modules/postgrest/postgrest.controller.ts"], "names": [], "mappings": ";;AAAA,qCAAoD;AACpD,2DAAuD;AACvD,uDAAiH;AACjH,qDAA2C;AAC3C,yDAAiD;AACjD,yFAAyI;AAEzI,MAAM,eAAe,GAAG,IAAA,gBAAM,GAAE,CAAC;AACjC,MAAM,gBAAgB,GAAG,IAAI,oCAAgB,EAAE,CAAC;AAEhD,yBAAyB;AACzB,4CAA4C;AAC5C,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,yCAAiB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACtF,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC7B,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,GAAG,CAAC,KAAY,CAAC;QAEnF,MAAM,QAAQ,GAAG,IAAA,gCAAY,EAAC,iCAAiB,EAAE;YAC/C,KAAK;YACL,WAAW;YACX,MAAM;YACN,KAAK;YACL,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YAC1C,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7C,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YAC9D,SAAS,EAAE,kCAAkB,CAAC,MAAM;SACrC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;SAC9E;QAED,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE7D,gCAAgC;QAChC,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAEpE,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzB;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACvD;AACH,CAAC,CAAC,CAAC;AAEH,uBAAuB;AACvB,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,0CAAkB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACxF,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC7B,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,KAAY,CAAC;QACzC,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAgB,CAAC;QAE5C,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACtB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpC,MAAM,QAAQ,GAAG,IAAA,gCAAY,EAAC,iCAAiB,EAAE;YAC/C,KAAK;YACL,WAAW;YACX,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAChC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACrC,SAAS,EAAE,kCAAkB,CAAC,MAAM;YACpC,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;SAC9E;QAED,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE7D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACrC;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACvD;AACH,CAAC,CAAC,CAAC;AAEH,qCAAqC;AACrC,eAAe,CAAC,KAAK,CAAC,SAAS,EAAE,0CAAkB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACzF,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC7B,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,EAAE,GAAG,GAAG,CAAC,KAAY,CAAC;QACrD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAgB,CAAC;QAC5C,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QAEtB,MAAM,QAAQ,GAAG,IAAA,gCAAY,EAAC,iCAAiB,EAAE;YAC/C,KAAK;YACL,WAAW;YACX,IAAI;YACJ,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YAC9D,SAAS,EAAE,kCAAkB,CAAC,MAAM;YACpC,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;SAC9E;QAED,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE7D,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzB;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACvD;AACH,CAAC,CAAC,CAAC;AAEH,sCAAsC;AACtC,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,2CAAmB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC3F,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC7B,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,EAAE,GAAG,GAAG,CAAC,KAAY,CAAC;QAErD,MAAM,QAAQ,GAAG,IAAA,gCAAY,EAAC,iCAAiB,EAAE;YAC/C,KAAK;YACL,WAAW;YACX,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YAC9D,SAAS,EAAE,kCAAkB,CAAC,MAAM;SACrC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;SAC9E;QAED,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE7D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;KAC/B;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACvD;AACH,CAAC,CAAC,CAAC;AAEH,sCAAsC;AACtC,eAAe,CAAC,IAAI,CAAC,oBAAoB,EAAE,0CAAkB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACnG,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QACpC,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,KAAY,CAAC;QACzC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC;QAExB,MAAM,MAAM,GAAG,IAAA,gCAAY,EAAC,+BAAe,EAAE;YAC3C,YAAY;YACZ,WAAW;YACX,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,MAAM,CAAC,CAAC;QACtC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;SAC9E;QAED,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAEzD,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzB;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACvD;AACH,CAAC,CAAC,CAAC;AAEH,uCAAuC;AACvC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,yCAAiB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAChF,IAAI;QACF,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,KAAY,CAAC;QAEjD,MAAM,SAAS,GAAG,IAAA,gCAAY,EAAC,kCAAkB,EAAE;YACjD,WAAW;YACX,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,SAAS,CAAC,CAAC;QACzC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;SAC9E;QAED,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAErE,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzB;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACvD;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,eAAe,CAAC"}