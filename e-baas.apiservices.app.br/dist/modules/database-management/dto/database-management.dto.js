"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionDefinitionDto = exports.CreateViewDto = exports.TableInfoDto = exports.DropTableDto = exports.AlterTableDto = exports.CreateTableDto = exports.IndexDefinitionDto = exports.ColumnDefinitionDto = exports.IndexType = exports.ColumnType = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var ColumnType;
(function (ColumnType) {
    ColumnType["VARCHAR"] = "varchar";
    ColumnType["TEXT"] = "text";
    ColumnType["INTEGER"] = "integer";
    ColumnType["BIGINT"] = "bigint";
    ColumnType["DECIMAL"] = "decimal";
    ColumnType["BOOLEAN"] = "boolean";
    ColumnType["DATE"] = "date";
    ColumnType["DATETIME"] = "datetime";
    ColumnType["TIMESTAMP"] = "timestamp";
    ColumnType["JSON"] = "json";
    ColumnType["UUID"] = "uuid";
})(ColumnType = exports.ColumnType || (exports.ColumnType = {}));
var IndexType;
(function (IndexType) {
    IndexType["BTREE"] = "btree";
    IndexType["HASH"] = "hash";
    IndexType["GIN"] = "gin";
    IndexType["GIST"] = "gist";
})(IndexType = exports.IndexType || (exports.IndexType = {}));
class ColumnDefinitionDto {
    constructor() {
        this.nullable = true;
        this.unique = false;
        this.primary = false;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ColumnDefinitionDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(ColumnType),
    __metadata("design:type", String)
], ColumnDefinitionDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ColumnDefinitionDto.prototype, "length", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ColumnDefinitionDto.prototype, "precision", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ColumnDefinitionDto.prototype, "scale", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ColumnDefinitionDto.prototype, "nullable", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ColumnDefinitionDto.prototype, "unique", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ColumnDefinitionDto.prototype, "primary", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ColumnDefinitionDto.prototype, "defaultValue", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ColumnDefinitionDto.prototype, "comment", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ColumnDefinitionDto.prototype, "foreignKeyTable", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ColumnDefinitionDto.prototype, "foreignKeyColumn", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ColumnDefinitionDto.prototype, "onDelete", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ColumnDefinitionDto.prototype, "onUpdate", void 0);
exports.ColumnDefinitionDto = ColumnDefinitionDto;
class IndexDefinitionDto {
    constructor() {
        this.unique = false;
        this.type = IndexType.BTREE;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndexDefinitionDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], IndexDefinitionDto.prototype, "columns", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], IndexDefinitionDto.prototype, "unique", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(IndexType),
    __metadata("design:type", String)
], IndexDefinitionDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IndexDefinitionDto.prototype, "where", void 0);
exports.IndexDefinitionDto = IndexDefinitionDto;
class CreateTableDto {
    constructor() {
        this.enableRLS = false;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTableDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTableDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ColumnDefinitionDto),
    __metadata("design:type", Array)
], CreateTableDto.prototype, "columns", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => IndexDefinitionDto),
    __metadata("design:type", Array)
], CreateTableDto.prototype, "indexes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTableDto.prototype, "comment", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateTableDto.prototype, "enableRLS", void 0);
exports.CreateTableDto = CreateTableDto;
class AlterTableDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AlterTableDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AlterTableDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ColumnDefinitionDto),
    __metadata("design:type", Array)
], AlterTableDto.prototype, "addColumns", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AlterTableDto.prototype, "dropColumns", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ColumnDefinitionDto),
    __metadata("design:type", Array)
], AlterTableDto.prototype, "modifyColumns", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => IndexDefinitionDto),
    __metadata("design:type", Array)
], AlterTableDto.prototype, "addIndexes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AlterTableDto.prototype, "dropIndexes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AlterTableDto.prototype, "newTableName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AlterTableDto.prototype, "comment", void 0);
exports.AlterTableDto = AlterTableDto;
class DropTableDto {
    constructor() {
        this.cascade = false;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DropTableDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DropTableDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DropTableDto.prototype, "cascade", void 0);
exports.DropTableDto = DropTableDto;
class TableInfoDto {
    constructor() {
        this.includeColumns = true;
        this.includeIndexes = true;
        this.includeConstraints = true;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TableInfoDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TableInfoDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TableInfoDto.prototype, "includeColumns", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TableInfoDto.prototype, "includeIndexes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TableInfoDto.prototype, "includeConstraints", void 0);
exports.TableInfoDto = TableInfoDto;
class CreateViewDto {
    constructor() {
        this.materialized = false;
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateViewDto.prototype, "viewName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateViewDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateViewDto.prototype, "query", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateViewDto.prototype, "comment", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateViewDto.prototype, "materialized", void 0);
exports.CreateViewDto = CreateViewDto;
class FunctionDefinitionDto {
    constructor() {
        this.returnType = 'void';
        this.language = 'plpgsql';
    }
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionDefinitionDto.prototype, "functionName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionDefinitionDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionDefinitionDto.prototype, "body", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], FunctionDefinitionDto.prototype, "parameters", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionDefinitionDto.prototype, "returnType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionDefinitionDto.prototype, "language", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FunctionDefinitionDto.prototype, "comment", void 0);
exports.FunctionDefinitionDto = FunctionDefinitionDto;
//# sourceMappingURL=database-management.dto.js.map