"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManagementService = void 0;
const data_source_1 = require("../../infra/database/data-source");
const database_management_dto_1 = require("./dto/database-management.dto");
class DatabaseManagementService {
    constructor() {
        this.queryRunner = data_source_1.AppDataSource.createQueryRunner();
    }
    async createTable(createTableDto) {
        const { tableName, workspaceId, columns, indexes, comment, enableRLS } = createTableDto;
        // Validate table name (workspace isolation)
        const fullTableName = this.getWorkspaceTableName(tableName, workspaceId);
        try {
            await this.queryRunner.connect();
            await this.queryRunner.startTransaction();
            // Build CREATE TABLE query
            let createQuery = `CREATE TABLE "${fullTableName}" (\n`;
            const columnDefinitions = [];
            const primaryKeys = [];
            // Process columns
            for (const column of columns) {
                const columnDef = this.buildColumnDefinition(column);
                columnDefinitions.push(columnDef);
                if (column.primary) {
                    primaryKeys.push(column.name);
                }
            }
            // Add primary key constraint if exists
            if (primaryKeys.length > 0) {
                columnDefinitions.push(`PRIMARY KEY (${primaryKeys.map(k => `"${k}"`).join(', ')})`);
            }
            createQuery += columnDefinitions.join(',\n') + '\n)';
            // Add table comment
            if (comment) {
                createQuery += `;\nCOMMENT ON TABLE "${fullTableName}" IS '${comment.replace(/'/g, "''")}'`;
            }
            await this.queryRunner.query(createQuery);
            // Create indexes
            if (indexes && indexes.length > 0) {
                for (const index of indexes) {
                    await this.createIndex(fullTableName, index);
                }
            }
            // Enable RLS if requested
            if (enableRLS) {
                await this.queryRunner.query(`ALTER TABLE "${fullTableName}" ENABLE ROW LEVEL SECURITY`);
            }
            await this.queryRunner.commitTransaction();
            return {
                message: `Table ${fullTableName} created successfully`,
                tableName: fullTableName
            };
        }
        catch (error) {
            await this.queryRunner.rollbackTransaction();
            throw new Error(`Failed to create table: ${error.message}`);
        }
        finally {
            await this.queryRunner.release();
        }
    }
    async alterTable(alterTableDto) {
        const { tableName, workspaceId, addColumns, dropColumns, modifyColumns, addIndexes, dropIndexes, newTableName, comment } = alterTableDto;
        const fullTableName = this.getWorkspaceTableName(tableName, workspaceId);
        try {
            await this.queryRunner.connect();
            await this.queryRunner.startTransaction();
            // Add columns
            if (addColumns && addColumns.length > 0) {
                for (const column of addColumns) {
                    const columnDef = this.buildColumnDefinition(column);
                    await this.queryRunner.query(`ALTER TABLE "${fullTableName}" ADD COLUMN ${columnDef}`);
                }
            }
            // Drop columns
            if (dropColumns && dropColumns.length > 0) {
                for (const columnName of dropColumns) {
                    await this.queryRunner.query(`ALTER TABLE "${fullTableName}" DROP COLUMN "${columnName}"`);
                }
            }
            // Modify columns
            if (modifyColumns && modifyColumns.length > 0) {
                for (const column of modifyColumns) {
                    await this.modifyColumn(fullTableName, column);
                }
            }
            // Add indexes
            if (addIndexes && addIndexes.length > 0) {
                for (const index of addIndexes) {
                    await this.createIndex(fullTableName, index);
                }
            }
            // Drop indexes
            if (dropIndexes && dropIndexes.length > 0) {
                for (const indexName of dropIndexes) {
                    await this.queryRunner.query(`DROP INDEX IF EXISTS "${indexName}"`);
                }
            }
            // Rename table
            if (newTableName) {
                const newFullTableName = this.getWorkspaceTableName(newTableName, workspaceId);
                await this.queryRunner.query(`ALTER TABLE "${fullTableName}" RENAME TO "${newFullTableName}"`);
            }
            // Update comment
            if (comment !== undefined) {
                const targetName = newTableName ? this.getWorkspaceTableName(newTableName, workspaceId) : fullTableName;
                if (comment) {
                    await this.queryRunner.query(`COMMENT ON TABLE "${targetName}" IS '${comment.replace(/'/g, "''")}'`);
                }
                else {
                    await this.queryRunner.query(`COMMENT ON TABLE "${targetName}" IS NULL`);
                }
            }
            await this.queryRunner.commitTransaction();
            return { message: `Table ${fullTableName} altered successfully` };
        }
        catch (error) {
            await this.queryRunner.rollbackTransaction();
            throw new Error(`Failed to alter table: ${error.message}`);
        }
        finally {
            await this.queryRunner.release();
        }
    }
    async dropTable(dropTableDto) {
        const { tableName, workspaceId, cascade } = dropTableDto;
        const fullTableName = this.getWorkspaceTableName(tableName, workspaceId);
        try {
            const cascadeClause = cascade ? 'CASCADE' : 'RESTRICT';
            await this.queryRunner.query(`DROP TABLE IF EXISTS "${fullTableName}" ${cascadeClause}`);
            return { message: `Table ${fullTableName} dropped successfully` };
        }
        catch (error) {
            throw new Error(`Failed to drop table: ${error.message}`);
        }
    }
    async getTableInfo(tableInfoDto) {
        const { workspaceId, tableName, includeColumns, includeIndexes, includeConstraints } = tableInfoDto;
        try {
            let tablesQuery = `
        SELECT table_name, table_comment
        FROM information_schema.tables 
        WHERE table_schema = 'main' 
        AND table_name LIKE '${workspaceId}_%'
      `;
            if (tableName) {
                const fullTableName = this.getWorkspaceTableName(tableName, workspaceId);
                tablesQuery += ` AND table_name = '${fullTableName}'`;
            }
            const tables = await this.queryRunner.query(tablesQuery);
            const result = [];
            for (const table of tables) {
                const schema = {
                    tableName: this.extractTableName(table.table_name, workspaceId),
                    columns: [],
                    indexes: [],
                    constraints: [],
                    comment: table.table_comment
                };
                if (includeColumns) {
                    schema.columns = await this.getTableColumns(table.table_name);
                }
                if (includeIndexes) {
                    schema.indexes = await this.getTableIndexes(table.table_name);
                }
                if (includeConstraints) {
                    schema.constraints = await this.getTableConstraints(table.table_name);
                }
                result.push(schema);
            }
            return result;
        }
        catch (error) {
            throw new Error(`Failed to get table info: ${error.message}`);
        }
    }
    async getDatabaseSchema(workspaceId) {
        try {
            const tables = await this.getTableInfo({
                workspaceId,
                includeColumns: true,
                includeIndexes: true,
                includeConstraints: true
            });
            const views = await this.getWorkspaceViews(workspaceId);
            const functions = await this.getWorkspaceFunctions(workspaceId);
            return {
                tables,
                views,
                functions
            };
        }
        catch (error) {
            throw new Error(`Failed to get database schema: ${error.message}`);
        }
    }
    async createView(createViewDto) {
        const { viewName, workspaceId, query, comment, materialized } = createViewDto;
        const fullViewName = this.getWorkspaceTableName(viewName, workspaceId);
        try {
            const viewType = materialized ? 'MATERIALIZED VIEW' : 'VIEW';
            const createQuery = `CREATE ${viewType} "${fullViewName}" AS ${query}`;
            await this.queryRunner.query(createQuery);
            if (comment) {
                await this.queryRunner.query(`COMMENT ON ${viewType} "${fullViewName}" IS '${comment.replace(/'/g, "''")}'`);
            }
            return {
                message: `${viewType} ${fullViewName} created successfully`,
                viewName: fullViewName
            };
        }
        catch (error) {
            throw new Error(`Failed to create view: ${error.message}`);
        }
    }
    async createFunction(functionDto) {
        const { functionName, workspaceId, body, parameters, returnType, language, comment } = functionDto;
        const fullFunctionName = this.getWorkspaceTableName(functionName, workspaceId);
        try {
            const paramsList = parameters ? parameters.join(', ') : '';
            const createQuery = `
        CREATE OR REPLACE FUNCTION "${fullFunctionName}"(${paramsList})
        RETURNS ${returnType}
        LANGUAGE ${language}
        AS $$
        ${body}
        $$
      `;
            await this.queryRunner.query(createQuery);
            if (comment) {
                await this.queryRunner.query(`COMMENT ON FUNCTION "${fullFunctionName}" IS '${comment.replace(/'/g, "''")}'`);
            }
            return {
                message: `Function ${fullFunctionName} created successfully`,
                functionName: fullFunctionName
            };
        }
        catch (error) {
            throw new Error(`Failed to create function: ${error.message}`);
        }
    }
    buildColumnDefinition(column) {
        let definition = `"${column.name}" ${this.mapColumnType(column)}`;
        if (!column.nullable) {
            definition += ' NOT NULL';
        }
        if (column.unique) {
            definition += ' UNIQUE';
        }
        if (column.defaultValue !== undefined) {
            definition += ` DEFAULT ${column.defaultValue}`;
        }
        return definition;
    }
    mapColumnType(column) {
        switch (column.type) {
            case database_management_dto_1.ColumnType.VARCHAR:
                return `VARCHAR(${column.length || 255})`;
            case database_management_dto_1.ColumnType.DECIMAL:
                return `DECIMAL(${column.precision || 10}, ${column.scale || 2})`;
            case database_management_dto_1.ColumnType.TEXT:
                return 'TEXT';
            case database_management_dto_1.ColumnType.INTEGER:
                return 'INTEGER';
            case database_management_dto_1.ColumnType.BIGINT:
                return 'BIGINT';
            case database_management_dto_1.ColumnType.BOOLEAN:
                return 'BOOLEAN';
            case database_management_dto_1.ColumnType.DATE:
                return 'DATE';
            case database_management_dto_1.ColumnType.DATETIME:
                return 'DATETIME';
            case database_management_dto_1.ColumnType.TIMESTAMP:
                return 'TIMESTAMP';
            case database_management_dto_1.ColumnType.JSON:
                return 'JSON';
            case database_management_dto_1.ColumnType.UUID:
                return 'VARCHAR(36)'; // SQLite doesn't have native UUID
            default:
                return 'TEXT';
        }
    }
    async createIndex(tableName, index) {
        const uniqueClause = index.unique ? 'UNIQUE ' : '';
        const columnsClause = index.columns.map(col => `"${col}"`).join(', ');
        const whereClause = index.where ? ` WHERE ${index.where}` : '';
        const createIndexQuery = `CREATE ${uniqueClause}INDEX "${index.name}" ON "${tableName}" (${columnsClause})${whereClause}`;
        await this.queryRunner.query(createIndexQuery);
    }
    async modifyColumn(tableName, column) {
        // SQLite doesn't support ALTER COLUMN directly, so we need to recreate the table
        // For now, we'll throw an error suggesting to use a more complex migration
        throw new Error('Column modification requires table recreation. Use a custom migration for complex changes.');
    }
    getWorkspaceTableName(tableName, workspaceId) {
        return `${workspaceId}_${tableName}`;
    }
    extractTableName(fullTableName, workspaceId) {
        const prefix = `${workspaceId}_`;
        return fullTableName.startsWith(prefix) ? fullTableName.substring(prefix.length) : fullTableName;
    }
    async getTableColumns(tableName) {
        const query = `PRAGMA table_info("${tableName}")`;
        const columns = await this.queryRunner.query(query);
        return columns.map((col) => ({
            name: col.name,
            type: col.type,
            nullable: !col.notnull,
            default: col.dflt_value,
            primary: !!col.pk,
            unique: false,
            comment: null
        }));
    }
    async getTableIndexes(tableName) {
        const query = `PRAGMA index_list("${tableName}")`;
        const indexes = await this.queryRunner.query(query);
        const result = [];
        for (const index of indexes) {
            const indexInfoQuery = `PRAGMA index_info("${index.name}")`;
            const indexInfo = await this.queryRunner.query(indexInfoQuery);
            result.push({
                name: index.name,
                columns: indexInfo.map((info) => info.name),
                unique: !!index.unique,
                primary: false
            });
        }
        return result;
    }
    async getTableConstraints(tableName) {
        const query = `PRAGMA foreign_key_list("${tableName}")`;
        const foreignKeys = await this.queryRunner.query(query);
        return foreignKeys.map((fk) => ({
            name: `fk_${tableName}_${fk.from}_${fk.table}_${fk.to}`,
            type: 'foreign_key',
            columns: [fk.from],
            referencedTable: fk.table,
            referencedColumns: [fk.to]
        }));
    }
    async getWorkspaceViews(workspaceId) {
        // SQLite doesn't have a standard information_schema for views
        // Return empty array for now
        return [];
    }
    async getWorkspaceFunctions(workspaceId) {
        // SQLite doesn't support stored procedures/functions
        // Return empty array for now
        return [];
    }
}
exports.DatabaseManagementService = DatabaseManagementService;
//# sourceMappingURL=database-management.service.js.map