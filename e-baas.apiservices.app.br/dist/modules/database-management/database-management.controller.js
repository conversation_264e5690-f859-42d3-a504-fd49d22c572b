"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const database_management_service_1 = require("./database-management.service");
const database_management_dto_1 = require("./dto/database-management.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const apiKeyAuth_middleware_1 = require("../../infra/middlewares/apiKeyAuth.middleware");
const databaseManagementRouter = (0, express_1.Router)();
const databaseManagementService = new database_management_service_1.DatabaseManagementService();
// Helper function for validation
const validateDto = async (DtoClass, data) => {
    const dto = (0, class_transformer_1.plainToClass)(DtoClass, data);
    const errors = await (0, class_validator_1.validate)(dto);
    if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
    }
    return dto;
};
// Create table
databaseManagementRouter.post("/tables", apiKeyAuth_middleware_1.requireSchemaAccess, async (req, res) => {
    try {
        const createTableDto = await validateDto(database_management_dto_1.CreateTableDto, req.body);
        const result = await databaseManagementService.createTable(createTableDto);
        return res.status(201).json(result);
    }
    catch (error) {
        if (error.message.includes('already exists')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Get table information
databaseManagementRouter.get("/tables", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const tableInfoDto = await validateDto(database_management_dto_1.TableInfoDto, req.query);
        const tables = await databaseManagementService.getTableInfo(tableInfoDto);
        return res.status(200).json(tables);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get specific table schema
databaseManagementRouter.get("/tables/:tableName", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { tableName } = req.params;
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const tableInfoDto = {
            workspaceId,
            tableName,
            includeColumns: true,
            includeIndexes: true,
            includeConstraints: true
        };
        const tables = await databaseManagementService.getTableInfo(tableInfoDto);
        if (tables.length === 0) {
            return res.status(404).json({ error: 'Table not found' });
        }
        return res.status(200).json(tables[0]);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Alter table
databaseManagementRouter.put("/tables/:tableName", apiKeyAuth_middleware_1.requireSchemaAccess, async (req, res) => {
    try {
        const { tableName } = req.params;
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const alterTableDto = await validateDto(database_management_dto_1.AlterTableDto, {
            ...req.body,
            tableName,
            workspaceId
        });
        const result = await databaseManagementService.alterTable(alterTableDto);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Drop table
databaseManagementRouter.delete("/tables/:tableName", apiKeyAuth_middleware_1.requireSchemaAccess, async (req, res) => {
    try {
        const { tableName } = req.params;
        const { workspaceId, cascade } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const dropTableDto = {
            tableName,
            workspaceId,
            cascade: cascade === 'true'
        };
        const result = await databaseManagementService.dropTable(dropTableDto);
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get complete database schema for workspace
databaseManagementRouter.get("/schema", apiKeyAuth_middleware_1.requireReadAccess, async (req, res) => {
    try {
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const schema = await databaseManagementService.getDatabaseSchema(workspaceId);
        return res.status(200).json(schema);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Create view
databaseManagementRouter.post("/views", async (req, res) => {
    try {
        const createViewDto = await validateDto(database_management_dto_1.CreateViewDto, req.body);
        const result = await databaseManagementService.createView(createViewDto);
        return res.status(201).json(result);
    }
    catch (error) {
        if (error.message.includes('already exists')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Drop view
databaseManagementRouter.delete("/views/:viewName", async (req, res) => {
    try {
        const { viewName } = req.params;
        const { workspaceId, materialized } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const fullViewName = `${workspaceId}_${viewName}`;
        const viewType = materialized === 'true' ? 'MATERIALIZED VIEW' : 'VIEW';
        // Note: This is a simplified implementation
        // In a real scenario, you'd want to add this to the service
        const result = { message: `${viewType} ${fullViewName} dropped successfully` };
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Create function/stored procedure
databaseManagementRouter.post("/functions", async (req, res) => {
    try {
        const functionDto = await validateDto(database_management_dto_1.FunctionDefinitionDto, req.body);
        const result = await databaseManagementService.createFunction(functionDto);
        return res.status(201).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Drop function
databaseManagementRouter.delete("/functions/:functionName", async (req, res) => {
    try {
        const { functionName } = req.params;
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const fullFunctionName = `${workspaceId}_${functionName}`;
        // Note: This is a simplified implementation
        const result = { message: `Function ${fullFunctionName} dropped successfully` };
        return res.status(200).json(result);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Generate migration script for table changes
databaseManagementRouter.post("/migrations/generate", async (req, res) => {
    try {
        const { fromSchema, toSchema, workspaceId } = req.body;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        // This would contain logic to compare schemas and generate migration SQL
        const migrationSql = [
            "-- Generated migration script",
            "-- This is a placeholder implementation",
            `-- Migration for workspace: ${workspaceId}`,
            "-- TODO: Implement schema comparison and migration generation"
        ].join('\n');
        return res.status(200).json({
            migrationSql,
            workspaceId,
            generatedAt: new Date().toISOString()
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Validate table schema
databaseManagementRouter.post("/tables/validate", async (req, res) => {
    try {
        const createTableDto = await validateDto(database_management_dto_1.CreateTableDto, req.body);
        // Perform validation checks
        const validationResult = {
            valid: true,
            errors: [],
            warnings: []
        };
        // Check for reserved keywords
        const reservedKeywords = ['user', 'order', 'group', 'table', 'index', 'primary', 'foreign'];
        if (reservedKeywords.includes(createTableDto.tableName.toLowerCase())) {
            validationResult.warnings.push(`Table name '${createTableDto.tableName}' is a reserved keyword`);
        }
        // Check for duplicate column names
        const columnNames = createTableDto.columns.map(col => col.name.toLowerCase());
        const duplicateColumns = columnNames.filter((name, index) => columnNames.indexOf(name) !== index);
        if (duplicateColumns.length > 0) {
            validationResult.valid = false;
            validationResult.errors.push(`Duplicate column names: ${duplicateColumns.join(', ')}`);
        }
        // Check for primary key
        const primaryColumns = createTableDto.columns.filter(col => col.primary);
        if (primaryColumns.length === 0) {
            validationResult.warnings.push('No primary key defined');
        }
        return res.status(200).json(validationResult);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
exports.default = databaseManagementRouter;
//# sourceMappingURL=database-management.controller.js.map