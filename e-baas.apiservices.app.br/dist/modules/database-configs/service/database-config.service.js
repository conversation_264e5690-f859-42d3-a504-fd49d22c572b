"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConfigService = void 0;
const data_source_1 = require("data-source");
const DatabaseConfig_entity_1 = require("../entity/DatabaseConfig.entity");
const http_errors_1 = __importDefault(require("http-errors"));
const connection_service_1 = require("./connection.service");
class DatabaseConfigService {
    constructor() {
        this.dbConfigRepository = data_source_1.AppDataSource.getRepository(DatabaseConfig_entity_1.DatabaseConfig);
        this.connectionService = new connection_service_1.ConnectionService();
    }
    /**
     * Cria uma nova configuração de banco de dados
     */
    async create(data, workspace) {
        try {
            const newDbConfig = this.dbConfigRepository.create({
                ...data,
                workspaceId: workspace.id,
            });
            await this.dbConfigRepository.save(newDbConfig);
            return newDbConfig;
        }
        catch (error) {
            throw (0, http_errors_1.default)(500, `Erro ao criar configuração de banco de dados: ${error.message}`);
        }
    }
    /**
     * Testa a conexão com um banco de dados
     */
    async testConnection(dbConfig) {
        try {
            const connection = await this.connectionService.getConnection(dbConfig);
            const isConnected = dbConfig.databaseType === DatabaseConfig_entity_1.DatabaseType.MONGODB
                ? !!connection
                : !!connection.isInitialized;
            // Fechar a conexão após o teste
            if (isConnected) {
                await this.connectionService.closeConnection(dbConfig);
            }
            return isConnected;
        }
        catch (error) {
            throw (0, http_errors_1.default)(500, `Erro ao testar conexão: ${error.message}`);
        }
    }
    /**
     * Obtém uma configuração de banco de dados pelo ID
     */
    async findById(id) {
        const dbConfig = await this.dbConfigRepository.findOneBy({ id });
        if (!dbConfig) {
            throw (0, http_errors_1.default)(404, "Configuração de banco de dados não encontrada");
        }
        return dbConfig;
    }
    /**
     * Lista as configurações de banco de dados de um workspace
     */
    async findByWorkspace(workspaceId) {
        return this.dbConfigRepository.findBy({ workspaceId, isActive: true });
    }
    /**
     * Verifica se um usuário tem acesso a uma configuração de banco de dados
     */
    async checkUserAccessToDatabase(dbConfigId, user) {
        // Admins têm acesso a todos os bancos
        if (user.isAdmin) {
            return true;
        }
        // Verificar se o banco pertence a algum workspace do usuário
        const dbConfig = await this.dbConfigRepository.findOne({
            where: { id: dbConfigId },
            relations: ["workspace"],
        });
        if (!dbConfig) {
            return false;
        }
        return dbConfig.workspace.ownerId === user.id;
    }
    /**
     * Atualiza uma configuração de banco de dados
     */
    async update(id, data) {
        const dbConfig = await this.findById(id);
        // Atualizar propriedades
        Object.assign(dbConfig, data);
        await this.dbConfigRepository.save(dbConfig);
        return dbConfig;
    }
    /**
     * Desativa uma configuração de banco de dados
     */
    async deactivate(id) {
        const dbConfig = await this.findById(id);
        dbConfig.isActive = false;
        await this.dbConfigRepository.save(dbConfig);
        return dbConfig;
    }
}
exports.DatabaseConfigService = DatabaseConfigService;
//# sourceMappingURL=database-config.service.js.map