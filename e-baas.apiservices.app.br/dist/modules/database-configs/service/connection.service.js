"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionService = void 0;
const DatabaseConfig_entity_1 = require("../entity/DatabaseConfig.entity");
const data_source_1 = require("data-source");
const http_errors_1 = __importDefault(require("http-errors"));
/**
 * Serviço responsável por gerenciar as conexões de banco de dados
 */
class ConnectionService {
    /**
     * Obtém uma conexão para um banco de dados específico
     */
    async getConnection(dbConfig) {
        try {
            // Verificar se o banco está ativo
            if (!dbConfig.isActive) {
                throw (0, http_errors_1.default)(400, "Banco de dados inativo");
            }
            // Conexão para MongoDB
            if (dbConfig.databaseType === DatabaseConfig_entity_1.DatabaseType.MONGODB) {
                const connectionString = dbConfig.connectionString ||
                    `mongodb://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
                return (0, data_source_1.getMongoConnection)(dbConfig.workspaceId, {
                    url: connectionString,
                });
            }
            // Conexão para bancos relacionais (PostgreSQL/MySQL)
            return (0, data_source_1.getWorkspaceConnection)(dbConfig.workspaceId, {
                type: dbConfig.databaseType,
                host: dbConfig.host,
                port: dbConfig.port,
                username: dbConfig.username,
                password: dbConfig.password,
                database: dbConfig.database,
            });
        }
        catch (error) {
            throw (0, http_errors_1.default)(500, `Erro ao obter conexão: ${error.message}`);
        }
    }
    /**
     * Fecha uma conexão específica
     */
    async closeConnection(dbConfig) {
        try {
            await (0, data_source_1.closeWorkspaceConnection)(dbConfig.workspaceId, dbConfig.databaseType);
        }
        catch (error) {
            throw (0, http_errors_1.default)(500, `Erro ao fechar conexão: ${error.message}`);
        }
    }
    /**
     * Executa uma consulta em um banco de dados
     */
    async executeQuery(dbConfig, query, params = []) {
        let connection;
        try {
            // Obter conexão
            connection = await this.getConnection(dbConfig);
            // Executar consulta de acordo com o tipo de banco
            if (dbConfig.databaseType === DatabaseConfig_entity_1.DatabaseType.MONGODB) {
                // Implementação para MongoDB
                const db = connection.db(dbConfig.database);
                // Para MongoDB, a query seria um objeto de configuração com collection, operation, etc.
                // Esta é uma implementação simplificada
                const parsedQuery = JSON.parse(query);
                const collection = db.collection(parsedQuery.collection);
                switch (parsedQuery.operation) {
                    case "find":
                        return collection.find(parsedQuery.filter || {}).toArray();
                    case "findOne":
                        return collection.findOne(parsedQuery.filter || {});
                    case "insertOne":
                        return collection.insertOne(parsedQuery.document || {});
                    case "updateOne":
                        return collection.updateOne(parsedQuery.filter || {}, {
                            $set: parsedQuery.update || {},
                        });
                    case "deleteOne":
                        return collection.deleteOne(parsedQuery.filter || {});
                    default:
                        throw (0, http_errors_1.default)(400, "Operação MongoDB não suportada");
                }
            }
            else {
                // Implementação para bancos relacionais
                return connection.query(query, params);
            }
        }
        catch (error) {
            throw (0, http_errors_1.default)(500, `Erro ao executar consulta: ${error.message}`);
        }
    }
}
exports.ConnectionService = ConnectionService;
//# sourceMappingURL=connection.service.js.map