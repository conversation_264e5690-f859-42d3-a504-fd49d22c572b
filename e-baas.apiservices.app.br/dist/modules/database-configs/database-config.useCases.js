"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConfigUseCases = void 0;
const database_config_service_1 = require("./service/database-config.service");
const connection_service_1 = require("./service/connection.service");
const http_errors_1 = __importDefault(require("http-errors"));
class DatabaseConfigUseCases {
    constructor() {
        this.databaseConfigService = new database_config_service_1.DatabaseConfigService();
        this.connectionService = new connection_service_1.ConnectionService();
    }
    /**
     * Cria uma nova configuração de banco de dados
     */
    async create(data, workspace) {
        return this.databaseConfigService.create(data, workspace);
    }
    /**
     * Testa a conexão com um banco de dados
     */
    async testConnection(databaseId) {
        const dbConfig = await this.databaseConfigService.findById(databaseId);
        return this.databaseConfigService.testConnection(dbConfig);
    }
    /**
     * Obtém uma configuração de banco de dados pelo ID
     */
    async findById(id) {
        return this.databaseConfigService.findById(id);
    }
    /**
     * Lista as configurações de banco de dados de um workspace
     */
    async findByWorkspace(workspaceId) {
        return this.databaseConfigService.findByWorkspace(workspaceId);
    }
    /**
     * Atualiza uma configuração de banco de dados
     */
    async update(id, data) {
        return this.databaseConfigService.update(id, data);
    }
    /**
     * Desativa uma configuração de banco de dados
     */
    async deactivate(id) {
        return this.databaseConfigService.deactivate(id);
    }
    /**
     * Executa uma consulta em um banco de dados
     */
    async executeQuery(databaseId, query, params = []) {
        try {
            const dbConfig = await this.databaseConfigService.findById(databaseId);
            // Verificar se o banco de dados está ativo
            if (!dbConfig.isActive) {
                throw (0, http_errors_1.default)(400, "Banco de dados inativo");
            }
            // Executar a consulta
            return this.connectionService.executeQuery(dbConfig, query, params);
        }
        catch (error) {
            if (error instanceof Error) {
                throw (0, http_errors_1.default)(500, `Erro ao executar consulta: ${error.message}`);
            }
            throw error;
        }
    }
}
exports.DatabaseConfigUseCases = DatabaseConfigUseCases;
//# sourceMappingURL=database-config.useCases.js.map