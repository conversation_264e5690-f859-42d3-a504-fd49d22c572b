"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConfigController = void 0;
const database_config_useCases_1 = require("./database-config.useCases");
const http_errors_1 = __importDefault(require("http-errors"));
class DatabaseConfigController {
    constructor() {
        this.databaseConfigUseCases = new database_config_useCases_1.DatabaseConfigUseCases();
    }
    /**
     * Cria uma nova configuração de banco de dados
     */
    async create(req, res, next) {
        try {
            const workspace = req.workspace;
            if (!workspace) {
                throw (0, http_errors_1.default)(400, "Workspace não encontrado");
            }
            const dbConfig = await this.databaseConfigUseCases.create(req.body, workspace);
            res.status(201).json(dbConfig);
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Testa a conexão com um banco de dados
     */
    async testConnection(req, res, next) {
        try {
            const { databaseId } = req.params;
            const result = await this.databaseConfigUseCases.testConnection(databaseId);
            res.status(200).json({ success: result });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Lista configurações de banco de dados de um workspace
     */
    async findByWorkspace(req, res, next) {
        try {
            const { workspaceId } = req.params;
            const dbConfigs = await this.databaseConfigUseCases.findByWorkspace(workspaceId);
            res.status(200).json(dbConfigs);
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Obtém uma configuração de banco de dados pelo ID
     */
    async findById(req, res, next) {
        try {
            const { id } = req.params;
            const dbConfig = await this.databaseConfigUseCases.findById(id);
            res.status(200).json(dbConfig);
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Atualiza uma configuração de banco de dados
     */
    async update(req, res, next) {
        try {
            const { id } = req.params;
            const dbConfig = await this.databaseConfigUseCases.update(id, req.body);
            res.status(200).json(dbConfig);
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Desativa uma configuração de banco de dados
     */
    async deactivate(req, res, next) {
        try {
            const { id } = req.params;
            const dbConfig = await this.databaseConfigUseCases.deactivate(id);
            res.status(200).json(dbConfig);
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Executa uma consulta em um banco de dados
     */
    async executeQuery(req, res, next) {
        try {
            const { databaseId } = req.params;
            const { query, params } = req.body;
            const result = await this.databaseConfigUseCases.executeQuery(databaseId, query, params);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.DatabaseConfigController = DatabaseConfigController;
//# sourceMappingURL=database-config.controller.js.map