"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConfigResponseDto = exports.DatabaseConfigDto = void 0;
require("reflect-metadata");
const class_validator_1 = require("class-validator");
const DatabaseConfig_entity_1 = require("modules/database-configs/entity/DatabaseConfig.entity");
class DatabaseConfigDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DatabaseConfigDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(DatabaseConfig_entity_1.DatabaseType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DatabaseConfigDto.prototype, "databaseType", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DatabaseConfigDto.prototype, "host", void 0);
__decorate([
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], DatabaseConfigDto.prototype, "port", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DatabaseConfigDto.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DatabaseConfigDto.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DatabaseConfigDto.prototype, "database", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DatabaseConfigDto.prototype, "connectionString", void 0);
exports.DatabaseConfigDto = DatabaseConfigDto;
class DatabaseConfigResponseDto extends DatabaseConfigDto {
}
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DatabaseConfigResponseDto.prototype, "id", void 0);
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DatabaseConfigResponseDto.prototype, "workspaceId", void 0);
exports.DatabaseConfigResponseDto = DatabaseConfigResponseDto;
//# sourceMappingURL=database-config.dto.js.map