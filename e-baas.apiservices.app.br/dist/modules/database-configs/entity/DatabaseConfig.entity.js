"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConfig = exports.DatabaseType = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
// Enumeração para os tipos de banco de dados suportados
var DatabaseType;
(function (DatabaseType) {
    DatabaseType["POSTGRES"] = "postgres";
    DatabaseType["MYSQL"] = "mysql";
    DatabaseType["MONGODB"] = "mongodb";
})(DatabaseType = exports.DatabaseType || (exports.DatabaseType = {}));
let DatabaseConfig = class DatabaseConfig {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "database_type",
        type: process.env.NODE_ENV === "test" ? "varchar" : "enum",
        enum: DatabaseType,
        default: DatabaseType.POSTGRES,
    }),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "databaseType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255 }),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "host", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "int" }),
    __metadata("design:type", Number)
], DatabaseConfig.prototype, "port", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255 }),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "database", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_active", type: "boolean", default: true }),
    __metadata("design:type", Boolean)
], DatabaseConfig.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "connection_string",
        type: "varchar",
        length: 1000,
        nullable: true,
    }),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "connectionString", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], DatabaseConfig.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], DatabaseConfig.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id" }),
    __metadata("design:type", String)
], DatabaseConfig.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)("Workspace", "databaseConfigs"),
    (0, typeorm_1.JoinColumn)({ name: "workspace_id" }),
    __metadata("design:type", Object)
], DatabaseConfig.prototype, "workspace", void 0);
DatabaseConfig = __decorate([
    (0, typeorm_1.Entity)("database_configs")
], DatabaseConfig);
exports.DatabaseConfig = DatabaseConfig;
//# sourceMappingURL=DatabaseConfig.entity.js.map