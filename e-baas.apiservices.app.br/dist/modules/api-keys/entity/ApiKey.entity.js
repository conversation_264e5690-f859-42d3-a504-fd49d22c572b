"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKey = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const crypto_1 = __importDefault(require("crypto"));
const config_1 = __importDefault(require("../../../infra/config"));
const api_key_dto_1 = require("../dto/api-key.dto");
let ApiKey = class ApiKey {
    generateApiKey() {
        const keyPrefix = config_1.default.api.keyPrefix || 'ebaas_';
        const typePrefix = this.getTypePrefix();
        const randomBytes = crypto_1.default.randomBytes(32).toString('hex');
        this.apiKey = `${keyPrefix}${typePrefix}_${randomBytes}`;
        this.keyPrefix = this.apiKey.substring(0, 20);
    }
    updateUsage() {
        if (this.lastUsedAt) {
            this.usageCount += 1;
        }
    }
    getTypePrefix() {
        switch (this.type) {
            case api_key_dto_1.ApiKeyType.ANON:
                return 'anon';
            case api_key_dto_1.ApiKeyType.SERVICE_ROLE:
                return 'svc';
            case api_key_dto_1.ApiKeyType.AUTHENTICATED:
                return 'auth';
            default:
                return 'key';
        }
    }
    // Check if API key is expired
    isExpired() {
        if (!this.expiresAt)
            return false;
        return new Date() > this.expiresAt;
    }
    // Check if API key is valid (active and not expired)
    isValid() {
        return this.isActive && !this.isExpired();
    }
    // Check if key has specific scope
    hasScope(scope) {
        return this.scopes.includes(api_key_dto_1.ApiKeyScope.ALL) || this.scopes.includes(scope);
    }
    // Check if IP is allowed
    isIpAllowed(ipAddress) {
        if (!this.allowedIps || this.allowedIps.length === 0)
            return true;
        return this.allowedIps.includes(ipAddress);
    }
    // Check if origin is allowed
    isOriginAllowed(origin) {
        if (!this.allowedOrigins || this.allowedOrigins.length === 0)
            return true;
        return this.allowedOrigins.some(allowedOrigin => {
            if (allowedOrigin === '*')
                return true;
            if (allowedOrigin.includes('*')) {
                const regex = new RegExp(allowedOrigin.replace(/\*/g, '.*'));
                return regex.test(origin);
            }
            return allowedOrigin === origin;
        });
    }
    // Update last used timestamp
    markAsUsed() {
        this.lastUsedAt = new Date();
    }
    // Get safe object (without secret)
    toSafeObject() {
        const { apiKey, ...safeKey } = this;
        return {
            ...safeKey,
            prefix: this.keyPrefix
        };
    }
    // Get remaining time until expiration in milliseconds
    getRemainingTime() {
        if (!this.expiresAt)
            return null;
        return Math.max(0, this.expiresAt.getTime() - Date.now());
    }
    // Check if key is close to expiry (within 7 days)
    isNearExpiry() {
        if (!this.expiresAt)
            return false;
        const sevenDays = 7 * 24 * 60 * 60 * 1000;
        return (this.expiresAt.getTime() - Date.now()) < sevenDays;
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], ApiKey.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 100 }),
    __metadata("design:type", String)
], ApiKey.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "api_key", type: "varchar", length: 500, unique: true, select: false }),
    __metadata("design:type", String)
], ApiKey.prototype, "apiKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "key_prefix", type: "varchar", length: 20 }),
    __metadata("design:type", String)
], ApiKey.prototype, "keyPrefix", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "workspace_id", type: "varchar", length: 255 }),
    __metadata("design:type", String)
], ApiKey.prototype, "workspaceId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: api_key_dto_1.ApiKeyType,
        default: api_key_dto_1.ApiKeyType.AUTHENTICATED
    }),
    __metadata("design:type", String)
], ApiKey.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "simple-array",
        default: api_key_dto_1.ApiKeyScope.ALL
    }),
    __metadata("design:type", Array)
], ApiKey.prototype, "scopes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_active", type: "boolean", default: true }),
    __metadata("design:type", Boolean)
], ApiKey.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "expires_at",
        type: process.env.NODE_ENV === "test" ? "datetime" : "timestamp",
        nullable: true,
    }),
    __metadata("design:type", Date)
], ApiKey.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "text", nullable: true }),
    __metadata("design:type", String)
], ApiKey.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rate_limit", type: "integer", nullable: true }),
    __metadata("design:type", Number)
], ApiKey.prototype, "rateLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "allowed_origins",
        type: "simple-array",
        nullable: true
    }),
    __metadata("design:type", Array)
], ApiKey.prototype, "allowedOrigins", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "allowed_ips",
        type: "simple-array",
        nullable: true
    }),
    __metadata("design:type", Array)
], ApiKey.prototype, "allowedIps", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "last_used_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], ApiKey.prototype, "lastUsedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "usage_count", type: "integer", default: 0 }),
    __metadata("design:type", Number)
], ApiKey.prototype, "usageCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "created_by", type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], ApiKey.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], ApiKey.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], ApiKey.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)("Workspace", "apiKeys"),
    (0, typeorm_1.JoinColumn)({ name: "workspace_id" }),
    __metadata("design:type", Object)
], ApiKey.prototype, "workspace", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApiKey.prototype, "generateApiKey", null);
__decorate([
    (0, typeorm_1.BeforeUpdate)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApiKey.prototype, "updateUsage", null);
ApiKey = __decorate([
    (0, typeorm_1.Entity)("api_keys"),
    (0, typeorm_1.Index)(["apiKey"], { unique: true }),
    (0, typeorm_1.Index)(["workspaceId", "type"]),
    (0, typeorm_1.Index)(["workspaceId", "isActive"])
], ApiKey);
exports.ApiKey = ApiKey;
//# sourceMappingURL=ApiKey.entity.js.map