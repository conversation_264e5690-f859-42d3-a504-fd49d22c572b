"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitDto = exports.ApiKeyUsageDto = exports.ApiKeyWithSecretDto = exports.ApiKeyResponseDto = exports.UpdateApiKeyDto = exports.CreateApiKeyDto = exports.ApiKeyScope = exports.ApiKeyType = void 0;
const class_validator_1 = require("class-validator");
var ApiKeyType;
(function (ApiKeyType) {
    ApiKeyType["ANON"] = "anon";
    ApiKeyType["SERVICE_ROLE"] = "service_role";
    ApiKeyType["AUTHENTICATED"] = "authenticated";
})(ApiKeyType = exports.ApiKeyType || (exports.ApiKeyType = {}));
var ApiKeyScope;
(function (ApiKeyScope) {
    ApiKeyScope["ALL"] = "*";
    ApiKeyScope["READ"] = "read";
    ApiKeyScope["WRITE"] = "write";
    ApiKeyScope["ADMIN"] = "admin";
    ApiKeyScope["DATABASE"] = "database";
    ApiKeyScope["STORAGE"] = "storage";
    ApiKeyScope["AUTH"] = "auth";
    ApiKeyScope["REALTIME"] = "realtime";
    ApiKeyScope["EDGE_FUNCTIONS"] = "edge_functions";
})(ApiKeyScope = exports.ApiKeyScope || (exports.ApiKeyScope = {}));
class CreateApiKeyDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateApiKeyDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateApiKeyDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(ApiKeyType),
    __metadata("design:type", String)
], CreateApiKeyDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(ApiKeyScope, { each: true }),
    __metadata("design:type", Array)
], CreateApiKeyDto.prototype, "scopes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateApiKeyDto.prototype, "expiresAt", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateApiKeyDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateApiKeyDto.prototype, "rateLimit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateApiKeyDto.prototype, "allowedOrigins", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateApiKeyDto.prototype, "allowedIps", void 0);
exports.CreateApiKeyDto = CreateApiKeyDto;
class UpdateApiKeyDto {
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateApiKeyDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateApiKeyDto.prototype, "isActive", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(ApiKeyScope, { each: true }),
    __metadata("design:type", Array)
], UpdateApiKeyDto.prototype, "scopes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], UpdateApiKeyDto.prototype, "expiresAt", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateApiKeyDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateApiKeyDto.prototype, "rateLimit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], UpdateApiKeyDto.prototype, "allowedOrigins", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], UpdateApiKeyDto.prototype, "allowedIps", void 0);
exports.UpdateApiKeyDto = UpdateApiKeyDto;
class ApiKeyResponseDto {
}
exports.ApiKeyResponseDto = ApiKeyResponseDto;
class ApiKeyWithSecretDto extends ApiKeyResponseDto {
}
exports.ApiKeyWithSecretDto = ApiKeyWithSecretDto;
class ApiKeyUsageDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ApiKeyUsageDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiKeyUsageDto.prototype, "keyId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], ApiKeyUsageDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], ApiKeyUsageDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], ApiKeyUsageDto.prototype, "limit", void 0);
exports.ApiKeyUsageDto = ApiKeyUsageDto;
class RateLimitDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RateLimitDto.prototype, "keyId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RateLimitDto.prototype, "endpoint", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RateLimitDto.prototype, "ipAddress", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RateLimitDto.prototype, "userAgent", void 0);
exports.RateLimitDto = RateLimitDto;
//# sourceMappingURL=api-key.dto.js.map