"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyService = void 0;
const data_source_1 = require("../../infra/database/data-source");
const ApiKey_entity_1 = require("./entity/ApiKey.entity");
const api_key_dto_1 = require("./dto/api-key.dto");
class ApiKeyService {
    constructor() {
        this.rateLimitCache = new Map();
        this.RATE_LIMIT_WINDOW = 60000; // 1 minute in milliseconds
        this.apiKeyRepository = data_source_1.AppDataSource.getRepository(ApiKey_entity_1.ApiKey);
    }
    async createApiKey(createDto, createdBy) {
        // Check if name already exists in workspace
        const existingKey = await this.apiKeyRepository.findOne({
            where: { name: createDto.name, workspaceId: createDto.workspaceId }
        });
        if (existingKey) {
            throw new Error('API key with this name already exists in workspace');
        }
        // Set default scopes if not provided
        const scopes = createDto.scopes && createDto.scopes.length > 0
            ? createDto.scopes
            : [api_key_dto_1.ApiKeyScope.ALL];
        // Create new API key
        const apiKey = this.apiKeyRepository.create({
            name: createDto.name,
            workspaceId: createDto.workspaceId,
            type: createDto.type,
            scopes,
            expiresAt: createDto.expiresAt,
            description: createDto.description,
            rateLimit: createDto.rateLimit,
            allowedOrigins: createDto.allowedOrigins,
            allowedIps: createDto.allowedIps,
            createdBy,
            usageCount: 0,
            isActive: true
        });
        await this.apiKeyRepository.save(apiKey);
        // Return with secret (only time it's exposed)
        const savedKey = await this.apiKeyRepository.findOne({
            where: { id: apiKey.id },
            select: ['id', 'name', 'apiKey', 'workspaceId', 'type', 'scopes', 'keyPrefix', 'isActive',
                'expiresAt', 'description', 'rateLimit', 'allowedOrigins', 'allowedIps',
                'lastUsedAt', 'usageCount', 'createdAt', 'updatedAt']
        });
        return {
            ...savedKey.toSafeObject(),
            apiKey: savedKey.apiKey
        };
    }
    async getApiKeys(workspaceId, type) {
        const whereClause = { workspaceId };
        if (type) {
            whereClause.type = type;
        }
        const keys = await this.apiKeyRepository.find({
            where: whereClause,
            order: { createdAt: 'DESC' }
        });
        return keys.map(key => key.toSafeObject());
    }
    async getApiKey(keyId, workspaceId) {
        const key = await this.apiKeyRepository.findOne({
            where: { id: keyId, workspaceId }
        });
        return key ? key.toSafeObject() : null;
    }
    async updateApiKey(keyId, workspaceId, updateDto) {
        const key = await this.apiKeyRepository.findOne({
            where: { id: keyId, workspaceId }
        });
        if (!key) {
            throw new Error('API key not found');
        }
        // Update fields
        if (updateDto.name !== undefined)
            key.name = updateDto.name;
        if (updateDto.isActive !== undefined)
            key.isActive = updateDto.isActive;
        if (updateDto.scopes !== undefined)
            key.scopes = updateDto.scopes;
        if (updateDto.expiresAt !== undefined)
            key.expiresAt = updateDto.expiresAt;
        if (updateDto.description !== undefined)
            key.description = updateDto.description;
        if (updateDto.rateLimit !== undefined)
            key.rateLimit = updateDto.rateLimit;
        if (updateDto.allowedOrigins !== undefined)
            key.allowedOrigins = updateDto.allowedOrigins;
        if (updateDto.allowedIps !== undefined)
            key.allowedIps = updateDto.allowedIps;
        await this.apiKeyRepository.save(key);
        return key.toSafeObject();
    }
    async deleteApiKey(keyId, workspaceId) {
        const result = await this.apiKeyRepository.delete({ id: keyId, workspaceId });
        if (result.affected === 0) {
            throw new Error('API key not found');
        }
        return { message: 'API key deleted successfully' };
    }
    async rotateApiKey(keyId, workspaceId) {
        const key = await this.apiKeyRepository.findOne({
            where: { id: keyId, workspaceId },
            select: ['id', 'name', 'workspaceId', 'type', 'scopes', 'isActive', 'expiresAt',
                'description', 'rateLimit', 'allowedOrigins', 'allowedIps', 'createdBy']
        });
        if (!key) {
            throw new Error('API key not found');
        }
        // Generate new API key while keeping other properties
        key.generateApiKey();
        key.usageCount = 0; // Reset usage count
        key.lastUsedAt = undefined; // Reset last used
        await this.apiKeyRepository.save(key);
        const rotatedKey = await this.apiKeyRepository.findOne({
            where: { id: keyId },
            select: ['id', 'name', 'apiKey', 'workspaceId', 'type', 'scopes', 'keyPrefix', 'isActive',
                'expiresAt', 'description', 'rateLimit', 'allowedOrigins', 'allowedIps',
                'lastUsedAt', 'usageCount', 'createdAt', 'updatedAt']
        });
        return {
            ...rotatedKey.toSafeObject(),
            apiKey: rotatedKey.apiKey
        };
    }
    async validateApiKey(apiKeyString, requiredScope, ipAddress, origin) {
        const key = await this.apiKeyRepository.findOne({
            where: { apiKey: apiKeyString },
            select: ['id', 'name', 'apiKey', 'workspaceId', 'type', 'scopes', 'isActive',
                'expiresAt', 'rateLimit', 'allowedOrigins', 'allowedIps', 'lastUsedAt', 'usageCount']
        });
        if (!key || !key.isValid()) {
            return null;
        }
        // Check scope
        if (requiredScope && !key.hasScope(requiredScope)) {
            return null;
        }
        // Check IP whitelist
        if (ipAddress && !key.isIpAllowed(ipAddress)) {
            return null;
        }
        // Check origin whitelist
        if (origin && !key.isOriginAllowed(origin)) {
            return null;
        }
        // Check rate limit
        if (key.rateLimit && !this.checkRateLimit(key.id, key.rateLimit)) {
            return null;
        }
        // Update usage
        key.markAsUsed();
        await this.apiKeyRepository.save(key);
        return key;
    }
    async getApiKeyUsage(usageDto) {
        let query = this.apiKeyRepository.createQueryBuilder('apiKey')
            .where('apiKey.workspaceId = :workspaceId', { workspaceId: usageDto.workspaceId })
            .select([
            'apiKey.id',
            'apiKey.name',
            'apiKey.keyPrefix',
            'apiKey.type',
            'apiKey.usageCount',
            'apiKey.lastUsedAt',
            'apiKey.createdAt'
        ]);
        if (usageDto.keyId) {
            query = query.andWhere('apiKey.id = :keyId', { keyId: usageDto.keyId });
        }
        if (usageDto.startDate) {
            query = query.andWhere('apiKey.lastUsedAt >= :startDate', { startDate: usageDto.startDate });
        }
        if (usageDto.endDate) {
            query = query.andWhere('apiKey.lastUsedAt <= :endDate', { endDate: usageDto.endDate });
        }
        const limit = usageDto.limit || 100;
        query = query.orderBy('apiKey.lastUsedAt', 'DESC').limit(limit);
        return await query.getRawMany();
    }
    async getWorkspaceStats(workspaceId) {
        const stats = await this.apiKeyRepository
            .createQueryBuilder('apiKey')
            .where('apiKey.workspaceId = :workspaceId', { workspaceId })
            .select([
            'COUNT(*) as totalKeys',
            'COUNT(CASE WHEN apiKey.isActive = true THEN 1 END) as activeKeys',
            'COUNT(CASE WHEN apiKey.expiresAt < NOW() THEN 1 END) as expiredKeys',
            'SUM(apiKey.usageCount) as totalUsage',
            'MAX(apiKey.lastUsedAt) as lastActivity'
        ])
            .getRawOne();
        const typeDistribution = await this.apiKeyRepository
            .createQueryBuilder('apiKey')
            .where('apiKey.workspaceId = :workspaceId', { workspaceId })
            .select(['apiKey.type', 'COUNT(*) as count'])
            .groupBy('apiKey.type')
            .getRawMany();
        return {
            ...stats,
            typeDistribution: typeDistribution.reduce((acc, item) => {
                acc[item.type] = parseInt(item.count);
                return acc;
            }, {})
        };
    }
    checkRateLimit(keyId, limit) {
        const now = Date.now();
        const rateLimitInfo = this.rateLimitCache.get(keyId);
        if (!rateLimitInfo) {
            // First request
            this.rateLimitCache.set(keyId, {
                keyId,
                requests: 1,
                windowStart: now,
                isBlocked: false
            });
            return true;
        }
        // Check if window has expired
        if (now - rateLimitInfo.windowStart >= this.RATE_LIMIT_WINDOW) {
            // Reset window
            rateLimitInfo.requests = 1;
            rateLimitInfo.windowStart = now;
            rateLimitInfo.isBlocked = false;
            return true;
        }
        // Increment requests in current window
        rateLimitInfo.requests++;
        // Check if limit exceeded
        if (rateLimitInfo.requests > limit) {
            rateLimitInfo.isBlocked = true;
            return false;
        }
        return true;
    }
    async getRateLimitStatus(keyId) {
        const rateLimitInfo = this.rateLimitCache.get(keyId);
        if (!rateLimitInfo) {
            return null;
        }
        const key = await this.apiKeyRepository.findOne({ where: { id: keyId } });
        if (!key || !key.rateLimit) {
            return null;
        }
        const remaining = Math.max(0, key.rateLimit - rateLimitInfo.requests);
        const resetTime = rateLimitInfo.windowStart + this.RATE_LIMIT_WINDOW;
        return { remaining, resetTime };
    }
    // Default API keys for new workspaces
    async createDefaultApiKeys(workspaceId, createdBy) {
        // Create anonymous key
        const anonKey = await this.createApiKey({
            name: 'Anonymous Key',
            workspaceId,
            type: api_key_dto_1.ApiKeyType.ANON,
            scopes: [api_key_dto_1.ApiKeyScope.READ],
            description: 'Default anonymous access key'
        }, createdBy);
        // Create service role key
        const serviceKey = await this.createApiKey({
            name: 'Service Role Key',
            workspaceId,
            type: api_key_dto_1.ApiKeyType.SERVICE_ROLE,
            scopes: [api_key_dto_1.ApiKeyScope.ALL],
            description: 'Default service role key with full access'
        }, createdBy);
        return {
            anon: anonKey,
            serviceRole: serviceKey
        };
    }
    // Cleanup expired rate limit entries
    cleanupRateLimit() {
        const now = Date.now();
        for (const [keyId, info] of this.rateLimitCache.entries()) {
            if (now - info.windowStart >= this.RATE_LIMIT_WINDOW) {
                this.rateLimitCache.delete(keyId);
            }
        }
    }
    // Start cleanup interval
    startCleanupInterval() {
        setInterval(() => {
            this.cleanupRateLimit();
        }, this.RATE_LIMIT_WINDOW);
    }
}
exports.ApiKeyService = ApiKeyService;
//# sourceMappingURL=api-key.service.js.map