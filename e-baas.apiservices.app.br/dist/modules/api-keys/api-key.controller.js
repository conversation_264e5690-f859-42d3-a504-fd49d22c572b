"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const api_key_service_1 = require("./api-key.service");
const api_key_dto_1 = require("./dto/api-key.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const apiKeyRouter = (0, express_1.Router)();
const apiKeyService = new api_key_service_1.ApiKeyService();
// Helper function for validation
const validateDto = async (DtoClass, data) => {
    const dto = (0, class_transformer_1.plainToClass)(DtoClass, data);
    const errors = await (0, class_validator_1.validate)(dto);
    if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
    }
    return dto;
};
// Create API key
apiKeyRouter.post("/", async (req, res) => {
    try {
        const createDto = await validateDto(api_key_dto_1.CreateApiKeyDto, req.body);
        const createdBy = req.user?.id;
        const apiKey = await apiKeyService.createApiKey(createDto, createdBy);
        return res.status(201).json(apiKey);
    }
    catch (error) {
        if (error.message.includes('already exists')) {
            return res.status(409).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Get all API keys for workspace
apiKeyRouter.get("/", async (req, res) => {
    try {
        const { workspaceId, type } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const apiKeys = await apiKeyService.getApiKeys(workspaceId, type);
        return res.status(200).json(apiKeys);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get specific API key
apiKeyRouter.get("/:keyId", async (req, res) => {
    try {
        const { keyId } = req.params;
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const apiKey = await apiKeyService.getApiKey(keyId, workspaceId);
        if (!apiKey) {
            return res.status(404).json({ error: 'API key not found' });
        }
        return res.status(200).json(apiKey);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Update API key
apiKeyRouter.put("/:keyId", async (req, res) => {
    try {
        const { keyId } = req.params;
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const updateDto = await validateDto(api_key_dto_1.UpdateApiKeyDto, req.body);
        const apiKey = await apiKeyService.updateApiKey(keyId, workspaceId, updateDto);
        return res.status(200).json(apiKey);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Delete API key
apiKeyRouter.delete("/:keyId", async (req, res) => {
    try {
        const { keyId } = req.params;
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const result = await apiKeyService.deleteApiKey(keyId, workspaceId);
        return res.status(200).json(result);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Rotate API key (generate new secret)
apiKeyRouter.post("/:keyId/rotate", async (req, res) => {
    try {
        const { keyId } = req.params;
        const { workspaceId } = req.query;
        if (!workspaceId) {
            return res.status(400).json({ error: 'Workspace ID is required' });
        }
        const apiKey = await apiKeyService.rotateApiKey(keyId, workspaceId);
        return res.status(200).json(apiKey);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return res.status(404).json({ error: error.message });
        }
        return res.status(400).json({ error: error.message });
    }
});
// Validate API key
apiKeyRouter.post("/validate", async (req, res) => {
    try {
        const { apiKey, scope, ipAddress, origin } = req.body;
        if (!apiKey) {
            return res.status(400).json({ error: 'API key is required' });
        }
        const validKey = await apiKeyService.validateApiKey(apiKey, scope, ipAddress, origin);
        if (!validKey) {
            return res.status(401).json({ valid: false, error: 'Invalid API key' });
        }
        return res.status(200).json({
            valid: true,
            key: validKey.toSafeObject(),
            workspaceId: validKey.workspaceId,
            type: validKey.type,
            scopes: validKey.scopes
        });
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get API key usage statistics
apiKeyRouter.get("/usage/stats", async (req, res) => {
    try {
        const usageDto = await validateDto(api_key_dto_1.ApiKeyUsageDto, req.query);
        const usage = await apiKeyService.getApiKeyUsage(usageDto);
        return res.status(200).json(usage);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get workspace API key statistics
apiKeyRouter.get("/workspace/:workspaceId/stats", async (req, res) => {
    try {
        const { workspaceId } = req.params;
        const stats = await apiKeyService.getWorkspaceStats(workspaceId);
        return res.status(200).json(stats);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Get rate limit status for API key
apiKeyRouter.get("/:keyId/rate-limit", async (req, res) => {
    try {
        const { keyId } = req.params;
        const rateLimitStatus = await apiKeyService.getRateLimitStatus(keyId);
        if (!rateLimitStatus) {
            return res.status(404).json({ error: 'Rate limit info not found' });
        }
        return res.status(200).json(rateLimitStatus);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Create default API keys for new workspace
apiKeyRouter.post("/workspace/:workspaceId/defaults", async (req, res) => {
    try {
        const { workspaceId } = req.params;
        const createdBy = req.user?.id;
        const defaultKeys = await apiKeyService.createDefaultApiKeys(workspaceId, createdBy);
        return res.status(201).json(defaultKeys);
    }
    catch (error) {
        return res.status(400).json({ error: error.message });
    }
});
// Start the cleanup interval when the module loads
apiKeyService.startCleanupInterval();
exports.default = apiKeyRouter;
//# sourceMappingURL=api-key.controller.js.map