"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RlsHelperFunctionDto = exports.RlsPolicyTestDto = exports.RlsTableConfigDto = exports.RlsPolicyDto = exports.PolicyRole = exports.PolicyCommand = void 0;
const class_validator_1 = require("class-validator");
var PolicyCommand;
(function (PolicyCommand) {
    PolicyCommand["ALL"] = "ALL";
    PolicyCommand["SELECT"] = "SELECT";
    PolicyCommand["INSERT"] = "INSERT";
    PolicyCommand["UPDATE"] = "UPDATE";
    PolicyCommand["DELETE"] = "DELETE";
})(PolicyCommand = exports.PolicyCommand || (exports.PolicyCommand = {}));
var PolicyRole;
(function (PolicyRole) {
    PolicyRole["AUTHENTICATED"] = "authenticated";
    PolicyRole["ANONYMOUS"] = "anonymous";
    PolicyRole["SERVICE_ROLE"] = "service_role";
    PolicyRole["CUSTOM"] = "custom";
})(PolicyRole = exports.PolicyRole || (exports.PolicyRole = {}));
class RlsPolicyDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(PolicyCommand),
    __metadata("design:type", String)
], RlsPolicyDto.prototype, "command", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyDto.prototype, "role", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyDto.prototype, "usingExpression", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyDto.prototype, "withCheckExpression", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RlsPolicyDto.prototype, "permissive", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RlsPolicyDto.prototype, "enabled", void 0);
exports.RlsPolicyDto = RlsPolicyDto;
class RlsTableConfigDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsTableConfigDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsTableConfigDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RlsTableConfigDto.prototype, "enableRls", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RlsTableConfigDto.prototype, "forceRls", void 0);
exports.RlsTableConfigDto = RlsTableConfigDto;
class RlsPolicyTestDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyTestDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyTestDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyTestDto.prototype, "testQuery", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyTestDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsPolicyTestDto.prototype, "role", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], RlsPolicyTestDto.prototype, "contextData", void 0);
exports.RlsPolicyTestDto = RlsPolicyTestDto;
class RlsHelperFunctionDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsHelperFunctionDto.prototype, "functionName", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsHelperFunctionDto.prototype, "workspaceId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsHelperFunctionDto.prototype, "functionBody", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsHelperFunctionDto.prototype, "returnType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], RlsHelperFunctionDto.prototype, "parameters", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RlsHelperFunctionDto.prototype, "description", void 0);
exports.RlsHelperFunctionDto = RlsHelperFunctionDto;
//# sourceMappingURL=rlsPolicies.dto.js.map