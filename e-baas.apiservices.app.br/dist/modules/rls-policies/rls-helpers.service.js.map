{"version": 3, "file": "rls-helpers.service.js", "sourceRoot": "", "sources": ["../../../src/modules/rls-policies/rls-helpers.service.ts"], "names": [], "mappings": ";;;AAAA,kEAAiE;AAEjE,MAAa,UAAU;IAAvB;QACU,eAAU,GAAG,2BAAa,CAAC,OAAO,CAAC,UAAU,CAAC;IA0WxD,CAAC;IAxWC;;OAEG;IACH,KAAK,CAAC,wBAAwB;QAC5B,IAAI;YACF,oCAAoC;YACpC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,CAAC,CAAC;YAEH,yCAAyC;YACzC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,CAAC,CAAC;YAEH,iCAAiC;YACjC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,CAAC,CAAC;YAEH,4DAA4D;YAC5D,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;OAW3B,CAAC,CAAC;YAEH,2CAA2C;YAC3C,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,CAAC,CAAC;YAEH,0DAA0D;YAC1D,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,CAAC,CAAC;YAEH,sDAAsD;YACtD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQ3B,CAAC,CAAC;YAEH,kDAAkD;YAClD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;OAa3B,CAAC,CAAC;YAEH,uDAAuD;YACvD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;OAa3B,CAAC,CAAC;YAEH,sDAAsD;YACtD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;OAW3B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;SAC5D;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,SAAiB,EACjB,UASI,EAAE;QAEN,MAAM,EACJ,SAAS,GAAG,IAAI,EAChB,cAAc,GAAG,IAAI,EACrB,YAAY,GAAG,SAAS,EACxB,iBAAiB,GAAG,cAAc,EAClC,YAAY,GAAG,IAAI,EACnB,YAAY,GAAG,IAAI,EACnB,YAAY,GAAG,IAAI,EACnB,YAAY,GAAG,IAAI,EACpB,GAAG,OAAO,CAAC;QAEZ,IAAI;YACF,uBAAuB;YACvB,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,eAAe,SAAS,4BAA4B,CAAC,CAAC;YAElF,uBAAuB;YACvB,IAAI,YAAY,EAAE;gBAChB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,MAAM,CAAC,CAAC;gBAChH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;2BACT,SAAS,sBAAsB,SAAS;;mBAEhD,eAAe;SACzB,CAAC,CAAC;aACJ;YAED,uBAAuB;YACvB,IAAI,YAAY,EAAE;gBAChB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBACjH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;2BACT,SAAS,sBAAsB,SAAS;;wBAE3C,eAAe;SAC9B,CAAC,CAAC;aACJ;YAED,uBAAuB;YACvB,IAAI,YAAY,EAAE;gBAChB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBACjH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;2BACT,SAAS,sBAAsB,SAAS;;mBAEhD,eAAe;wBACV,eAAe;SAC9B,CAAC,CAAC;aACJ;YAED,uBAAuB;YACvB,IAAI,YAAY,EAAE;gBAChB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;gBAClH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;2BACT,SAAS,sBAAsB,SAAS;;mBAEhD,eAAe;SACzB,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,SAAS,EAAE,CAAC,CAAC;SACtE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,6CAA6C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,SAAiB;QACrC,IAAI;YACF,sCAAsC;YACtC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;OAE5C,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEhB,wBAAwB;YACxB,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE;gBAC7B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,yBAAyB,MAAM,CAAC,UAAU,OAAO,SAAS,EAAE,CAAC,CAAC;aAC3F;YAED,OAAO,CAAC,GAAG,CAAC,wCAAwC,SAAS,EAAE,CAAC,CAAC;SAClE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,2CAA2C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,SAAkB;QACnC,IAAI;YACF,IAAI,KAAK,GAAG;;;;;;;;;;;OAWX,CAAC;YAEF,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,IAAI,SAAS,EAAE;gBACb,KAAK,IAAI,uBAAuB,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACxB;YAED,KAAK,IAAI,iCAAiC,CAAC;YAE3C,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SACnD;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACd,SAAiB,EACjB,SAAoD,EACpD,WAAgB,EAAE,EAClB,MAAe,EACf,WAAoB;QAEpB,IAAI;YACF,+BAA+B;YAC/B,IAAI,MAAM,EAAE;gBACV,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oCAAoC,MAAM,GAAG,CAAC,CAAC;aAC5E;YACD,IAAI,WAAW,EAAE;gBACf,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,yCAAyC,WAAW,GAAG,CAAC,CAAC;aACtF;YAED,IAAI,KAAa,CAAC;YAClB,IAAI,MAAM,GAAU,EAAE,CAAC;YAEvB,QAAQ,SAAS,EAAE;gBACjB,KAAK,QAAQ;oBACX,KAAK,GAAG,iBAAiB,SAAS,UAAU,CAAC;oBAC7C,MAAM;gBACR,KAAK,QAAQ;oBACX,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC3E,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACjC,KAAK,GAAG,eAAe,SAAS,KAAK,OAAO,aAAa,MAAM,eAAe,CAAC;oBAC/E,MAAM;gBACR,KAAK,QAAQ;oBACX,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACzF,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACjC,KAAK,GAAG,UAAU,SAAS,QAAQ,SAAS,gBAAgB,MAAM,CAAC,MAAM,GAAG,CAAC,cAAc,CAAC;oBAC5F,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACvB,MAAM;gBACR,KAAK,QAAQ;oBACX,KAAK,GAAG,eAAe,SAAS,4BAA4B,CAAC;oBAC7D,MAAM,GAAG,CAAC,SAAS,CAAC,CAAC;oBACrB,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;aACxC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE1D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACd,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC;SACH;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CACpB,SAAkB,EAClB,cAAuB,EACvB,YAAoB,EACpB,iBAAyB,EACzB,SAAsC;QAEtC,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,6BAA6B;QAC7B,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEnC,yCAAyC;QACzC,IAAI,SAAS,EAAE;YACb,UAAU,CAAC,IAAI,CAAC,YAAY,SAAS,IAAI,YAAY,GAAG,CAAC,CAAC;SAC3D;QAED,IAAI,cAAc,EAAE;YAClB,UAAU,CAAC,IAAI,CAAC,YAAY,SAAS,UAAU,iBAAiB,GAAG,CAAC,CAAC;SACtE;QAED,8DAA8D;QAC9D,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,EAAE;YACjC,UAAU,CAAC,IAAI,CAAC,wBAAwB,SAAS,QAAQ,CAAC,CAAC;SAC5D;QAED,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;CACF;AA3WD,gCA2WC"}