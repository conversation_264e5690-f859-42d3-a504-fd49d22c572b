"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHandler = void 0;
const http_errors_1 = __importDefault(require("http-errors"));
exports.ErrorHandler = {
    Unauthorized: (message = "Unauthorized") => (0, http_errors_1.default)(401, message),
    NotFound: (message = "Not Found") => (0, http_errors_1.default)(404, message),
    BadRequest: (message = "Bad Request") => (0, http_errors_1.default)(400, message),
    InternalServerError: (error) => {
        const message = error instanceof Error ? error.message : "Internal Server Error";
        return (0, http_errors_1.default)(500, message);
    },
};
//# sourceMappingURL=errorHandlers.js.map