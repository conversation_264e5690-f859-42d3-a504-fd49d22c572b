"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.requireOwner = exports.requireAdmin = exports.requireAuthenticated = exports.jwtAuthMiddleware = exports.JwtAuthMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const auth_service_1 = require("../../modules/auth/auth.service");
const config_1 = __importDefault(require("../config"));
class JwtAuthMiddleware {
    constructor() {
        // Middleware to validate JWT tokens
        this.validateJwt = () => {
            return async (req, res, next) => {
                try {
                    const authHeader = req.headers['authorization'];
                    if (!authHeader || !authHeader.startsWith('Bearer ')) {
                        return res.status(401).json({
                            error: 'Access token required',
                            hint: 'Provide JWT token in Authorization header as "Bearer <token>"'
                        });
                    }
                    const token = authHeader.substring(7);
                    // Check if this looks like an API key (not a JWT)
                    if (token.startsWith('ebaas_')) {
                        return res.status(401).json({
                            error: 'JWT token expected, API key provided',
                            hint: 'Use the JWT authentication endpoint, not an API key'
                        });
                    }
                    // Verify and decode the JWT
                    const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwt.secret);
                    // Validate that the user still exists and is active
                    const user = await this.authService.getUserById(decoded.sub);
                    if (!user || !user.isActive) {
                        return res.status(401).json({
                            error: 'User account not found or inactive'
                        });
                    }
                    // Attach user info to request
                    req.user = {
                        id: user.id,
                        email: user.email,
                        role: user.role || 'authenticated',
                        workspaceId: user.workspaceId
                    };
                    req.workspaceId = user.workspaceId;
                    req.jwt = decoded;
                    next();
                }
                catch (error) {
                    if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                        return res.status(401).json({
                            error: 'Invalid access token',
                            hint: 'Token may be expired, malformed, or invalid'
                        });
                    }
                    console.error('JWT validation error:', error);
                    return res.status(500).json({
                        error: 'Authentication service unavailable'
                    });
                }
            };
        };
        // Middleware to require specific roles
        this.requireRole = (allowedRoles) => {
            return (req, res, next) => {
                if (!req.user) {
                    return res.status(401).json({
                        error: 'Authentication required'
                    });
                }
                if (!allowedRoles.includes(req.user.role)) {
                    return res.status(403).json({
                        error: `Access denied. Required roles: ${allowedRoles.join(', ')}`,
                        hint: `Your role '${req.user.role}' does not have permission for this operation`
                    });
                }
                next();
            };
        };
        // Middleware to ensure workspace isolation for JWT
        this.enforceWorkspaceIsolation = () => {
            return (req, res, next) => {
                if (!req.user) {
                    return res.status(401).json({
                        error: 'Authentication required'
                    });
                }
                const requestWorkspaceId = req.query.workspaceId || req.body.workspaceId || req.params.workspaceId;
                if (requestWorkspaceId && requestWorkspaceId !== req.user.workspaceId) {
                    return res.status(403).json({
                        error: 'Access denied. Workspace mismatch',
                        hint: 'User can only access resources in their assigned workspace'
                    });
                }
                // Ensure workspace ID is available in request
                if (!requestWorkspaceId) {
                    req.query.workspaceId = req.user.workspaceId;
                    req.body = { ...req.body, workspaceId: req.user.workspaceId };
                }
                next();
            };
        };
        // Optional JWT middleware - doesn't fail if no token provided
        this.optionalJwt = () => {
            return async (req, res, next) => {
                try {
                    const authHeader = req.headers['authorization'];
                    if (!authHeader || !authHeader.startsWith('Bearer ')) {
                        return next(); // No token provided, continue without user context
                    }
                    const token = authHeader.substring(7);
                    // Skip if this looks like an API key
                    if (token.startsWith('ebaas_')) {
                        return next();
                    }
                    // Try to verify and decode the JWT
                    const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwt.secret);
                    const user = await this.authService.getUserById(decoded.sub);
                    if (user && user.isActive) {
                        req.user = {
                            id: user.id,
                            email: user.email,
                            role: user.role || 'authenticated',
                            workspaceId: user.workspaceId
                        };
                        req.workspaceId = user.workspaceId;
                        req.jwt = decoded;
                    }
                    next();
                }
                catch (error) {
                    // If JWT is invalid, just continue without user context
                    next();
                }
            };
        };
        // Create RLS context for database queries
        this.createRlsContext = () => {
            return (req, res, next) => {
                if (!req.user && !req.jwt) {
                    return next(); // No authentication context available
                }
                // Create JWT-compatible context for RLS policies
                const rlsContext = {
                    'request.jwt.claims': {
                        sub: req.user?.id || req.jwt?.sub,
                        email: req.user?.email || req.jwt?.email,
                        role: req.user?.role || req.jwt?.role || 'anon',
                        workspace_id: req.user?.workspaceId || req.jwt?.workspaceId,
                        aud: req.jwt?.aud || 'authenticated',
                        iss: req.jwt?.iss || config_1.default.jwt.issuer || 'ebaas'
                    }
                };
                // Attach RLS context to request for use in database operations
                req.rlsContext = rlsContext;
                next();
            };
        };
        this.authService = new auth_service_1.AuthService();
    }
}
exports.JwtAuthMiddleware = JwtAuthMiddleware;
// Create singleton instance
exports.jwtAuthMiddleware = new JwtAuthMiddleware();
// Export commonly used middleware combinations
exports.requireAuthenticated = [
    exports.jwtAuthMiddleware.validateJwt(),
    exports.jwtAuthMiddleware.enforceWorkspaceIsolation(),
    exports.jwtAuthMiddleware.createRlsContext()
];
exports.requireAdmin = [
    exports.jwtAuthMiddleware.validateJwt(),
    exports.jwtAuthMiddleware.requireRole(['admin', 'owner']),
    exports.jwtAuthMiddleware.enforceWorkspaceIsolation(),
    exports.jwtAuthMiddleware.createRlsContext()
];
exports.requireOwner = [
    exports.jwtAuthMiddleware.validateJwt(),
    exports.jwtAuthMiddleware.requireRole(['owner']),
    exports.jwtAuthMiddleware.enforceWorkspaceIsolation(),
    exports.jwtAuthMiddleware.createRlsContext()
];
exports.optionalAuth = [
    exports.jwtAuthMiddleware.optionalJwt(),
    exports.jwtAuthMiddleware.createRlsContext()
];
//# sourceMappingURL=jwtAuth.middleware.js.map