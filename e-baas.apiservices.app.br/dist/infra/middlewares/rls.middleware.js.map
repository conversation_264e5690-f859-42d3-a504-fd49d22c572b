{"version": 3, "file": "rls.middleware.js", "sourceRoot": "", "sources": ["../../../src/infra/middlewares/rls.middleware.ts"], "names": [], "mappings": ";;;AAEA,yDAAwD;AAYxD;;;GAGG;AACI,MAAM,eAAe,GAAG,KAAK,EAClC,GAAe,EACf,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI;QACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yCAAyC,EAAE,CAAC,CAAC;SACnF;QAED,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAErE,wCAAwC;QACxC,MAAM,UAAU,GAAG,2BAAa,CAAC,OAAO,CAAC,UAAU,CAAC;QAEpD,IAAI;YACF,wCAAwC;YACxC,MAAM,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEhC,wDAAwD;YACxD,MAAM,UAAU,CAAC,KAAK,CAAC,oCAAoC,MAAM,GAAG,CAAC,CAAC;YACtE,MAAM,UAAU,CAAC,KAAK,CAAC,yCAAyC,WAAW,GAAG,CAAC,CAAC;YAChF,MAAM,UAAU,CAAC,KAAK,CAAC,sCAAsC,IAAI,GAAG,CAAC,CAAC;YAEtE,wCAAwC;YACxC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,MAAM,UAAU,CAAC,KAAK,CAAC,wCAAwC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aAChG;YAED,2BAA2B;YAC3B,MAAM,UAAU,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAEhD,OAAO,CAAC,GAAG,CAAC,qCAAqC,MAAM,iBAAiB,WAAW,EAAE,CAAC,CAAC;YAEvF,oCAAoC;YACpC,GAAG,CAAC,UAAU,GAAG;gBACf,MAAM;gBACN,WAAW;gBACX,IAAI;gBACJ,WAAW;gBACX,QAAQ,EAAE,EAAE,CAAC,kDAAkD;aAChE,CAAC;YAEF,sCAAsC;YACtC,OAAO,IAAI,EAAE,CAAC;SAEf;QAAC,OAAO,QAAQ,EAAE;YACjB,2BAA2B;YAC3B,MAAM,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACnC,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAAC;YAC1D,MAAM,QAAQ,CAAC;SAChB;KAEF;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,kCAAkC;YACzC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAClE,CAAC,CAAC;KACJ;AACH,CAAC,CAAC;AA5DW,QAAA,eAAe,mBA4D1B;AAEF;;GAEG;AACI,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAe,EACf,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI;QACF,MAAM,UAAU,GAAG,2BAAa,CAAC,OAAO,CAAC,UAAU,CAAC;QAEpD,6BAA6B;QAC7B,MAAM,UAAU,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACpD,MAAM,UAAU,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACzD,MAAM,UAAU,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACtD,MAAM,UAAU,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACxD,MAAM,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAE7C,sBAAsB;QACtB,MAAM,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEjC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEzC,OAAO,IAAI,EAAE,CAAC;KACf;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QAC3C,qDAAqD;QACrD,OAAO,IAAI,EAAE,CAAC;KACf;AACH,CAAC,CAAC;AA1BW,QAAA,iBAAiB,qBA0B5B;AAEF;;GAEG;AACI,MAAM,iBAAiB,GAAG,CAAC,kBAA0B,EAAE,EAAE;IAC9D,OAAO,CAAC,GAAe,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC5D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;SACnE;QAED,MAAM,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;QAEnD,gDAAgD;QAChD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YACjD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,0BAA0B;gBACjC,QAAQ,EAAE,kBAAkB;gBAC5B,eAAe;aAChB,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AAnBW,QAAA,iBAAiB,qBAmB5B;AAEF;;GAEG;AACI,MAAM,WAAW,GAAG,CAAC,YAAoB,EAAE,EAAE;IAClD,OAAO,CAAC,GAAe,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC5D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;SACnE;QAED,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;YAClC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,mBAAmB;gBAC1B,QAAQ,EAAE,YAAY;gBACtB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;aACxB,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AAhBW,QAAA,WAAW,eAgBtB;AAEF;;GAEG;AACI,MAAM,sBAAsB,GAAG,CAAC,GAAe,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC3F,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;QACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;KACnE;IAED,MAAM,oBAAoB,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC;IAErG,IAAI,oBAAoB,IAAI,oBAAoB,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;QACzE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,4BAA4B;YACnC,kBAAkB,EAAE,oBAAoB;YACxC,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,WAAW;SACpC,CAAC,CAAC;KACJ;IAED,OAAO,IAAI,EAAE,CAAC;AAChB,CAAC,CAAC;AAhBW,QAAA,sBAAsB,0BAgBjC;AAEF;;GAEG;AACI,MAAM,eAAe,GAAG,KAAK,EAClC,SAAiB,EACjB,UAAkB,EAClB,SAAoD,EACpD,SAAiB,EACF,EAAE;IACjB,MAAM,UAAU,GAAG,2BAAa,CAAC,OAAO,CAAC,UAAU,CAAC;IAEpD,IAAI;QACF,oCAAoC;QACpC,MAAM,cAAc,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;KAG7C,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;QAE5B,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,+BAA+B;YAC/B,MAAM,UAAU,CAAC,KAAK,CAAC;gCACG,UAAU,OAAO,SAAS;OACnD,CAAC,CAAC;SACJ;QAED,sBAAsB;QACtB,MAAM,UAAU,CAAC,KAAK,CAAC;sBACL,UAAU,OAAO,SAAS;YACpC,SAAS;eACN,SAAS;KACnB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,wBAAwB,SAAS,GAAG,CAAC,CAAC;KAC9E;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,kCAAkC,UAAU,IAAI,EAAE,KAAK,CAAC,CAAC;QACvE,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CAAC;AAlCW,QAAA,eAAe,mBAkC1B;AAEF;;GAEG;AACI,MAAM,SAAS,GAAG,KAAK,EAAE,SAAiB,EAAiB,EAAE;IAClE,MAAM,UAAU,GAAG,2BAAa,CAAC,OAAO,CAAC,UAAU,CAAC;IAEpD,IAAI;QACF,MAAM,UAAU,CAAC,KAAK,CAAC,eAAe,SAAS,4BAA4B,CAAC,CAAC;QAC7E,OAAO,CAAC,GAAG,CAAC,4BAA4B,SAAS,GAAG,CAAC,CAAC;KACvD;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,qCAAqC,SAAS,IAAI,EAAE,KAAK,CAAC,CAAC;QACzE,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CAAC;AAVW,QAAA,SAAS,aAUpB;AAEF;;GAEG;AACI,MAAM,UAAU,GAAG,KAAK,EAAE,SAAiB,EAAiB,EAAE;IACnE,MAAM,UAAU,GAAG,2BAAa,CAAC,OAAO,CAAC,UAAU,CAAC;IAEpD,IAAI;QACF,MAAM,UAAU,CAAC,KAAK,CAAC,eAAe,SAAS,6BAA6B,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,6BAA6B,SAAS,GAAG,CAAC,CAAC;KACxD;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,sCAAsC,SAAS,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1E,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CAAC;AAVW,QAAA,UAAU,cAUrB"}