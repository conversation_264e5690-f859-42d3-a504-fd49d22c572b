{"version": 3, "file": "auth.middleware.js", "sourceRoot": "", "sources": ["../../../src/infra/middlewares/auth.middleware.ts"], "names": [], "mappings": ";;;AAGA,8CAAiE;AACjE,kEAA8D;AAsBvD,MAAM,eAAe,GAAG,KAAK,EAClC,GAAgB,EAChB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI;QACF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,GAAG;iBACP,MAAM,CAAC,GAAG,CAAC;iBACX,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC,CAAC;SACxD;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;SACvD;QAED,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;QAE9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC;SAC9D;QAED,IAAI;YACF,MAAM,WAAW,GAAG,IAAI,0BAAW,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,WAAW,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAErD,6CAA6C;YAC7C,MAAM,IAAI,GAAG,MAAM,2BAAc,CAAC,OAAO,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE;gBACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;aAC1D;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC;aAC5D;YAED,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;aAC1D;YAED,sDAAsD;YACtD,GAAG,CAAC,IAAI,GAAG;gBACT,EAAE,EAAE,OAAO,CAAC,GAAG;gBACf,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW;gBACpD,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;gBAC/B,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;gBACtC,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,GAAG,EAAE,OAAO,CAAC,GAAG;aACjB,CAAC;YAEF,wCAAwC;YACxC,IAAI,OAAO,CAAC,WAAW,EAAE;gBACvB,GAAG,CAAC,SAAS,GAAG;oBACd,EAAE,EAAE,OAAO,CAAC,WAAW;iBACxB,CAAC;aACH;YAED,OAAO,IAAI,EAAE,CAAC;SACf;QAAC,OAAO,QAAQ,EAAE;YACjB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;SACpE;KACF;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;KACjE;AACH,CAAC,CAAC;AAzEW,QAAA,eAAe,mBAyE1B;AAEK,MAAM,kBAAkB,GAAG,KAAK,EACrC,GAAgB,EAChB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI;QACF,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;QAElD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;SAC/D;QAED,mCAAmC;QACnC,MAAM,YAAY,GAAG,MAAM,6BAAgB,CAAC,OAAO,CAAC;YAClD,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;YACjC,SAAS,EAAE,CAAC,WAAW,CAAC;SACzB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;SAC3D;QAED,sCAAsC;QACtC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,EAAE;YACpC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;QAED,uCAAuC;QACvC,IAAI,YAAY,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG,YAAY,CAAC,SAAS,EAAE;YACjE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;SAC3D;QAED,+CAA+C;QAC/C,GAAG,CAAC,SAAS,GAAG;YACd,EAAE,EAAE,YAAY,CAAC,WAAW;SAC7B,CAAC;QAEF,OAAO,IAAI,EAAE,CAAC;KACf;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;KACjE;AACH,CAAC,CAAC;AAzCW,QAAA,kBAAkB,sBAyC7B;AAEF,mDAAmD;AAC5C,MAAM,iBAAiB,GAAG,CAAC,UAAkB,EAAE,EAAE;IACtD,OAAO,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC7D,IAAI;YACF,8CAA8C;YAC9C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;aACnE;YAED,gEAAgE;YAChE,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBACrE,OAAO,IAAI,EAAE,CAAC;aACf;YAED,kDAAkD;YAClD,OAAO,GAAG;iBACP,MAAM,CAAC,GAAG,CAAC;iBACX,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,UAAU,YAAY,EAAE,CAAC,CAAC;SAC3D;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACjE;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AArBW,QAAA,iBAAiB,qBAqB5B"}