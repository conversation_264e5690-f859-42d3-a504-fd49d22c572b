"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requirePermission = exports.authenticateApiKey = exports.authenticateJWT = void 0;
const repository_1 = require("../repository");
const auth_service_1 = require("../../modules/auth/auth.service");
const authenticateJWT = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            return res
                .status(401)
                .json({ error: "Authorization header is required" });
        }
        const parts = authHeader.split(" ");
        if (parts.length !== 2) {
            return res.status(401).json({ error: "Token error" });
        }
        const [scheme, token] = parts;
        if (!/^Bearer$/i.test(scheme)) {
            return res.status(401).json({ error: "Token malformatted" });
        }
        try {
            const authService = new auth_service_1.AuthService();
            const decoded = authService.verifyAccessToken(token);
            // Verificar se o usuário existe e está ativo
            const user = await repository_1.userRepository.findOne({
                where: { id: decoded.sub },
            });
            if (!user) {
                return res.status(401).json({ error: "User not found" });
            }
            if (!user.isActive) {
                return res.status(401).json({ error: "User is inactive" });
            }
            if (user.isBanned) {
                return res.status(401).json({ error: "User is banned" });
            }
            // Adiciona o usuário completo ao objeto de requisição
            req.user = {
                id: decoded.sub,
                email: decoded.email,
                workspaceId: decoded.workspaceId || user.workspaceId,
                role: decoded.role || user.role,
                permissions: decoded.permissions || [],
                iat: decoded.iat,
                exp: decoded.exp,
            };
            // Adiciona workspace info se disponível
            if (decoded.workspaceId) {
                req.workspace = {
                    id: decoded.workspaceId,
                };
            }
            return next();
        }
        catch (jwtError) {
            return res.status(401).json({ error: "Invalid or expired token" });
        }
    }
    catch (error) {
        console.error("Auth middleware error:", error);
        return res.status(500).json({ error: "Internal server error" });
    }
};
exports.authenticateJWT = authenticateJWT;
const authenticateApiKey = async (req, res, next) => {
    try {
        const apiKey = req.headers["x-api-key"];
        if (!apiKey) {
            return res.status(401).json({ error: "API Key is required" });
        }
        // Buscar API Key no banco de dados
        const apiKeyEntity = await repository_1.apiKeyRepository.findOne({
            where: { apiKey, isActive: true },
            relations: ["workspace"],
        });
        if (!apiKeyEntity) {
            return res.status(401).json({ error: "Invalid API Key" });
        }
        // Verificar se o workspace está ativo
        if (!apiKeyEntity.workspace.isActive) {
            return res.status(401).json({ error: "Workspace is inactive" });
        }
        // Verificar se a API Key está expirada
        if (apiKeyEntity.expiresAt && new Date() > apiKeyEntity.expiresAt) {
            return res.status(401).json({ error: "API Key expired" });
        }
        // Adiciona o workspace ao objeto de requisição
        req.workspace = {
            id: apiKeyEntity.workspaceId,
        };
        return next();
    }
    catch (error) {
        return res.status(500).json({ error: "Internal server error" });
    }
};
exports.authenticateApiKey = authenticateApiKey;
// Middleware para verificar permissões específicas
const requirePermission = (permission) => {
    return (req, res, next) => {
        try {
            // Se não há usuário autenticado, retorna erro
            if (!req.user) {
                return res.status(401).json({ error: "Authentication required" });
            }
            // Se o usuário tem permissões e a permissão específica, permite
            if (req.user.permissions && req.user.permissions.includes(permission)) {
                return next();
            }
            // Se não tem a permissão específica, retorna erro
            return res
                .status(403)
                .json({ error: `Permission '${permission}' required` });
        }
        catch (error) {
            return res.status(500).json({ error: "Internal server error" });
        }
    };
};
exports.requirePermission = requirePermission;
//# sourceMappingURL=auth.middleware.js.map