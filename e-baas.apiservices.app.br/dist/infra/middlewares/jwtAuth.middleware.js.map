{"version": 3, "file": "jwtAuth.middleware.js", "sourceRoot": "", "sources": ["../../../src/infra/middlewares/jwtAuth.middleware.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,kEAA8D;AAC9D,uDAA+B;AAwB/B,MAAa,iBAAiB;IAG5B;QAIA,oCAAoC;QACpC,gBAAW,GAAG,GAAG,EAAE;YACjB,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBAC5E,IAAI;oBACF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAW,CAAC;oBAE1D,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;wBACpD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BAC1B,KAAK,EAAE,uBAAuB;4BAC9B,IAAI,EAAE,+DAA+D;yBACtE,CAAC,CAAC;qBACJ;oBAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAEtC,kDAAkD;oBAClD,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;wBAC9B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BAC1B,KAAK,EAAE,sCAAsC;4BAC7C,IAAI,EAAE,qDAAqD;yBAC5D,CAAC,CAAC;qBACJ;oBAED,4BAA4B;oBAC5B,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAe,CAAC;oBAEnE,oDAAoD;oBACpD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBAC7D,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;wBAC3B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BAC1B,KAAK,EAAE,oCAAoC;yBAC5C,CAAC,CAAC;qBACJ;oBAED,8BAA8B;oBAC9B,GAAG,CAAC,IAAI,GAAG;wBACT,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,eAAe;wBAClC,WAAW,EAAE,IAAI,CAAC,WAAW;qBAC9B,CAAC;oBACF,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;oBACnC,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC;oBAElB,IAAI,EAAE,CAAC;iBACR;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE;wBAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BAC1B,KAAK,EAAE,sBAAsB;4BAC7B,IAAI,EAAE,6CAA6C;yBACpD,CAAC,CAAC;qBACJ;oBAED,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;oBAC9C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,oCAAoC;qBAC5C,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,uCAAuC;QACvC,gBAAW,GAAG,CAAC,YAAsB,EAAE,EAAE;YACvC,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBACtE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;oBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,yBAAyB;qBACjC,CAAC,CAAC;iBACJ;gBAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACzC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,kCAAkC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBAClE,IAAI,EAAE,cAAc,GAAG,CAAC,IAAI,CAAC,IAAI,+CAA+C;qBACjF,CAAC,CAAC;iBACJ;gBAED,IAAI,EAAE,CAAC;YACT,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,mDAAmD;QACnD,8BAAyB,GAAG,GAAG,EAAE;YAC/B,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBACtE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;oBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,yBAAyB;qBACjC,CAAC,CAAC;iBACJ;gBAED,MAAM,kBAAkB,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC;gBAEnG,IAAI,kBAAkB,IAAI,kBAAkB,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;oBACrE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,mCAAmC;wBAC1C,IAAI,EAAE,4DAA4D;qBACnE,CAAC,CAAC;iBACJ;gBAED,8CAA8C;gBAC9C,IAAI,CAAC,kBAAkB,EAAE;oBACvB,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;oBAC7C,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;iBAC/D;gBAED,IAAI,EAAE,CAAC;YACT,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,8DAA8D;QAC9D,gBAAW,GAAG,GAAG,EAAE;YACjB,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBAC5E,IAAI;oBACF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAW,CAAC;oBAE1D,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;wBACpD,OAAO,IAAI,EAAE,CAAC,CAAC,mDAAmD;qBACnE;oBAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAEtC,qCAAqC;oBACrC,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;wBAC9B,OAAO,IAAI,EAAE,CAAC;qBACf;oBAED,mCAAmC;oBACnC,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAe,CAAC;oBACnE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBAE7D,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACzB,GAAG,CAAC,IAAI,GAAG;4BACT,EAAE,EAAE,IAAI,CAAC,EAAE;4BACX,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,eAAe;4BAClC,WAAW,EAAE,IAAI,CAAC,WAAW;yBAC9B,CAAC;wBACF,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;wBACnC,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC;qBACnB;oBAED,IAAI,EAAE,CAAC;iBACR;gBAAC,OAAO,KAAK,EAAE;oBACd,wDAAwD;oBACxD,IAAI,EAAE,CAAC;iBACR;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,0CAA0C;QAC1C,qBAAgB,GAAG,GAAG,EAAE;YACtB,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBACtE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;oBACzB,OAAO,IAAI,EAAE,CAAC,CAAC,sCAAsC;iBACtD;gBAED,iDAAiD;gBACjD,MAAM,UAAU,GAAG;oBACjB,oBAAoB,EAAE;wBACpB,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,GAAG,CAAC,GAAG,EAAE,GAAG;wBACjC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,IAAI,GAAG,CAAC,GAAG,EAAE,KAAK;wBACxC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,IAAI,MAAM;wBAC/C,YAAY,EAAE,GAAG,CAAC,IAAI,EAAE,WAAW,IAAI,GAAG,CAAC,GAAG,EAAE,WAAW;wBAC3D,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,eAAe;wBACpC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,gBAAM,CAAC,GAAG,CAAC,MAAM,IAAI,OAAO;qBAClD;iBACF,CAAC;gBAEF,+DAA+D;gBAC9D,GAAW,CAAC,UAAU,GAAG,UAAU,CAAC;gBAErC,IAAI,EAAE,CAAC;YACT,CAAC,CAAC;QACJ,CAAC,CAAC;QAhLA,IAAI,CAAC,WAAW,GAAG,IAAI,0BAAW,EAAE,CAAC;IACvC,CAAC;CAgLF;AArLD,8CAqLC;AAED,4BAA4B;AACf,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;AAEzD,+CAA+C;AAClC,QAAA,oBAAoB,GAAG;IAClC,yBAAiB,CAAC,WAAW,EAAE;IAC/B,yBAAiB,CAAC,yBAAyB,EAAE;IAC7C,yBAAiB,CAAC,gBAAgB,EAAE;CACrC,CAAC;AAEW,QAAA,YAAY,GAAG;IAC1B,yBAAiB,CAAC,WAAW,EAAE;IAC/B,yBAAiB,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACjD,yBAAiB,CAAC,yBAAyB,EAAE;IAC7C,yBAAiB,CAAC,gBAAgB,EAAE;CACrC,CAAC;AAEW,QAAA,YAAY,GAAG;IAC1B,yBAAiB,CAAC,WAAW,EAAE;IAC/B,yBAAiB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;IACxC,yBAAiB,CAAC,yBAAyB,EAAE;IAC7C,yBAAiB,CAAC,gBAAgB,EAAE;CACrC,CAAC;AAEW,QAAA,YAAY,GAAG;IAC1B,yBAAiB,CAAC,WAAW,EAAE;IAC/B,yBAAiB,CAAC,gBAAgB,EAAE;CACrC,CAAC"}