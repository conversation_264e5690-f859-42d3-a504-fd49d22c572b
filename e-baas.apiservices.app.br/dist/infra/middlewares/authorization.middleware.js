"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminAuthorization = exports.databaseAccessAuthorization = void 0;
const data_source_1 = require("../../data-source");
const ApiKey_entity_1 = require("../../modules/api-keys/entity/ApiKey.entity");
const DatabaseConfig_entity_1 = require("../../modules/database-configs/entity/DatabaseConfig.entity");
const http_errors_1 = __importDefault(require("http-errors"));
/**
 * Middleware que verifica se o usuário tem permissão para acessar o banco de dados
 * Deve ser usado em rotas que acessam banco de dados específicos
 */
const databaseAccessAuthorization = async (req, res, next) => {
    try {
        const apiKey = req.headers["x-api-key"];
        const databaseId = req.params.databaseId || req.body.databaseId;
        if (!apiKey) {
            throw (0, http_errors_1.default)(401, "API Key não fornecida");
        }
        if (!databaseId) {
            throw (0, http_errors_1.default)(400, "ID do banco de dados não fornecido");
        }
        // Verificar se a API Key é válida e obter o workspace associado
        const apiKeyRepository = data_source_1.AppDataSource.getRepository(ApiKey_entity_1.ApiKey);
        const foundApiKey = await apiKeyRepository.findOne({
            where: { apiKey, isActive: true },
            relations: ["workspace"],
        });
        if (!foundApiKey) {
            throw (0, http_errors_1.default)(401, "API Key inválida ou inativa");
        }
        // Verificar se o banco de dados pertence ao workspace da API Key
        const dbConfigRepository = data_source_1.AppDataSource.getRepository(DatabaseConfig_entity_1.DatabaseConfig);
        const databaseConfig = await dbConfigRepository.findOne({
            where: {
                id: databaseId,
                workspaceId: foundApiKey.workspaceId,
                isActive: true,
            },
        });
        if (!databaseConfig) {
            throw (0, http_errors_1.default)(403, "Acesso negado a este banco de dados");
        }
        // Adicionar o workspace e configuração do banco ao objeto de requisição
        req.workspace = foundApiKey.workspace;
        req.databaseConfig = databaseConfig;
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.databaseAccessAuthorization = databaseAccessAuthorization;
/**
 * Middleware que verifica se o usuário é admin
 * Admins podem acessar todos os bancos de dados
 */
const adminAuthorization = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            throw (0, http_errors_1.default)(401, "Usuário não autenticado");
        }
        // Verificar se o usuário é admin (pode ser implementado conforme necessário)
        const isAdmin = req.user?.isAdmin === true;
        if (!isAdmin) {
            throw (0, http_errors_1.default)(403, "Acesso restrito a administradores");
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.adminAuthorization = adminAuthorization;
//# sourceMappingURL=authorization.middleware.js.map