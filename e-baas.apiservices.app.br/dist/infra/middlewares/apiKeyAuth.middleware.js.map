{"version": 3, "file": "apiKeyAuth.middleware.js", "sourceRoot": "", "sources": ["../../../src/infra/middlewares/apiKeyAuth.middleware.ts"], "names": [], "mappings": ";;;AACA,4EAAuE;AACvE,wEAAiF;AAYjF,MAAa,oBAAoB;IAG/B;QAIA,kCAAkC;QAClC,mBAAc,GAAG,CAAC,aAA2B,EAAE,EAAE;YAC/C,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBAC5E,IAAI;oBACF,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAW,CAAC;oBACrD,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAW,CAAC;oBAE1D,IAAI,YAAgC,CAAC;oBAErC,4EAA4E;oBAC5E,IAAI,YAAY,EAAE;wBAChB,YAAY,GAAG,YAAY,CAAC;qBAC7B;yBAAM,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;wBACzD,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBACtC,0CAA0C;wBAC1C,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;4BAC9B,YAAY,GAAG,KAAK,CAAC;yBACtB;qBACF;oBAED,IAAI,CAAC,YAAY,EAAE;wBACjB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BAC1B,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,uDAAuD;yBAC9D,CAAC,CAAC;qBACJ;oBAED,2BAA2B;oBAC3B,MAAM,SAAS,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC;oBACzD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAgB,CAAC;oBAE5C,uBAAuB;oBACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CACtD,YAAY,EACZ,aAAa,EACb,SAAS,EACT,MAAM,CACP,CAAC;oBAEF,IAAI,CAAC,QAAQ,EAAE;wBACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BAC1B,KAAK,EAAE,4BAA4B;4BACnC,IAAI,EAAE,qEAAqE;yBAC5E,CAAC,CAAC;qBACJ;oBAED,6BAA6B;oBAC7B,GAAG,CAAC,MAAM,GAAG;wBACX,EAAE,EAAE,QAAQ,CAAC,EAAE;wBACf,WAAW,EAAE,QAAQ,CAAC,WAAW;wBACjC,IAAI,EAAE,QAAQ,CAAC,IAAI;wBACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;qBACxB,CAAC;oBACF,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;oBAEvC,IAAI,EAAE,CAAC;iBACR;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;oBAClD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,oCAAoC;qBAC5C,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,8CAA8C;QAC9C,mBAAc,GAAG,CAAC,YAA0B,EAAE,EAAE;YAC9C,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBACtE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;oBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,kBAAkB;qBAC1B,CAAC,CAAC;iBACJ;gBAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;oBAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,sCAAsC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACtE,IAAI,EAAE,kBAAkB,GAAG,CAAC,MAAM,CAAC,IAAI,+CAA+C;qBACvF,CAAC,CAAC;iBACJ;gBAED,IAAI,EAAE,CAAC;YACT,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,wCAAwC;QACxC,iBAAY,GAAG,CAAC,cAA6B,EAAE,EAAE;YAC/C,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBACtE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;oBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,kBAAkB;qBAC1B,CAAC,CAAC;iBACJ;gBAED,MAAM,gBAAgB,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACnD,GAAG,CAAC,MAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,yBAAW,CAAC,GAAG,CAAC;oBAC5C,GAAG,CAAC,MAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CACnC,CAAC;gBAEF,IAAI,CAAC,gBAAgB,EAAE;oBACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,mCAAmC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACrE,IAAI,EAAE,qDAAqD;qBAC5D,CAAC,CAAC;iBACJ;gBAED,IAAI,EAAE,CAAC;YACT,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,2CAA2C;QAC3C,8BAAyB,GAAG,GAAG,EAAE;YAC/B,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;gBACtE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;oBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,kBAAkB;qBAC1B,CAAC,CAAC;iBACJ;gBAED,MAAM,kBAAkB,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC;gBAEnG,IAAI,kBAAkB,IAAI,kBAAkB,KAAK,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE;oBACvE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,mCAAmC;wBAC1C,IAAI,EAAE,6DAA6D;qBACpE,CAAC,CAAC;iBACJ;gBAED,8CAA8C;gBAC9C,IAAI,CAAC,kBAAkB,EAAE;oBACvB,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC;oBAC/C,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;iBACjE;gBAED,IAAI,EAAE,CAAC;YACT,CAAC,CAAC;QACJ,CAAC,CAAC;QA3IA,IAAI,CAAC,aAAa,GAAG,IAAI,+BAAa,EAAE,CAAC;IAC3C,CAAC;CA2IF;AAhJD,oDAgJC;AAED,4BAA4B;AACf,QAAA,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAE/D,+CAA+C;AAClC,QAAA,cAAc,GAAG;IAC5B,4BAAoB,CAAC,cAAc,EAAE;IACrC,4BAAoB,CAAC,cAAc,CAAC,CAAC,wBAAU,CAAC,IAAI,EAAE,wBAAU,CAAC,aAAa,EAAE,wBAAU,CAAC,YAAY,CAAC,CAAC;CAC1G,CAAC;AAEW,QAAA,uBAAuB,GAAG;IACrC,4BAAoB,CAAC,cAAc,EAAE;IACrC,4BAAoB,CAAC,cAAc,CAAC,CAAC,wBAAU,CAAC,aAAa,EAAE,wBAAU,CAAC,YAAY,CAAC,CAAC;CACzF,CAAC;AAEW,QAAA,qBAAqB,GAAG;IACnC,4BAAoB,CAAC,cAAc,EAAE;IACrC,4BAAoB,CAAC,cAAc,CAAC,CAAC,wBAAU,CAAC,YAAY,CAAC,CAAC;CAC/D,CAAC;AAEW,QAAA,iBAAiB,GAAG;IAC/B,4BAAoB,CAAC,cAAc,CAAC,yBAAW,CAAC,IAAI,CAAC;IACrD,4BAAoB,CAAC,yBAAyB,EAAE;CACjD,CAAC;AAEW,QAAA,kBAAkB,GAAG;IAChC,4BAAoB,CAAC,cAAc,CAAC,yBAAW,CAAC,KAAK,CAAC;IACtD,4BAAoB,CAAC,yBAAyB,EAAE;CACjD,CAAC;AAEW,QAAA,mBAAmB,GAAG;IACjC,4BAAoB,CAAC,cAAc,CAAC,yBAAW,CAAC,MAAM,CAAC;IACvD,4BAAoB,CAAC,yBAAyB,EAAE;CACjD,CAAC;AAEW,QAAA,mBAAmB,GAAG;IACjC,4BAAoB,CAAC,cAAc,CAAC,yBAAW,CAAC,MAAM,CAAC;IACvD,4BAAoB,CAAC,yBAAyB,EAAE;IAChD,4BAAoB,CAAC,cAAc,CAAC,CAAC,wBAAU,CAAC,YAAY,CAAC,CAAC;CAC/D,CAAC"}