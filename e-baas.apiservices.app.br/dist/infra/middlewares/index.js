"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requirePermission = exports.authenticateApiKey = exports.authenticateJWT = void 0;
const auth_middleware_1 = require("./auth.middleware");
Object.defineProperty(exports, "authenticateJWT", { enumerable: true, get: function () { return auth_middleware_1.authenticateJWT; } });
Object.defineProperty(exports, "authenticateApiKey", { enumerable: true, get: function () { return auth_middleware_1.authenticateApiKey; } });
Object.defineProperty(exports, "requirePermission", { enumerable: true, get: function () { return auth_middleware_1.requirePermission; } });
//# sourceMappingURL=index.js.map