"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireSchemaAccess = exports.requireDeleteAccess = exports.requireWriteAccess = exports.requireReadAccess = exports.requireServiceRoleKey = exports.requireAuthenticatedKey = exports.requireAnonKey = exports.apiKeyAuthMiddleware = exports.ApiKeyAuthMiddleware = void 0;
const api_key_service_1 = require("../../modules/api-keys/api-key.service");
const api_key_dto_1 = require("../../modules/api-keys/dto/api-key.dto");
class ApiKeyAuthMiddleware {
    constructor() {
        // Middleware to validate API keys
        this.validateApiKey = (requiredScope) => {
            return async (req, res, next) => {
                try {
                    const apiKeyHeader = req.headers['apikey'];
                    const authHeader = req.headers['authorization'];
                    let apiKeyString;
                    // Check for API key in headers (priority: apikey header, then Bearer token)
                    if (apiKeyHeader) {
                        apiKeyString = apiKeyHeader;
                    }
                    else if (authHeader && authHeader.startsWith('Bearer ')) {
                        const token = authHeader.substring(7);
                        // Check if this is an API key (not a JWT)
                        if (token.startsWith('ebaas_')) {
                            apiKeyString = token;
                        }
                    }
                    if (!apiKeyString) {
                        return res.status(401).json({
                            error: 'API key required',
                            hint: 'Provide API key in "apikey" header or as Bearer token'
                        });
                    }
                    // Get client IP and origin
                    const ipAddress = req.ip || req.connection.remoteAddress;
                    const origin = req.headers.origin;
                    // Validate the API key
                    const validKey = await this.apiKeyService.validateApiKey(apiKeyString, requiredScope, ipAddress, origin);
                    if (!validKey) {
                        return res.status(401).json({
                            error: 'Invalid or expired API key',
                            hint: 'Check that your API key is correct and has the required permissions'
                        });
                    }
                    // Attach key info to request
                    req.apiKey = {
                        id: validKey.id,
                        workspaceId: validKey.workspaceId,
                        type: validKey.type,
                        scopes: validKey.scopes
                    };
                    req.workspaceId = validKey.workspaceId;
                    next();
                }
                catch (error) {
                    console.error('API key validation error:', error);
                    return res.status(500).json({
                        error: 'Authentication service unavailable'
                    });
                }
            };
        };
        // Middleware to require specific API key type
        this.requireKeyType = (allowedTypes) => {
            return (req, res, next) => {
                if (!req.apiKey) {
                    return res.status(401).json({
                        error: 'API key required'
                    });
                }
                if (!allowedTypes.includes(req.apiKey.type)) {
                    return res.status(403).json({
                        error: `Access denied. Required key types: ${allowedTypes.join(', ')}`,
                        hint: `Your key type '${req.apiKey.type}' does not have permission for this operation`
                    });
                }
                next();
            };
        };
        // Middleware to require specific scopes
        this.requireScope = (requiredScopes) => {
            return (req, res, next) => {
                if (!req.apiKey) {
                    return res.status(401).json({
                        error: 'API key required'
                    });
                }
                const hasRequiredScope = requiredScopes.some(scope => req.apiKey.scopes.includes(api_key_dto_1.ApiKeyScope.ALL) ||
                    req.apiKey.scopes.includes(scope));
                if (!hasRequiredScope) {
                    return res.status(403).json({
                        error: `Access denied. Required scopes: ${requiredScopes.join(', ')}`,
                        hint: `Your API key does not have the required permissions`
                    });
                }
                next();
            };
        };
        // Middleware to ensure workspace isolation
        this.enforceWorkspaceIsolation = () => {
            return (req, res, next) => {
                if (!req.apiKey) {
                    return res.status(401).json({
                        error: 'API key required'
                    });
                }
                const requestWorkspaceId = req.query.workspaceId || req.body.workspaceId || req.params.workspaceId;
                if (requestWorkspaceId && requestWorkspaceId !== req.apiKey.workspaceId) {
                    return res.status(403).json({
                        error: 'Access denied. Workspace mismatch',
                        hint: 'API key can only access resources in its assigned workspace'
                    });
                }
                // Ensure workspace ID is available in request
                if (!requestWorkspaceId) {
                    req.query.workspaceId = req.apiKey.workspaceId;
                    req.body = { ...req.body, workspaceId: req.apiKey.workspaceId };
                }
                next();
            };
        };
        this.apiKeyService = new api_key_service_1.ApiKeyService();
    }
}
exports.ApiKeyAuthMiddleware = ApiKeyAuthMiddleware;
// Create singleton instance
exports.apiKeyAuthMiddleware = new ApiKeyAuthMiddleware();
// Export commonly used middleware combinations
exports.requireAnonKey = [
    exports.apiKeyAuthMiddleware.validateApiKey(),
    exports.apiKeyAuthMiddleware.requireKeyType([api_key_dto_1.ApiKeyType.ANON, api_key_dto_1.ApiKeyType.AUTHENTICATED, api_key_dto_1.ApiKeyType.SERVICE_ROLE])
];
exports.requireAuthenticatedKey = [
    exports.apiKeyAuthMiddleware.validateApiKey(),
    exports.apiKeyAuthMiddleware.requireKeyType([api_key_dto_1.ApiKeyType.AUTHENTICATED, api_key_dto_1.ApiKeyType.SERVICE_ROLE])
];
exports.requireServiceRoleKey = [
    exports.apiKeyAuthMiddleware.validateApiKey(),
    exports.apiKeyAuthMiddleware.requireKeyType([api_key_dto_1.ApiKeyType.SERVICE_ROLE])
];
exports.requireReadAccess = [
    exports.apiKeyAuthMiddleware.validateApiKey(api_key_dto_1.ApiKeyScope.READ),
    exports.apiKeyAuthMiddleware.enforceWorkspaceIsolation()
];
exports.requireWriteAccess = [
    exports.apiKeyAuthMiddleware.validateApiKey(api_key_dto_1.ApiKeyScope.WRITE),
    exports.apiKeyAuthMiddleware.enforceWorkspaceIsolation()
];
exports.requireDeleteAccess = [
    exports.apiKeyAuthMiddleware.validateApiKey(api_key_dto_1.ApiKeyScope.DELETE),
    exports.apiKeyAuthMiddleware.enforceWorkspaceIsolation()
];
exports.requireSchemaAccess = [
    exports.apiKeyAuthMiddleware.validateApiKey(api_key_dto_1.ApiKeyScope.SCHEMA),
    exports.apiKeyAuthMiddleware.enforceWorkspaceIsolation(),
    exports.apiKeyAuthMiddleware.requireKeyType([api_key_dto_1.ApiKeyType.SERVICE_ROLE])
];
//# sourceMappingURL=apiKeyAuth.middleware.js.map