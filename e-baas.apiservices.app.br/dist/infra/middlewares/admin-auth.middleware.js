"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scopeToWorkspace = exports.requirePermission = exports.requireWorkspaceAdmin = exports.requirePlatformAdmin = exports.requireAdmin = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("config"));
function requireAdmin(req, res, next) {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({ error: 'Authorization token required' });
        }
        const token = authHeader.substring(7);
        const jwtSecret = config_1.default.get('jwt.secret');
        const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
        // Check if user can access admin interface
        if (!decoded.can_access_admin) {
            return res.status(403).json({
                error: 'Admin access required',
                details: 'User does not have admin privileges'
            });
        }
        req.user = {
            id: decoded.user_id || decoded.sub,
            email: decoded.email,
            role: decoded.role,
            adminRole: decoded.admin_role,
            workspaceId: decoded.workspaceId || decoded.workspace_id,
            permissions: decoded.permissions || [],
            isPlatformAdmin: decoded.is_platform_admin || false,
            isWorkspaceAdmin: decoded.is_workspace_admin || false,
            canAccessAdmin: decoded.can_access_admin || false
        };
        next();
    }
    catch (error) {
        return res.status(401).json({
            error: 'Invalid or expired token',
            details: error instanceof Error ? error.message : 'Token verification failed'
        });
    }
}
exports.requireAdmin = requireAdmin;
function requirePlatformAdmin(req, res, next) {
    requireAdmin(req, res, () => {
        if (!req.user?.isPlatformAdmin) {
            return res.status(403).json({
                error: 'Platform admin access required',
                details: 'This operation requires platform administrator privileges'
            });
        }
        next();
    });
}
exports.requirePlatformAdmin = requirePlatformAdmin;
function requireWorkspaceAdmin(req, res, next) {
    requireAdmin(req, res, () => {
        if (!req.user?.isWorkspaceAdmin && !req.user?.isPlatformAdmin) {
            return res.status(403).json({
                error: 'Workspace admin access required',
                details: 'This operation requires workspace administrator privileges'
            });
        }
        next();
    });
}
exports.requireWorkspaceAdmin = requireWorkspaceAdmin;
function requirePermission(permission) {
    return (req, res, next) => {
        requireAdmin(req, res, () => {
            if (!req.user?.permissions.includes(permission) && !req.user?.isPlatformAdmin) {
                return res.status(403).json({
                    error: 'Insufficient permissions',
                    details: `Operation requires permission: ${permission}`
                });
            }
            next();
        });
    };
}
exports.requirePermission = requirePermission;
function scopeToWorkspace(req, res, next) {
    requireAdmin(req, res, () => {
        // Platform admins can access any workspace
        if (req.user?.isPlatformAdmin) {
            return next();
        }
        // Workspace admins are restricted to their workspace
        const requestedWorkspaceId = req.params.workspaceId || req.query.workspaceId || req.body.workspaceId;
        if (requestedWorkspaceId && requestedWorkspaceId !== req.user?.workspaceId) {
            return res.status(403).json({
                error: 'Access denied',
                details: 'Cannot access resources from other workspaces'
            });
        }
        // If no workspace specified, default to user's workspace
        if (!requestedWorkspaceId) {
            req.query.workspaceId = req.user?.workspaceId;
            req.body.workspaceId = req.user?.workspaceId;
        }
        next();
    });
}
exports.scopeToWorkspace = scopeToWorkspace;
//# sourceMappingURL=admin-auth.middleware.js.map