"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.disableRLS = exports.enableRLS = exports.createRLSPolicy = exports.requireWorkspaceAccess = exports.requireRole = exports.requirePermission = exports.cleanupRLSContext = exports.setupRLSContext = void 0;
const data_source_1 = require("../database/data-source");
/**
 * Middleware para configurar Row-Level Security context
 * Este middleware deve ser usado após o authenticateJWT
 */
const setupRLSContext = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({ error: "Authentication required for RLS context" });
        }
        const { id: userId, workspaceId, role, permissions = [] } = req.user;
        // Configurar contexto RLS no PostgreSQL
        const connection = data_source_1.AppDataSource.manager.connection;
        try {
            // Iniciar transação para configurar RLS
            await connection.query('BEGIN');
            // Configurar variáveis de sessão do PostgreSQL para RLS
            await connection.query(`SET LOCAL app.current_user_id = '${userId}'`);
            await connection.query(`SET LOCAL app.current_workspace_id = '${workspaceId}'`);
            await connection.query(`SET LOCAL app.current_user_role = '${role}'`);
            // Configurar permissões como array JSON
            if (permissions.length > 0) {
                await connection.query(`SET LOCAL app.current_permissions = '${JSON.stringify(permissions)}'`);
            }
            // Ativar RLS para a sessão
            await connection.query('SET row_security = on');
            console.log(`✅ RLS context configured for user ${userId} in workspace ${workspaceId}`);
            // Adicionar contexto RLS ao request
            req.rlsContext = {
                userId,
                workspaceId,
                role,
                permissions,
                policies: [] // Será preenchido pelos helpers RLS se necessário
            };
            // Continuar para o próximo middleware
            return next();
        }
        catch (rlsError) {
            // Rollback em caso de erro
            await connection.query('ROLLBACK');
            console.error('❌ Failed to setup RLS context:', rlsError);
            throw rlsError;
        }
    }
    catch (error) {
        console.error("RLS middleware error:", error);
        return res.status(500).json({
            error: "Failed to setup security context",
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.setupRLSContext = setupRLSContext;
/**
 * Middleware para limpar contexto RLS
 */
const cleanupRLSContext = async (req, res, next) => {
    try {
        const connection = data_source_1.AppDataSource.manager.connection;
        // Limpar variáveis de sessão
        await connection.query('RESET app.current_user_id');
        await connection.query('RESET app.current_workspace_id');
        await connection.query('RESET app.current_user_role');
        await connection.query('RESET app.current_permissions');
        await connection.query('RESET row_security');
        // Finalizar transação
        await connection.query('COMMIT');
        console.log('🧹 RLS context cleaned up');
        return next();
    }
    catch (error) {
        console.error("RLS cleanup error:", error);
        // Não retornar erro aqui para não quebrar a resposta
        return next();
    }
};
exports.cleanupRLSContext = cleanupRLSContext;
/**
 * Middleware para verificar permissões específicas
 */
const requirePermission = (requiredPermission) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: "Authentication required" });
        }
        const userPermissions = req.user.permissions || [];
        // Verificar se usuário tem permissão específica
        if (!userPermissions.includes(requiredPermission)) {
            return res.status(403).json({
                error: "Insufficient permissions",
                required: requiredPermission,
                userPermissions
            });
        }
        return next();
    };
};
exports.requirePermission = requirePermission;
/**
 * Middleware para verificar role específica
 */
const requireRole = (requiredRole) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: "Authentication required" });
        }
        if (req.user.role !== requiredRole) {
            return res.status(403).json({
                error: "Insufficient role",
                required: requiredRole,
                userRole: req.user.role
            });
        }
        return next();
    };
};
exports.requireRole = requireRole;
/**
 * Middleware para verificar se usuário tem acesso ao workspace
 */
const requireWorkspaceAccess = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ error: "Authentication required" });
    }
    const requestedWorkspaceId = req.params.workspaceId || req.body.workspaceId || req.query.workspaceId;
    if (requestedWorkspaceId && requestedWorkspaceId !== req.user.workspaceId) {
        return res.status(403).json({
            error: "Access denied to workspace",
            requestedWorkspace: requestedWorkspaceId,
            userWorkspace: req.user.workspaceId
        });
    }
    return next();
};
exports.requireWorkspaceAccess = requireWorkspaceAccess;
/**
 * Helper para criar políticas RLS customizadas
 */
const createRLSPolicy = async (tableName, policyName, operation, condition) => {
    const connection = data_source_1.AppDataSource.manager.connection;
    try {
        // Verificar se a política já existe
        const existingPolicy = await connection.query(`
      SELECT 1 FROM pg_policies 
      WHERE tablename = $1 AND policyname = $2
    `, [tableName, policyName]);
        if (existingPolicy.length > 0) {
            // Atualizar política existente
            await connection.query(`
        DROP POLICY IF EXISTS ${policyName} ON ${tableName}
      `);
        }
        // Criar nova política
        await connection.query(`
      CREATE POLICY ${policyName} ON ${tableName}
      FOR ${operation}
      USING (${condition})
    `);
        console.log(`✅ RLS policy '${policyName}' created for table '${tableName}'`);
    }
    catch (error) {
        console.error(`❌ Failed to create RLS policy '${policyName}':`, error);
        throw error;
    }
};
exports.createRLSPolicy = createRLSPolicy;
/**
 * Helper para ativar RLS em uma tabela
 */
const enableRLS = async (tableName) => {
    const connection = data_source_1.AppDataSource.manager.connection;
    try {
        await connection.query(`ALTER TABLE ${tableName} ENABLE ROW LEVEL SECURITY`);
        console.log(`✅ RLS enabled for table '${tableName}'`);
    }
    catch (error) {
        console.error(`❌ Failed to enable RLS for table '${tableName}':`, error);
        throw error;
    }
};
exports.enableRLS = enableRLS;
/**
 * Helper para desativar RLS em uma tabela
 */
const disableRLS = async (tableName) => {
    const connection = data_source_1.AppDataSource.manager.connection;
    try {
        await connection.query(`ALTER TABLE ${tableName} DISABLE ROW LEVEL SECURITY`);
        console.log(`✅ RLS disabled for table '${tableName}'`);
    }
    catch (error) {
        console.error(`❌ Failed to disable RLS for table '${tableName}':`, error);
        throw error;
    }
};
exports.disableRLS = disableRLS;
//# sourceMappingURL=rls.middleware.js.map