"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validator = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
function validator(dto) {
    return async (req, res, next) => {
        try {
            const data = (0, class_transformer_1.plainToClass)(dto, req.body);
            const errors = await (0, class_validator_1.validate)(data, {
                validationError: { target: false, value: true },
            });
            if (errors.length > 0) {
                return res.status(400).json({ errors });
            }
            req.body = data;
            next();
        }
        catch (error) {
            next(error);
        }
    };
}
exports.validator = validator;
//# sourceMappingURL=middlewares.js.map