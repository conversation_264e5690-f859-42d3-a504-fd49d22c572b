"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddMagicLinkFields1749681709876 = void 0;
class AddMagicLinkFields1749681709876 {
    constructor() {
        this.name = 'AddMagicLinkFields1749681709876';
    }
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "users" 
            ADD COLUMN "magic_link_token" varchar(500), 
            ADD COLUMN "magic_link_expires" timestamp
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_user_magic_link_token" ON "users" ("magic_link_token") 
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "IDX_user_magic_link_token"`);
        await queryRunner.query(`
            ALTER TABLE "users" 
            DROP COLUMN "magic_link_expires", 
            DROP COLUMN "magic_link_token"
        `);
    }
}
exports.AddMagicLinkFields1749681709876 = AddMagicLinkFields1749681709876;
//# sourceMappingURL=1749681709876-AddMagicLinkFields.js.map