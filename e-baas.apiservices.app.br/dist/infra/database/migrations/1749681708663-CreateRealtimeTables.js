"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateRealtimeTables1749681708663 = void 0;
class CreateRealtimeTables1749681708663 {
    async up(queryRunner) {
        // Create realtime_channels table
        await queryRunner.query(`
            CREATE TABLE "realtime_channels" (
                "id" varchar PRIMARY KEY NOT NULL,
                "name" varchar(100) NOT NULL,
                "workspace_id" varchar(255) NOT NULL,
                "type" varchar CHECK( type IN ('database','broadcast','presence') ) NOT NULL DEFAULT ('broadcast'),
                "description" text,
                "is_private" boolean NOT NULL DEFAULT (0),
                "max_connections" integer,
                "config" text,
                "allowed_events" text,
                "connection_count" integer NOT NULL DEFAULT (0),
                "message_count" bigint NOT NULL DEFAULT (0),
                "last_activity" datetime,
                "is_active" boolean NOT NULL DEFAULT (1),
                "created_by" varchar(255),
                "created_at" datetime NOT NULL DEFAULT (datetime('now')),
                "updated_at" datetime NOT NULL DEFAULT (datetime('now'))
            )
        `);
        // Create realtime_subscriptions table
        await queryRunner.query(`
            CREATE TABLE "realtime_subscriptions" (
                "id" varchar PRIMARY KEY NOT NULL,
                "channel_id" varchar(255) NOT NULL,
                "client_id" varchar(255) NOT NULL,
                "user_id" varchar(255),
                "workspace_id" varchar(255) NOT NULL,
                "subscription_config" text,
                "presence_key" varchar(255),
                "presence_metadata" text,
                "connection_metadata" text,
                "last_activity" datetime NOT NULL DEFAULT (datetime('now')),
                "is_active" boolean NOT NULL DEFAULT (1),
                "created_at" datetime NOT NULL DEFAULT (datetime('now')),
                "updated_at" datetime NOT NULL DEFAULT (datetime('now')),
                FOREIGN KEY ("channel_id") REFERENCES "realtime_channels" ("id") ON DELETE CASCADE
            )
        `);
        // Create indexes for realtime_channels
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_realtime_channels_workspace_name" ON "realtime_channels" ("workspace_id", "name")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_channels_workspace" ON "realtime_channels" ("workspace_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_channels_type" ON "realtime_channels" ("type")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_channels_active" ON "realtime_channels" ("is_active")
        `);
        // Create indexes for realtime_subscriptions
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_realtime_subscriptions_channel_client" ON "realtime_subscriptions" ("channel_id", "client_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_subscriptions_channel" ON "realtime_subscriptions" ("channel_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_subscriptions_client" ON "realtime_subscriptions" ("client_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_subscriptions_user" ON "realtime_subscriptions" ("user_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_subscriptions_workspace" ON "realtime_subscriptions" ("workspace_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_subscriptions_active" ON "realtime_subscriptions" ("is_active")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_realtime_subscriptions_activity" ON "realtime_subscriptions" ("last_activity")
        `);
    }
    async down(queryRunner) {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_realtime_subscriptions_activity"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_subscriptions_active"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_subscriptions_workspace"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_subscriptions_user"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_subscriptions_client"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_subscriptions_channel"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_subscriptions_channel_client"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_channels_active"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_channels_type"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_channels_workspace"`);
        await queryRunner.query(`DROP INDEX "IDX_realtime_channels_workspace_name"`);
        // Drop tables
        await queryRunner.query(`DROP TABLE "realtime_subscriptions"`);
        await queryRunner.query(`DROP TABLE "realtime_channels"`);
    }
}
exports.CreateRealtimeTables1749681708663 = CreateRealtimeTables1749681708663;
//# sourceMappingURL=1749681708663-CreateRealtimeTables.js.map