"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyService1749680366089 = void 0;
class ApiKeyService1749680366089 {
    async up(queryRunner) {
        await queryRunner.query(`
            CREATE TABLE "api_keys" (
                "id" varchar PRIMARY KEY NOT NULL,
                "name" varchar(100) NOT NULL,
                "api_key" varchar(500) NOT NULL UNIQUE,
                "key_prefix" varchar(20) NOT NULL,
                "workspace_id" varchar(255) NOT NULL,
                "type" varchar CHECK( type IN ('anon','service_role','authenticated') ) NOT NULL DEFAULT ('authenticated'),
                "scopes" text NOT NULL DEFAULT ('all'),
                "is_active" boolean NOT NULL DEFAULT (1),
                "expires_at" datetime,
                "description" text,
                "rate_limit" integer,
                "allowed_origins" text,
                "allowed_ips" text,
                "last_used_at" datetime,
                "usage_count" integer NOT NULL DEFAULT (0),
                "created_by" varchar(255),
                "created_at" datetime NOT NULL DEFAULT (datetime('now')),
                "updated_at" datetime NOT NULL DEFAULT (datetime('now'))
            )
        `);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_api_keys_api_key" ON "api_keys" ("api_key")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_api_keys_workspace_type" ON "api_keys" ("workspace_id", "type")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_api_keys_workspace_active" ON "api_keys" ("workspace_id", "is_active")
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "IDX_api_keys_workspace_active"`);
        await queryRunner.query(`DROP INDEX "IDX_api_keys_workspace_type"`);
        await queryRunner.query(`DROP INDEX "IDX_api_keys_api_key"`);
        await queryRunner.query(`DROP TABLE "api_keys"`);
    }
}
exports.ApiKeyService1749680366089 = ApiKeyService1749680366089;
//# sourceMappingURL=1749680366089-apiKeyService.js.map