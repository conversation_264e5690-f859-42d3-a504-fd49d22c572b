{"version": 3, "file": "1749745017266-AddFileVersioning.js", "sourceRoot": "", "sources": ["../../../../src/infra/database/migrations/1749745017266-AddFileVersioning.ts"], "names": [], "mappings": ";;;AAAA,qCAAiE;AAEjE,MAAa,8BAA8B;IAEhC,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,gDAAgD;QAChD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKvB,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,WAAW,CAAC,WAAW,CACzB,IAAI,eAAK,CAAC;YACN,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAChC;gBACD;oBACI,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;iBAChB;gBACD;oBACI,IAAI,EAAE,gBAAgB;oBACtB,IAAI,EAAE,SAAS;iBAClB;gBACD;oBACI,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,QAAQ,EAAE,IAAI;iBACjB;gBACD;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,QAAQ;iBACjB;gBACD;oBACI,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;iBAChB;gBACD;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;iBAChB;gBACD;oBACI,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,MAAM;iBACjB;gBACD;oBACI,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;oBACvC,OAAO,EAAE,UAAU;iBACtB;gBACD;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;oBACpC,OAAO,EAAE,QAAQ;iBACpB;gBACD;oBACI,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,kBAAkB;oBACxB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;iBAC/B;aACJ;SACJ,CAAC,EACF,IAAI,CACP,CAAC;QAEF,yCAAyC;QACzC,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGvB,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGvB,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGvB,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGvB,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGvB,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGvB,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKvB,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,qBAAqB;QACrB,MAAM,WAAW,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACzF,MAAM,WAAW,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;QACjF,MAAM,WAAW,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;QACpF,MAAM,WAAW,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QAC7E,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACvE,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAE1E,8BAA8B;QAC9B,MAAM,WAAW,CAAC,KAAK,CAAC,8EAA8E,CAAC,CAAC;QAExG,2BAA2B;QAC3B,MAAM,WAAW,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAE7C,qDAAqD;QACrD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKvB,CAAC,CAAC;IACP,CAAC;CAEJ;AA1LD,wEA0LC"}