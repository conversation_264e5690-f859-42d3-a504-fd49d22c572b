"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeAllConnections = exports.closeWorkspaceConnection = exports.getMongoConnection = exports.getWorkspaceConnection = exports.AppDataSource = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const mongodb_1 = require("mongodb");
const config_1 = __importDefault(require("../../infra/config"));
// Configurações padrão para os diferentes ambientes
const dbSettings = {
    test: {
        type: "sqlite",
        database: config_1.default.database.databaseFile,
    },
    development: {
        type: "postgres",
        host: config_1.default.database.host,
        port: config_1.default.database.port,
        username: config_1.default.database.username,
        password: config_1.default.database.password,
        database: config_1.default.database.database,
    },
    production: {
        type: "postgres",
        host: config_1.default.database.host,
        port: config_1.default.database.port,
        username: config_1.default.database.username,
        password: config_1.default.database.password,
        database: config_1.default.database.database,
    },
};
// Determine if running compiled JS or TypeScript source
const isCompiledJS = process.argv[0].includes('node') && !process.argv.some(arg => arg.includes('ts-node'));
// Entidades a carregar apenas para a aplicação (não durante migrations)
const entitiesForApp = process.env.NODE_ENV === "test" ||
    process.argv.some((arg) => arg.includes("migration"))
    ? []
    : isCompiledJS
        ? ["dist/modules/**/*.entity.js"]
        : ["src/modules/**/*.entity.ts"];
// DataSource principal para o banco de dados da aplicação
exports.AppDataSource = new typeorm_1.DataSource({
    synchronize: false,
    logging: false,
    entities: entitiesForApp,
    migrations: isCompiledJS ? [
        "dist/infra/database/migrations/*.js",
        "!dist/infra/database/migrations/*.spec.js",
    ] : [
        "src/infra/database/migrations/*.ts",
        "!src/infra/database/migrations/*.spec.ts",
    ],
    subscribers: [],
    ...dbSettings[process.env.NODE_ENV ?? "test"],
});
// Pool de conexões por workspace para PostgreSQL e MySQL
const dataSourceConnections = new Map();
// Pool de conexões para MongoDB
const mongoConnections = new Map();
// Função para obter ou criar uma nova conexão TypeORM para PostgreSQL ou MySQL
const getWorkspaceConnection = async (workspaceId, config) => {
    const connectionKey = `${workspaceId}-${config.type}`;
    if (dataSourceConnections.has(connectionKey)) {
        const connection = dataSourceConnections.get(connectionKey);
        if (connection && connection.isInitialized) {
            return connection;
        }
    }
    // Criar nova conexão
    const newConnection = new typeorm_1.DataSource({
        ...config,
        synchronize: false,
        logging: false,
    });
    await newConnection.initialize();
    dataSourceConnections.set(connectionKey, newConnection);
    return newConnection;
};
exports.getWorkspaceConnection = getWorkspaceConnection;
// Função para obter ou criar uma nova conexão MongoDB
const getMongoConnection = async (workspaceId, config) => {
    const connectionKey = `${workspaceId}-mongodb`;
    if (mongoConnections.has(connectionKey)) {
        const connection = mongoConnections.get(connectionKey);
        if (connection) {
            return connection;
        }
    }
    // Criar nova conexão
    const newConnection = new mongodb_1.MongoClient(config.url);
    await newConnection.connect();
    mongoConnections.set(connectionKey, newConnection);
    return newConnection;
};
exports.getMongoConnection = getMongoConnection;
// Função para fechar uma conexão específica
const closeWorkspaceConnection = async (workspaceId, type) => {
    const connectionKey = `${workspaceId}-${type}`;
    if (type === "mongodb") {
        const mongoConnection = mongoConnections.get(connectionKey);
        if (mongoConnection) {
            await mongoConnection.close();
            mongoConnections.delete(connectionKey);
        }
    }
    else {
        const connection = dataSourceConnections.get(connectionKey);
        if (connection && connection.isInitialized) {
            await connection.destroy();
            dataSourceConnections.delete(connectionKey);
        }
    }
};
exports.closeWorkspaceConnection = closeWorkspaceConnection;
// Função para fechar todas as conexões
const closeAllConnections = async () => {
    // Fechar conexões TypeORM
    for (const [, connection] of dataSourceConnections.entries()) {
        if (connection.isInitialized) {
            await connection.destroy();
        }
    }
    dataSourceConnections.clear();
    // Fechar conexões MongoDB
    for (const [, connection] of mongoConnections.entries()) {
        await connection.close();
    }
    mongoConnections.clear();
};
exports.closeAllConnections = closeAllConnections;
//# sourceMappingURL=data-source.js.map