"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class HttpRequest {
    constructor(baseUrl) {
        this.baseUrl = baseUrl || process.env.EXTERNAL_API_URL || "";
        this.bearerToken = process.env.EXTERNAL_API_TOKEN;
    }
    api() {
        return axios_1.default.create({
            baseURL: this.baseUrl,
            headers: {
                Accept: "application/json",
                Authorization: `Basic ${this.bearerToken}`,
            },
        });
    }
    async get(path) {
        const response = await this.api().get(`${path}`);
        return response?.data;
    }
    async post(path, data) {
        const response = await this.api().post(`${path}`, data);
        return response?.data;
    }
    async put(path, data) {
        const response = await this.api().put(`${path}`, data);
        return response?.data;
    }
    async patch(path, data) {
        const response = await this.api().patch(`${path}`, data);
        return response?.data;
    }
    async delete(path) {
        const response = await this.api().delete(`${path}`);
        return response?.data;
    }
}
exports.default = HttpRequest;
//# sourceMappingURL=httpRequest.js.map