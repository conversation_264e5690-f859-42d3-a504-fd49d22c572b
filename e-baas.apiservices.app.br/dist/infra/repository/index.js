"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiKeyServiceRepository = exports.rlsPoliciesRepository = exports.sqlExecutorRepository = exports.postgrestRepository = exports.refreshTokenRepository = exports.databaseConfigRepository = exports.apiKeyRepository = exports.workspaceRepository = exports.userRepository = void 0;
const data_source_1 = require("../database/data-source");
const user_entity_1 = require("../../modules/users/entity/user.entity");
const Workspace_entity_1 = require("../../modules/workspaces/entity/Workspace.entity");
const ApiKey_entity_1 = require("../../modules/api-keys/entity/ApiKey.entity");
const DatabaseConfig_entity_1 = require("../../modules/database-configs/entity/DatabaseConfig.entity");
const RefreshToken_entity_1 = require("../../modules/auth/entity/RefreshToken.entity");
// Repositórios principais da aplicação
exports.userRepository = data_source_1.AppDataSource.getRepository(user_entity_1.User);
exports.workspaceRepository = data_source_1.AppDataSource.getRepository(Workspace_entity_1.Workspace);
exports.apiKeyRepository = data_source_1.AppDataSource.getRepository(ApiKey_entity_1.ApiKey);
exports.databaseConfigRepository = data_source_1.AppDataSource.getRepository(DatabaseConfig_entity_1.DatabaseConfig);
exports.refreshTokenRepository = data_source_1.AppDataSource.getRepository(RefreshToken_entity_1.RefreshToken);
const postgrest_entity_1 = require("../../modules/postgrest/entity/postgrest.entity");
exports.postgrestRepository = data_source_1.AppDataSource.getRepository(postgrest_entity_1.Postgrest);
const sqlExecutor_entity_1 = require("../../modules/sql-executor/entity/sqlExecutor.entity");
exports.sqlExecutorRepository = data_source_1.AppDataSource.getRepository(sqlExecutor_entity_1.SqlExecutor);
const rlsPolicies_entity_1 = require("../../modules/rls-policies/entity/rlsPolicies.entity");
exports.rlsPoliciesRepository = data_source_1.AppDataSource.getRepository(rlsPolicies_entity_1.RlsPolicies);
const apiKeyService_entity_1 = require("../../modules/api-key-service/entity/apiKeyService.entity");
exports.apiKeyServiceRepository = data_source_1.AppDataSource.getRepository(apiKeyService_entity_1.ApiKeyService);
//# sourceMappingURL=index.js.map