import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { User } from "../users/entity/user.entity";
import { Workspace } from "../workspaces/entity/Workspace.entity";
import { StorageFile } from "../storage/entity/StorageFile.entity";
import { Bucket } from "../storage/entity/Bucket.entity";
import * as fs from 'fs';
import * as path from 'path';
import * as archiver from 'archiver';
import { createWriteStream } from 'fs';

export interface BackupOptions {
  includeDatabase: boolean;
  includeStorage: boolean;
  includeUsers: boolean;
  includeWorkspaces: boolean;
  compressionLevel?: number;
}

export interface PlatformBackupOptions extends BackupOptions {
  adminOnly: boolean; // Only admin data, not client workspaces
}

export interface WorkspaceBackupOptions extends BackupOptions {
  workspaceId: string;
}

export interface BackupResult {
  id: string;
  filename: string;
  size: number;
  path: string;
  createdAt: Date;
  type: 'platform' | 'workspace' | 'admin_only';
  workspaceId?: string;
  metadata: {
    databaseTables: string[];
    storageFiles: number;
    userCount: number;
    compressionRatio: number;
  };
}

export class BackupService {
  private userRepository: Repository<User>;
  private workspaceRepository: Repository<Workspace>;
  private storageFileRepository: Repository<StorageFile>;
  private bucketRepository: Repository<Bucket>;
  private backupDirectory: string;

  constructor() {
    this.userRepository = AppDataSource.getRepository(User);
    this.workspaceRepository = AppDataSource.getRepository(Workspace);
    this.storageFileRepository = AppDataSource.getRepository(StorageFile);
    this.bucketRepository = AppDataSource.getRepository(Bucket);
    this.backupDirectory = process.env.BACKUP_DIRECTORY || './backups';
    
    // Ensure backup directory exists
    if (!fs.existsSync(this.backupDirectory)) {
      fs.mkdirSync(this.backupDirectory, { recursive: true });
    }
  }

  // ==== PLATFORM ADMIN BACKUPS ====

  async createPlatformBackup(options: PlatformBackupOptions): Promise<BackupResult> {
    const backupId = this.generateBackupId('platform');
    const backupPath = path.join(this.backupDirectory, `${backupId}.zip`);
    
    console.log('Starting platform backup...', { backupId, options });

    const archive = archiver('zip', {
      zlib: { level: options.compressionLevel || 6 }
    });

    const output = createWriteStream(backupPath);
    archive.pipe(output);

    let metadata = {
      databaseTables: [] as string[],
      storageFiles: 0,
      userCount: 0,
      compressionRatio: 0
    };

    try {
      // 1. Backup Database
      if (options.includeDatabase) {
        const dbBackup = await this.createDatabaseBackup(options.adminOnly ? 'admin' : 'full');
        archive.append(JSON.stringify(dbBackup, null, 2), { name: 'database/full_backup.json' });
        metadata.databaseTables = dbBackup.tables.map(t => t.name);
      }

      // 2. Backup Users
      if (options.includeUsers) {
        const users = options.adminOnly 
          ? await this.getAdminUsers()
          : await this.getAllUsers();
        
        archive.append(JSON.stringify(users, null, 2), { name: 'users/users.json' });
        metadata.userCount = users.length;
      }

      // 3. Backup Workspaces
      if (options.includeWorkspaces && !options.adminOnly) {
        const workspaces = await this.getAllWorkspaces();
        archive.append(JSON.stringify(workspaces, null, 2), { name: 'workspaces/workspaces.json' });
      }

      // 4. Backup Storage
      if (options.includeStorage) {
        const storageBackup = options.adminOnly 
          ? await this.createAdminStorageBackup(archive)
          : await this.createFullStorageBackup(archive);
        
        metadata.storageFiles = storageBackup.fileCount;
      }

      // 5. Backup System Configuration
      const systemConfig = await this.getSystemConfiguration();
      archive.append(JSON.stringify(systemConfig, null, 2), { name: 'system/configuration.json' });

      // 6. Create backup manifest
      const manifest = {
        backupId,
        createdAt: new Date().toISOString(),
        type: options.adminOnly ? 'admin_only' : 'platform',
        options,
        metadata
      };
      archive.append(JSON.stringify(manifest, null, 2), { name: 'manifest.json' });

      await this.finalizeArchive(archive);

      const stats = fs.statSync(backupPath);
      metadata.compressionRatio = this.calculateCompressionRatio(metadata);

      return {
        id: backupId,
        filename: `${backupId}.zip`,
        size: stats.size,
        path: backupPath,
        createdAt: new Date(),
        type: options.adminOnly ? 'admin_only' : 'platform',
        metadata
      };

    } catch (error) {
      // Cleanup failed backup
      if (fs.existsSync(backupPath)) {
        fs.unlinkSync(backupPath);
      }
      throw new Error(`Platform backup failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // ==== WORKSPACE ADMIN BACKUPS ====

  async createWorkspaceBackup(options: WorkspaceBackupOptions): Promise<BackupResult> {
    const workspace = await this.workspaceRepository.findOne({
      where: { id: options.workspaceId },
      relations: ['owner']
    });

    if (!workspace) {
      throw new Error('Workspace not found');
    }

    const backupId = this.generateBackupId('workspace', options.workspaceId);
    const backupPath = path.join(this.backupDirectory, `${backupId}.zip`);
    
    console.log('Starting workspace backup...', { backupId, workspaceId: options.workspaceId, options });

    const archive = archiver('zip', {
      zlib: { level: options.compressionLevel || 6 }
    });

    const output = createWriteStream(backupPath);
    archive.pipe(output);

    let metadata = {
      databaseTables: [] as string[],
      storageFiles: 0,
      userCount: 0,
      compressionRatio: 0
    };

    try {
      // 1. Backup Workspace Database Data
      if (options.includeDatabase) {
        const dbBackup = await this.createWorkspaceDatabaseBackup(options.workspaceId);
        archive.append(JSON.stringify(dbBackup, null, 2), { name: 'database/workspace_data.json' });
        metadata.databaseTables = dbBackup.tables.map(t => t.name);
      }

      // 2. Backup Workspace Users
      if (options.includeUsers) {
        const users = await this.getWorkspaceUsers(options.workspaceId);
        archive.append(JSON.stringify(users, null, 2), { name: 'users/workspace_users.json' });
        metadata.userCount = users.length;
      }

      // 3. Backup Workspace Info
      const workspaceInfo = {
        ...workspace,
        owner: workspace.owner ? {
          id: workspace.owner.id,
          email: workspace.owner.email,
          fullName: workspace.owner.fullName
        } : null
      };
      archive.append(JSON.stringify(workspaceInfo, null, 2), { name: 'workspace/info.json' });

      // 4. Backup Workspace Storage
      if (options.includeStorage) {
        const storageBackup = await this.createWorkspaceStorageBackup(options.workspaceId, archive);
        metadata.storageFiles = storageBackup.fileCount;
      }

      // 5. Backup Workspace Configuration
      const workspaceConfig = await this.getWorkspaceConfiguration(options.workspaceId);
      archive.append(JSON.stringify(workspaceConfig, null, 2), { name: 'configuration/workspace_settings.json' });

      // 6. Create backup manifest
      const manifest = {
        backupId,
        createdAt: new Date().toISOString(),
        type: 'workspace',
        workspaceId: options.workspaceId,
        workspaceName: workspace.name,
        options,
        metadata
      };
      archive.append(JSON.stringify(manifest, null, 2), { name: 'manifest.json' });

      await this.finalizeArchive(archive);

      const stats = fs.statSync(backupPath);
      metadata.compressionRatio = this.calculateCompressionRatio(metadata);

      return {
        id: backupId,
        filename: `${backupId}.zip`,
        size: stats.size,
        path: backupPath,
        createdAt: new Date(),
        type: 'workspace',
        workspaceId: options.workspaceId,
        metadata
      };

    } catch (error) {
      // Cleanup failed backup
      if (fs.existsSync(backupPath)) {
        fs.unlinkSync(backupPath);
      }
      throw new Error(`Workspace backup failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // ==== BACKUP LISTING & MANAGEMENT ====

  async listBackups(workspaceId?: string): Promise<BackupResult[]> {
    const backupFiles = fs.readdirSync(this.backupDirectory)
      .filter(file => file.endsWith('.zip'))
      .map(file => {
        const filepath = path.join(this.backupDirectory, file);
        const stats = fs.statSync(filepath);
        
        // Extract backup info from filename
        const parts = file.replace('.zip', '').split('_');
        const type = parts[1] as 'platform' | 'workspace' | 'admin';
        const extractedWorkspaceId = type === 'workspace' ? parts[2] : undefined;
        
        return {
          id: file.replace('.zip', ''),
          filename: file,
          size: stats.size,
          path: filepath,
          createdAt: stats.birthtime,
          type: type as any,
          workspaceId: extractedWorkspaceId,
          metadata: {
            databaseTables: [],
            storageFiles: 0,
            userCount: 0,
            compressionRatio: 0
          }
        };
      });

    // Filter by workspace if specified
    if (workspaceId) {
      return backupFiles.filter(backup => backup.workspaceId === workspaceId);
    }

    return backupFiles.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async deleteBackup(backupId: string, requesterWorkspaceId?: string): Promise<void> {
    const backupPath = path.join(this.backupDirectory, `${backupId}.zip`);
    
    if (!fs.existsSync(backupPath)) {
      throw new Error('Backup not found');
    }

    // Security check: workspace admins can only delete their own backups
    if (requesterWorkspaceId) {
      const parts = backupId.split('_');
      if (parts[1] === 'workspace' && parts[2] !== requesterWorkspaceId) {
        throw new Error('Cannot delete backups from other workspaces');
      }
      if (parts[1] === 'platform' || parts[1] === 'admin') {
        throw new Error('Cannot delete platform/admin backups');
      }
    }

    fs.unlinkSync(backupPath);
  }

  async downloadBackup(backupId: string, requesterWorkspaceId?: string): Promise<string> {
    const backupPath = path.join(this.backupDirectory, `${backupId}.zip`);
    
    if (!fs.existsSync(backupPath)) {
      throw new Error('Backup not found');
    }

    // Security check: workspace admins can only download their own backups
    if (requesterWorkspaceId) {
      const parts = backupId.split('_');
      if (parts[1] === 'workspace' && parts[2] !== requesterWorkspaceId) {
        throw new Error('Cannot download backups from other workspaces');
      }
      if (parts[1] === 'platform' || parts[1] === 'admin') {
        throw new Error('Cannot download platform/admin backups');
      }
    }

    return backupPath;
  }

  // ==== PRIVATE HELPER METHODS ====

  private generateBackupId(type: 'platform' | 'workspace' | 'admin', workspaceId?: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const random = Math.random().toString(36).substring(2, 8);
    
    if (type === 'workspace' && workspaceId) {
      return `backup_workspace_${workspaceId}_${timestamp}_${random}`;
    }
    
    return `backup_${type}_${timestamp}_${random}`;
  }

  private async createDatabaseBackup(scope: 'admin' | 'full') {
    // This would connect to database and export schema + data
    // For now, mock implementation
    const tables = scope === 'admin' 
      ? ['users', 'workspaces', 'api_keys', 'system_settings']
      : ['users', 'workspaces', 'api_keys', 'storage_files', 'buckets', 'storage_policies'];

    return {
      scope,
      tables: tables.map(name => ({
        name,
        schema: `CREATE TABLE ${name} (...)`, // Mock schema
        data: [] // Mock data
      })),
      exportedAt: new Date().toISOString()
    };
  }

  private async createWorkspaceDatabaseBackup(workspaceId: string) {
    // Export only data related to specific workspace
    const tables = ['users', 'workspace_specific_tables'];
    
    return {
      workspaceId,
      tables: tables.map(name => ({
        name,
        schema: `CREATE TABLE ${name} (...)`,
        data: [] // Data filtered by workspace_id
      })),
      exportedAt: new Date().toISOString()
    };
  }

  private async getAdminUsers() {
    return await this.userRepository.find({
      where: [
        { adminRole: 'platform_admin' },
        { adminRole: 'service_role' }
      ],
      select: ['id', 'email', 'role', 'adminRole', 'createdAt']
    });
  }

  private async getAllUsers() {
    return await this.userRepository.find({
      select: ['id', 'email', 'role', 'adminRole', 'workspaceId', 'createdAt']
    });
  }

  private async getWorkspaceUsers(workspaceId: string) {
    return await this.userRepository.find({
      where: { workspaceId },
      select: ['id', 'email', 'role', 'adminRole', 'createdAt']
    });
  }

  private async getAllWorkspaces() {
    return await this.workspaceRepository.find({
      relations: ['owner'],
      select: ['id', 'name', 'description', 'isActive', 'createdAt']
    });
  }

  private async createAdminStorageBackup(archive: archiver.Archiver) {
    // Only backup system/admin files, not user content
    return { fileCount: 0 };
  }

  private async createFullStorageBackup(archive: archiver.Archiver) {
    // Backup all storage files
    const files = await this.storageFileRepository.find();
    
    for (const file of files.slice(0, 10)) { // Limit for demo
      // In real implementation, would copy actual files
      archive.append(`Mock file content for ${file.name}`, { 
        name: `storage/${file.bucket_id}/${file.path}` 
      });
    }

    return { fileCount: files.length };
  }

  private async createWorkspaceStorageBackup(workspaceId: string, archive: archiver.Archiver) {
    // Backup only files belonging to workspace
    const buckets = await this.bucketRepository.find({
      where: { workspaceId } // Assuming buckets have workspaceId
    });

    let fileCount = 0;
    for (const bucket of buckets) {
      const files = await this.storageFileRepository.find({
        where: { bucket_id: bucket.id }
      });

      for (const file of files) {
        archive.append(`Mock file content for ${file.name}`, { 
          name: `storage/${bucket.name}/${file.path}` 
        });
        fileCount++;
      }
    }

    return { fileCount };
  }

  private async getSystemConfiguration() {
    return {
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'production',
      databaseUrl: process.env.DATABASE_URL ? '[REDACTED]' : null,
      features: {
        storage: true,
        realtime: true,
        functions: true,
        auth: true
      }
    };
  }

  private async getWorkspaceConfiguration(workspaceId: string) {
    // Get workspace-specific settings
    return {
      workspaceId,
      settings: {
        allowPublicSignup: false,
        emailVerificationRequired: true,
        passwordMinLength: 8
      },
      integrations: [],
      apiKeys: [] // Without actual keys for security
    };
  }

  private async finalizeArchive(archive: archiver.Archiver): Promise<void> {
    return new Promise((resolve, reject) => {
      archive.on('error', reject);
      archive.on('end', resolve);
      archive.finalize();
    });
  }

  private calculateCompressionRatio(metadata: any): number {
    // Mock calculation
    return Math.random() * 0.3 + 0.5; // 50-80% compression
  }
}