import { IsBoolean, Is<PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Max, IsString, IsE<PERSON> } from 'class-validator';

export class CreateBackupDto {
  @IsOptional()
  @IsBoolean()
  adminOnly?: boolean;

  @IsOptional()
  @IsBoolean()
  includeDatabase?: boolean = true;

  @IsOptional()
  @IsBoolean()
  includeStorage?: boolean = true;

  @IsOptional()
  @IsBoolean()
  includeUsers?: boolean = true;

  @IsOptional()
  @IsBoolean()
  includeWorkspaces?: boolean = true;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(9)
  compressionLevel?: number = 6;
}

export class BackupFilterDto {
  @IsOptional()
  @IsString()
  type?: 'platform' | 'workspace' | 'admin_only';

  @IsOptional()
  @IsString()
  workspaceId?: string;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;
}