import { Router, Request, Response } from "express";
import { AdminService } from "./admin.service";
import { 
  requireAdmin, 
  requirePlatformAdmin, 
  requireWorkspaceAdmin,
  scopeToWorkspace,
  AdminRequest 
} from "../../infra/middlewares/admin-auth.middleware";
import { AssignAdminRoleDto } from "../auth/dto/admin-roles.dto";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";
import backupRouter from "../backup/backup.controller";

const adminRouter = Router();
const adminService = new AdminService();

// Helper function for validation
const validateDto = async (DtoClass: any, data: any) => {
  const dto = plainToClass(DtoClass, data);
  const errors = await validate(dto);
  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
  }
  return dto;
};

// ==== PLATFORM ADMIN ROUTES ====

// Get all workspaces (Platform Admin only)
adminRouter.get("/platform/workspaces", requirePlatformAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const workspaces = await adminService.getAllWorkspaces();
    return res.status(200).json({ workspaces });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get global analytics (Platform Admin only)
adminRouter.get("/platform/analytics", requirePlatformAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { period = '30d' } = req.query;
    const analytics = await adminService.getGlobalAnalytics(period as string);
    return res.status(200).json(analytics);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Assign admin roles (Platform Admin only)
adminRouter.post("/platform/assign-admin-role", requirePlatformAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const assignDto = req.body as AssignAdminRoleDto;
    const result = await adminService.assignAdminRole(assignDto);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get all users across all workspaces (Platform Admin only)
adminRouter.get("/platform/users", requirePlatformAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { page = 1, limit = 50, search } = req.query;
    const users = await adminService.getAllUsers({
      page: Number(page),
      limit: Number(limit),
      search: search as string
    });
    return res.status(200).json(users);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// ==== WORKSPACE ADMIN ROUTES ====

// Get workspace info (accessible to workspace admins and platform admins)
adminRouter.get("/workspace", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.workspaceId;
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID required' });
    }
    
    const workspace = await adminService.getWorkspaceDetails(workspaceId);
    return res.status(200).json(workspace);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get workspace users
adminRouter.get("/workspace/users", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.workspaceId;
    const { page = 1, limit = 50, search } = req.query;
    
    const users = await adminService.getWorkspaceUsers(workspaceId!, {
      page: Number(page),
      limit: Number(limit),
      search: search as string
    });
    
    return res.status(200).json(users);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get workspace analytics
adminRouter.get("/workspace/analytics", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.workspaceId;
    const { period = '30d' } = req.query;
    
    const analytics = await adminService.getWorkspaceAnalytics(workspaceId!, period as string);
    return res.status(200).json(analytics);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get workspace API keys
adminRouter.get("/workspace/api-keys", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.workspaceId;
    const apiKeys = await adminService.getWorkspaceApiKeys(workspaceId!);
    return res.status(200).json({ apiKeys });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Create workspace API key
adminRouter.post("/workspace/api-keys", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.workspaceId;
    const { name, permissions, expiresAt } = req.body;
    
    const apiKey = await adminService.createWorkspaceApiKey(workspaceId!, {
      name,
      permissions,
      expiresAt,
      createdBy: req.user?.id
    });
    
    return res.status(201).json(apiKey);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get workspace database schemas
adminRouter.get("/workspace/database/schemas", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.workspaceId;
    const schemas = await adminService.getWorkspaceDatabaseSchemas(workspaceId!);
    return res.status(200).json({ schemas });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get workspace storage usage
adminRouter.get("/workspace/storage/usage", requireWorkspaceAdmin, scopeToWorkspace, async (req: AdminRequest, res: Response) => {
  try {
    const workspaceId = req.user?.workspaceId;
    const usage = await adminService.getWorkspaceStorageUsage(workspaceId!);
    return res.status(200).json(usage);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// ==== COMMON ADMIN ROUTES ====

// Get current admin user info
adminRouter.get("/me", requireAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const user = req.user;
    return res.status(200).json({ 
      user: {
        ...user,
        adminCapabilities: {
          canManageWorkspaces: user?.isPlatformAdmin,
          canManageUsers: user?.isWorkspaceAdmin,
          canViewGlobalAnalytics: user?.isPlatformAdmin,
          canManageApiKeys: user?.isWorkspaceAdmin,
          canManageDatabase: user?.isWorkspaceAdmin,
          canManageStorage: user?.isWorkspaceAdmin
        }
      }
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Check admin permissions
adminRouter.post("/check-permission", requireAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { permission } = req.body;
    const hasPermission = req.user?.permissions.includes(permission) || req.user?.isPlatformAdmin;
    
    return res.status(200).json({ 
      hasPermission,
      permission,
      userPermissions: req.user?.permissions,
      isPlatformAdmin: req.user?.isPlatformAdmin,
      isWorkspaceAdmin: req.user?.isWorkspaceAdmin
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// ==== BACKUP ROUTES ====
adminRouter.use(backupRouter);

export default adminRouter;