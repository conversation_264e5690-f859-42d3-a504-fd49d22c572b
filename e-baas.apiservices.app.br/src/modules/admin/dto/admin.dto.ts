import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsBoolean, IsArray, IsUUID, IsEnum } from "class-validator";

export class WorkspaceStatsDto {
  @IsNumber()
  userCount: number;

  @IsNumber()
  adminCount: number;

  @IsNumber()
  storageUsage: number;

  @IsNumber()
  apiCallsThisMonth: number;
}

export class AdminAnalyticsDto {
  @IsString()
  period: string;

  @IsOptional()
  @IsString()
  workspaceId?: string;

  @IsOptional()
  workspaces?: {
    total: number;
    active: number;
    growth: number;
  };

  @IsOptional()
  users?: {
    total: number;
    verified: number;
    growth: number;
  };

  @IsOptional()
  apiCalls?: {
    total: number;
    successful: number;
    errors: number;
  };

  @IsOptional()
  storage?: {
    totalUsage: number;
    totalFiles: number;
  };
}

export class AdminUserDto {
  @IsUUID()
  id: string;

  @IsString()
  email: string;

  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsString()
  role: string;

  @IsOptional()
  @IsString()
  adminRole?: string;

  @IsString()
  workspaceId: string;

  @IsString()
  provider: string;

  @IsBoolean()
  isActive: boolean;

  @IsArray()
  @IsString({ each: true })
  permissions: string[];

  @IsBoolean()
  canAccessAdmin: boolean;
}

export class CreateApiKeyDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];

  @IsOptional()
  expiresAt?: Date;

  @IsOptional()
  @IsString()
  description?: string;
}