import { Request, Response, NextFunction } from "express";
import { DatabaseConfigUseCases } from "./database-config.useCases";
import createHttpError from "http-errors";

export class DatabaseConfigController {
  private databaseConfigUseCases = new DatabaseConfigUseCases();

  /**
   * Cria uma nova configuração de banco de dados
   */
  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const workspace = req.workspace;
      if (!workspace) {
        throw createHttpError(400, "Workspace não encontrado");
      }

      const dbConfig = await this.databaseConfigUseCases.create(
        req.body,
        workspace
      );
      res.status(201).json(dbConfig);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Testa a conexão com um banco de dados
   */
  async testConnection(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { databaseId } = req.params;
      const result = await this.databaseConfigUseCases.testConnection(
        databaseId
      );
      res.status(200).json({ success: result });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Lista configurações de banco de dados de um workspace
   */
  async findByWorkspace(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { workspaceId } = req.params;
      const dbConfigs = await this.databaseConfigUseCases.findByWorkspace(
        workspaceId
      );
      res.status(200).json(dbConfigs);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Obtém uma configuração de banco de dados pelo ID
   */
  async findById(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      const dbConfig = await this.databaseConfigUseCases.findById(id);
      res.status(200).json(dbConfig);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Atualiza uma configuração de banco de dados
   */
  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const dbConfig = await this.databaseConfigUseCases.update(id, req.body);
      res.status(200).json(dbConfig);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Desativa uma configuração de banco de dados
   */
  async deactivate(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      const dbConfig = await this.databaseConfigUseCases.deactivate(id);
      res.status(200).json(dbConfig);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Executa uma consulta em um banco de dados
   */
  async executeQuery(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { databaseId } = req.params;
      const { query, params } = req.body;

      const result = await this.databaseConfigUseCases.executeQuery(
        databaseId,
        query,
        params
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }
}
