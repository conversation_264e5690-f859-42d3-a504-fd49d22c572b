import { DatabaseConfigService } from "./service/database-config.service";
import { ConnectionService } from "./service/connection.service";
import { DatabaseConfig } from "./entity/DatabaseConfig.entity";
import { Workspace } from "../workspaces/entity/Workspace.entity";
import createHttpError from "http-errors";

export class DatabaseConfigUseCases {
  private databaseConfigService = new DatabaseConfigService();
  private connectionService = new ConnectionService();

  /**
   * Cria uma nova configuração de banco de dados
   */
  async create(
    data: Partial<DatabaseConfig>,
    workspace: Workspace
  ): Promise<DatabaseConfig> {
    return this.databaseConfigService.create(data, workspace);
  }

  /**
   * Testa a conexão com um banco de dados
   */
  async testConnection(databaseId: string): Promise<boolean> {
    const dbConfig = await this.databaseConfigService.findById(databaseId);
    return this.databaseConfigService.testConnection(dbConfig);
  }

  /**
   * Obtém uma configuração de banco de dados pelo ID
   */
  async findById(id: string): Promise<DatabaseConfig> {
    return this.databaseConfigService.findById(id);
  }

  /**
   * Lista as configurações de banco de dados de um workspace
   */
  async findByWorkspace(workspaceId: string): Promise<DatabaseConfig[]> {
    return this.databaseConfigService.findByWorkspace(workspaceId);
  }

  /**
   * Atualiza uma configuração de banco de dados
   */
  async update(
    id: string,
    data: Partial<DatabaseConfig>
  ): Promise<DatabaseConfig> {
    return this.databaseConfigService.update(id, data);
  }

  /**
   * Desativa uma configuração de banco de dados
   */
  async deactivate(id: string): Promise<DatabaseConfig> {
    return this.databaseConfigService.deactivate(id);
  }

  /**
   * Executa uma consulta em um banco de dados
   */
  async executeQuery(
    databaseId: string,
    query: string,
    params: unknown[] = []
  ): Promise<unknown> {
    try {
      const dbConfig = await this.databaseConfigService.findById(databaseId);

      // Verificar se o banco de dados está ativo
      if (!dbConfig.isActive) {
        throw createHttpError(400, "Banco de dados inativo");
      }

      // Executar a consulta
      return this.connectionService.executeQuery(dbConfig, query, params);
    } catch (error) {
      if (error instanceof Error) {
        throw createHttpError(
          500,
          `Erro ao executar consulta: ${error.message}`
        );
      }
      throw error;
    }
  }
}
