import { DataSource } from "typeorm";
import { MongoClient, Collection, Db } from "mongodb";
import { DatabaseConfig, DatabaseType } from "../entity/DatabaseConfig.entity";
import {
  getWorkspaceConnection,
  getMongoConnection,
  closeWorkspaceConnection,
} from "data-source";
import createHttpError from "http-errors";

interface MongoQuery {
  collection: string;
  operation: "find" | "findOne" | "insertOne" | "updateOne" | "deleteOne";
  filter?: Record<string, unknown>;
  document?: Record<string, unknown>;
  update?: Record<string, unknown>;
}

/**
 * Serviço responsável por gerenciar as conexões de banco de dados
 */
export class ConnectionService {
  /**
   * Obtém uma conexão para um banco de dados específico
   */
  async getConnection(
    dbConfig: DatabaseConfig
  ): Promise<DataSource | MongoClient> {
    try {
      // Verificar se o banco está ativo
      if (!dbConfig.isActive) {
        throw createHttpError(400, "Banco de dados inativo");
      }

      // Conexão para MongoDB
      if (dbConfig.databaseType === DatabaseType.MONGODB) {
        const connectionString =
          dbConfig.connectionString ||
          `mongodb://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;

        return getMongoConnection(dbConfig.workspaceId, {
          url: connectionString,
        });
      }

      // Conexão para bancos relacionais (PostgreSQL/MySQL)
      return getWorkspaceConnection(dbConfig.workspaceId, {
        type: dbConfig.databaseType,
        host: dbConfig.host,
        port: dbConfig.port,
        username: dbConfig.username,
        password: dbConfig.password,
        database: dbConfig.database,
      });
    } catch (error) {
      throw createHttpError(
        500,
        `Erro ao obter conexão: ${(error as Error).message}`
      );
    }
  }

  /**
   * Fecha uma conexão específica
   */
  async closeConnection(dbConfig: DatabaseConfig): Promise<void> {
    try {
      await closeWorkspaceConnection(
        dbConfig.workspaceId,
        dbConfig.databaseType
      );
    } catch (error) {
      throw createHttpError(
        500,
        `Erro ao fechar conexão: ${(error as Error).message}`
      );
    }
  }

  /**
   * Executa uma consulta em um banco de dados
   */
  async executeQuery(
    dbConfig: DatabaseConfig,
    query: string,
    params: unknown[] = []
  ): Promise<unknown> {
    let connection: DataSource | MongoClient;

    try {
      // Obter conexão
      connection = await this.getConnection(dbConfig);

      // Executar consulta de acordo com o tipo de banco
      if (dbConfig.databaseType === DatabaseType.MONGODB) {
        // Implementação para MongoDB
        const db = (connection as MongoClient).db(dbConfig.database) as Db;
        // Para MongoDB, a query seria um objeto de configuração com collection, operation, etc.
        // Esta é uma implementação simplificada
        const parsedQuery = JSON.parse(query) as MongoQuery;
        const collection = db.collection(parsedQuery.collection) as Collection;

        switch (parsedQuery.operation) {
          case "find":
            return collection.find(parsedQuery.filter || {}).toArray();
          case "findOne":
            return collection.findOne(parsedQuery.filter || {});
          case "insertOne":
            return collection.insertOne(parsedQuery.document || {});
          case "updateOne":
            return collection.updateOne(parsedQuery.filter || {}, {
              $set: parsedQuery.update || {},
            });
          case "deleteOne":
            return collection.deleteOne(parsedQuery.filter || {});
          default:
            throw createHttpError(400, "Operação MongoDB não suportada");
        }
      } else {
        // Implementação para bancos relacionais
        return (connection as DataSource).query(query, params);
      }
    } catch (error) {
      throw createHttpError(
        500,
        `Erro ao executar consulta: ${(error as Error).message}`
      );
    }
  }
}
