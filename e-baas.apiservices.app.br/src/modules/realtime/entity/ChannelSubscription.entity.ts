import "reflect-metadata";
import {
  BeforeI<PERSON>rt,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Channel } from "./Channel.entity";

@Entity("realtime_subscriptions")
@Index(["channelId", "clientId"], { unique: true })
@Index(["channelId"])
@Index(["clientId"])
@Index(["userId"])
@Index(["workspaceId"])
export class ChannelSubscription {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "channel_id", type: "varchar", length: 255 })
  channelId: string;

  @Column({ name: "client_id", type: "varchar", length: 255 })
  clientId: string;

  @Column({ name: "user_id", type: "varchar", length: 255, nullable: true })
  userId?: string;

  @Column({ name: "workspace_id", type: "varchar", length: 255 })
  workspaceId: string;

  @Column({
    name: "subscription_config",
    type: "json",
    nullable: true
  })
  subscriptionConfig?: {
    filter?: string;
    events?: string[];
    columns?: string[];
    metadata?: Record<string, any>;
  };

  @Column({
    name: "presence_key",
    type: "varchar",
    length: 255,
    nullable: true
  })
  presenceKey?: string;

  @Column({
    name: "presence_metadata",
    type: "json",
    nullable: true
  })
  presenceMetadata?: Record<string, any>;

  @Column({
    name: "connection_metadata",
    type: "json",
    nullable: true
  })
  connectionMetadata?: Record<string, any>;

  @Column({
    name: "last_activity",
    type: "timestamp",
    default: () => "CURRENT_TIMESTAMP"
  })
  lastActivity: Date;

  @Column({
    name: "is_active",
    type: "boolean",
    default: true
  })
  isActive: boolean;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @ManyToOne(() => Channel, channel => channel.subscriptions)
  @JoinColumn({ name: "channel_id" })
  channel?: Channel;

  @BeforeInsert()
  setDefaults() {
    this.lastActivity = new Date();
  }

  // Update activity timestamp
  updateActivity(): void {
    this.lastActivity = new Date();
  }

  // Check if subscription matches filter
  matchesFilter(data: Record<string, any>): boolean {
    if (!this.subscriptionConfig?.filter) {
      return true; // No filter means match all
    }

    try {
      // Simple filter implementation
      // In a real implementation, you'd want a more sophisticated filter parser
      const filter = this.subscriptionConfig.filter;
      
      // Handle simple equality filters like "column=value"
      if (filter.includes('=')) {
        const [column, value] = filter.split('=');
        return data[column?.trim()] === value?.trim();
      }

      // Handle IN filters like "column.in.(value1,value2)"
      if (filter.includes('.in.')) {
        const [column, values] = filter.split('.in.');
        const valueArray = values.replace(/[()]/g, '').split(',').map(v => v.trim());
        return valueArray.includes(String(data[column?.trim()]));
      }

      // Handle NOT filters like "column.not.value"
      if (filter.includes('.not.')) {
        const [column, value] = filter.split('.not.');
        return data[column?.trim()] !== value?.trim();
      }

      return true;
    } catch (error) {
      console.warn('Filter parsing error:', error);
      return true; // Default to match if filter parsing fails
    }
  }

  // Check if event is subscribed
  isEventSubscribed(event: string): boolean {
    if (!this.subscriptionConfig?.events || this.subscriptionConfig.events.length === 0) {
      return true; // No event filter means subscribe to all
    }

    return this.subscriptionConfig.events.includes(event) || 
           this.subscriptionConfig.events.includes('*');
  }

  // Check if subscription is for database changes
  isDatabaseSubscription(): boolean {
    return this.channel?.type === 'database';
  }

  // Check if subscription is for presence
  isPresenceSubscription(): boolean {
    return this.channel?.type === 'presence';
  }

  // Check if subscription is for broadcast
  isBroadcastSubscription(): boolean {
    return this.channel?.type === 'broadcast';
  }

  // Get presence state
  getPresenceState(): { key: string; metadata: Record<string, any> } | null {
    if (!this.isPresenceSubscription() || !this.presenceKey) {
      return null;
    }

    return {
      key: this.presenceKey,
      metadata: this.presenceMetadata || {}
    };
  }

  // Update presence metadata
  updatePresence(key: string, metadata: Record<string, any>): void {
    this.presenceKey = key;
    this.presenceMetadata = metadata;
    this.updateActivity();
  }

  // Check if subscription is stale (inactive for too long)
  isStale(maxInactiveMinutes = 30): boolean {
    const now = new Date();
    const inactiveTime = now.getTime() - this.lastActivity.getTime();
    const maxInactiveTime = maxInactiveMinutes * 60 * 1000;
    
    return inactiveTime > maxInactiveTime;
  }

  // Convert to safe object
  toSafeObject() {
    return {
      id: this.id,
      channelId: this.channelId,
      clientId: this.clientId,
      userId: this.userId,
      workspaceId: this.workspaceId,
      subscriptionConfig: this.subscriptionConfig,
      presenceKey: this.presenceKey,
      presenceMetadata: this.presenceMetadata,
      lastActivity: this.lastActivity,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}