import { AppDataSource } from "../../infra/database/data-source";
import { EventEmitter } from "events";
import { RealtimeService } from "./realtime.service";

export interface DatabaseChange {
  id: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  schema: string;
  oldRecord?: any;
  newRecord?: any;
  columns: string[];
  commitTimestamp: Date;
  workspaceId?: string;
}

export interface SubscriptionFilter {
  table: string;
  schema?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string; // SQL WHERE clause
  columns?: string[];
}

export class DatabaseChangeStreamsService extends EventEmitter {
  private realtimeService: RealtimeService;
  private isListening = false;
  private activeSubscriptions: Map<string, SubscriptionFilter[]> = new Map();
  private connection = AppDataSource.manager.connection;
  private pollingInterval?: NodeJS.Timeout;
  private lastLSN: string | null = null;

  constructor() {
    super();
    this.realtimeService = new RealtimeService();
    this.setupEventHandlers();
  }

  /**
   * Inicia o sistema de change streams
   */
  async startChangeStreams(): Promise<void> {
    if (this.isListening) {
      console.log('Database change streams already listening');
      return;
    }

    try {
      await this.setupLogicalReplication();
      await this.createTriggerFunctions();
      this.startPolling();
      this.isListening = true;
      console.log('✅ Database change streams started successfully');
    } catch (error) {
      console.error('❌ Failed to start database change streams:', error);
      throw error;
    }
  }

  /**
   * Para o sistema de change streams
   */
  async stopChangeStreams(): Promise<void> {
    if (!this.isListening) {
      return;
    }

    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = undefined;
    }

    this.isListening = false;
    this.activeSubscriptions.clear();
    console.log('🛑 Database change streams stopped');
  }

  /**
   * Subscreve mudanças em uma tabela
   */
  async subscribeToTable(
    workspaceId: string,
    channelId: string,
    filter: SubscriptionFilter
  ): Promise<void> {
    try {
      // Validar se a tabela existe
      await this.validateTable(filter.table, filter.schema);

      // Adicionar subscription
      const subscriptions = this.activeSubscriptions.get(channelId) || [];
      subscriptions.push({
        ...filter,
        schema: filter.schema || 'public'
      });
      this.activeSubscriptions.set(channelId, subscriptions);

      // Criar triggers na tabela se necessário
      await this.createTableTriggers(filter.table, filter.schema);

      console.log(`✅ Subscribed to table ${filter.table} for channel ${channelId}`);
    } catch (error) {
      console.error(`❌ Failed to subscribe to table ${filter.table}:`, error);
      throw error;
    }
  }

  /**
   * Remove subscription de uma tabela
   */
  async unsubscribeFromTable(channelId: string, tableName?: string): Promise<void> {
    if (tableName) {
      // Remover subscription específica
      const subscriptions = this.activeSubscriptions.get(channelId) || [];
      const filtered = subscriptions.filter(sub => sub.table !== tableName);
      
      if (filtered.length > 0) {
        this.activeSubscriptions.set(channelId, filtered);
      } else {
        this.activeSubscriptions.delete(channelId);
      }
    } else {
      // Remover todas as subscriptions do channel
      this.activeSubscriptions.delete(channelId);
    }

    console.log(`✅ Unsubscribed from table ${tableName || 'all'} for channel ${channelId}`);
  }

  /**
   * Obtém estatísticas das subscriptions ativas
   */
  getSubscriptionStats(): {
    totalChannels: number;
    totalSubscriptions: number;
    tableBreakdown: Record<string, number>;
  } {
    const totalChannels = this.activeSubscriptions.size;
    let totalSubscriptions = 0;
    const tableBreakdown: Record<string, number> = {};

    for (const subscriptions of this.activeSubscriptions.values()) {
      totalSubscriptions += subscriptions.length;
      
      for (const sub of subscriptions) {
        const key = `${sub.schema || 'public'}.${sub.table}`;
        tableBreakdown[key] = (tableBreakdown[key] || 0) + 1;
      }
    }

    return {
      totalChannels,
      totalSubscriptions,
      tableBreakdown
    };
  }

  /**
   * Configura logical replication (PostgreSQL)
   */
  private async setupLogicalReplication(): Promise<void> {
    try {
      // Verificar se wal_level está configurado corretamente
      const walLevel = await this.connection.query('SHOW wal_level');
      if (walLevel[0]?.wal_level !== 'logical') {
        console.warn('⚠️ PostgreSQL wal_level is not set to "logical". Change streams may not work optimally.');
      }

      // Criar slot de replicação se não existir
      const slotName = 'ebaas_realtime_slot';
      const existingSlots = await this.connection.query(`
        SELECT slot_name FROM pg_replication_slots WHERE slot_name = $1
      `, [slotName]);

      if (existingSlots.length === 0) {
        try {
          await this.connection.query(`
            SELECT pg_create_logical_replication_slot($1, 'pgoutput')
          `, [slotName]);
          console.log('✅ Logical replication slot created');
        } catch (error) {
          console.warn('⚠️ Could not create replication slot, falling back to trigger-based approach');
        }
      }
    } catch (error) {
      console.warn('⚠️ Logical replication setup failed, using trigger-based approach:', error);
    }
  }

  /**
   * Cria funções de trigger para capturar mudanças
   */
  private async createTriggerFunctions(): Promise<void> {
    try {
      // Criar schema para realtime se não existir
      await this.connection.query(`
        CREATE SCHEMA IF NOT EXISTS realtime
      `);

      // Criar tabela para armazenar mudanças
      await this.connection.query(`
        CREATE TABLE IF NOT EXISTS realtime.database_changes (
          id BIGSERIAL PRIMARY KEY,
          operation VARCHAR(10) NOT NULL,
          table_name VARCHAR(255) NOT NULL,
          schema_name VARCHAR(255) NOT NULL DEFAULT 'public',
          old_record JSONB,
          new_record JSONB,
          changed_columns TEXT[],
          workspace_id VARCHAR(255),
          commit_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          processed BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `);

      // Criar índices para performance
      await this.connection.query(`
        CREATE INDEX IF NOT EXISTS idx_database_changes_processed 
        ON realtime.database_changes (processed, commit_timestamp)
      `);

      await this.connection.query(`
        CREATE INDEX IF NOT EXISTS idx_database_changes_table 
        ON realtime.database_changes (table_name, schema_name)
      `);

      // Criar função trigger genérica
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION realtime.capture_changes()
        RETURNS TRIGGER AS $$
        DECLARE
          old_record JSONB;
          new_record JSONB;
          changed_cols TEXT[];
          workspace_id_val TEXT;
        BEGIN
          -- Obter workspace_id se a coluna existir
          workspace_id_val := NULL;
          
          IF TG_OP = 'DELETE' THEN
            old_record := to_jsonb(OLD);
            new_record := NULL;
            
            -- Tentar obter workspace_id do registro antigo
            IF OLD ? 'workspace_id' THEN
              workspace_id_val := OLD.workspace_id;
            ELSIF OLD ? 'workspaceId' THEN
              workspace_id_val := OLD.workspaceId;
            END IF;
            
          ELSIF TG_OP = 'INSERT' THEN
            old_record := NULL;
            new_record := to_jsonb(NEW);
            
            -- Tentar obter workspace_id do registro novo
            IF NEW ? 'workspace_id' THEN
              workspace_id_val := NEW.workspace_id;
            ELSIF NEW ? 'workspaceId' THEN
              workspace_id_val := NEW.workspaceId;
            END IF;
            
          ELSIF TG_OP = 'UPDATE' THEN
            old_record := to_jsonb(OLD);
            new_record := to_jsonb(NEW);
            
            -- Detectar colunas alteradas
            changed_cols := ARRAY(
              SELECT key FROM jsonb_each(new_record) 
              WHERE NOT (old_record ? key AND old_record->key = new_record->key)
            );
            
            -- Tentar obter workspace_id do registro novo
            IF NEW ? 'workspace_id' THEN
              workspace_id_val := NEW.workspace_id;
            ELSIF NEW ? 'workspaceId' THEN
              workspace_id_val := NEW.workspaceId;
            END IF;
          END IF;

          -- Inserir mudança na tabela de log
          INSERT INTO realtime.database_changes (
            operation,
            table_name,
            schema_name,
            old_record,
            new_record,
            changed_columns,
            workspace_id
          ) VALUES (
            TG_OP,
            TG_TABLE_NAME,
            TG_TABLE_SCHEMA,
            old_record,
            new_record,
            changed_cols,
            workspace_id_val
          );

          -- Retornar o registro apropriado
          IF TG_OP = 'DELETE' THEN
            RETURN OLD;
          ELSE
            RETURN NEW;
          END IF;
        END;
        $$ LANGUAGE plpgsql;
      `);

      console.log('✅ Database change trigger functions created');
    } catch (error) {
      console.error('❌ Failed to create trigger functions:', error);
      throw error;
    }
  }

  /**
   * Cria triggers para uma tabela específica
   */
  private async createTableTriggers(tableName: string, schema = 'public'): Promise<void> {
    try {
      const triggerName = `realtime_${tableName}_trigger`;
      
      // Verificar se o trigger já existe
      const existingTrigger = await this.connection.query(`
        SELECT trigger_name FROM information_schema.triggers 
        WHERE trigger_name = $1 AND event_object_table = $2 AND event_object_schema = $3
      `, [triggerName, tableName, schema]);

      if (existingTrigger.length === 0) {
        // Criar trigger
        await this.connection.query(`
          CREATE TRIGGER ${triggerName}
          AFTER INSERT OR UPDATE OR DELETE ON ${schema}.${tableName}
          FOR EACH ROW EXECUTE FUNCTION realtime.capture_changes()
        `);

        console.log(`✅ Trigger created for table ${schema}.${tableName}`);
      }
    } catch (error) {
      console.error(`❌ Failed to create trigger for table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Inicia polling para processar mudanças
   */
  private startPolling(): void {
    this.pollingInterval = setInterval(async () => {
      await this.processUnprocessedChanges();
    }, 1000); // Poll every second

    console.log('✅ Database change polling started');
  }

  /**
   * Processa mudanças não processadas
   */
  private async processUnprocessedChanges(): Promise<void> {
    try {
      const changes = await this.connection.query(`
        SELECT * FROM realtime.database_changes 
        WHERE processed = FALSE 
        ORDER BY commit_timestamp ASC 
        LIMIT 100
      `);

      for (const change of changes) {
        await this.processChange(change);
        
        // Marcar como processado
        await this.connection.query(`
          UPDATE realtime.database_changes 
          SET processed = TRUE 
          WHERE id = $1
        `, [change.id]);
      }

      // Limpar mudanças antigas (mais de 24 horas)
      if (changes.length === 0) {
        await this.connection.query(`
          DELETE FROM realtime.database_changes 
          WHERE processed = TRUE 
          AND commit_timestamp < NOW() - INTERVAL '24 hours'
        `);
      }
    } catch (error) {
      console.error('❌ Error processing database changes:', error);
    }
  }

  /**
   * Processa uma mudança individual
   */
  private async processChange(change: any): Promise<void> {
    const databaseChange: DatabaseChange = {
      id: change.id.toString(),
      operation: change.operation,
      table: change.table_name,
      schema: change.schema_name,
      oldRecord: change.old_record,
      newRecord: change.new_record,
      columns: change.changed_columns || [],
      commitTimestamp: change.commit_timestamp,
      workspaceId: change.workspace_id
    };

    // Verificar se há subscriptions ativas para esta mudança
    const matchingChannels = this.findMatchingSubscriptions(databaseChange);
    
    for (const channelId of matchingChannels) {
      await this.realtimeService.broadcastDatabaseChange(channelId, databaseChange);
    }

    // Emitir evento para outros listeners
    this.emit('database_change', databaseChange);
  }

  /**
   * Encontra subscriptions que correspondem a uma mudança
   */
  private findMatchingSubscriptions(change: DatabaseChange): string[] {
    const matchingChannels: string[] = [];

    for (const [channelId, subscriptions] of this.activeSubscriptions) {
      for (const subscription of subscriptions) {
        if (this.doesChangeMatchSubscription(change, subscription)) {
          matchingChannels.push(channelId);
          break; // Uma correspondência por canal é suficiente
        }
      }
    }

    return matchingChannels;
  }

  /**
   * Verifica se uma mudança corresponde a uma subscription
   */
  private doesChangeMatchSubscription(change: DatabaseChange, subscription: SubscriptionFilter): boolean {
    // Verificar tabela
    if (subscription.table !== change.table) {
      return false;
    }

    // Verificar schema
    if (subscription.schema && subscription.schema !== change.schema) {
      return false;
    }

    // Verificar operação
    if (subscription.event && subscription.event !== '*' && subscription.event !== change.operation) {
      return false;
    }

    // TODO: Implementar filtro SQL customizado
    if (subscription.filter) {
      // Por enquanto, aceitar todos se filtro estiver presente
      // Em uma implementação completa, seria necessário avaliar o filtro SQL
    }

    // Verificar colunas específicas
    if (subscription.columns && subscription.columns.length > 0) {
      const hasMatchingColumn = subscription.columns.some(col => 
        change.columns.includes(col)
      );
      if (!hasMatchingColumn && change.operation === 'UPDATE') {
        return false;
      }
    }

    return true;
  }

  /**
   * Valida se uma tabela existe
   */
  private async validateTable(tableName: string, schema = 'public'): Promise<void> {
    const exists = await this.connection.query(`
      SELECT 1 FROM information_schema.tables 
      WHERE table_name = $1 AND table_schema = $2
    `, [tableName, schema]);

    if (exists.length === 0) {
      throw new Error(`Table ${schema}.${tableName} does not exist`);
    }
  }

  /**
   * Configura event handlers
   */
  private setupEventHandlers(): void {
    this.realtimeService.on('subscription:created', async (data) => {
      const { channelId, filter } = data;
      if (filter && filter.table) {
        await this.subscribeToTable(data.workspaceId, channelId, filter);
      }
    });

    this.realtimeService.on('subscription:removed', async (data) => {
      const { channelId, filter } = data;
      if (filter && filter.table) {
        await this.unsubscribeFromTable(channelId, filter.table);
      }
    });
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    isListening: boolean;
    activeSubscriptions: number;
    pendingChanges: number;
  }> {
    try {
      let pendingChanges = 0;
      try {
        const result = await this.connection.query(`
          SELECT COUNT(*) as count FROM realtime.database_changes WHERE processed = FALSE
        `);
        pendingChanges = parseInt(result[0]?.count || '0');
      } catch (error) {
        // Tabela pode não existir ainda
      }

      const totalSubscriptions = Array.from(this.activeSubscriptions.values())
        .reduce((sum, subs) => sum + subs.length, 0);

      return {
        status: this.isListening ? 'healthy' : 'degraded',
        isListening: this.isListening,
        activeSubscriptions: totalSubscriptions,
        pendingChanges
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        isListening: false,
        activeSubscriptions: 0,
        pendingChanges: 0
      };
    }
  }
}