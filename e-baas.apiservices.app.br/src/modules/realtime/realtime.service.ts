import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { Channel } from "./entity/Channel.entity";
import { ChannelSubscription } from "./entity/ChannelSubscription.entity";
import {
  SubscribeChannelDto,
  UnsubscribeChannelDto,
  BroadcastMessageDto,
  PresenceUpdateDto,
  DatabaseSubscriptionDto,
  CreateChannelDto,
  RealtimeMessage,
  DatabaseChangePayload,
  PresenceState,
  ChannelStats,
  ConnectionInfo,
  ChannelType,
  DatabaseEvent
} from "./dto/realtime.dto";
import { EventEmitter } from "events";
import { v4 as uuidv4 } from "uuid";
import { DatabaseChange } from "./database-change-streams.service";

export class RealtimeService extends EventEmitter {
  private channelRepository: Repository<Channel>;
  private subscriptionRepository: Repository<ChannelSubscription>;
  
  // In-memory store for active connections and presence
  private connections: Map<string, ConnectionInfo> = new Map();
  private presenceState: Map<string, PresenceState> = new Map();
  private messageQueue: Map<string, RealtimeMessage[]> = new Map();

  constructor() {
    super();
    this.channelRepository = AppDataSource.getRepository(Channel);
    this.subscriptionRepository = AppDataSource.getRepository(ChannelSubscription);
    
    // Start cleanup interval for stale connections
    this.startCleanupInterval();
  }

  // Channel Management
  async createChannel(createChannelDto: CreateChannelDto, createdBy?: string): Promise<Channel> {
    // Check if channel already exists
    const existingChannel = await this.channelRepository.findOne({
      where: {
        name: createChannelDto.name,
        workspaceId: createChannelDto.workspaceId
      }
    });

    if (existingChannel) {
      throw new Error(`Channel '${createChannelDto.name}' already exists in this workspace`);
    }

    // Create channel
    const channel = this.channelRepository.create({
      ...createChannelDto,
      createdBy,
      connectionCount: 0,
      messageCount: 0
    });

    await this.channelRepository.save(channel);
    return channel;
  }

  async getChannel(channelName: string, workspaceId: string): Promise<Channel | null> {
    return await this.channelRepository.findOne({
      where: { name: channelName, workspaceId, isActive: true }
    });
  }

  async listChannels(workspaceId: string, type?: ChannelType): Promise<Channel[]> {
    const whereClause: any = { workspaceId, isActive: true };
    if (type) {
      whereClause.type = type;
    }

    return await this.channelRepository.find({
      where: whereClause,
      order: { createdAt: 'DESC' }
    });
  }

  async deleteChannel(channelName: string, workspaceId: string): Promise<{ message: string }> {
    const channel = await this.channelRepository.findOne({
      where: { name: channelName, workspaceId, isActive: true }
    });

    if (!channel) {
      throw new Error('Channel not found');
    }

    // Soft delete channel
    channel.isActive = false;
    await this.channelRepository.save(channel);

    // Remove all subscriptions
    await this.subscriptionRepository.update(
      { channelId: channel.id },
      { isActive: false }
    );

    // Emit channel deletion event
    this.emit('channel:deleted', {
      channelId: channel.id,
      workspaceId,
      channelName
    });

    return { message: `Channel '${channelName}' deleted successfully` };
  }

  // Subscription Management
  async subscribeToChannel(
    subscribeDto: SubscribeChannelDto,
    clientId: string,
    userId?: string
  ): Promise<{ success: boolean; channel: Channel }> {
    // Get or create channel
    let channel = await this.getChannel(subscribeDto.channel, subscribeDto.workspaceId);
    
    if (!channel) {
      // Auto-create channel for database subscriptions
      if (subscribeDto.type === ChannelType.DATABASE && subscribeDto.config?.table) {
        channel = await this.createChannel({
          name: subscribeDto.channel,
          workspaceId: subscribeDto.workspaceId,
          type: ChannelType.DATABASE,
          config: subscribeDto.config,
          description: `Database subscription for table ${subscribeDto.config.table}`
        });
      } else {
        throw new Error('Channel not found');
      }
    }

    // Check if channel can accept new connections
    if (!channel.canAcceptConnection()) {
      throw new Error('Channel cannot accept new connections');
    }

    // Check access permissions
    if (!channel.hasAccess(userId)) {
      throw new Error('Access denied to channel');
    }

    // Check if subscription already exists
    const existingSubscription = await this.subscriptionRepository.findOne({
      where: { channelId: channel.id, clientId, isActive: true }
    });

    if (existingSubscription) {
      // Update existing subscription
      existingSubscription.subscriptionConfig = subscribeDto.config;
      existingSubscription.updateActivity();
      await this.subscriptionRepository.save(existingSubscription);
    } else {
      // Create new subscription
      const subscription = this.subscriptionRepository.create({
        channelId: channel.id,
        clientId,
        userId,
        workspaceId: subscribeDto.workspaceId,
        subscriptionConfig: subscribeDto.config,
        presenceKey: subscribeDto.presence?.key,
        presenceMetadata: subscribeDto.presence?.metadata
      });

      await this.subscriptionRepository.save(subscription);

      // Update channel connection count
      channel.updateConnectionCount(1);
      await this.channelRepository.save(channel);
    }

    // Update connection info
    this.updateConnectionInfo(clientId, userId, subscribeDto.workspaceId, channel.name);

    // Handle presence subscription
    if (channel.isPresenceChannel() && subscribeDto.presence) {
      await this.updatePresence({
        channel: subscribeDto.channel,
        workspaceId: subscribeDto.workspaceId,
        key: subscribeDto.presence.key || clientId,
        metadata: subscribeDto.presence.metadata || {}
      }, clientId);
    }

    // Emit subscription event
    this.emit('subscription:created', {
      channelId: channel.id,
      clientId,
      userId,
      workspaceId: subscribeDto.workspaceId
    });

    return { success: true, channel };
  }

  async unsubscribeFromChannel(
    unsubscribeDto: UnsubscribeChannelDto,
    clientId: string
  ): Promise<{ success: boolean }> {
    const channel = await this.getChannel(unsubscribeDto.channel, unsubscribeDto.workspaceId);
    
    if (!channel) {
      throw new Error('Channel not found');
    }

    // Remove subscription
    const subscription = await this.subscriptionRepository.findOne({
      where: { channelId: channel.id, clientId, isActive: true }
    });

    if (subscription) {
      subscription.isActive = false;
      await this.subscriptionRepository.save(subscription);

      // Update channel connection count
      channel.updateConnectionCount(-1);
      await this.channelRepository.save(channel);

      // Handle presence unsubscription
      if (channel.isPresenceChannel()) {
        await this.removeFromPresence(unsubscribeDto.channel, unsubscribeDto.workspaceId, clientId);
      }
    }

    // Update connection info
    this.removeChannelFromConnection(clientId, unsubscribeDto.channel);

    // Emit unsubscription event
    this.emit('subscription:removed', {
      channelId: channel.id,
      clientId,
      workspaceId: unsubscribeDto.workspaceId
    });

    return { success: true };
  }

  // Message Broadcasting
  async broadcastMessage(
    broadcastDto: BroadcastMessageDto,
    senderId?: string
  ): Promise<{ success: boolean; messageId: string }> {
    const channel = await this.getChannel(broadcastDto.channel, broadcastDto.workspaceId);
    
    if (!channel) {
      throw new Error('Channel not found');
    }

    if (!channel.isEventAllowed(broadcastDto.event)) {
      throw new Error('Event not allowed in this channel');
    }

    // Create message
    const message: RealtimeMessage = {
      id: uuidv4(),
      channel: broadcastDto.channel,
      event: broadcastDto.event,
      payload: broadcastDto.payload,
      timestamp: new Date(),
      clientId: senderId || 'system',
      type: channel.type
    };

    // Get active subscriptions for the channel
    const subscriptions = await this.subscriptionRepository.find({
      where: { channelId: channel.id, isActive: true }
    });

    // Filter subscriptions and broadcast
    const targetClients: string[] = [];
    
    for (const subscription of subscriptions) {
      // Skip excluded clients
      if (broadcastDto.excludeClientIds?.includes(subscription.clientId)) {
        continue;
      }

      // Check if subscription is interested in this event
      if (!subscription.isEventSubscribed(broadcastDto.event)) {
        continue;
      }

      targetClients.push(subscription.clientId);
    }

    // Emit message to subscribers
    this.emit('message:broadcast', {
      message,
      targetClients,
      workspaceId: broadcastDto.workspaceId
    });

    // Update channel statistics
    channel.incrementMessageCount();
    await this.channelRepository.save(channel);

    return { success: true, messageId: message.id };
  }

  // Database Change Notifications
  async notifyDatabaseChange(payload: DatabaseChangePayload, workspaceId: string): Promise<void> {
    // Find database subscriptions for this table
    const subscriptions = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .innerJoin('subscription.channel', 'channel')
      .where('channel.type = :type', { type: ChannelType.DATABASE })
      .andWhere('channel.workspaceId = :workspaceId', { workspaceId })
      .andWhere('subscription.isActive = :isActive', { isActive: true })
      .getMany();

    const relevantSubscriptions = subscriptions.filter(subscription => {
      const config = subscription.channel?.getDatabaseConfig();
      
      // Check table match
      if (config?.table && config.table !== payload.table) {
        return false;
      }

      // Check schema match
      if (config?.schema && config.schema !== payload.schema) {
        return false;
      }

      // Check event match
      if (config?.event && config.event !== '*' && config.event !== payload.eventType) {
        return false;
      }

      // Check filter match
      const data = payload.new || payload.old || {};
      if (!subscription.matchesFilter(data)) {
        return false;
      }

      return true;
    });

    // Create message for each relevant subscription
    for (const subscription of relevantSubscriptions) {
      const message: RealtimeMessage = {
        id: uuidv4(),
        channel: subscription.channel?.name || '',
        event: payload.eventType,
        payload: {
          table: payload.table,
          schema: payload.schema,
          eventType: payload.eventType,
          old: payload.old,
          new: payload.new,
          columns: payload.columns,
          timestamp: payload.timestamp,
          commitTimestamp: payload.commitTimestamp
        },
        timestamp: new Date(),
        clientId: 'database',
        type: ChannelType.DATABASE
      };

      // Emit to specific client
      this.emit('message:database', {
        message,
        targetClient: subscription.clientId,
        workspaceId
      });
    }
  }

  // Presence Management
  async updatePresence(
    presenceDto: PresenceUpdateDto,
    clientId: string
  ): Promise<PresenceState> {
    const channel = await this.getChannel(presenceDto.channel, presenceDto.workspaceId);
    
    if (!channel || !channel.isPresenceChannel()) {
      throw new Error('Invalid presence channel');
    }

    // Update subscription presence data
    const subscription = await this.subscriptionRepository.findOne({
      where: { channelId: channel.id, clientId, isActive: true }
    });

    if (subscription) {
      subscription.updatePresence(presenceDto.key, presenceDto.metadata);
      await this.subscriptionRepository.save(subscription);
    }

    // Update in-memory presence state
    const channelTopic = channel.getTopic();
    let presenceState = this.presenceState.get(channelTopic) || {};
    
    presenceState[presenceDto.key] = {
      metadata: presenceDto.metadata,
      joinedAt: presenceState[presenceDto.key]?.joinedAt || new Date(),
      lastSeen: new Date()
    };

    this.presenceState.set(channelTopic, presenceState);

    // Broadcast presence update
    await this.broadcastMessage({
      channel: presenceDto.channel,
      workspaceId: presenceDto.workspaceId,
      event: 'presence_update',
      payload: {
        key: presenceDto.key,
        metadata: presenceDto.metadata,
        event: 'presence_update'
      }
    });

    return presenceState;
  }

  async getPresenceState(channelName: string, workspaceId: string): Promise<PresenceState> {
    const channel = await this.getChannel(channelName, workspaceId);
    
    if (!channel) {
      throw new Error('Channel not found');
    }

    const channelTopic = channel.getTopic();
    return this.presenceState.get(channelTopic) || {};
  }

  async removeFromPresence(channelName: string, workspaceId: string, clientId: string): Promise<void> {
    const channel = await this.getChannel(channelName, workspaceId);
    
    if (!channel) {
      return;
    }

    // Find subscription to get presence key
    const subscription = await this.subscriptionRepository.findOne({
      where: { channelId: channel.id, clientId }
    });

    if (!subscription?.presenceKey) {
      return;
    }

    // Remove from presence state
    const channelTopic = channel.getTopic();
    const presenceState = this.presenceState.get(channelTopic) || {};
    
    delete presenceState[subscription.presenceKey];
    this.presenceState.set(channelTopic, presenceState);

    // Broadcast presence leave
    await this.broadcastMessage({
      channel: channelName,
      workspaceId,
      event: 'presence_leave',
      payload: {
        key: subscription.presenceKey,
        event: 'presence_leave'
      }
    });
  }

  // Connection Management
  registerConnection(clientId: string, userId?: string, workspaceId?: string, metadata?: Record<string, any>): void {
    const connectionInfo: ConnectionInfo = {
      id: clientId,
      userId,
      workspaceId: workspaceId || '',
      channels: [],
      connectedAt: new Date(),
      lastActivity: new Date(),
      metadata: metadata || {}
    };

    this.connections.set(clientId, connectionInfo);
  }

  unregisterConnection(clientId: string): void {
    const connection = this.connections.get(clientId);
    
    if (connection) {
      // Remove from all channels
      for (const channelName of connection.channels) {
        this.removeChannelFromConnection(clientId, channelName);
      }
    }

    this.connections.delete(clientId);
  }

  private updateConnectionInfo(clientId: string, userId?: string, workspaceId?: string, channelName?: string): void {
    let connection = this.connections.get(clientId);
    
    if (!connection) {
      this.registerConnection(clientId, userId, workspaceId);
      connection = this.connections.get(clientId)!;
    }

    connection.lastActivity = new Date();
    
    if (channelName && !connection.channels.includes(channelName)) {
      connection.channels.push(channelName);
    }

    if (userId) connection.userId = userId;
    if (workspaceId) connection.workspaceId = workspaceId;
  }

  private removeChannelFromConnection(clientId: string, channelName: string): void {
    const connection = this.connections.get(clientId);
    
    if (connection) {
      connection.channels = connection.channels.filter(ch => ch !== channelName);
      connection.lastActivity = new Date();
    }
  }

  // Statistics and Monitoring
  async getChannelStats(workspaceId: string): Promise<ChannelStats[]> {
    const channels = await this.channelRepository.find({
      where: { workspaceId, isActive: true }
    });

    return channels.map(channel => ({
      name: channel.name,
      type: channel.type,
      connectionCount: channel.connectionCount,
      messageCount: channel.messageCount,
      createdAt: channel.createdAt,
      lastActivity: channel.lastActivity || channel.createdAt
    }));
  }

  getActiveConnections(): ConnectionInfo[] {
    return Array.from(this.connections.values());
  }

  getConnectionsForWorkspace(workspaceId: string): ConnectionInfo[] {
    return Array.from(this.connections.values()).filter(
      conn => conn.workspaceId === workspaceId
    );
  }

  // Cleanup and Maintenance
  private startCleanupInterval(): void {
    setInterval(async () => {
      await this.cleanupStaleConnections();
      await this.cleanupStaleSubscriptions();
    }, 5 * 60 * 1000); // Run every 5 minutes
  }

  private async cleanupStaleConnections(): Promise<void> {
    const staleConnections: string[] = [];
    const now = new Date();
    const maxInactiveTime = 30 * 60 * 1000; // 30 minutes

    for (const [clientId, connection] of this.connections.entries()) {
      if (now.getTime() - connection.lastActivity.getTime() > maxInactiveTime) {
        staleConnections.push(clientId);
      }
    }

    for (const clientId of staleConnections) {
      this.unregisterConnection(clientId);
    }

    if (staleConnections.length > 0) {
      console.log(`Cleaned up ${staleConnections.length} stale connections`);
    }
  }

  private async cleanupStaleSubscriptions(): Promise<void> {
    const staleSubscriptions = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .where('subscription.isActive = :isActive', { isActive: true })
      .andWhere('subscription.lastActivity < :threshold', {
        threshold: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
      })
      .getMany();

    for (const subscription of staleSubscriptions) {
      subscription.isActive = false;
      await this.subscriptionRepository.save(subscription);

      // Update channel connection count
      const channel = await this.channelRepository.findOne({
        where: { id: subscription.channelId }
      });

      if (channel) {
        channel.updateConnectionCount(-1);
        await this.channelRepository.save(channel);
      }
    }

    if (staleSubscriptions.length > 0) {
      console.log(`Cleaned up ${staleSubscriptions.length} stale subscriptions`);
    }
  }

  // Database Change Streams Integration
  async broadcastDatabaseChange(channelId: string, change: DatabaseChange): Promise<void> {
    try {
      // Obter channel
      const channel = await this.channelRepository.findOne({
        where: { id: channelId, isActive: true }
      });

      if (!channel) {
        console.warn(`Channel ${channelId} not found for database change`);
        return;
      }

      // Obter subscriptions ativas do channel
      const subscriptions = await this.subscriptionRepository.find({
        where: { channelId, isActive: true }
      });

      if (subscriptions.length === 0) {
        return;
      }

      // Criar mensagem de database change
      const message = {
        id: uuidv4(),
        event: `database:${change.operation.toLowerCase()}`,
        payload: {
          schema: change.schema,
          table: change.table,
          operation: change.operation,
          old: change.oldRecord,
          new: change.newRecord,
          columns: change.columns,
          commitTimestamp: change.commitTimestamp.toISOString(),
          eventType: 'database_change'
        } as any,
        timestamp: new Date(),
        channel: channel.name,
        workspaceId: channel.workspaceId
      } as any;

      // Broadcast para cada client subscription
      for (const subscription of subscriptions) {
        this.emit('message:database', {
          message,
          targetClient: subscription.clientId
        });
      }

      // Atualizar estatísticas do canal
      channel.incrementMessageCount();
      await this.channelRepository.save(channel);

      console.log(`📡 Database change broadcasted to ${subscriptions.length} clients in channel ${channel.name}`);
    } catch (error) {
      console.error('❌ Failed to broadcast database change:', error);
    }
  }

  async subscribeToDatabaseChanges(
    channelName: string,
    workspaceId: string,
    subscriptionDto: DatabaseSubscriptionDto,
    clientId: string,
    userId?: string
  ): Promise<{ channel: Channel; subscription: ChannelSubscription }> {
    try {
      // Verificar/criar channel para database subscriptions
      let channel = await this.getChannel(channelName, workspaceId);
      
      if (!channel) {
        channel = await this.createChannel({
          name: channelName,
          type: ChannelType.DATABASE,
          workspaceId,
          isPrivate: false,
          config: {
            enablePresence: false,
            enableBroadcast: false,
            enableDatabase: true,
            maxSubscriptions: 1000
          }
        }, userId);
      }

      // Verificar se já existe subscription ativa
      const existingSubscription = await this.subscriptionRepository.findOne({
        where: {
          channelId: channel.id,
          clientId,
          isActive: true
        }
      });

      if (existingSubscription) {
        // Atualizar subscription existente com novos filtros
        (existingSubscription as any).databaseFilter = subscriptionDto;
        existingSubscription.lastActivity = new Date();
        await this.subscriptionRepository.save(existingSubscription);

        return { channel, subscription: existingSubscription };
      }

      // Criar nova subscription
      const subscription = this.subscriptionRepository.create({
        channel: channel,
        clientId,
        userId,
        workspaceId,
        databaseFilter: subscriptionDto,
        joinedAt: new Date(),
        lastActivity: new Date(),
        isActive: true
      } as any);

      await this.subscriptionRepository.save(subscription);

      // Atualizar contador de conexões do canal
      channel.updateConnectionCount(1);
      await this.channelRepository.save(channel);

      // Atualizar info de conexão
      this.updateConnectionInfo(clientId, userId, workspaceId, channelName);

      // Emitir evento para database change streams service
      this.emit('subscription:created', {
        channelId: channel.id,
        workspaceId,
        filter: subscriptionDto
      });

      console.log(`✅ Database subscription created: ${clientId} -> ${channelName}`);

      return { channel, subscription: subscription as any };
    } catch (error) {
      console.error('❌ Failed to create database subscription:', error);
      throw error;
    }
  }

  async getDatabaseSubscriptions(workspaceId: string): Promise<{
    channelName: string;
    subscriptionCount: number;
    filters: any[];
  }[]> {
    try {
      const subscriptions = await this.subscriptionRepository
        .createQueryBuilder('subscription')
        .leftJoin('subscription.channel', 'channel')
        .where('subscription.workspaceId = :workspaceId', { workspaceId })
        .andWhere('subscription.isActive = :isActive', { isActive: true })
        .andWhere('channel.type = :type', { type: ChannelType.DATABASE })
        .select([
          'channel.name as channelName',
          'COUNT(subscription.id) as subscriptionCount',
          'ARRAY_AGG(subscription.databaseFilter) as filters'
        ])
        .groupBy('channel.name')
        .getRawMany();

      return subscriptions.map(sub => ({
        channelName: sub.channelName,
        subscriptionCount: parseInt(sub.subscriptionCount),
        filters: sub.filters || []
      }));
    } catch (error) {
      console.error('❌ Failed to get database subscriptions:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    connections: number;
    activeChannels: number;
    totalSubscriptions: number;
    memoryUsage: {
      connections: number;
      presenceStates: number;
      messageQueues: number;
    };
  }> {
    try {
      const activeChannels = await this.channelRepository.count({
        where: { isActive: true }
      });

      const totalSubscriptions = await this.subscriptionRepository.count({
        where: { isActive: true }
      });

      return {
        status: 'healthy',
        connections: this.connections.size,
        activeChannels,
        totalSubscriptions,
        memoryUsage: {
          connections: this.connections.size,
          presenceStates: this.presenceState.size,
          messageQueues: this.messageQueue.size
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        connections: 0,
        activeChannels: 0,
        totalSubscriptions: 0,
        memoryUsage: {
          connections: 0,
          presenceStates: 0,
          messageQueues: 0
        }
      };
    }
  }
}