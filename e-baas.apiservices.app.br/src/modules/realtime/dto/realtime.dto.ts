import { IsString, <PERSON>Optional, IsArray, IsBoolean, IsObject, IsEnum, IsNumber } from "class-validator";

export enum ChannelType {
  DATABASE = 'database',
  BROADCAST = 'broadcast',
  PRESENCE = 'presence'
}

export enum DatabaseEvent {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  ALL = '*'
}

export enum PresenceEvent {
  JOIN = 'presence_join',
  LEAVE = 'presence_leave',
  SYNC = 'presence_sync'
}

export enum BroadcastEvent {
  MESSAGE = 'broadcast',
  CUSTOM = 'custom'
}

export class SubscribeChannelDto {
  @IsString()
  channel: string;

  @IsString()
  workspaceId: string;

  @IsEnum(ChannelType)
  type: ChannelType;

  @IsOptional()
  @IsObject()
  config?: {
    table?: string;
    schema?: string;
    filter?: string;
    event?: DatabaseEvent | string;
    private?: boolean;
  };

  @IsOptional()
  @IsObject()
  presence?: {
    key?: string;
    metadata?: Record<string, any>;
  };
}

export class UnsubscribeChannelDto {
  @IsString()
  channel: string;

  @IsString()
  workspaceId: string;
}

export class BroadcastMessageDto {
  @IsString()
  channel: string;

  @IsString()
  workspaceId: string;

  @IsString()
  event: string;

  @IsObject()
  payload: Record<string, any>;

  @IsOptional()
  @IsString()
  type?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludeClientIds?: string[];
}

export class PresenceUpdateDto {
  @IsString()
  channel: string;

  @IsString()
  workspaceId: string;

  @IsString()
  key: string;

  @IsObject()
  metadata: Record<string, any>;
}

export class DatabaseSubscriptionDto {
  @IsString()
  workspaceId: string;

  @IsString()
  table: string;

  @IsOptional()
  @IsString()
  schema?: string = 'public';

  @IsOptional()
  @IsEnum(DatabaseEvent)
  event?: DatabaseEvent = DatabaseEvent.ALL;

  @IsOptional()
  @IsString()
  filter?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  columns?: string[];
}

export class CreateChannelDto {
  @IsString()
  name: string;

  @IsString()
  workspaceId: string;

  @IsEnum(ChannelType)
  type: ChannelType;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean = false;

  @IsOptional()
  @IsNumber()
  maxConnections?: number;

  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedEvents?: string[];
}

export interface RealtimeMessage {
  id: string;
  channel: string;
  event: string;
  payload: any;
  timestamp: Date;
  clientId: string;
  userId?: string;
  type: ChannelType;
}

export interface DatabaseChangePayload {
  table: string;
  schema: string;
  eventType: DatabaseEvent;
  old?: Record<string, any>;
  new?: Record<string, any>;
  columns: {
    name: string;
    type: string;
  }[];
  timestamp: Date;
  commitTimestamp: Date;
}

export interface PresenceState {
  [key: string]: {
    metadata: Record<string, any>;
    joinedAt: Date;
    lastSeen: Date;
  };
}

export interface ChannelStats {
  name: string;
  type: ChannelType;
  connectionCount: number;
  messageCount: number;
  createdAt: Date;
  lastActivity: Date;
}

export interface ConnectionInfo {
  id: string;
  userId?: string;
  workspaceId: string;
  channels: string[];
  connectedAt: Date;
  lastActivity: Date;
  metadata: Record<string, any>;
}

export interface RealtimeConfig {
  enableDatabase: boolean;
  enableBroadcast: boolean;
  enablePresence: boolean;
  maxConnectionsPerWorkspace: number;
  maxChannelsPerConnection: number;
  messageRetentionHours: number;
  heartbeatInterval: number;
  connectionTimeout: number;
}