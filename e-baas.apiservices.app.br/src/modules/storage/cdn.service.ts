import axios, { AxiosInstance } from "axios";
import config from "../../infra/config";

export interface CDNPurgeOptions {
  urls?: string[];
  prefixes?: string[];
  tags?: string[];
  purgeAll?: boolean;
}

export interface CDNPurgeResult {
  success: boolean;
  purgeId?: string;
  message?: string;
  error?: string;
}

export interface CDNMetrics {
  requests: number;
  bandwidth: number;
  cacheHitRatio: number;
  topUrls: Array<{ url: string; requests: number; bandwidth: number }>;
}

export abstract class CDNProvider {
  protected baseUrl: string;
  protected httpClient: AxiosInstance;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || "";
    this.httpClient = axios.create({
      timeout: 30000,
      headers: {
        'User-Agent': 'E-BaaS-Storage/1.0'
      }
    });
  }

  abstract purgeCache(options: CDNPurgeOptions): Promise<CDNPurgeResult>;
  abstract getMetrics(startDate?: Date, endDate?: Date): Promise<CDNMetrics>;
  abstract generateCdnUrl(path: string): string;
  abstract validateConfiguration(): boolean;
}

export class CloudflareCDNProvider extends CDNProvider {
  private apiToken: string;
  private zoneId: string;
  private accountId: string;

  constructor() {
    super(config.cdn.baseUrl);
    this.apiToken = config.cdn.cloudflare.apiToken;
    this.zoneId = config.cdn.cloudflare.zoneId;
    this.accountId = config.cdn.cloudflare.accountId;

    this.httpClient.defaults.headers.common['Authorization'] = `Bearer ${this.apiToken}`;
    this.httpClient.defaults.baseURL = 'https://api.cloudflare.com/v4';
  }

  validateConfiguration(): boolean {
    return !!(this.apiToken && this.zoneId && this.baseUrl);
  }

  generateCdnUrl(path: string): string {
    if (!this.baseUrl) return path;
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return `${this.baseUrl}/${cleanPath}`;
  }

  async purgeCache(options: CDNPurgeOptions): Promise<CDNPurgeResult> {
    try {
      if (!this.validateConfiguration()) {
        throw new Error('Cloudflare CDN not properly configured');
      }

      const payload: any = {};

      if (options.purgeAll) {
        payload.purge_everything = true;
      } else {
        if (options.urls) {
          payload.files = options.urls.map(url => 
            url.startsWith('http') ? url : this.generateCdnUrl(url)
          );
        }
        if (options.prefixes) {
          payload.prefixes = options.prefixes;
        }
        if (options.tags) {
          payload.tags = options.tags;
        }
      }

      const response = await this.httpClient.post(
        `/zones/${this.zoneId}/purge_cache`,
        payload
      );

      if (response.data.success) {
        return {
          success: true,
          purgeId: response.data.result?.id,
          message: 'Cache purged successfully'
        };
      } else {
        throw new Error(response.data.errors?.[0]?.message || 'Unknown error');
      }
    } catch (error: any) {
      console.error('Cloudflare cache purge failed:', error);
      return {
        success: false,
        error: error.message || 'Cache purge failed'
      };
    }
  }

  async getMetrics(startDate?: Date, endDate?: Date): Promise<CDNMetrics> {
    try {
      const since = startDate || new Date(Date.now() - 24 * 60 * 60 * 1000); // 24h ago
      const until = endDate || new Date();

      const params = {
        since: since.toISOString(),
        until: until.toISOString(),
        dimensions: 'responseContentType,clientCountryName',
        metrics: 'requests,bytes,cachedRequests,cachedBytes',
        sort: '-requests'
      };

      const response = await this.httpClient.get(
        `/zones/${this.zoneId}/analytics/dashboard`,
        { params }
      );

      if (response.data.success) {
        const data = response.data.result;
        const totalRequests = data.totals.requests || 0;
        const cachedRequests = data.totals.cachedRequests || 0;
        
        return {
          requests: totalRequests,
          bandwidth: data.totals.bytes || 0,
          cacheHitRatio: totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0,
          topUrls: [] // Cloudflare doesn't provide URL-level data in free tier
        };
      } else {
        throw new Error('Failed to fetch metrics');
      }
    } catch (error) {
      console.error('Failed to fetch Cloudflare metrics:', error);
      return {
        requests: 0,
        bandwidth: 0,
        cacheHitRatio: 0,
        topUrls: []
      };
    }
  }
}

export class AWSCloudFrontCDNProvider extends CDNProvider {
  private accessKeyId: string;
  private secretAccessKey: string;
  private region: string;
  private distributionId: string;

  constructor() {
    super(config.cdn.baseUrl);
    this.accessKeyId = config.cdn.aws.accessKeyId;
    this.secretAccessKey = config.cdn.aws.secretAccessKey;
    this.region = config.cdn.aws.region;
    this.distributionId = config.cdn.aws.distributionId;
  }

  validateConfiguration(): boolean {
    return !!(this.accessKeyId && this.secretAccessKey && this.distributionId && this.baseUrl);
  }

  generateCdnUrl(path: string): string {
    if (!this.baseUrl) return path;
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return `${this.baseUrl}/${cleanPath}`;
  }

  async purgeCache(options: CDNPurgeOptions): Promise<CDNPurgeResult> {
    try {
      if (!this.validateConfiguration()) {
        throw new Error('AWS CloudFront CDN not properly configured');
      }

      // Note: This is a simplified implementation
      // In production, you would use AWS SDK to create invalidation
      const paths = options.urls || ['/*'];
      
      console.log(`Would create CloudFront invalidation for paths: ${paths.join(', ')}`);
      
      return {
        success: true,
        message: 'CloudFront invalidation created (mock implementation)'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'CloudFront purge failed'
      };
    }
  }

  async getMetrics(startDate?: Date, endDate?: Date): Promise<CDNMetrics> {
    // Mock implementation - would use CloudWatch in production
    return {
      requests: 0,
      bandwidth: 0,
      cacheHitRatio: 0,
      topUrls: []
    };
  }
}

export class CustomCDNProvider extends CDNProvider {
  private apiEndpoint: string;
  private apiKey: string;
  private purgeEndpoint: string;

  constructor() {
    super(config.cdn.baseUrl);
    this.apiEndpoint = config.cdn.custom.apiEndpoint;
    this.apiKey = config.cdn.custom.apiKey;
    this.purgeEndpoint = config.cdn.custom.purgeEndpoint;

    if (this.apiKey) {
      this.httpClient.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
    }
  }

  validateConfiguration(): boolean {
    return !!(this.apiEndpoint && this.apiKey && this.baseUrl);
  }

  generateCdnUrl(path: string): string {
    if (!this.baseUrl) return path;
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return `${this.baseUrl}/${cleanPath}`;
  }

  async purgeCache(options: CDNPurgeOptions): Promise<CDNPurgeResult> {
    try {
      if (!this.validateConfiguration()) {
        throw new Error('Custom CDN not properly configured');
      }

      const response = await this.httpClient.post(this.purgeEndpoint, {
        urls: options.urls,
        prefixes: options.prefixes,
        tags: options.tags,
        purgeAll: options.purgeAll
      });

      return {
        success: true,
        purgeId: response.data.id,
        message: response.data.message || 'Cache purged successfully'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Custom CDN purge failed'
      };
    }
  }

  async getMetrics(startDate?: Date, endDate?: Date): Promise<CDNMetrics> {
    try {
      const response = await this.httpClient.get(`${this.apiEndpoint}/metrics`, {
        params: {
          start: startDate?.toISOString(),
          end: endDate?.toISOString()
        }
      });

      return response.data;
    } catch (error) {
      return {
        requests: 0,
        bandwidth: 0,
        cacheHitRatio: 0,
        topUrls: []
      };
    }
  }
}

export class CDNService {
  private provider: CDNProvider | null = null;

  constructor() {
    this.initializeProvider();
  }

  private initializeProvider(): void {
    if (!config.cdn.enabled) {
      return;
    }

    switch (config.cdn.provider) {
      case 'cloudflare':
        this.provider = new CloudflareCDNProvider();
        break;
      case 'aws':
        this.provider = new AWSCloudFrontCDNProvider();
        break;
      case 'custom':
        this.provider = new CustomCDNProvider();
        break;
      default:
        console.warn(`Unknown CDN provider: ${config.cdn.provider}`);
    }

    if (this.provider && !this.provider.validateConfiguration()) {
      console.warn('CDN provider configuration is invalid');
      this.provider = null;
    }
  }

  isEnabled(): boolean {
    return config.cdn.enabled && this.provider !== null;
  }

  getProvider(): string | null {
    return this.isEnabled() ? config.cdn.provider : null;
  }

  generateFileUrl(bucketName: string, filePath: string): string {
    if (!this.isEnabled()) {
      // Return original API URL if CDN is not enabled
      const baseUrl = config.app.baseUrl;
      return `${baseUrl}/storage/v1/object/public/${bucketName}/${filePath}`;
    }

    const cdnPath = `storage/${bucketName}/${filePath}`;
    return this.provider!.generateCdnUrl(cdnPath);
  }

  async purgeFile(bucketName: string, filePath: string): Promise<CDNPurgeResult> {
    if (!this.isEnabled()) {
      return { success: true, message: 'CDN not enabled' };
    }

    const cdnPath = `storage/${bucketName}/${filePath}`;
    const cdnUrl = this.provider!.generateCdnUrl(cdnPath);

    return await this.provider!.purgeCache({
      urls: [cdnUrl]
    });
  }

  async purgeBucket(bucketName: string): Promise<CDNPurgeResult> {
    if (!this.isEnabled()) {
      return { success: true, message: 'CDN not enabled' };
    }

    const prefix = `storage/${bucketName}/`;
    
    return await this.provider!.purgeCache({
      prefixes: [prefix]
    });
  }

  async purgeAll(): Promise<CDNPurgeResult> {
    if (!this.isEnabled()) {
      return { success: true, message: 'CDN not enabled' };
    }

    return await this.provider!.purgeCache({
      purgeAll: true
    });
  }

  async getMetrics(startDate?: Date, endDate?: Date): Promise<CDNMetrics | null> {
    if (!this.isEnabled()) {
      return null;
    }

    return await this.provider!.getMetrics(startDate, endDate);
  }

  getCacheHeaders(maxAge?: number): Record<string, string> {
    if (!config.cdn.cacheEnabled) {
      return {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      };
    }

    const cacheMaxAge = maxAge || config.cdn.cacheMaxAge;
    
    return {
      'Cache-Control': `public, max-age=${cacheMaxAge}, s-maxage=${cacheMaxAge}`,
      'Vary': 'Accept-Encoding',
      'X-CDN-Cache': 'MISS'
    };
  }

  async handleFileUpdate(bucketName: string, filePath: string): Promise<void> {
    if (!this.isEnabled() || !config.cdn.purgeOnUpdate) {
      return;
    }

    try {
      const result = await this.purgeFile(bucketName, filePath);
      if (result.success) {
        console.log(`✅ CDN cache purged for file: ${bucketName}/${filePath}`);
      } else {
        console.error(`❌ Failed to purge CDN cache for file: ${bucketName}/${filePath}`, result.error);
      }
    } catch (error) {
      console.error('Error purging CDN cache on file update:', error);
    }
  }

  async handleFileDelete(bucketName: string, filePath: string): Promise<void> {
    if (!this.isEnabled()) {
      return;
    }

    try {
      const result = await this.purgeFile(bucketName, filePath);
      if (result.success) {
        console.log(`✅ CDN cache purged for deleted file: ${bucketName}/${filePath}`);
      } else {
        console.error(`❌ Failed to purge CDN cache for deleted file: ${bucketName}/${filePath}`, result.error);
      }
    } catch (error) {
      console.error('Error purging CDN cache on file delete:', error);
    }
  }
}