import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { MultipartUpload } from "./entity/MultipartUpload.entity";
import { Bucket } from "./entity/Bucket.entity";
import { StorageFile } from "./entity/StorageFile.entity";
import {
  InitiateMultipartUploadDto,
  UploadPartDto,
  CompleteMultipartUploadDto,
  AbortMultipartUploadDto,
  ListMultipartUploadsDto,
  ListPartsDto,
  MultipartUploadResponse,
  UploadPartResponse,
  CompleteMultipartUploadResponse,
  PartInfo,
  UploadedPart
} from "./dto/storage.dto";
import fs from "fs/promises";
import path from "path";
import crypto from "crypto";
import { CDNService } from "./cdn.service";

export class MultipartUploadService {
  private multipartUploadRepository: Repository<MultipartUpload>;
  private bucketRepository: Repository<Bucket>;
  private fileRepository: Repository<StorageFile>;
  private readonly storageBasePath: string;
  private readonly tempPartsPath: string;
  private cdnService: CDNService;

  constructor() {
    this.multipartUploadRepository = AppDataSource.getRepository(MultipartUpload);
    this.bucketRepository = AppDataSource.getRepository(Bucket);
    this.fileRepository = AppDataSource.getRepository(StorageFile);
    this.storageBasePath = process.env.STORAGE_PATH || './storage';
    this.tempPartsPath = path.join(this.storageBasePath, 'temp-parts');
    this.cdnService = new CDNService();
    this.ensureTempPartsDirectoryExists();
  }

  private async ensureTempPartsDirectoryExists(): Promise<void> {
    try {
      await fs.access(this.tempPartsPath);
    } catch {
      await fs.mkdir(this.tempPartsPath, { recursive: true });
    }
  }

  async initiateMultipartUpload(
    initiateDto: InitiateMultipartUploadDto,
    createdBy?: string
  ): Promise<MultipartUploadResponse> {
    // Validate bucket exists and has permissions
    const bucket = await this.bucketRepository.findOne({
      where: { name: initiateDto.bucketName, workspaceId: initiateDto.workspaceId }
    });

    if (!bucket) {
      throw new Error('Bucket not found');
    }

    // Check file size limits
    if (initiateDto.totalSize && bucket.maxFileSize && initiateDto.totalSize > bucket.maxFileSize) {
      throw new Error(`File size exceeds bucket limit of ${bucket.maxFileSize} bytes`);
    }

    // Check MIME type if restricted
    if (initiateDto.contentType && bucket.allowedMimeTypes?.length) {
      if (!bucket.allowedMimeTypes.includes(initiateDto.contentType)) {
        throw new Error(`File type ${initiateDto.contentType} is not allowed in this bucket`);
      }
    }

    // Check if file already exists and handle accordingly
    const existingFile = await this.fileRepository.findOne({
      where: {
        bucket: initiateDto.bucketName,
        workspaceId: initiateDto.workspaceId,
        path: this.getFullPath(initiateDto.path, initiateDto.fileName)
      } as any
    });

    if (existingFile && !bucket.enableVersioning) {
      throw new Error('File already exists and versioning is not enabled');
    }

    // Create multipart upload record
    const multipartUpload = this.multipartUploadRepository.create({
      bucketName: initiateDto.bucketName,
      workspaceId: initiateDto.workspaceId,
      fileName: initiateDto.fileName,
      path: initiateDto.path || '',
      contentType: initiateDto.contentType,
      metadata: initiateDto.metadata,
      cacheControl: initiateDto.cacheControl,
      totalSize: initiateDto.totalSize,
      chunkSize: initiateDto.chunkSize || 5 * 1024 * 1024, // 5MB default
      createdBy
    });

    const savedUpload = await this.multipartUploadRepository.save(multipartUpload);

    // Create temporary directory for this upload
    const uploadTempDir = path.join(this.tempPartsPath, savedUpload.uploadId);
    await fs.mkdir(uploadTempDir, { recursive: true });

    const response: MultipartUploadResponse = {
      uploadId: savedUpload.uploadId,
      bucketName: savedUpload.bucketName,
      fileName: savedUpload.fileName,
      chunkSize: savedUpload.chunkSize,
      expiresAt: savedUpload.expiresAt
    };

    if (initiateDto.totalSize) {
      response.totalChunks = Math.ceil(initiateDto.totalSize / savedUpload.chunkSize);
    }

    console.log(`✅ Multipart upload initiated: ${savedUpload.uploadId} for ${savedUpload.fileName}`);
    return response;
  }

  async uploadPart(
    uploadPartDto: UploadPartDto,
    partData: Buffer
  ): Promise<UploadPartResponse> {
    // Get multipart upload record
    const multipartUpload = await this.multipartUploadRepository.findOne({
      where: {
        uploadId: uploadPartDto.uploadId,
        bucketName: uploadPartDto.bucketName,
        workspaceId: uploadPartDto.workspaceId
      }
    });

    if (!multipartUpload) {
      throw new Error('Multipart upload not found');
    }

    if (!multipartUpload.isActive()) {
      throw new Error(`Multipart upload is ${multipartUpload.status}`);
    }

    if (multipartUpload.isExpired()) {
      throw new Error('Multipart upload has expired');
    }

    // Validate part number
    if (uploadPartDto.partNumber < 1) {
      throw new Error('Part number must be >= 1');
    }

    // Validate content length if provided
    if (uploadPartDto.contentLength && uploadPartDto.contentLength !== partData.length) {
      throw new Error('Content length mismatch');
    }

    // Generate ETag (MD5 hash of the part)
    const md5Hash = crypto.createHash('md5').update(partData).digest('hex');
    const etag = `"${md5Hash}"`;

    // Validate MD5 hash if provided
    if (uploadPartDto.md5Hash && uploadPartDto.md5Hash !== md5Hash) {
      throw new Error('MD5 hash mismatch');
    }

    // Save part to temporary location
    const uploadTempDir = path.join(this.tempPartsPath, multipartUpload.uploadId);
    const partFile = path.join(uploadTempDir, `part-${uploadPartDto.partNumber.toString().padStart(5, '0')}`);
    
    await fs.writeFile(partFile, partData);

    // Update multipart upload record
    multipartUpload.addPart(uploadPartDto.partNumber, etag, partData.length, md5Hash);
    await this.multipartUploadRepository.save(multipartUpload);

    const response: UploadPartResponse = {
      partNumber: uploadPartDto.partNumber,
      etag,
      size: partData.length,
      uploadedAt: new Date()
    };

    console.log(`✅ Part ${uploadPartDto.partNumber} uploaded for ${multipartUpload.uploadId} (${partData.length} bytes)`);
    return response;
  }

  async completeMultipartUpload(
    completeDto: CompleteMultipartUploadDto
  ): Promise<CompleteMultipartUploadResponse> {
    const startTime = Date.now();

    // Get multipart upload record
    const multipartUpload = await this.multipartUploadRepository.findOne({
      where: {
        uploadId: completeDto.uploadId,
        bucketName: completeDto.bucketName,
        workspaceId: completeDto.workspaceId
      }
    });

    if (!multipartUpload) {
      throw new Error('Multipart upload not found');
    }

    if (!multipartUpload.isActive()) {
      throw new Error(`Multipart upload is ${multipartUpload.status}`);
    }

    if (multipartUpload.isExpired()) {
      throw new Error('Multipart upload has expired');
    }

    // Validate parts
    if (completeDto.parts.length === 0) {
      throw new Error('No parts provided');
    }

    // Sort parts by part number
    const sortedParts = completeDto.parts.sort((a, b) => a.partNumber - b.partNumber);

    // Validate consecutive part numbers starting from 1
    for (let i = 0; i < sortedParts.length; i++) {
      if (sortedParts[i].partNumber !== i + 1) {
        throw new Error(`Missing part number ${i + 1}`);
      }
    }

    // Validate all parts exist in the upload record
    for (const part of sortedParts) {
      const uploadedPart = multipartUpload.getPart(part.partNumber);
      if (!uploadedPart) {
        throw new Error(`Part ${part.partNumber} was not uploaded`);
      }
      if (uploadedPart.etag !== part.etag) {
        throw new Error(`ETag mismatch for part ${part.partNumber}`);
      }
    }

    // Create final file by concatenating parts
    const uploadTempDir = path.join(this.tempPartsPath, multipartUpload.uploadId);
    const finalFilePath = this.getFinalFilePath(multipartUpload);
    
    // Ensure final directory exists
    await fs.mkdir(path.dirname(finalFilePath), { recursive: true });

    // Concatenate parts
    const writeStream = await fs.open(finalFilePath, 'w');
    let totalSize = 0;

    try {
      for (const part of sortedParts) {
        const partFile = path.join(uploadTempDir, `part-${part.partNumber.toString().padStart(5, '0')}`);
        const partData = await fs.readFile(partFile);
        await writeStream.write(partData);
        totalSize += partData.length;
      }
    } finally {
      await writeStream.close();
    }

    // Generate final file ETag
    const fileBuffer = await fs.readFile(finalFilePath);
    const finalETag = crypto.createHash('md5').update(fileBuffer).digest('hex');
    const finalMd5 = `"${finalETag}"`;

    // Create file record
    const storageFile = this.fileRepository.create({
      bucket: multipartUpload.bucketName,
      workspaceId: multipartUpload.workspaceId,
      name: multipartUpload.fileName,
      path: multipartUpload.getFullPath(),
      size: totalSize,
      mimeType: multipartUpload.contentType || 'application/octet-stream',
      etag: finalMd5,
      metadata: multipartUpload.metadata || {},
      cacheControl: multipartUpload.cacheControl,
      uploadedBy: multipartUpload.createdBy
    } as any);

    const savedFile = await this.fileRepository.save(storageFile) as any;

    // Mark multipart upload as completed
    multipartUpload.complete();
    await this.multipartUploadRepository.save(multipartUpload);

    // Clean up temporary parts
    await this.cleanupTempParts(multipartUpload.uploadId);

    const uploadDuration = Date.now() - startTime;

    // Generate CDN URL if enabled
    const publicUrl = this.cdnService.isEnabled() 
      ? this.cdnService.generateFileUrl(multipartUpload.bucketName, savedFile.path)
      : savedFile.path;

    const response: CompleteMultipartUploadResponse = {
      id: savedFile.id,
      path: savedFile.path,
      fullPath: publicUrl,
      size: savedFile.size,
      metadata: {
        id: savedFile.id,
        name: savedFile.name,
        bucketName: savedFile.bucketName,
        path: savedFile.path,
        size: savedFile.size,
        mimeType: savedFile.mimeType,
        etag: savedFile.etag,
        lastModified: savedFile.updatedAt,
        metadata: savedFile.metadata,
        cacheControl: savedFile.cacheControl,
        uploadedBy: savedFile.uploadedBy,
        uploadedAt: savedFile.createdAt,
        cdnEnabled: this.cdnService.isEnabled(),
        cdnProvider: this.cdnService.getProvider()
      } as any,
      uploadId: multipartUpload.uploadId,
      totalParts: sortedParts.length,
      uploadDuration
    };

    console.log(`✅ Multipart upload completed: ${multipartUpload.uploadId} -> ${savedFile.path} (${totalSize} bytes)`);
    return response;
  }

  async abortMultipartUpload(abortDto: AbortMultipartUploadDto): Promise<{ message: string }> {
    // Get multipart upload record
    const multipartUpload = await this.multipartUploadRepository.findOne({
      where: {
        uploadId: abortDto.uploadId,
        bucketName: abortDto.bucketName,
        workspaceId: abortDto.workspaceId
      }
    });

    if (!multipartUpload) {
      throw new Error('Multipart upload not found');
    }

    if (multipartUpload.isCompleted()) {
      throw new Error('Cannot abort completed upload');
    }

    // Mark upload as aborted
    multipartUpload.abort('Manually aborted');
    await this.multipartUploadRepository.save(multipartUpload);

    // Clean up temporary parts
    await this.cleanupTempParts(multipartUpload.uploadId);

    console.log(`✅ Multipart upload aborted: ${multipartUpload.uploadId}`);
    return { message: 'Multipart upload aborted successfully' };
  }

  async listMultipartUploads(listDto: ListMultipartUploadsDto): Promise<{
    uploads: any[];
    hasMore: boolean;
    nextKeyMarker?: string;
    nextUploadIdMarker?: string;
  }> {
    const queryBuilder = this.multipartUploadRepository.createQueryBuilder('upload');
    
    queryBuilder
      .where('upload.bucketName = :bucketName', { bucketName: listDto.bucketName })
      .andWhere('upload.workspaceId = :workspaceId', { workspaceId: listDto.workspaceId })
      .andWhere('upload.status IN (:...statuses)', { statuses: ['initiated', 'in_progress'] });

    if (listDto.prefix) {
      queryBuilder.andWhere('upload.fileName LIKE :prefix', { prefix: `${listDto.prefix}%` });
    }

    if (listDto.keyMarker) {
      queryBuilder.andWhere('upload.fileName > :keyMarker', { keyMarker: listDto.keyMarker });
    }

    if (listDto.uploadIdMarker) {
      queryBuilder.andWhere('upload.uploadId > :uploadIdMarker', { uploadIdMarker: listDto.uploadIdMarker });
    }

    queryBuilder
      .orderBy('upload.fileName', 'ASC')
      .addOrderBy('upload.uploadId', 'ASC')
      .limit((listDto.maxUploads || 1000) + 1); // +1 to check if there are more

    const uploads = await queryBuilder.getMany();
    const hasMore = uploads.length > (listDto.maxUploads || 1000);
    
    if (hasMore) {
      uploads.pop(); // Remove the extra item
    }

    let nextKeyMarker: string | undefined;
    let nextUploadIdMarker: string | undefined;
    
    if (hasMore && uploads.length > 0) {
      const lastUpload = uploads[uploads.length - 1];
      nextKeyMarker = lastUpload.fileName;
      nextUploadIdMarker = lastUpload.uploadId;
    }

    return {
      uploads: uploads.map(upload => upload.toSafeObject()),
      hasMore,
      nextKeyMarker,
      nextUploadIdMarker
    };
  }

  async listParts(listDto: ListPartsDto): Promise<{
    parts: UploadedPart[];
    hasMore: boolean;
    nextPartNumberMarker?: number;
    maxParts: number;
  }> {
    const multipartUpload = await this.multipartUploadRepository.findOne({
      where: {
        uploadId: listDto.uploadId,
        bucketName: listDto.bucketName,
        workspaceId: listDto.workspaceId
      }
    });

    if (!multipartUpload) {
      throw new Error('Multipart upload not found');
    }

    const maxParts = listDto.maxParts || 1000;
    const startIndex = listDto.partNumberMarker || 0;
    
    const filteredParts = multipartUpload.uploadedParts
      .filter(part => part.partNumber > startIndex)
      .sort((a, b) => a.partNumber - b.partNumber)
      .slice(0, maxParts + 1); // +1 to check if there are more

    const hasMore = filteredParts.length > maxParts;
    if (hasMore) {
      filteredParts.pop(); // Remove the extra item
    }

    let nextPartNumberMarker: number | undefined;
    if (hasMore && filteredParts.length > 0) {
      nextPartNumberMarker = filteredParts[filteredParts.length - 1].partNumber;
    }

    return {
      parts: filteredParts,
      hasMore,
      nextPartNumberMarker,
      maxParts
    };
  }

  async getMultipartUpload(uploadId: string, workspaceId: string): Promise<MultipartUpload | null> {
    return await this.multipartUploadRepository.findOne({
      where: { uploadId, workspaceId }
    });
  }

  // Cleanup expired uploads (should be run periodically)
  async cleanupExpiredUploads(): Promise<{ cleaned: number }> {
    const expiredUploads = await this.multipartUploadRepository.find({
      where: [
        { status: 'initiated', expiresAt: { $lt: new Date() } as any },
        { status: 'in_progress', expiresAt: { $lt: new Date() } as any }
      ]
    });

    let cleanedCount = 0;
    for (const upload of expiredUploads) {
      try {
        upload.abort('Expired');
        await this.multipartUploadRepository.save(upload);
        await this.cleanupTempParts(upload.uploadId);
        cleanedCount++;
      } catch (error) {
        console.error(`Failed to cleanup expired upload ${upload.uploadId}:`, error);
      }
    }

    if (cleanedCount > 0) {
      console.log(`✅ Cleaned up ${cleanedCount} expired multipart uploads`);
    }

    return { cleaned: cleanedCount };
  }

  private async cleanupTempParts(uploadId: string): Promise<void> {
    try {
      const uploadTempDir = path.join(this.tempPartsPath, uploadId);
      await fs.rm(uploadTempDir, { recursive: true, force: true });
    } catch (error) {
      console.warn(`Failed to cleanup temp parts for upload ${uploadId}:`, error);
    }
  }

  private getFullPath(filePath?: string, fileName?: string): string {
    return filePath ? `${filePath}/${fileName}` : fileName || '';
  }

  private getFinalFilePath(multipartUpload: MultipartUpload): string {
    const bucketPath = path.join(this.storageBasePath, multipartUpload.bucketName);
    return path.join(bucketPath, multipartUpload.getFullPath());
  }
}