import { IsString, <PERSON>Optional, <PERSON>A<PERSON>y, IsBoolean, IsNumber, IsEnum, IsDateString, IsObject } from "class-validator";
import { Type } from "class-transformer";

export enum BucketVisibility {
  PUBLIC = 'public',
  PRIVATE = 'private'
}

export enum FileOperation {
  UPLOAD = 'upload',
  DOWNLOAD = 'download',
  DELETE = 'delete',
  UPDATE = 'update'
}

export class CreateBucketDto {
  @IsString()
  name: string;

  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsEnum(BucketVisibility)
  visibility?: BucketVisibility = BucketVisibility.PRIVATE;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedMimeTypes?: string[];

  @IsOptional()
  @IsNumber()
  maxFileSize?: number; // in bytes

  @IsOptional()
  @IsBoolean()
  enableVersioning?: boolean = false;

  @IsOptional()
  @IsBoolean()
  enableCors?: boolean = true;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  corsOrigins?: string[];
}

export class UpdateBucketDto {
  @IsOptional()
  @IsEnum(BucketVisibility)
  visibility?: BucketVisibility;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedMimeTypes?: string[];

  @IsOptional()
  @IsNumber()
  maxFileSize?: number;

  @IsOptional()
  @IsBoolean()
  enableVersioning?: boolean;

  @IsOptional()
  @IsBoolean()
  enableCors?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  corsOrigins?: string[];
}

export class UploadFileDto {
  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsString()
  fileName: string;

  @IsOptional()
  @IsString()
  path?: string; // folder path

  @IsOptional()
  @IsString()
  contentType?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  overwrite?: boolean = false;

  @IsOptional()
  @IsString()
  cacheControl?: string;

  @IsOptional()
  @IsNumber()
  expiresIn?: number; // for signed URLs
}

export class DownloadFileDto {
  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsString()
  filePath: string;

  @IsOptional()
  @IsNumber()
  expiresIn?: number; // for signed URLs

  @IsOptional()
  @IsString()
  responseType?: 'json' | 'blob' | 'stream';

  @IsOptional()
  @IsString()
  transform?: string; // image transformation parameters
}

export class DeleteFileDto {
  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsString()
  filePath: string;

  @IsOptional()
  @IsBoolean()
  deleteAllVersions?: boolean = false;
}

export class ListFilesDto {
  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsString()
  prefix?: string; // folder path prefix

  @IsOptional()
  @IsNumber()
  limit?: number = 100;

  @IsOptional()
  @IsString()
  offset?: string; // pagination cursor

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  sortBy?: string[];
}

export class CreateSignedUrlDto {
  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsString()
  filePath: string;

  @IsEnum(FileOperation)
  operation: FileOperation;

  @IsOptional()
  @IsNumber()
  expiresIn?: number = 3600; // 1 hour default

  @IsOptional()
  @IsObject()
  options?: {
    contentType?: string;
    metadata?: Record<string, any>;
    cacheControl?: string;
  };
}

export class FileTransformDto {
  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsString()
  filePath: string;

  @IsOptional()
  @IsNumber()
  width?: number;

  @IsOptional()
  @IsNumber()
  height?: number;

  @IsOptional()
  @IsString()
  format?: 'webp' | 'jpeg' | 'png' | 'avif';

  @IsOptional()
  @IsNumber()
  quality?: number; // 1-100

  @IsOptional()
  @IsString()
  resize?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';

  @IsOptional()
  @IsBoolean()
  progressive?: boolean;

  @IsOptional()
  @IsString()
  blur?: string; // blur amount
}

export interface FileMetadata {
  id: string;
  name: string;
  path: string;
  bucket: string;
  size: number;
  mimeType: string;
  etag: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
  version?: string;
  cacheControl?: string;
  owner?: string;
  isPublic: boolean;
}

export interface BucketInfo {
  id: string;
  name: string;
  workspaceId: string;
  visibility: BucketVisibility;
  description?: string;
  allowedMimeTypes?: string[];
  maxFileSize?: number;
  enableVersioning: boolean;
  enableCors: boolean;
  corsOrigins?: string[];
  filesCount: number;
  totalSize: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface StorageUsage {
  workspaceId: string;
  totalBuckets: number;
  totalFiles: number;
  totalSize: number;
  buckets: {
    name: string;
    filesCount: number;
    size: number;
  }[];
}

export interface SignedUrlResponse {
  signedUrl: string;
  token: string;
  expiresAt: Date;
  operation: FileOperation;
}

export interface UploadResponse {
  id: string;
  path: string;
  fullPath: string;
  metadata: FileMetadata;
  signedUrl?: string;
}

export interface FileListResponse {
  files: FileMetadata[];
  hasMore: boolean;
  nextOffset?: string;
  totalCount?: number;
}

// Multipart Upload DTOs
export class InitiateMultipartUploadDto {
  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsString()
  fileName: string;

  @IsOptional()
  @IsString()
  path?: string;

  @IsOptional()
  @IsString()
  contentType?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @IsOptional()
  @IsString()
  cacheControl?: string;

  @IsOptional()
  @IsNumber()
  totalSize?: number;

  @IsOptional()
  @IsNumber()
  chunkSize?: number = 5 * 1024 * 1024; // 5MB default
}

export class UploadPartDto {
  @IsString()
  uploadId: string;

  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsNumber()
  partNumber: number;

  @IsOptional()
  @IsString()
  md5Hash?: string;

  @IsOptional()
  @IsNumber()
  contentLength?: number;
}

export class CompleteMultipartUploadDto {
  @IsString()
  uploadId: string;

  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsArray()
  @Type(() => PartInfo)
  parts: PartInfo[];
}

export class PartInfo {
  @IsNumber()
  partNumber: number;

  @IsString()
  etag: string;

  @IsOptional()
  @IsNumber()
  size?: number;
}

export class AbortMultipartUploadDto {
  @IsString()
  uploadId: string;

  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;
}

export class ListMultipartUploadsDto {
  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsString()
  prefix?: string;

  @IsOptional()
  @IsNumber()
  maxUploads?: number = 1000;

  @IsOptional()
  @IsString()
  keyMarker?: string;

  @IsOptional()
  @IsString()
  uploadIdMarker?: string;
}

export class ListPartsDto {
  @IsString()
  uploadId: string;

  @IsString()
  bucketName: string;

  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsNumber()
  maxParts?: number = 1000;

  @IsOptional()
  @IsNumber()
  partNumberMarker?: number;
}

// Multipart Upload Interfaces
export interface MultipartUpload {
  uploadId: string;
  bucketName: string;
  workspaceId: string;
  fileName: string;
  path: string;
  contentType?: string;
  metadata?: Record<string, any>;
  totalSize?: number;
  chunkSize: number;
  initiatedAt: Date;
  expiresAt: Date;
  status: 'initiated' | 'in_progress' | 'completed' | 'aborted';
  uploadedParts: UploadedPart[];
  uploadedSize: number;
  createdBy?: string;
}

export interface UploadedPart {
  partNumber: number;
  etag: string;
  size: number;
  uploadedAt: Date;
  md5Hash?: string;
}

export interface MultipartUploadResponse {
  uploadId: string;
  bucketName: string;
  fileName: string;
  uploadUrls?: string[];
  chunkSize: number;
  totalChunks?: number;
  expiresAt: Date;
}

export interface UploadPartResponse {
  partNumber: number;
  etag: string;
  size: number;
  uploadedAt: Date;
}

export interface CompleteMultipartUploadResponse {
  id: string;
  path: string;
  fullPath: string;
  size: number;
  metadata: FileMetadata;
  uploadId: string;
  totalParts: number;
  uploadDuration: number;
}

// File Versioning DTOs
export class EnableVersioningDto {
  @IsString()
  fileId: string;

  @IsOptional()
  @IsNumber()
  maxVersions?: number;

  @IsOptional()
  @IsNumber()
  retentionDays?: number;

  @IsOptional()
  @IsBoolean()
  autoCleanup?: boolean = true;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludeExtensions?: string[];
}

export class CreateVersionDto {
  @IsString()
  fileId: string;

  @IsOptional()
  @IsString()
  changelog?: string;

  @IsOptional()
  @IsEnum(['manual', 'auto', 'rollback'])
  type?: 'manual' | 'auto' | 'rollback';

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @IsOptional()
  @IsNumber()
  retentionDays?: number;
}

export class RollbackVersionDto {
  @IsString()
  fileId: string;

  @IsNumber()
  versionNumber: number;

  @IsOptional()
  @IsBoolean()
  createBackup?: boolean = true;

  @IsOptional()
  @IsString()
  changelog?: string;
}

export class DeleteVersionDto {
  @IsString()
  fileId: string;

  @IsNumber()
  versionNumber: number;

  @IsOptional()
  @IsBoolean()
  permanent?: boolean = false;
}

export class ListVersionsDto {
  @IsString()
  fileId: string;

  @IsOptional()
  @IsBoolean()
  includeDeleted?: boolean = false;

  @IsOptional()
  @IsNumber()
  limit?: number = 50;

  @IsOptional()
  @IsNumber()
  offset?: number = 0;
}

export class ApplyRetentionPolicyDto {
  @IsString()
  fileId: string;

  @IsOptional()
  @IsNumber()
  maxVersions?: number;

  @IsOptional()
  @IsNumber()
  retentionDays?: number;

  @IsOptional()
  @IsBoolean()
  autoCleanup?: boolean = true;
}

// Version Interfaces
export interface VersionInfo {
  id: string;
  fileId: string;
  versionNumber: number;
  versionHash: string;
  size: number;
  humanSize: string;
  mimeType: string;
  etag: string;
  status: 'active' | 'archived' | 'deleted';
  type: 'manual' | 'auto' | 'rollback';
  isLatest: boolean;
  metadata?: Record<string, any>;
  changelog?: string;
  createdBy?: string;
  checksum?: string;
  compressionType?: string;
  originalSize?: number;
  compressionRatio: number;
  ageInDays: number;
  isExpired: boolean;
  expiresAt?: Date;
  createdAt: Date;
}

export interface VersioningStats {
  totalFiles: number;
  versionedFiles: number;
  totalVersions: number;
  totalSize: number;
  averageVersionsPerFile: number;
}

export interface CleanupResult {
  deletedVersions: number;
  freedSpace: number;
  errors: string[];
}