import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { Bucket } from "./entity/Bucket.entity";
import { StorageFile } from "./entity/StorageFile.entity";
import {
  CreateBucketDto,
  UpdateBucketDto,
  UploadFileDto,
  DownloadFileDto,
  DeleteFileDto,
  ListFilesDto,
  CreateSignedUrlDto,
  FileTransformDto,
  BucketInfo,
  FileMetadata,
  StorageUsage,
  SignedUrlResponse,
  UploadResponse,
  FileListResponse,
  BucketVisibility,
  FileOperation
} from "./dto/storage.dto";
import fs from "fs/promises";
import path from "path";
import crypto from "crypto";
import jwt from "jsonwebtoken";
import config from "../../infra/config";
import sharp from "sharp";
import { CDNService } from "./cdn.service";
import { VersioningService } from "./versioning.service";
import { StorageRLSService } from "./rls.service";
import { PolicyAction } from "./dto/rls.dto";

export class StorageService {
  private bucketRepository: Repository<Bucket>;
  private fileRepository: Repository<StorageFile>;
  private readonly storageBasePath: string;
  private cdnService: CDNService;
  private versioningService: VersioningService;
  private rlsService: StorageRLSService;

  constructor() {
    this.bucketRepository = AppDataSource.getRepository(Bucket);
    this.fileRepository = AppDataSource.getRepository(StorageFile);
    this.storageBasePath = process.env.STORAGE_PATH || './storage';
    this.cdnService = new CDNService();
    this.versioningService = new VersioningService();
    this.rlsService = new StorageRLSService();
    this.ensureStorageDirectoryExists();
  }

  private async ensureStorageDirectoryExists(): Promise<void> {
    try {
      await fs.access(this.storageBasePath);
    } catch {
      await fs.mkdir(this.storageBasePath, { recursive: true });
    }
  }

  // Bucket Management
  async createBucket(createBucketDto: CreateBucketDto, createdBy?: string): Promise<BucketInfo> {
    // Check if bucket already exists
    const existingBucket = await this.bucketRepository.findOne({
      where: {
        name: createBucketDto.name,
        workspaceId: createBucketDto.workspaceId
      }
    });

    if (existingBucket) {
      throw new Error(`Bucket '${createBucketDto.name}' already exists in this workspace`);
    }

    // Create bucket
    const bucket = this.bucketRepository.create({
      ...createBucketDto,
      createdBy,
      filesCount: 0,
      totalSize: 0
    });

    await this.bucketRepository.save(bucket);

    // Create physical directory
    const bucketPath = path.join(this.storageBasePath, bucket.getStoragePath());
    await fs.mkdir(bucketPath, { recursive: true });

    return bucket.toSafeObject() as BucketInfo;
  }

  async getBucket(bucketName: string, workspaceId: string): Promise<BucketInfo | null> {
    const bucket = await this.bucketRepository.findOne({
      where: { name: bucketName, workspaceId, isActive: true }
    });

    return bucket ? bucket.toSafeObject() as BucketInfo : null;
  }

  async listBuckets(workspaceId: string): Promise<BucketInfo[]> {
    const buckets = await this.bucketRepository.find({
      where: { workspaceId, isActive: true },
      order: { createdAt: 'DESC' }
    });

    return buckets.map(bucket => bucket.toSafeObject() as BucketInfo);
  }

  async updateBucket(bucketName: string, workspaceId: string, updateDto: UpdateBucketDto): Promise<BucketInfo> {
    const bucket = await this.bucketRepository.findOne({
      where: { name: bucketName, workspaceId, isActive: true }
    });

    if (!bucket) {
      throw new Error('Bucket not found');
    }

    // Update bucket properties
    Object.assign(bucket, updateDto);
    await this.bucketRepository.save(bucket);

    return bucket.toSafeObject() as BucketInfo;
  }

  async deleteBucket(bucketName: string, workspaceId: string, force = false): Promise<{ message: string }> {
    const bucket = await this.bucketRepository.findOne({
      where: { name: bucketName, workspaceId, isActive: true }
    });

    if (!bucket) {
      throw new Error('Bucket not found');
    }

    // Check if bucket has files
    const fileCount = await this.fileRepository.count({
      where: { bucketId: bucket.id, isDeleted: false }
    });

    if (fileCount > 0 && !force) {
      throw new Error('Bucket is not empty. Use force=true to delete all files');
    }

    // Delete all files in bucket if force is true
    if (force && fileCount > 0) {
      await this.fileRepository.update(
        { bucketId: bucket.id },
        { isDeleted: true, deletedAt: new Date() }
      );
    }

    // Soft delete bucket
    bucket.isActive = false;
    await this.bucketRepository.save(bucket);

    // Optionally remove physical directory
    try {
      const bucketPath = path.join(this.storageBasePath, bucket.getStoragePath());
      await fs.rm(bucketPath, { recursive: true, force: true });
    } catch (error) {
      console.warn(`Failed to remove bucket directory: ${error}`);
    }

    return { message: `Bucket '${bucketName}' deleted successfully` };
  }

  // RLS Integration Methods
  private async checkBucketAccess(bucketName: string, workspaceId: string, userId: string, action: PolicyAction): Promise<void> {
    if (!userId) {
      return; // Skip RLS for unauthenticated requests (will be handled by bucket visibility)
    }
    
    const accessResult = await this.rlsService.checkBucketAccess(bucketName, workspaceId, userId, action);
    if (!accessResult.allowed) {
      throw new Error(`Access denied: ${accessResult.reason}`);
    }
  }

  private async checkFileAccess(fileId: string, userId: string, action: PolicyAction): Promise<void> {
    if (!userId) {
      return; // Skip RLS for unauthenticated requests (will be handled by file visibility)
    }
    
    const accessResult = await this.rlsService.checkFileAccess(fileId, userId, action);
    if (!accessResult.allowed) {
      throw new Error(`Access denied: ${accessResult.reason}`);
    }
  }

  // File Operations
  async uploadFile(uploadDto: UploadFileDto, fileBuffer: Buffer, userId?: string): Promise<UploadResponse> {
    // Check RLS policies for bucket access
    if (userId) {
      await this.checkBucketAccess(uploadDto.bucketName, uploadDto.workspaceId, userId, PolicyAction.UPLOAD);
    }

    // Get bucket
    const bucket = await this.bucketRepository.findOne({
      where: { name: uploadDto.bucketName, workspaceId: uploadDto.workspaceId, isActive: true }
    });

    if (!bucket) {
      throw new Error('Bucket not found');
    }

    // Validate file type
    if (uploadDto.contentType && !bucket.isFileTypeAllowed(uploadDto.contentType)) {
      throw new Error('File type not allowed in this bucket');
    }

    // Validate file size
    if (!bucket.isFileSizeAllowed(fileBuffer.length)) {
      throw new Error(`File size exceeds bucket limit of ${bucket.maxFileSize} bytes`);
    }

    // Construct file path
    const filePath = uploadDto.path 
      ? `${uploadDto.path}/${uploadDto.fileName}`.replace(/\/+/g, '/')
      : uploadDto.fileName;

    // Check if file exists and overwrite is not allowed
    const existingFile = await this.fileRepository.findOne({
      where: { bucketId: bucket.id, path: filePath, isDeleted: false }
    });

    if (existingFile && !uploadDto.overwrite) {
      throw new Error('File already exists. Use overwrite=true to replace it');
    }

    // Create or update file record
    let file: StorageFile;
    let isNewFile = false;
    
    if (existingFile && uploadDto.overwrite) {
      // Create version if versioning is enabled
      if (existingFile.hasVersioning()) {
        try {
          await this.versioningService.createVersion(existingFile.id, fileBuffer, {
            type: 'auto' as any,
            changelog: 'File updated via upload'
          });
        } catch (error) {
          console.warn('Failed to create version on file update:', error);
        }
      }
      
      // Update existing file
      existingFile.size = fileBuffer.length;
      existingFile.mimeType = uploadDto.contentType || 'application/octet-stream';
      existingFile.metadata = uploadDto.metadata;
      existingFile.cacheControl = uploadDto.cacheControl;
      existingFile.ownerId = userId;
      existingFile.isPublic = bucket.isPublic();
      file = existingFile;
    } else {
      // Create new file
      isNewFile = true;
      file = this.fileRepository.create({
        name: uploadDto.fileName,
        path: filePath,
        bucketId: bucket.id,
        workspaceId: uploadDto.workspaceId,
        size: fileBuffer.length,
        mimeType: uploadDto.contentType || 'application/octet-stream',
        metadata: uploadDto.metadata,
        cacheControl: uploadDto.cacheControl,
        ownerId: userId,
        isPublic: bucket.isPublic(),
        versioningEnabled: bucket.enableVersioning || false
      });
    }

    await this.fileRepository.save(file);

    // Save file to disk
    const fullStoragePath = path.join(this.storageBasePath, file.storagePath);
    await fs.mkdir(path.dirname(fullStoragePath), { recursive: true });
    await fs.writeFile(fullStoragePath, fileBuffer);

    // Update bucket statistics
    if (!existingFile) {
      bucket.updateStats(fileBuffer.length, 1);
    } else {
      bucket.updateStats(fileBuffer.length - existingFile.size, 0);
    }
    await this.bucketRepository.save(bucket);

    // Handle CDN integration
    if (existingFile && this.cdnService.isEnabled()) {
      // Purge CDN cache when updating existing file
      await this.cdnService.handleFileUpdate(uploadDto.bucketName, file.path);
    }

    // Generate CDN URL if enabled, otherwise use regular API URL
    const publicUrl = this.cdnService.isEnabled() 
      ? this.cdnService.generateFileUrl(uploadDto.bucketName, file.path)
      : file.getUrlPath();

    return {
      id: file.id,
      path: file.path,
      fullPath: publicUrl,
      metadata: {
        ...file.toMetadata(),
        cdnEnabled: this.cdnService.isEnabled(),
        cdnProvider: this.cdnService.getProvider()
      } as any,
      signedUrl: uploadDto.expiresIn ? await this.createSignedUrl({
        bucketName: uploadDto.bucketName,
        workspaceId: uploadDto.workspaceId,
        filePath: file.path,
        operation: FileOperation.DOWNLOAD,
        expiresIn: uploadDto.expiresIn
      }).then(res => res.signedUrl) : undefined
    };
  }

  async downloadFile(downloadDto: DownloadFileDto, userId?: string): Promise<{ file: StorageFile; buffer: Buffer }> {
    // Get file
    const file = await this.fileRepository.findOne({
      where: {
        bucketId: await this.getBucketId(downloadDto.bucketName, downloadDto.workspaceId),
        path: downloadDto.filePath,
        isDeleted: false
      },
      relations: ['bucket']
    });

    if (!file) {
      throw new Error('File not found');
    }

    // Check RLS policies for file access
    if (userId) {
      await this.checkFileAccess(file.id, userId, PolicyAction.DOWNLOAD);
    }

    // Check permissions
    if (!file.isPublic && !file.bucket?.isPublic()) {
      // This would be checked by middleware in real implementation
      // For now, we'll allow download if the workspace matches
    }

    // Read file from disk
    const fullStoragePath = path.join(this.storageBasePath, file.storagePath);
    let buffer: Buffer;

    try {
      buffer = await fs.readFile(fullStoragePath);
    } catch (error) {
      throw new Error('File not found on storage');
    }

    // Apply transformations if requested and file is an image
    if (downloadDto.transform && file.isImage()) {
      try {
        const transformParams = this.parseTransformParams(downloadDto.transform);
        buffer = await this.transformImage(buffer, transformParams);
      } catch (error) {
        console.warn('Image transformation failed:', error);
        // Continue with original file if transformation fails
      }
    }

    // Update access statistics
    file.markAsAccessed();
    await this.fileRepository.save(file);

    return { file, buffer };
  }

  async deleteFile(deleteDto: DeleteFileDto, userId?: string): Promise<{ message: string }> {
    const bucket = await this.bucketRepository.findOne({
      where: { name: deleteDto.bucketName, workspaceId: deleteDto.workspaceId, isActive: true }
    });

    if (!bucket) {
      throw new Error('Bucket not found');
    }

    const file = await this.fileRepository.findOne({
      where: { bucketId: bucket.id, path: deleteDto.filePath, isDeleted: false }
    });

    if (!file) {
      throw new Error('File not found');
    }

    // Check RLS policies for file deletion
    if (userId) {
      await this.checkFileAccess(file.id, userId, PolicyAction.DELETE);
    }

    // Soft delete file
    file.softDelete();
    await this.fileRepository.save(file);

    // Update bucket statistics
    bucket.updateStats(-file.size, -1);
    await this.bucketRepository.save(bucket);

    // Handle CDN cache purging
    if (this.cdnService.isEnabled()) {
      await this.cdnService.handleFileDelete(deleteDto.bucketName, file.path);
    }

    // Optionally delete physical file
    try {
      const fullStoragePath = path.join(this.storageBasePath, file.storagePath);
      await fs.unlink(fullStoragePath);
    } catch (error) {
      console.warn(`Failed to delete physical file: ${error}`);
    }

    return { message: 'File deleted successfully' };
  }

  async listFiles(listDto: ListFilesDto, userId?: string): Promise<FileListResponse> {
    const bucket = await this.bucketRepository.findOne({
      where: { name: listDto.bucketName, workspaceId: listDto.workspaceId, isActive: true }
    });

    if (!bucket) {
      throw new Error('Bucket not found');
    }

    // Check RLS policies for bucket listing
    if (userId) {
      await this.checkBucketAccess(listDto.bucketName, listDto.workspaceId, userId, PolicyAction.LIST);
    }

    const queryBuilder = this.fileRepository
      .createQueryBuilder('file')
      .where('file.bucketId = :bucketId', { bucketId: bucket.id })
      .andWhere('file.isDeleted = :isDeleted', { isDeleted: false });

    // Apply prefix filter (folder)
    if (listDto.prefix) {
      queryBuilder.andWhere('file.path LIKE :prefix', { prefix: `${listDto.prefix}%` });
    }

    // Apply search filter
    if (listDto.search) {
      queryBuilder.andWhere('file.name LIKE :search', { search: `%${listDto.search}%` });
    }

    // Apply sorting
    if (listDto.sortBy && listDto.sortBy.length > 0) {
      listDto.sortBy.forEach((sort, index) => {
        const [field, direction = 'ASC'] = sort.split(':');
        if (index === 0) {
          queryBuilder.orderBy(`file.${field}`, direction.toUpperCase() as 'ASC' | 'DESC');
        } else {
          queryBuilder.addOrderBy(`file.${field}`, direction.toUpperCase() as 'ASC' | 'DESC');
        }
      });
    } else {
      queryBuilder.orderBy('file.createdAt', 'DESC');
    }

    // Apply pagination
    const limit = Math.min(listDto.limit || 100, 1000); // Max 1000 files per request
    queryBuilder.limit(limit + 1); // Get one extra to check if there are more

    if (listDto.offset) {
      // Implement cursor-based pagination
      queryBuilder.andWhere('file.id > :offset', { offset: listDto.offset });
    }

    const files = await queryBuilder.getMany();
    const hasMore = files.length > limit;
    
    if (hasMore) {
      files.pop(); // Remove the extra file
    }

    const nextOffset = hasMore && files.length > 0 ? files[files.length - 1].id : undefined;

    return {
      files: files.map(file => file.toMetadata()),
      hasMore,
      nextOffset
    };
  }

  // Signed URLs
  async createSignedUrl(signedUrlDto: CreateSignedUrlDto): Promise<SignedUrlResponse> {
    const bucket = await this.bucketRepository.findOne({
      where: { name: signedUrlDto.bucketName, workspaceId: signedUrlDto.workspaceId, isActive: true }
    });

    if (!bucket) {
      throw new Error('Bucket not found');
    }

    // For download operations, check if file exists
    if (signedUrlDto.operation === FileOperation.DOWNLOAD) {
      const file = await this.fileRepository.findOne({
        where: { bucketId: bucket.id, path: signedUrlDto.filePath, isDeleted: false }
      });

      if (!file) {
        throw new Error('File not found');
      }
    }

    // Generate JWT token for signed URL
    const expiresAt = new Date(Date.now() + (signedUrlDto.expiresIn * 1000));
    const token = jwt.sign(
      {
        bucket: signedUrlDto.bucketName,
        path: signedUrlDto.filePath,
        operation: signedUrlDto.operation,
        workspaceId: signedUrlDto.workspaceId,
        options: signedUrlDto.options,
        exp: Math.floor(expiresAt.getTime() / 1000)
      },
      config.jwt.secret
    );

    const baseUrl = process.env.BASE_URL || 'http://localhost:3333';
    const signedUrl = `${baseUrl}/storage/v1/object/sign/${signedUrlDto.bucketName}/${signedUrlDto.filePath}?token=${token}`;

    return {
      signedUrl,
      token,
      expiresAt,
      operation: signedUrlDto.operation
    };
  }

  // Storage Usage
  async getStorageUsage(workspaceId: string): Promise<StorageUsage> {
    const buckets = await this.bucketRepository.find({
      where: { workspaceId, isActive: true }
    });

    const totalBuckets = buckets.length;
    const totalFiles = buckets.reduce((sum, bucket) => sum + bucket.filesCount, 0);
    const totalSize = buckets.reduce((sum, bucket) => sum + bucket.totalSize, 0);

    return {
      workspaceId,
      totalBuckets,
      totalFiles,
      totalSize,
      buckets: buckets.map(bucket => ({
        name: bucket.name,
        filesCount: bucket.filesCount,
        size: bucket.totalSize
      }))
    };
  }

  // CDN Management methods
  async purgeCDNCache(bucketName?: string, filePath?: string): Promise<{ success: boolean; message: string }> {
    if (!this.cdnService.isEnabled()) {
      return { success: false, message: 'CDN is not enabled' };
    }

    try {
      let result;
      
      if (bucketName && filePath) {
        // Purge specific file
        result = await this.cdnService.purgeFile(bucketName, filePath);
      } else if (bucketName) {
        // Purge entire bucket
        result = await this.cdnService.purgeBucket(bucketName);
      } else {
        // Purge all cache
        result = await this.cdnService.purgeAll();
      }

      return {
        success: result.success,
        message: result.message || (result.success ? 'Cache purged successfully' : result.error || 'Failed to purge cache')
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Failed to purge CDN cache'
      };
    }
  }

  async getCDNMetrics(startDate?: Date, endDate?: Date) {
    if (!this.cdnService.isEnabled()) {
      return null;
    }

    return await this.cdnService.getMetrics(startDate, endDate);
  }

  getCDNInfo() {
    return {
      enabled: this.cdnService.isEnabled(),
      provider: this.cdnService.getProvider(),
      cacheEnabled: config.cdn.cacheEnabled,
      cacheMaxAge: config.cdn.cacheMaxAge,
      purgeOnUpdate: config.cdn.purgeOnUpdate
    };
  }

  // File Versioning methods
  async enableFileVersioning(fileId: string, policy?: any): Promise<StorageFile> {
    return await this.versioningService.enableVersioning(fileId, policy);
  }

  async disableFileVersioning(fileId: string, keepExistingVersions = true): Promise<StorageFile> {
    return await this.versioningService.disableVersioning(fileId, keepExistingVersions);
  }

  async createFileVersion(fileId: string, fileBuffer: Buffer, options: any = {}): Promise<any> {
    return await this.versioningService.createVersion(fileId, fileBuffer, options);
  }

  async getFileVersions(fileId: string, includeDeleted = false): Promise<any[]> {
    const versions = await this.versioningService.getVersions(fileId, includeDeleted);
    return versions.map(v => v.toVersionInfo());
  }

  async getFileVersion(fileId: string, versionNumber: number): Promise<any | null> {
    const version = await this.versioningService.getVersion(fileId, versionNumber);
    return version ? version.toVersionInfo() : null;
  }

  async downloadFileVersion(fileId: string, versionNumber: number): Promise<{ version: any; buffer: Buffer }> {
    const { version, buffer } = await this.versioningService.downloadVersion(fileId, versionNumber);
    return { version: version.toVersionInfo(), buffer };
  }

  async rollbackFileToVersion(fileId: string, versionNumber: number, options: any = {}): Promise<any> {
    const version = await this.versioningService.rollbackToVersion(fileId, versionNumber, options);
    return version.toVersionInfo();
  }

  async deleteFileVersion(fileId: string, versionNumber: number, permanent = false): Promise<void> {
    return await this.versioningService.deleteVersion(fileId, versionNumber, permanent);
  }

  async applyVersionRetentionPolicy(fileId: string, policy: any): Promise<any> {
    return await this.versioningService.applyRetentionPolicy(fileId, policy);
  }

  async cleanupExpiredVersions(): Promise<any> {
    return await this.versioningService.cleanupExpiredVersions();
  }

  async getVersioningStats(fileId?: string): Promise<any> {
    return await this.versioningService.getVersioningStats(fileId);
  }

  // Helper methods
  private async getBucketId(bucketName: string, workspaceId: string): Promise<string> {
    const bucket = await this.bucketRepository.findOne({
      where: { name: bucketName, workspaceId, isActive: true },
      select: ['id']
    });

    if (!bucket) {
      throw new Error('Bucket not found');
    }

    return bucket.id;
  }

  private parseTransformParams(transform: string): Partial<FileTransformDto> {
    const params: Partial<FileTransformDto> = {};
    const pairs = transform.split(',');

    for (const pair of pairs) {
      const [key, value] = pair.split('=');
      switch (key) {
        case 'w':
        case 'width':
          params.width = parseInt(value);
          break;
        case 'h':
        case 'height':
          params.height = parseInt(value);
          break;
        case 'q':
        case 'quality':
          params.quality = parseInt(value);
          break;
        case 'f':
        case 'format':
          params.format = value as 'webp' | 'jpeg' | 'png' | 'avif';
          break;
        case 'r':
        case 'resize':
          params.resize = value as 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
          break;
      }
    }

    return params;
  }

  private async transformImage(buffer: Buffer, transform: Partial<FileTransformDto>): Promise<Buffer> {
    let image = sharp(buffer);

    // Apply resize
    if (transform.width || transform.height) {
      image = image.resize(transform.width, transform.height, {
        fit: transform.resize || 'cover',
        withoutEnlargement: true
      });
    }

    // Apply format conversion
    if (transform.format) {
      switch (transform.format) {
        case 'webp':
          image = image.webp({ quality: transform.quality || 80 });
          break;
        case 'jpeg':
          image = image.jpeg({ quality: transform.quality || 80, progressive: transform.progressive });
          break;
        case 'png':
          image = image.png({ quality: transform.quality || 80 });
          break;
        case 'avif':
          image = image.avif({ quality: transform.quality || 80 });
          break;
      }
    }

    return await image.toBuffer();
  }
}