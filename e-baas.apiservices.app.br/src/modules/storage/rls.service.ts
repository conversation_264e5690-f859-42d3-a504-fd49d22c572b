import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { StorageFile } from "./entity/StorageFile.entity";
import { Bucket } from "./entity/Bucket.entity";
import { StoragePolicy } from "./entity/StoragePolicy.entity";
import { User } from "../users/entity/user.entity";
import {
  PolicyCondition,
  PolicyAction,
  PolicyEffect,
  StorageAccessContext,
  PolicyEvaluationResult,
} from "./dto/rls.dto";

export class StorageRLSService {
  private policyRepository: Repository<StoragePolicy>;
  private fileRepository: Repository<StorageFile>;
  private bucketRepository: Repository<Bucket>;
  private userRepository: Repository<User>;

  constructor() {
    this.policyRepository = AppDataSource.getRepository(StoragePolicy);
    this.fileRepository = AppDataSource.getRepository(StorageFile);
    this.bucketRepository = AppDataSource.getRepository(Bucket);
    this.userRepository = AppDataSource.getRepository(User);
  }

  // Policy Management
  async createPolicy(
    name: string,
    effect: PolicyEffect,
    actions: PolicyAction[],
    conditions: PolicyCondition[],
    workspaceId: string,
    bucketName?: string,
    description?: string
  ): Promise<StoragePolicy> {
    const policy = this.policyRepository.create({
      name,
      effect,
      actions,
      conditions,
      workspaceId,
      bucketName,
      description,
      isActive: true,
    });

    return await this.policyRepository.save(policy);
  }

  async updatePolicy(
    policyId: string,
    updates: Partial<{
      name: string;
      effect: PolicyEffect;
      actions: PolicyAction[];
      conditions: PolicyCondition[];
      description: string;
      isActive: boolean;
    }>
  ): Promise<StoragePolicy> {
    const policy = await this.policyRepository.findOne({
      where: { id: policyId },
    });

    if (!policy) {
      throw new Error("Policy not found");
    }

    Object.assign(policy, updates);
    return await this.policyRepository.save(policy);
  }

  async deletePolicy(policyId: string): Promise<void> {
    const result = await this.policyRepository.delete(policyId);
    if (result.affected === 0) {
      throw new Error("Policy not found");
    }
  }

  async getPolicy(policyId: string): Promise<StoragePolicy | null> {
    return await this.policyRepository.findOne({
      where: { id: policyId },
    });
  }

  async getPolicies(
    workspaceId: string,
    bucketName?: string
  ): Promise<StoragePolicy[]> {
    const where: any = { workspaceId, isActive: true };
    if (bucketName) {
      where.bucketName = bucketName;
    }

    return await this.policyRepository.find({
      where,
      order: { priority: "DESC", createdAt: "ASC" },
    });
  }

  // Access Control Evaluation
  async checkFileAccess(
    fileId: string,
    userId: string,
    action: PolicyAction,
    additionalContext?: Record<string, any>
  ): Promise<PolicyEvaluationResult> {
    const file = await this.fileRepository.findOne({
      where: { id: fileId },
      relations: ["bucket"],
    });

    if (!file) {
      return {
        allowed: false,
        reason: "File not found",
        matchedPolicies: [],
      };
    }

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      return {
        allowed: false,
        reason: "User not found",
        matchedPolicies: [],
      };
    }

    const context: StorageAccessContext = {
      user,
      file,
      bucket: file.bucket!,
      action,
      workspaceId: file.workspaceId,
      timestamp: new Date(),
      userAgent: additionalContext?.userAgent,
      ipAddress: additionalContext?.ipAddress,
      ...additionalContext,
    };

    return await this.evaluatePolicies(context);
  }

  async checkBucketAccess(
    bucketName: string,
    workspaceId: string,
    userId: string,
    action: PolicyAction,
    additionalContext?: Record<string, any>
  ): Promise<PolicyEvaluationResult> {
    const bucket = await this.bucketRepository.findOne({
      where: { name: bucketName, workspaceId },
    });

    if (!bucket) {
      return {
        allowed: false,
        reason: "Bucket not found",
        matchedPolicies: [],
      };
    }

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      return {
        allowed: false,
        reason: "User not found",
        matchedPolicies: [],
      };
    }

    const context: StorageAccessContext = {
      user,
      bucket,
      action,
      workspaceId,
      timestamp: new Date(),
      userAgent: additionalContext?.userAgent,
      ipAddress: additionalContext?.ipAddress,
      ...additionalContext,
    };

    return await this.evaluatePolicies(context);
  }

  private async evaluatePolicies(
    context: StorageAccessContext
  ): Promise<PolicyEvaluationResult> {
    // Get applicable policies
    const policies = await this.getPolicies(
      context.workspaceId,
      context.bucket.name
    );

    // Add global workspace policies
    const globalPolicies = await this.getPolicies(context.workspaceId);
    policies.push(...globalPolicies.filter((p) => !p.bucketName));

    // Filter policies that match the action
    const applicablePolicies = policies.filter(
      (policy) =>
        policy.actions.includes(context.action) ||
        policy.actions.includes(PolicyAction.ALL)
    );

    if (applicablePolicies.length === 0) {
      // No policies found - check bucket visibility
      if (context.bucket.isPublic() && this.isReadAction(context.action)) {
        return {
          allowed: true,
          reason: "Public bucket allows read access",
          matchedPolicies: [],
        };
      }

      return {
        allowed: false,
        reason: "No applicable policies found",
        matchedPolicies: [],
      };
    }

    const matchedPolicies: StoragePolicy[] = [];
    let finalDecision = false;
    let decisionReason = "No policies matched conditions";

    // Evaluate policies in priority order (highest first)
    for (const policy of applicablePolicies.sort(
      (a, b) => (b.priority || 0) - (a.priority || 0)
    )) {
      const conditionsMatch = await this.evaluateConditions(
        policy.conditions,
        context
      );

      if (conditionsMatch) {
        matchedPolicies.push(policy);

        if (policy.effect === PolicyEffect.ALLOW) {
          finalDecision = true;
          decisionReason = `Allowed by policy: ${policy.name}`;
          // Continue to check for explicit DENY policies
        } else if (policy.effect === PolicyEffect.DENY) {
          // DENY always takes precedence
          return {
            allowed: false,
            reason: `Denied by policy: ${policy.name}`,
            matchedPolicies,
          };
        }
      }
    }

    return {
      allowed: finalDecision,
      reason: decisionReason,
      matchedPolicies,
    };
  }

  private async evaluateConditions(
    conditions: PolicyCondition[],
    context: StorageAccessContext
  ): Promise<boolean> {
    if (conditions.length === 0) {
      return true; // No conditions means always match
    }

    for (const condition of conditions) {
      const conditionResult = await this.evaluateCondition(condition, context);
      if (!conditionResult) {
        return false; // All conditions must match (AND logic)
      }
    }

    return true;
  }

  private async evaluateCondition(
    condition: PolicyCondition,
    context: StorageAccessContext
  ): Promise<boolean> {
    const { field, operator, value } = condition;
    const contextValue = this.getContextValue(field, context);

    switch (operator) {
      case "eq":
        return contextValue === value;
      case "ne":
        return contextValue !== value;
      case "in":
        return Array.isArray(value) && value.includes(contextValue);
      case "not_in":
        return Array.isArray(value) && !value.includes(contextValue);
      case "starts_with":
        return (
          typeof contextValue === "string" &&
          typeof value === "string" &&
          contextValue.startsWith(value)
        );
      case "ends_with":
        return (
          typeof contextValue === "string" &&
          typeof value === "string" &&
          contextValue.endsWith(value)
        );
      case "contains":
        return (
          typeof contextValue === "string" &&
          typeof value === "string" &&
          contextValue.includes(value)
        );
      case "gt":
        return Number(contextValue) > Number(value);
      case "gte":
        return Number(contextValue) >= Number(value);
      case "lt":
        return Number(contextValue) < Number(value);
      case "lte":
        return Number(contextValue) <= Number(value);
      case "regex":
        return (
          typeof contextValue === "string" &&
          typeof value === "string" &&
          new RegExp(value).test(contextValue)
        );
      case "exists":
        return value ? contextValue !== undefined : contextValue === undefined;
      case "time_between":
        return this.evaluateTimeBetween(contextValue, value);
      case "ip_in_range":
        return this.evaluateIpInRange(contextValue, value);
      default:
        return false;
    }
  }

  private getContextValue(field: string, context: StorageAccessContext): any {
    const fieldPath = field.split(".");
    let value: any = context;

    for (const key of fieldPath) {
      if (value && typeof value === "object") {
        value = value[key];
      } else {
        return undefined;
      }
    }

    return value;
  }

  private evaluateTimeBetween(contextValue: any, range: any): boolean {
    if (!Array.isArray(range) || range.length !== 2) {
      return false;
    }

    const time = new Date(contextValue).getTime();
    const start = new Date(range[0]).getTime();
    const end = new Date(range[1]).getTime();

    return time >= start && time <= end;
  }

  private evaluateIpInRange(ip: any, cidr: any): boolean {
    if (typeof ip !== "string" || typeof cidr !== "string") {
      return false;
    }

    // Simple IP range check - in production use a proper CIDR library
    const [network, prefix] = cidr.split("/");
    const prefixLength = parseInt(prefix) || 32;

    // Convert IPs to numbers for comparison
    const ipNum = this.ipToNumber(ip);
    const networkNum = this.ipToNumber(network);
    const mask = (0xffffffff << (32 - prefixLength)) >>> 0;

    return (ipNum & mask) === (networkNum & mask);
  }

  private ipToNumber(ip: string): number {
    return ip
      .split(".")
      .reduce((acc, octet) => (acc << 8) + parseInt(octet), 0);
  }

  private isReadAction(action: PolicyAction): boolean {
    return [
      PolicyAction.READ,
      PolicyAction.DOWNLOAD,
      PolicyAction.LIST,
    ].includes(action);
  }

  // Predefined Policy Templates
  async createOwnerOnlyPolicy(
    workspaceId: string,
    bucketName?: string
  ): Promise<StoragePolicy> {
    return await this.createPolicy(
      "Owner Only Access",
      PolicyEffect.ALLOW,
      [PolicyAction.ALL],
      [
        {
          field: "file.ownerId",
          operator: "eq",
          value: "${user.id}",
        },
      ],
      workspaceId,
      bucketName,
      "Only file owners can access their files"
    );
  }

  async createPublicReadPolicy(
    workspaceId: string,
    bucketName?: string
  ): Promise<StoragePolicy> {
    return await this.createPolicy(
      "Public Read Access",
      PolicyEffect.ALLOW,
      [PolicyAction.READ, PolicyAction.DOWNLOAD],
      [], // No conditions = always allow
      workspaceId,
      bucketName,
      "Anyone can read files in this bucket"
    );
  }

  async createAuthenticatedOnlyPolicy(
    workspaceId: string,
    bucketName?: string
  ): Promise<StoragePolicy> {
    return await this.createPolicy(
      "Authenticated Users Only",
      PolicyEffect.ALLOW,
      [PolicyAction.ALL],
      [
        {
          field: "user.id",
          operator: "exists",
          value: true,
        },
      ],
      workspaceId,
      bucketName,
      "Only authenticated users can access files"
    );
  }

  async createWorkspaceTeamPolicy(
    workspaceId: string,
    bucketName?: string
  ): Promise<StoragePolicy> {
    return await this.createPolicy(
      "Workspace Team Access",
      PolicyEffect.ALLOW,
      [PolicyAction.ALL],
      [
        {
          field: "user.workspaceId",
          operator: "eq",
          value: workspaceId,
        },
      ],
      workspaceId,
      bucketName,
      "Only workspace team members can access files"
    );
  }

  async createTimeBasedPolicy(
    workspaceId: string,
    startTime: Date,
    endTime: Date,
    bucketName?: string
  ): Promise<StoragePolicy> {
    return await this.createPolicy(
      "Time-Based Access",
      PolicyEffect.ALLOW,
      [PolicyAction.ALL],
      [
        {
          field: "timestamp",
          operator: "time_between",
          value: [startTime.toISOString(), endTime.toISOString()],
        },
      ],
      workspaceId,
      bucketName,
      `Access allowed between ${startTime.toISOString()} and ${endTime.toISOString()}`
    );
  }

  async createIPRestrictedPolicy(
    workspaceId: string,
    allowedCIDRs: string[],
    bucketName?: string
  ): Promise<StoragePolicy> {
    const conditions = allowedCIDRs.map((cidr) => ({
      field: "ipAddress",
      operator: "ip_in_range" as const,
      value: cidr,
    }));

    return await this.createPolicy(
      "IP Restricted Access",
      PolicyEffect.ALLOW,
      [PolicyAction.ALL],
      conditions,
      workspaceId,
      bucketName,
      `Access restricted to IP ranges: ${allowedCIDRs.join(", ")}`
    );
  }

  // Policy Testing
  async testPolicy(
    policyId: string,
    testContext: Partial<StorageAccessContext>
  ): Promise<PolicyEvaluationResult> {
    const policy = await this.getPolicy(policyId);
    if (!policy) {
      throw new Error("Policy not found");
    }

    // Create a mock context for testing
    const mockContext: StorageAccessContext = {
      user: testContext.user || ({ id: "test-user" } as User),
      bucket:
        testContext.bucket ||
        ({ name: "test-bucket", isPublic: () => false } as Bucket),
      file: testContext.file as StorageFile,
      action: testContext.action || PolicyAction.READ,
      workspaceId: testContext.workspaceId || "test-workspace",
      timestamp: testContext.timestamp || new Date(),
      ...testContext,
    };

    const conditionsMatch = await this.evaluateConditions(
      policy.conditions,
      mockContext
    );

    return {
      allowed: conditionsMatch && policy.effect === PolicyEffect.ALLOW,
      reason: conditionsMatch
        ? `Policy conditions matched, effect: ${policy.effect}`
        : "Policy conditions did not match",
      matchedPolicies: conditionsMatch ? [policy] : [],
    };
  }
}
