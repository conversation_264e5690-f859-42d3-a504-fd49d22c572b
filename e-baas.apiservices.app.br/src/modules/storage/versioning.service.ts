import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { StorageFile } from "./entity/StorageFile.entity";
import { FileVersion, VersionStatus, VersionType } from "./entity/FileVersion.entity";
import { Bucket } from "./entity/Bucket.entity";
import fs from "fs/promises";
import path from "path";
import crypto from "crypto";

export interface CreateVersionOptions {
  changelog?: string;
  type?: VersionType;
  createdBy?: string;
  metadata?: Record<string, any>;
  retentionDays?: number;
}

export interface VersioningPolicy {
  maxVersions?: number;
  retentionDays?: number;
  autoCleanup?: boolean;
  compressionEnabled?: boolean;
  excludeExtensions?: string[];
}

export interface RollbackOptions {
  createBackup?: boolean;
  changelog?: string;
  createdBy?: string;
}

export interface CleanupResult {
  deletedVersions: number;
  freedSpace: number;
  errors: string[];
}

export class VersioningService {
  private fileRepository: Repository<StorageFile>;
  private versionRepository: Repository<FileVersion>;
  private bucketRepository: Repository<Bucket>;
  private readonly storageBasePath: string;
  private readonly versionsPath: string;

  constructor() {
    this.fileRepository = AppDataSource.getRepository(StorageFile);
    this.versionRepository = AppDataSource.getRepository(FileVersion);
    this.bucketRepository = AppDataSource.getRepository(Bucket);
    this.storageBasePath = process.env.STORAGE_PATH || './storage';
    this.versionsPath = path.join(this.storageBasePath, 'versions');
    this.ensureVersionsDirectoryExists();
  }

  private async ensureVersionsDirectoryExists(): Promise<void> {
    try {
      await fs.access(this.versionsPath);
    } catch {
      await fs.mkdir(this.versionsPath, { recursive: true });
    }
  }

  async enableVersioning(fileId: string, policy?: VersioningPolicy): Promise<StorageFile> {
    const file = await this.fileRepository.findOne({
      where: { id: fileId, isDeleted: false }
    });

    if (!file) {
      throw new Error('File not found');
    }

    file.enableVersioning();
    
    // Create initial version if it doesn't exist
    if (file.totalVersions === 1) {
      await this.createInitialVersion(file);
    }

    await this.fileRepository.save(file);
    
    console.log(`✅ Versioning enabled for file: ${file.path}`);
    return file;
  }

  async disableVersioning(fileId: string, keepExistingVersions = true): Promise<StorageFile> {
    const file = await this.fileRepository.findOne({
      where: { id: fileId, isDeleted: false }
    });

    if (!file) {
      throw new Error('File not found');
    }

    file.disableVersioning();

    if (!keepExistingVersions) {
      // Delete all versions except the latest
      await this.versionRepository.update(
        { fileId, isLatest: false },
        { status: VersionStatus.DELETED }
      );
    }

    await this.fileRepository.save(file);
    
    console.log(`✅ Versioning disabled for file: ${file.path}`);
    return file;
  }

  async createVersion(
    fileId: string, 
    fileBuffer: Buffer, 
    options: CreateVersionOptions = {}
  ): Promise<FileVersion> {
    const file = await this.fileRepository.findOne({
      where: { id: fileId, isDeleted: false }
    });

    if (!file) {
      throw new Error('File not found');
    }

    if (!file.hasVersioning()) {
      throw new Error('Versioning is not enabled for this file');
    }

    // Generate file hash
    const etag = crypto.createHash('md5').update(fileBuffer).digest('hex');
    const checksum = crypto.createHash('sha256').update(fileBuffer).digest('hex');

    // Check if this version already exists
    const existingVersion = await this.versionRepository.findOne({
      where: { fileId, etag }
    });

    if (existingVersion) {
      throw new Error('A version with identical content already exists');
    }

    // Mark current latest as not latest
    await this.versionRepository.update(
      { fileId, isLatest: true },
      { isLatest: false }
    );

    // Create new version
    const newVersionNumber = file.currentVersion + 1;
    const version = this.versionRepository.create({
      fileId,
      versionNumber: newVersionNumber,
      size: fileBuffer.length,
      mimeType: file.mimeType,
      etag: `"${etag}"`,
      checksum,
      status: VersionStatus.ACTIVE,
      type: options.type || VersionType.AUTO,
      isLatest: true,
      metadata: options.metadata,
      changelog: options.changelog,
      createdBy: options.createdBy,
      expiresAt: options.retentionDays ? 
        new Date(Date.now() + options.retentionDays * 24 * 60 * 60 * 1000) : 
        undefined
    });

    const savedVersion = await this.versionRepository.save(version);

    // Save file to disk
    const versionPath = path.join(this.versionsPath, savedVersion.storagePath);
    await fs.mkdir(path.dirname(versionPath), { recursive: true });
    await fs.writeFile(versionPath, fileBuffer);

    // Update file metadata
    file.incrementVersion();
    file.size = fileBuffer.length;
    file.etag = `"${etag}"`;
    await this.fileRepository.save(file);

    console.log(`✅ Version ${newVersionNumber} created for file: ${file.path}`);
    return savedVersion;
  }

  async getVersions(fileId: string, includeDeleted = false): Promise<FileVersion[]> {
    const whereCondition: any = { fileId };
    
    if (!includeDeleted) {
      whereCondition.status = VersionStatus.ACTIVE;
    }

    return await this.versionRepository.find({
      where: whereCondition,
      order: { versionNumber: 'DESC' }
    });
  }

  async getVersion(fileId: string, versionNumber: number): Promise<FileVersion | null> {
    return await this.versionRepository.findOne({
      where: { 
        fileId, 
        versionNumber, 
        status: VersionStatus.ACTIVE 
      }
    });
  }

  async getLatestVersion(fileId: string): Promise<FileVersion | null> {
    return await this.versionRepository.findOne({
      where: { 
        fileId, 
        isLatest: true, 
        status: VersionStatus.ACTIVE 
      }
    });
  }

  async downloadVersion(fileId: string, versionNumber: number): Promise<{ version: FileVersion; buffer: Buffer }> {
    const version = await this.getVersion(fileId, versionNumber);
    
    if (!version) {
      throw new Error('Version not found');
    }

    const versionPath = path.join(this.versionsPath, version.storagePath);
    
    try {
      const buffer = await fs.readFile(versionPath);
      return { version, buffer };
    } catch (error) {
      throw new Error('Version file not found on storage');
    }
  }

  async rollbackToVersion(
    fileId: string, 
    versionNumber: number, 
    options: RollbackOptions = {}
  ): Promise<FileVersion> {
    const file = await this.fileRepository.findOne({
      where: { id: fileId, isDeleted: false }
    });

    if (!file) {
      throw new Error('File not found');
    }

    const targetVersion = await this.getVersion(fileId, versionNumber);
    
    if (!targetVersion) {
      throw new Error('Target version not found');
    }

    // Create backup of current version if requested
    if (options.createBackup) {
      const currentVersionPath = path.join(this.storageBasePath, file.storagePath);
      try {
        const currentBuffer = await fs.readFile(currentVersionPath);
        await this.createVersion(fileId, currentBuffer, {
          type: VersionType.ROLLBACK,
          changelog: `Backup before rollback to version ${versionNumber}`,
          createdBy: options.createdBy
        });
      } catch (error) {
        console.warn('Failed to create backup before rollback:', error);
      }
    }

    // Copy target version to current file location
    const targetVersionPath = path.join(this.versionsPath, targetVersion.storagePath);
    const currentFilePath = path.join(this.storageBasePath, file.storagePath);
    
    const versionBuffer = await fs.readFile(targetVersionPath);
    await fs.writeFile(currentFilePath, versionBuffer);

    // Update file metadata
    file.size = targetVersion.size;
    file.etag = targetVersion.etag;
    file.setCurrentVersion(versionNumber);
    await this.fileRepository.save(file);

    // Mark target version as latest
    await this.versionRepository.update(
      { fileId, isLatest: true },
      { isLatest: false }
    );
    
    targetVersion.markAsLatest();
    await this.versionRepository.save(targetVersion);

    console.log(`✅ Rolled back file ${file.path} to version ${versionNumber}`);
    return targetVersion;
  }

  async deleteVersion(fileId: string, versionNumber: number, permanent = false): Promise<void> {
    const version = await this.getVersion(fileId, versionNumber);
    
    if (!version) {
      throw new Error('Version not found');
    }

    if (version.isLatest) {
      throw new Error('Cannot delete the latest version');
    }

    if (permanent) {
      // Delete physical file
      try {
        const versionPath = path.join(this.versionsPath, version.storagePath);
        await fs.unlink(versionPath);
      } catch (error) {
        console.warn(`Failed to delete version file: ${error}`);
      }

      // Delete database record
      await this.versionRepository.remove(version);
    } else {
      // Soft delete
      version.softDelete();
      await this.versionRepository.save(version);
    }

    console.log(`✅ Version ${versionNumber} ${permanent ? 'permanently ' : ''}deleted`);
  }

  async cleanupExpiredVersions(): Promise<CleanupResult> {
    const expiredVersions = await this.versionRepository.find({
      where: {
        status: VersionStatus.ACTIVE,
        isLatest: false
      }
    });

    const result: CleanupResult = {
      deletedVersions: 0,
      freedSpace: 0,
      errors: []
    };

    for (const version of expiredVersions) {
      if (version.isExpired()) {
        try {
          const versionPath = path.join(this.versionsPath, version.storagePath);
          
          // Get file size before deletion
          try {
            const stats = await fs.stat(versionPath);
            result.freedSpace += stats.size;
          } catch (error) {
            // File might not exist
          }

          // Delete physical file
          try {
            await fs.unlink(versionPath);
          } catch (error) {
            // File might not exist
          }

          // Delete database record
          await this.versionRepository.remove(version);
          result.deletedVersions++;
          
        } catch (error: any) {
          result.errors.push(`Failed to cleanup version ${version.id}: ${error.message}`);
        }
      }
    }

    if (result.deletedVersions > 0) {
      console.log(`✅ Cleaned up ${result.deletedVersions} expired versions, freed ${(result.freedSpace / 1024 / 1024).toFixed(2)} MB`);
    }

    return result;
  }

  async applyRetentionPolicy(fileId: string, policy: VersioningPolicy): Promise<CleanupResult> {
    const versions = await this.getVersions(fileId, false);
    
    const result: CleanupResult = {
      deletedVersions: 0,
      freedSpace: 0,
      errors: []
    };

    // Sort versions by creation date (newest first)
    const sortedVersions = versions
      .filter(v => !v.isLatest) // Never delete latest version
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    // Apply max versions limit
    if (policy.maxVersions && sortedVersions.length > policy.maxVersions) {
      const versionsToDelete = sortedVersions.slice(policy.maxVersions);
      
      for (const version of versionsToDelete) {
        try {
          await this.deleteVersion(fileId, version.versionNumber, true);
          result.deletedVersions++;
          result.freedSpace += version.size;
        } catch (error: any) {
          result.errors.push(`Failed to delete version ${version.versionNumber}: ${error.message}`);
        }
      }
    }

    // Apply retention days
    if (policy.retentionDays) {
      const cutoffDate = new Date(Date.now() - policy.retentionDays * 24 * 60 * 60 * 1000);
      const oldVersions = sortedVersions.filter(v => v.createdAt < cutoffDate);
      
      for (const version of oldVersions) {
        try {
          await this.deleteVersion(fileId, version.versionNumber, true);
          result.deletedVersions++;
          result.freedSpace += version.size;
        } catch (error: any) {
          result.errors.push(`Failed to delete old version ${version.versionNumber}: ${error.message}`);
        }
      }
    }

    return result;
  }

  async getVersioningStats(fileId?: string): Promise<{
    totalFiles: number;
    versionedFiles: number;
    totalVersions: number;
    totalSize: number;
    averageVersionsPerFile: number;
  }> {
    const whereCondition = fileId ? { id: fileId } : {};
    
    const files = await this.fileRepository.find({
      where: { ...whereCondition, isDeleted: false }
    });

    const versionedFiles = files.filter(f => f.hasVersioning());
    
    const versionWhereCondition = fileId ? { fileId } : {};
    const versions = await this.versionRepository.find({
      where: { ...versionWhereCondition, status: VersionStatus.ACTIVE }
    });

    const totalSize = versions.reduce((sum, version) => sum + version.size, 0);

    return {
      totalFiles: files.length,
      versionedFiles: versionedFiles.length,
      totalVersions: versions.length,
      totalSize,
      averageVersionsPerFile: versionedFiles.length > 0 ? versions.length / versionedFiles.length : 0
    };
  }

  private async createInitialVersion(file: StorageFile): Promise<FileVersion> {
    // Read current file content
    const currentFilePath = path.join(this.storageBasePath, file.storagePath);
    let fileBuffer: Buffer;
    
    try {
      fileBuffer = await fs.readFile(currentFilePath);
    } catch (error) {
      throw new Error('Current file not found on storage');
    }

    // Create initial version
    const etag = crypto.createHash('md5').update(fileBuffer).digest('hex');
    const checksum = crypto.createHash('sha256').update(fileBuffer).digest('hex');

    const version = this.versionRepository.create({
      fileId: file.id,
      versionNumber: 1,
      size: fileBuffer.length,
      mimeType: file.mimeType,
      etag: `"${etag}"`,
      checksum,
      status: VersionStatus.ACTIVE,
      type: VersionType.AUTO,
      isLatest: true,
      changelog: 'Initial version'
    });

    const savedVersion = await this.versionRepository.save(version);

    // Save initial version to versions directory
    const versionPath = path.join(this.versionsPath, savedVersion.storagePath);
    await fs.mkdir(path.dirname(versionPath), { recursive: true });
    await fs.writeFile(versionPath, fileBuffer);

    return savedVersion;
  }
}