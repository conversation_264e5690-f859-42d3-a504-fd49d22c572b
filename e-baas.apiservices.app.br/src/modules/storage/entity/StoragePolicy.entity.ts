import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from "typeorm";
import { PolicyEffect, PolicyAction, PolicyCondition } from "../dto/rls.dto";

@Entity("storage_policies")
@Index("IDX_storage_policies_workspace_id", ["workspaceId"])
@Index("IDX_storage_policies_bucket_name", ["bucketName"])
@Index("IDX_storage_policies_active", ["isActive"])
@Index("IDX_storage_policies_priority", ["priority"])
export class StoragePolicy {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  name: string;

  @Column({ type: "text", nullable: true })
  description?: string;

  @Column({ 
    type: "enum", 
    enum: PolicyEffect,
    default: PolicyEffect.DENY 
  })
  effect: PolicyEffect;

  @Column({ 
    type: "json",
    transformer: {
      to: (value: PolicyAction[]) => JSON.stringify(value),
      from: (value: string) => value ? JSON.parse(value) : []
    }
  })
  actions: PolicyAction[];

  @Column({ 
    type: "json",
    transformer: {
      to: (value: PolicyCondition[]) => JSON.stringify(value),
      from: (value: string) => value ? JSON.parse(value) : []
    }
  })
  conditions: PolicyCondition[];

  @Column({ name: "workspace_id", type: "varchar", length: 255 })
  workspaceId: string;

  @Column({ name: "bucket_name", type: "varchar", length: 255, nullable: true })
  bucketName?: string;

  @Column({ type: "integer", default: 0 })
  priority: number;

  @Column({ name: "is_active", type: "boolean", default: true })
  isActive: boolean;

  @Column({ name: "created_by", type: "varchar", length: 255, nullable: true })
  createdBy?: string;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  // Helper methods
  isApplicableToAction(action: PolicyAction): boolean {
    return this.actions.includes(action) || this.actions.includes(PolicyAction.ALL);
  }

  isApplicableToBucket(bucketName: string): boolean {
    return !this.bucketName || this.bucketName === bucketName;
  }

  isGlobalPolicy(): boolean {
    return !this.bucketName;
  }

  getConditionByField(field: string): PolicyCondition | undefined {
    return this.conditions.find(condition => condition.field === field);
  }

  hasCondition(field: string): boolean {
    return this.conditions.some(condition => condition.field === field);
  }

  toSafeObject() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      effect: this.effect,
      actions: this.actions,
      conditions: this.conditions,
      workspaceId: this.workspaceId,
      bucketName: this.bucketName,
      priority: this.priority,
      isActive: this.isActive,
      createdBy: this.createdBy,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  toAuditLog() {
    return {
      policyId: this.id,
      name: this.name,
      effect: this.effect,
      actions: this.actions,
      workspaceId: this.workspaceId,
      bucketName: this.bucketName,
      isActive: this.isActive
    };
  }
}