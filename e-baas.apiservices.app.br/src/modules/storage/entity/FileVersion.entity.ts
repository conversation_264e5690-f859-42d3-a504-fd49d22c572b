import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  BeforeInsert,
  ManyToOne,
  JoinColumn
} from "typeorm";
import { StorageFile } from "./StorageFile.entity";
import crypto from "crypto";

export enum VersionStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

export enum VersionType {
  MANUAL = 'manual',
  AUTO = 'auto',
  ROLLBACK = 'rollback'
}

@Entity("file_versions")
@Index(["fileId", "versionNumber"], { unique: true })
@Index(["fileId", "status"])
@Index(["fileId", "isLatest"])
@Index(["createdAt"])
@Index(["size"])
export class FileVersion {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "file_id", type: "varchar", length: 255 })
  @Index()
  fileId: string;

  @Column({ name: "version_number", type: "integer" })
  versionNumber: number;

  @Column({ name: "version_hash", type: "varchar", length: 64, unique: true })
  versionHash: string; // Unique hash for this version

  @Column({ type: "bigint" })
  size: number; // file size in bytes

  @Column({ name: "mime_type", type: "varchar", length: 100 })
  mimeType: string;

  @Column({ type: "varchar", length: 100 })
  etag: string; // MD5 hash of file content

  @Column({ name: "storage_path", type: "varchar", length: 1000 })
  storagePath: string; // actual file system path for this version

  @Column({
    type: "enum",
    enum: VersionStatus,
    default: VersionStatus.ACTIVE
  })
  @Index()
  status: VersionStatus;

  @Column({
    type: "enum",
    enum: VersionType,
    default: VersionType.AUTO
  })
  type: VersionType;

  @Column({ name: "is_latest", type: "boolean", default: false })
  @Index()
  isLatest: boolean;

  @Column({ type: "json", nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: "text", nullable: true })
  changelog?: string; // Description of changes in this version

  @Column({ name: "created_by", type: "varchar", length: 255, nullable: true })
  createdBy?: string; // User ID who created this version

  @Column({ name: "checksum", type: "varchar", length: 128, nullable: true })
  checksum?: string; // SHA-256 checksum for integrity

  @Column({ name: "compression_type", type: "varchar", length: 50, nullable: true })
  compressionType?: string; // gzip, lz4, etc.

  @Column({ name: "original_size", type: "bigint", nullable: true })
  originalSize?: number; // Original size before compression

  @Column({ name: "expires_at", type: "timestamp", nullable: true })
  expiresAt?: Date; // When this version should be cleaned up

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @ManyToOne(() => StorageFile, file => file.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: "file_id" })
  file?: StorageFile;

  @BeforeInsert()
  generateVersionMetadata() {
    // Generate version hash if not provided
    if (!this.versionHash) {
      this.versionHash = this.generateVersionHash();
    }

    // Generate storage path for this version
    if (!this.storagePath) {
      this.storagePath = this.generateVersionStoragePath();
    }
  }

  private generateVersionHash(): string {
    const data = `${this.fileId}-${this.versionNumber}-${Date.now()}-${Math.random()}`;
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private generateVersionStoragePath(): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `versions/${this.fileId}/${year}/${month}/${day}/v${this.versionNumber}-${this.versionHash}`;
  }

  // Get human readable file size
  getHumanFileSize(): string {
    const bytes = this.size;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  // Get compression ratio
  getCompressionRatio(): number {
    if (!this.originalSize || this.originalSize === 0) return 1;
    return this.size / this.originalSize;
  }

  // Check if this version is expired
  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  // Mark version as latest
  markAsLatest(): void {
    this.isLatest = true;
  }

  // Mark version as archived
  archive(): void {
    this.status = VersionStatus.ARCHIVED;
    this.isLatest = false;
  }

  // Mark version as deleted
  softDelete(): void {
    this.status = VersionStatus.DELETED;
    this.isLatest = false;
  }

  // Restore version
  restore(): void {
    this.status = VersionStatus.ACTIVE;
  }

  // Calculate age in days
  getAgeInDays(): number {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - this.createdAt.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  // Get version info for API response
  toVersionInfo() {
    return {
      id: this.id,
      fileId: this.fileId,
      versionNumber: this.versionNumber,
      versionHash: this.versionHash,
      size: this.size,
      humanSize: this.getHumanFileSize(),
      mimeType: this.mimeType,
      etag: this.etag,
      status: this.status,
      type: this.type,
      isLatest: this.isLatest,
      metadata: this.metadata,
      changelog: this.changelog,
      createdBy: this.createdBy,
      checksum: this.checksum,
      compressionType: this.compressionType,
      originalSize: this.originalSize,
      compressionRatio: this.getCompressionRatio(),
      ageInDays: this.getAgeInDays(),
      isExpired: this.isExpired(),
      expiresAt: this.expiresAt,
      createdAt: this.createdAt
    };
  }

  // Convert to safe object (without sensitive paths)
  toSafeObject() {
    const { storagePath, ...safeObject } = this;
    return {
      ...safeObject,
      humanSize: this.getHumanFileSize(),
      compressionRatio: this.getCompressionRatio(),
      ageInDays: this.getAgeInDays()
    };
  }
}