import "reflect-metadata";
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { BucketVisibility } from "../dto/storage.dto";
import { StorageFile } from "./StorageFile.entity";

@Entity("storage_buckets")
@Index(["workspaceId", "name"], { unique: true })
@Index(["workspaceId"])
export class Bucket {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100 })
  name: string;

  @Column({ name: "workspace_id", type: "varchar", length: 255 })
  workspaceId: string;

  @Column({
    type: "enum",
    enum: BucketVisibility,
    default: BucketVisibility.PRIVATE
  })
  visibility: BucketVisibility;

  @Column({ type: "text", nullable: true })
  description?: string;

  @Column({
    name: "allowed_mime_types",
    type: "simple-array",
    nullable: true
  })
  allowedMimeTypes?: string[];

  @Column({
    name: "max_file_size",
    type: "bigint",
    nullable: true
  })
  maxFileSize?: number; // in bytes

  @Column({
    name: "enable_versioning",
    type: "boolean",
    default: false
  })
  enableVersioning: boolean;

  @Column({
    name: "enable_cors",
    type: "boolean",
    default: true
  })
  enableCors: boolean;

  @Column({
    name: "cors_origins",
    type: "simple-array",
    nullable: true
  })
  corsOrigins?: string[];

  @Column({
    name: "files_count",
    type: "integer",
    default: 0
  })
  filesCount: number;

  @Column({
    name: "total_size",
    type: "bigint",
    default: 0
  })
  totalSize: number; // total size in bytes

  @Column({
    name: "created_by",
    type: "varchar",
    length: 255,
    nullable: true
  })
  createdBy?: string; // User ID who created the bucket

  @Column({
    name: "is_active",
    type: "boolean",
    default: true
  })
  isActive: boolean;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @OneToMany(() => StorageFile, file => file.bucket)
  files?: StorageFile[];

  @BeforeInsert()
  @BeforeUpdate()
  validateBucket() {
    // Normalize bucket name
    this.name = this.name.toLowerCase().replace(/[^a-z0-9-]/g, '-');
    
    // Validate bucket name format
    if (!/^[a-z0-9][a-z0-9-]*[a-z0-9]$/.test(this.name) && this.name.length > 1) {
      throw new Error('Bucket name must contain only lowercase letters, numbers, and hyphens, and must start and end with alphanumeric characters');
    }

    if (this.name.length < 3 || this.name.length > 63) {
      throw new Error('Bucket name must be between 3 and 63 characters long');
    }

    // Validate CORS origins
    if (this.enableCors && this.corsOrigins) {
      this.corsOrigins = this.corsOrigins.filter(origin => origin.trim() !== '');
    }

    // Set default CORS if enabled but no origins specified
    if (this.enableCors && (!this.corsOrigins || this.corsOrigins.length === 0)) {
      this.corsOrigins = ['*'];
    }
  }

  // Check if bucket is public
  isPublic(): boolean {
    return this.visibility === BucketVisibility.PUBLIC;
  }

  // Check if file type is allowed
  isFileTypeAllowed(mimeType: string): boolean {
    if (!this.allowedMimeTypes || this.allowedMimeTypes.length === 0) {
      return true; // No restrictions
    }
    
    return this.allowedMimeTypes.some(allowedType => {
      if (allowedType.includes('*')) {
        const pattern = allowedType.replace(/\*/g, '.*');
        return new RegExp(`^${pattern}$`).test(mimeType);
      }
      return allowedType === mimeType;
    });
  }

  // Check if file size is within limits
  isFileSizeAllowed(fileSize: number): boolean {
    if (!this.maxFileSize) {
      return true; // No size limit
    }
    return fileSize <= this.maxFileSize;
  }

  // Check if origin is allowed (CORS)
  isOriginAllowed(origin: string): boolean {
    if (!this.enableCors) {
      return false;
    }

    if (!this.corsOrigins || this.corsOrigins.includes('*')) {
      return true;
    }

    return this.corsOrigins.some(allowedOrigin => {
      if (allowedOrigin.includes('*')) {
        const pattern = allowedOrigin.replace(/\*/g, '.*');
        return new RegExp(`^${pattern}$`).test(origin);
      }
      return allowedOrigin === origin;
    });
  }

  // Update statistics
  updateStats(fileSizeDelta: number, fileCountDelta: number): void {
    this.totalSize = Math.max(0, this.totalSize + fileSizeDelta);
    this.filesCount = Math.max(0, this.filesCount + fileCountDelta);
  }

  // Get storage path for this bucket
  getStoragePath(): string {
    return `${this.workspaceId}/${this.name}`;
  }

  // Get URL prefix for files in this bucket
  getUrlPrefix(baseUrl: string): string {
    if (this.isPublic()) {
      return `${baseUrl}/storage/v1/object/public/${this.name}`;
    }
    return `${baseUrl}/storage/v1/object/authenticated/${this.name}`;
  }

  // Convert to safe object (without sensitive data)
  toSafeObject() {
    const { ...safeObject } = this;
    return {
      ...safeObject,
      storageUsed: this.totalSize,
      fileCount: this.filesCount
    };
  }
}