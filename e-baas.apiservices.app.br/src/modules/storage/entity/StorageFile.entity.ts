import "reflect-metadata";
import {
  BeforeInsert,
  Before<PERSON>pdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Bucket } from "./Bucket.entity";
import { FileVersion } from "./FileVersion.entity";
import crypto from "crypto";

@Entity("storage_files")
@Index(["bucketId", "path"], { unique: true })
@Index(["bucketId"])
@Index(["workspaceId"])
@Index(["mimeType"])
@Index(["createdAt"])
export class StorageFile {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  name: string;

  @Column({ type: "varchar", length: 500 })
  path: string; // full path including folders

  @Column({ name: "bucket_id", type: "varchar", length: 255 })
  bucketId: string;

  @Column({ name: "workspace_id", type: "varchar", length: 255 })
  workspaceId: string;

  @Column({ type: "bigint" })
  size: number; // file size in bytes

  @Column({ name: "mime_type", type: "varchar", length: 100 })
  mimeType: string;

  @Column({ type: "varchar", length: 100 })
  etag: string; // MD5 hash or similar

  @Column({ name: "cache_control", type: "varchar", length: 100, nullable: true })
  cacheControl?: string;

  @Column({ type: "json", nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: "varchar", length: 50, nullable: true })
  version?: string; // for versioning

  @Column({ name: "storage_path", type: "varchar", length: 1000 })
  storagePath: string; // actual file system path

  @Column({ name: "is_public", type: "boolean", default: false })
  isPublic: boolean;

  @Column({ name: "owner_id", type: "varchar", length: 255, nullable: true })
  ownerId?: string; // User ID who uploaded the file

  @Column({ name: "last_accessed_at", type: "timestamp", nullable: true })
  lastAccessedAt?: Date;

  @Column({ name: "access_count", type: "integer", default: 0 })
  accessCount: number;

  @Column({ name: "is_deleted", type: "boolean", default: false })
  isDeleted: boolean;

  @Column({ name: "deleted_at", type: "timestamp", nullable: true })
  deletedAt?: Date;

  @Column({ name: "versioning_enabled", type: "boolean", default: false })
  versioningEnabled: boolean;

  @Column({ name: "current_version", type: "integer", default: 1 })
  currentVersion: number;

  @Column({ name: "total_versions", type: "integer", default: 1 })
  totalVersions: number;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @ManyToOne(() => Bucket, bucket => bucket.files)
  @JoinColumn({ name: "bucket_id" })
  bucket?: Bucket;

  @OneToMany(() => FileVersion, version => version.file, { cascade: true })
  versions?: FileVersion[];

  @BeforeInsert()
  generateFileMetadata() {
    // Generate ETag if not provided
    if (!this.etag) {
      this.etag = this.generateETag();
    }

    // Set version if versioning is enabled
    if (!this.version) {
      this.version = this.generateVersion();
    }

    // Normalize path
    this.path = this.normalizePath(this.path);

    // Generate storage path
    this.storagePath = this.generateStoragePath();
  }

  @BeforeUpdate()
  updateMetadata() {
    // Update ETag on content change
    if (this.size && this.etag) {
      this.etag = this.generateETag();
    }
  }

  private generateETag(): string {
    // Generate a unique ETag based on file properties
    const data = `${this.name}-${this.size}-${Date.now()}`;
    return crypto.createHash('md5').update(data).digest('hex');
  }

  private generateVersion(): string {
    return crypto.randomUUID();
  }

  private normalizePath(path: string): string {
    // Remove leading/trailing slashes and normalize
    return path
      .replace(/^\/+/, '')
      .replace(/\/+$/, '')
      .replace(/\/+/g, '/');
  }

  private generateStoragePath(): string {
    // Generate file system path
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${this.workspaceId}/${this.bucketId}/${year}/${month}/${day}/${this.id}`;
  }

  // Get file extension
  getExtension(): string {
    const lastDot = this.name.lastIndexOf('.');
    return lastDot > 0 ? this.name.substring(lastDot + 1).toLowerCase() : '';
  }

  // Check if file is an image
  isImage(): boolean {
    return this.mimeType.startsWith('image/');
  }

  // Check if file is a video
  isVideo(): boolean {
    return this.mimeType.startsWith('video/');
  }

  // Check if file is an audio
  isAudio(): boolean {
    return this.mimeType.startsWith('audio/');
  }

  // Check if file is a document
  isDocument(): boolean {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv'
    ];
    return documentTypes.includes(this.mimeType);
  }

  // Get human readable file size
  getHumanFileSize(): string {
    const bytes = this.size;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  // Get file URL path
  getUrlPath(): string {
    return `${this.bucketId}/${this.path}`;
  }

  // Mark file as accessed
  markAsAccessed(): void {
    this.lastAccessedAt = new Date();
    this.accessCount += 1;
  }

  // Soft delete file
  softDelete(): void {
    this.isDeleted = true;
    this.deletedAt = new Date();
  }

  // Restore soft deleted file
  restore(): void {
    this.isDeleted = false;
    this.deletedAt = undefined;
  }

  // Check if file is expired (for temporary files)
  isExpired(expirationHours = 24): boolean {
    if (!this.createdAt) return false;
    const expirationTime = new Date(this.createdAt.getTime() + (expirationHours * 60 * 60 * 1000));
    return new Date() > expirationTime;
  }

  // Get file metadata for API response
  toMetadata() {
    return {
      id: this.id,
      name: this.name,
      path: this.path,
      bucket: this.bucketId,
      size: this.size,
      mimeType: this.mimeType,
      etag: this.etag,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      metadata: this.metadata,
      version: this.version,
      cacheControl: this.cacheControl,
      owner: this.ownerId,
      isPublic: this.isPublic,
      lastAccessed: this.lastAccessedAt,
      accessCount: this.accessCount,
      humanSize: this.getHumanFileSize(),
      extension: this.getExtension(),
      isImage: this.isImage(),
      isVideo: this.isVideo(),
      isAudio: this.isAudio(),
      isDocument: this.isDocument()
    };
  }

  // Versioning methods
  enableVersioning(): void {
    this.versioningEnabled = true;
  }

  disableVersioning(): void {
    this.versioningEnabled = false;
  }

  incrementVersion(): void {
    this.currentVersion += 1;
    this.totalVersions += 1;
  }

  setCurrentVersion(version: number): void {
    this.currentVersion = version;
  }

  hasVersioning(): boolean {
    return this.versioningEnabled;
  }

  // Get version info
  getVersionInfo() {
    return {
      enabled: this.versioningEnabled,
      current: this.currentVersion,
      total: this.totalVersions,
      hasVersions: this.totalVersions > 1
    };
  }

  // Convert to safe object (without sensitive paths)
  toSafeObject() {
    const { storagePath, ...safeObject } = this;
    return {
      ...safeObject,
      humanSize: this.getHumanFileSize(),
      extension: this.getExtension(),
      urlPath: this.getUrlPath(),
      versionInfo: this.getVersionInfo()
    };
  }
}