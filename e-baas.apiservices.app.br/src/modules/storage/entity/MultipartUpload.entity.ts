import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  BeforeInsert
} from "typeorm";
import { UploadedPart } from "../dto/storage.dto";

@Entity("multipart_uploads")
@Index(["workspaceId", "bucketName"])
@Index(["status", "expiresAt"])
@Index(["uploadId"], { unique: true })
export class MultipartUpload {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "upload_id", unique: true })
  @Index()
  uploadId: string;

  @Column({ name: "bucket_name" })
  bucketName: string;

  @Column({ name: "workspace_id" })
  @Index()
  workspaceId: string;

  @Column({ name: "file_name" })
  fileName: string;

  @Column({ name: "file_path" })
  path: string;

  @Column({ name: "content_type", nullable: true })
  contentType?: string;

  @Column({ type: "jsonb", nullable: true })
  metadata?: Record<string, any>;

  @Column({ name: "cache_control", nullable: true })
  cacheControl?: string;

  @Column({ name: "total_size", type: "bigint", nullable: true })
  totalSize?: number;

  @Column({ name: "chunk_size", type: "integer", default: 5242880 }) // 5MB
  chunkSize: number;

  @Column({ name: "uploaded_size", type: "bigint", default: 0 })
  uploadedSize: number;

  @Column({
    type: "enum",
    enum: ["initiated", "in_progress", "completed", "aborted"],
    default: "initiated"
  })
  @Index()
  status: string;

  @Column({ name: "uploaded_parts", type: "jsonb", default: [] })
  uploadedParts: UploadedPart[];

  @Column({ name: "expires_at" })
  @Index()
  expiresAt: Date;

  @Column({ name: "created_by", nullable: true })
  createdBy?: string;

  @Column({ name: "completed_at", nullable: true })
  completedAt?: Date;

  @Column({ name: "aborted_at", nullable: true })
  abortedAt?: Date;

  @Column({ name: "failure_reason", nullable: true })
  failureReason?: string;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @BeforeInsert()
  generateUploadId() {
    if (!this.uploadId) {
      this.uploadId = this.generateRandomUploadId();
    }
    
    // Set expiration to 24 hours from now
    if (!this.expiresAt) {
      this.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
    }
  }

  private generateRandomUploadId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 15);
    return `multipart_${timestamp}_${random}`;
  }

  // Helper methods
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  isActive(): boolean {
    return this.status === 'initiated' || this.status === 'in_progress';
  }

  isCompleted(): boolean {
    return this.status === 'completed';
  }

  isAborted(): boolean {
    return this.status === 'aborted';
  }

  addPart(partNumber: number, etag: string, size: number, md5Hash?: string): void {
    // Remove existing part with same number if exists
    this.uploadedParts = this.uploadedParts.filter(part => part.partNumber !== partNumber);
    
    // Add new part
    this.uploadedParts.push({
      partNumber,
      etag,
      size,
      uploadedAt: new Date(),
      md5Hash
    });

    // Sort parts by part number
    this.uploadedParts.sort((a, b) => a.partNumber - b.partNumber);

    // Update uploaded size
    this.uploadedSize = this.uploadedParts.reduce((total, part) => total + part.size, 0);

    // Update status
    if (this.status === 'initiated') {
      this.status = 'in_progress';
    }
  }

  removePart(partNumber: number): void {
    const partIndex = this.uploadedParts.findIndex(part => part.partNumber === partNumber);
    if (partIndex !== -1) {
      this.uploadedParts.splice(partIndex, 1);
      
      // Recalculate uploaded size
      this.uploadedSize = this.uploadedParts.reduce((total, part) => total + part.size, 0);
    }
  }

  getPart(partNumber: number): UploadedPart | undefined {
    return this.uploadedParts.find(part => part.partNumber === partNumber);
  }

  getPartsCount(): number {
    return this.uploadedParts.length;
  }

  getExpectedPartsCount(): number {
    if (!this.totalSize) return 0;
    return Math.ceil(this.totalSize / this.chunkSize);
  }

  getUploadProgress(): number {
    if (!this.totalSize) return 0;
    return (this.uploadedSize / this.totalSize) * 100;
  }

  validateCompleteness(): boolean {
    if (!this.totalSize) return false;
    
    const expectedParts = this.getExpectedPartsCount();
    if (this.uploadedParts.length !== expectedParts) return false;
    
    // Check for consecutive part numbers starting from 1
    for (let i = 1; i <= expectedParts; i++) {
      if (!this.uploadedParts.find(part => part.partNumber === i)) {
        return false;
      }
    }
    
    return this.uploadedSize === this.totalSize;
  }

  complete(): void {
    this.status = 'completed';
    this.completedAt = new Date();
  }

  abort(reason?: string): void {
    this.status = 'aborted';
    this.abortedAt = new Date();
    if (reason) {
      this.failureReason = reason;
    }
  }

  getFullPath(): string {
    return this.path ? `${this.path}/${this.fileName}` : this.fileName;
  }

  getDurationMinutes(): number {
    const endTime = this.completedAt || this.abortedAt || new Date();
    return Math.round((endTime.getTime() - this.createdAt.getTime()) / (1000 * 60));
  }

  toSafeObject() {
    return {
      id: this.id,
      uploadId: this.uploadId,
      bucketName: this.bucketName,
      workspaceId: this.workspaceId,
      fileName: this.fileName,
      path: this.path,
      fullPath: this.getFullPath(),
      contentType: this.contentType,
      metadata: this.metadata,
      totalSize: this.totalSize,
      uploadedSize: this.uploadedSize,
      chunkSize: this.chunkSize,
      status: this.status,
      partsCount: this.getPartsCount(),
      expectedPartsCount: this.getExpectedPartsCount(),
      uploadProgress: this.getUploadProgress(),
      expiresAt: this.expiresAt,
      isExpired: this.isExpired(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      completedAt: this.completedAt,
      abortedAt: this.abortedAt,
      durationMinutes: this.getDurationMinutes()
    };
  }
}