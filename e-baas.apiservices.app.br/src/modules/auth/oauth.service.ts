import axios from "axios";
import { AuthProvider } from "./dto/auth.dto";
import config from "../../infra/config";

export interface OAuthUserInfo {
  id: string;
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  provider: AuthProvider;
  providerId: string;
  metadata?: Record<string, any>;
}

export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
}

export class OAuthService {
  private providers: Map<AuthProvider, OAuthConfig> = new Map();

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    // Google OAuth configuration
    if (config.oauth?.google?.clientId) {
      this.providers.set(AuthProvider.GOOGLE, {
        clientId: config.oauth.google.clientId,
        clientSecret: config.oauth.google.clientSecret,
        redirectUri: config.oauth.google.redirectUri || `${config.app.baseUrl}/auth/v1/callback/google`,
        scope: ['openid', 'email', 'profile']
      });
    }

    // GitHub OAuth configuration
    if (config.oauth?.github?.clientId) {
      this.providers.set(AuthProvider.GITHUB, {
        clientId: config.oauth.github.clientId,
        clientSecret: config.oauth.github.clientSecret,
        redirectUri: config.oauth.github.redirectUri || `${config.app.baseUrl}/auth/v1/callback/github`,
        scope: ['user:email', 'read:user']
      });
    }

    // Facebook OAuth configuration
    if (config.oauth?.facebook?.clientId) {
      this.providers.set(AuthProvider.FACEBOOK, {
        clientId: config.oauth.facebook.clientId,
        clientSecret: config.oauth.facebook.clientSecret,
        redirectUri: config.oauth.facebook.redirectUri || `${config.app.baseUrl}/auth/v1/callback/facebook`,
        scope: ['email', 'public_profile']
      });
    }
  }

  getAuthUrl(provider: AuthProvider, state?: string): string {
    const providerConfig = this.providers.get(provider);
    if (!providerConfig) {
      throw new Error(`OAuth provider ${provider} is not configured`);
    }

    const baseUrls = {
      [AuthProvider.GOOGLE]: 'https://accounts.google.com/o/oauth2/v2/auth',
      [AuthProvider.GITHUB]: 'https://github.com/login/oauth/authorize',
      [AuthProvider.FACEBOOK]: 'https://www.facebook.com/v18.0/dialog/oauth',
      [AuthProvider.EMAIL]: '',
      [AuthProvider.TWITTER]: 'https://api.twitter.com/oauth/authorize'
    };

    const params = new URLSearchParams({
      client_id: providerConfig.clientId,
      response_type: 'code',
      scope: providerConfig.scope.join(' '),
      redirect_uri: providerConfig.redirectUri,
      ...(state && { state })
    });

    return `${baseUrls[provider]}?${params.toString()}`;
  }

  async exchangeCodeForToken(provider: AuthProvider, code: string): Promise<string> {
    const providerConfig = this.providers.get(provider);
    if (!providerConfig) {
      throw new Error(`OAuth provider ${provider} is not configured`);
    }

    const tokenUrls = {
      [AuthProvider.GOOGLE]: 'https://oauth2.googleapis.com/token',
      [AuthProvider.GITHUB]: 'https://github.com/login/oauth/access_token',
      [AuthProvider.FACEBOOK]: 'https://graph.facebook.com/v18.0/oauth/access_token',
      [AuthProvider.EMAIL]: '',
      [AuthProvider.TWITTER]: ''
    };

    try {
      const response = await axios.post(tokenUrls[provider], {
        client_id: providerConfig.clientId,
        client_secret: providerConfig.clientSecret,
        code,
        redirect_uri: providerConfig.redirectUri,
        grant_type: 'authorization_code'
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      if (response.data.access_token) {
        return response.data.access_token;
      } else {
        throw new Error('No access token received from OAuth provider');
      }
    } catch (error: any) {
      console.error(`Failed to exchange code for token with ${provider}:`, error.response?.data || error.message);
      throw new Error(`OAuth token exchange failed: ${error.message}`);
    }
  }

  async getUserInfo(provider: AuthProvider, accessToken: string): Promise<OAuthUserInfo> {
    try {
      switch (provider) {
        case AuthProvider.GOOGLE:
          return await this.getGoogleUserInfo(accessToken);
        case AuthProvider.GITHUB:
          return await this.getGitHubUserInfo(accessToken);
        case AuthProvider.FACEBOOK:
          return await this.getFacebookUserInfo(accessToken);
        default:
          throw new Error(`OAuth provider ${provider} is not supported`);
      }
    } catch (error: any) {
      console.error(`Failed to get user info from ${provider}:`, error.message);
      throw new Error(`Failed to get user information from ${provider}: ${error.message}`);
    }
  }

  private async getGoogleUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    const response = await axios.get('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    const data = response.data;
    return {
      id: data.id,
      email: data.email,
      name: data.name,
      firstName: data.given_name,
      lastName: data.family_name,
      avatar: data.picture,
      provider: AuthProvider.GOOGLE,
      providerId: data.id,
      metadata: {
        locale: data.locale,
        verified_email: data.verified_email
      }
    };
  }

  private async getGitHubUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Accept': 'application/vnd.github.v3+json'
    };

    // Get user basic info
    const userResponse = await axios.get('https://api.github.com/user', { headers });
    const userData = userResponse.data;

    // Get user emails
    const emailsResponse = await axios.get('https://api.github.com/user/emails', { headers });
    const emails = emailsResponse.data;
    const primaryEmail = emails.find((email: any) => email.primary)?.email || userData.email;

    return {
      id: userData.id.toString(),
      email: primaryEmail,
      name: userData.name || userData.login,
      firstName: userData.name?.split(' ')[0],
      lastName: userData.name?.split(' ').slice(1).join(' '),
      avatar: userData.avatar_url,
      provider: AuthProvider.GITHUB,
      providerId: userData.id.toString(),
      metadata: {
        login: userData.login,
        bio: userData.bio,
        blog: userData.blog,
        location: userData.location,
        public_repos: userData.public_repos,
        followers: userData.followers,
        following: userData.following
      }
    };
  }

  private async getFacebookUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    const response = await axios.get('https://graph.facebook.com/v18.0/me', {
      params: {
        fields: 'id,name,email,first_name,last_name,picture.type(large)',
        access_token: accessToken
      }
    });

    const data = response.data;
    return {
      id: data.id,
      email: data.email,
      name: data.name,
      firstName: data.first_name,
      lastName: data.last_name,
      avatar: data.picture?.data?.url,
      provider: AuthProvider.FACEBOOK,
      providerId: data.id,
      metadata: {
        picture: data.picture
      }
    };
  }

  isProviderEnabled(provider: AuthProvider): boolean {
    return this.providers.has(provider);
  }

  getEnabledProviders(): AuthProvider[] {
    return Array.from(this.providers.keys());
  }

  validateState(receivedState: string, expectedState?: string): boolean {
    if (!expectedState) return true;
    return receivedState === expectedState;
  }

  generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }
}