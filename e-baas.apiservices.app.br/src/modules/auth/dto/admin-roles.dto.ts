import { IsEnum, IsString, IsUUID, IsOptional, IsArray } from "class-validator";

export enum AdminRole {
  PLATFORM_ADMIN = 'platform_admin',    // Admin master da plataforma
  WORKSPACE_ADMIN = 'workspace_admin',   // Admin do tenant/workspace
  WORKSPACE_OWNER = 'workspace_owner',   // Dono do workspace
  SERVICE_ROLE = 'service_role'          // Role de serviço (API keys)
}

export enum UserRole {
  ADMIN = 'admin',
  EDITOR = 'editor', 
  VIEWER = 'viewer',
  AUTHENTICATED = 'authenticated'
}

export class AdminRoleDto {
  @IsEnum(AdminRole)
  adminRole: AdminRole;
  
  @IsEnum(UserRole)
  userRole: UserRole;
  
  @IsString()
  @IsOptional()
  workspaceId?: string;
  
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  permissions?: string[];
}

export class AssignAdminRoleDto {
  @IsUUID()
  userId: string;
  
  @IsEnum(AdminRole)
  adminRole: AdminRole;
  
  @IsString()
  @IsOptional()
  workspaceId?: string;
  
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  customPermissions?: string[];
}