import "reflect-metadata";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { User } from "../../../modules/users/entity/user.entity";
import { v4 as uuidv4 } from "uuid";

@Entity("refresh_tokens")
@Index(["token"], { unique: true })
@Index(["userId", "workspaceId"])
export class RefreshToken {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "token", type: "varchar", length: 500, unique: true })
  token: string;

  @Column({ name: "workspace_id", type: "varchar", length: 255 })
  workspaceId: string;

  @Column({ name: "expires_at", type: "timestamp" })
  expiresAt: Date;

  @Column({ name: "is_revoked", type: "boolean", default: false })
  isRevoked: boolean;

  @Column({ name: "revoked_at", type: "timestamp", nullable: true })
  revokedAt?: Date;

  @Column({ name: "parent_id", type: "varchar", length: 255, nullable: true })
  parentId?: string; // For refresh token rotation

  @Column({ name: "session_id", type: "varchar", length: 255, nullable: true })
  sessionId?: string;

  @Column({ name: "ip_address", type: "varchar", length: 45, nullable: true })
  ipAddress?: string;

  @Column({ name: "user_agent", type: "text", nullable: true })
  userAgent?: string;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @Column({ name: "last_used_at", type: "timestamp", nullable: true })
  lastUsedAt?: Date;

  @Column({ name: "user_id" })
  userId: string;

  @ManyToOne(() => User, user => user.refreshTokens)
  @JoinColumn({ name: "user_id" })
  user: User;

  @BeforeInsert()
  generateToken() {
    this.token = this.generateSecureToken();
  }

  private generateSecureToken(): string {
    // Generate a more secure token combining UUID and timestamp
    const uuid = uuidv4().replace(/-/g, '');
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `${uuid}${timestamp}${random}`;
  }

  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  isValid(): boolean {
    return !this.isRevoked && !this.isExpired();
  }

  revoke(): void {
    this.isRevoked = true;
    this.revokedAt = new Date();
  }

  updateLastUsed(): void {
    this.lastUsedAt = new Date();
  }

  // Check if token is close to expiry (within 10 minutes)
  isNearExpiry(): boolean {
    const tenMinutes = 10 * 60 * 1000;
    return (this.expiresAt.getTime() - Date.now()) < tenMinutes;
  }

  // Get remaining time in milliseconds
  getRemainingTime(): number {
    return Math.max(0, this.expiresAt.getTime() - Date.now());
  }
}
