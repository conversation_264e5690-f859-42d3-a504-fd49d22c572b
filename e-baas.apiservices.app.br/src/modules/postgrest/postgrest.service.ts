import { DataSource, SelectQueryBuilder } from "typeorm";
import { getWorkspaceConnection } from "../../infra/database/data-source";
import { PostgrestQueryDto, PostgrestRpcDto, PostgrestOperation } from "./dto/postgrest.dto";

export class PostgrestService {
  private async getConnection(workspaceId: string): Promise<DataSource> {
    // Get workspace configuration from database-configs module
    // For now, we'll use a default PostgreSQL connection
    const config = {
      type: "postgres" as const,
      host: process.env.DB_HOST || "localhost",
      port: parseInt(process.env.DB_PORT || "5432"),
      username: process.env.DB_USERNAME || "postgres",
      password: process.env.DB_PASSWORD || "postgres",
      database: `workspace_${workspaceId}`,
      synchronize: false,
      logging: false,
    };

    return await getWorkspaceConnection(workspaceId, config);
  }

  async executeQuery(queryDto: PostgrestQueryDto): Promise<any> {
    const connection = await this.getConnection(queryDto.workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      switch (queryDto.operation) {
        case PostgrestOperation.SELECT:
          return await this.executeSelect(queryRunner.manager, queryDto);
        case PostgrestOperation.INSERT:
          return await this.executeInsert(queryRunner.manager, queryDto);
        case PostgrestOperation.UPDATE:
          return await this.executeUpdate(queryRunner.manager, queryDto);
        case PostgrestOperation.DELETE:
          return await this.executeDelete(queryRunner.manager, queryDto);
        case PostgrestOperation.UPSERT:
          return await this.executeUpsert(queryRunner.manager, queryDto);
        default:
          return await this.executeSelect(queryRunner.manager, queryDto);
      }
    } finally {
      await queryRunner.release();
    }
  }

  private async executeSelect(manager: any, queryDto: PostgrestQueryDto): Promise<any> {
    let query = manager.createQueryBuilder().select().from(queryDto.table, queryDto.table);

    // Apply select fields
    if (queryDto.select) {
      const fields = queryDto.select.split(',').map(field => `${queryDto.table}.${field.trim()}`);
      query = query.select(fields);
    }

    // Apply filters
    if (queryDto.filters) {
      Object.entries(queryDto.filters).forEach(([key, value], index) => {
        const paramName = `param${index}`;
        if (index === 0) {
          query = query.where(`${queryDto.table}.${key} = :${paramName}`, { [paramName]: value });
        } else {
          query = query.andWhere(`${queryDto.table}.${key} = :${paramName}`, { [paramName]: value });
        }
      });
    }

    // Apply ordering
    if (queryDto.order) {
      const [field, direction] = queryDto.order.split('.');
      query = query.orderBy(`${queryDto.table}.${field}`, direction?.toUpperCase() === 'DESC' ? 'DESC' : 'ASC');
    }

    // Apply pagination
    if (queryDto.limit) {
      query = query.limit(queryDto.limit);
    }
    if (queryDto.offset) {
      query = query.offset(queryDto.offset);
    }

    return await query.getRawMany();
  }

  private async executeInsert(manager: any, queryDto: PostgrestQueryDto): Promise<any> {
    if (queryDto.dataArray && queryDto.dataArray.length > 0) {
      // Bulk insert
      const result = await manager.createQueryBuilder()
        .insert()
        .into(queryDto.table)
        .values(queryDto.dataArray)
        .execute();
      
      if (queryDto.prefer === 'return=representation') {
        // Return the inserted records
        const ids = result.identifiers.map((item: any) => item.id);
        return await manager.createQueryBuilder()
          .select()
          .from(queryDto.table, queryDto.table)
          .whereInIds(ids)
          .getRawMany();
      }
      
      return result;
    } else if (queryDto.data) {
      // Single insert
      const result = await manager.createQueryBuilder()
        .insert()
        .into(queryDto.table)
        .values(queryDto.data)
        .execute();
      
      if (queryDto.prefer === 'return=representation') {
        const id = result.identifiers[0]?.id;
        if (id) {
          return await manager.createQueryBuilder()
            .select()
            .from(queryDto.table, queryDto.table)
            .where('id = :id', { id })
            .getRawOne();
        }
      }
      
      return result;
    }
    
    throw new Error('No data provided for insert operation');
  }

  private async executeUpdate(manager: any, queryDto: PostgrestQueryDto): Promise<any> {
    if (!queryDto.data) {
      throw new Error('No data provided for update operation');
    }

    let query = manager.createQueryBuilder()
      .update(queryDto.table)
      .set(queryDto.data);

    // Apply filters for WHERE clause
    if (queryDto.filters) {
      Object.entries(queryDto.filters).forEach(([key, value], index) => {
        const paramName = `param${index}`;
        if (index === 0) {
          query = query.where(`${key} = :${paramName}`, { [paramName]: value });
        } else {
          query = query.andWhere(`${key} = :${paramName}`, { [paramName]: value });
        }
      });
    }

    const result = await query.execute();

    if (queryDto.prefer === 'return=representation') {
      // Return updated records
      let selectQuery = manager.createQueryBuilder().select().from(queryDto.table, queryDto.table);
      
      if (queryDto.filters) {
        Object.entries(queryDto.filters).forEach(([key, value], index) => {
          const paramName = `returnParam${index}`;
          if (index === 0) {
            selectQuery = selectQuery.where(`${key} = :${paramName}`, { [paramName]: value });
          } else {
            selectQuery = selectQuery.andWhere(`${key} = :${paramName}`, { [paramName]: value });
          }
        });
      }
      
      return await selectQuery.getRawMany();
    }

    return result;
  }

  private async executeDelete(manager: any, queryDto: PostgrestQueryDto): Promise<any> {
    let query = manager.createQueryBuilder()
      .delete()
      .from(queryDto.table);

    // Apply filters for WHERE clause
    if (queryDto.filters) {
      Object.entries(queryDto.filters).forEach(([key, value], index) => {
        const paramName = `param${index}`;
        if (index === 0) {
          query = query.where(`${key} = :${paramName}`, { [paramName]: value });
        } else {
          query = query.andWhere(`${key} = :${paramName}`, { [paramName]: value });
        }
      });
    }

    return await query.execute();
  }

  private async executeUpsert(manager: any, queryDto: PostgrestQueryDto): Promise<any> {
    if (!queryDto.data) {
      throw new Error('No data provided for upsert operation');
    }

    // PostgreSQL UPSERT using ON CONFLICT
    const columns = Object.keys(queryDto.data);
    const values = Object.values(queryDto.data);
    const updateColumns = columns.filter(col => col !== 'id'); // Exclude id from update

    const conflictTarget = 'id'; // Assuming id is the conflict target
    const updateSet = updateColumns.map(col => `${col} = EXCLUDED.${col}`).join(', ');

    const query = `
      INSERT INTO ${queryDto.table} (${columns.join(', ')})
      VALUES (${values.map((_, i) => `$${i + 1}`).join(', ')})
      ON CONFLICT (${conflictTarget}) DO UPDATE SET ${updateSet}
      ${queryDto.prefer === 'return=representation' ? 'RETURNING *' : ''}
    `;

    return await manager.query(query, values);
  }

  async executeRpc(rpcDto: PostgrestRpcDto): Promise<any> {
    const connection = await this.getConnection(rpcDto.workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();
      
      // Execute stored procedure/function
      const paramKeys = rpcDto.params ? Object.keys(rpcDto.params) : [];
      const paramValues = rpcDto.params ? Object.values(rpcDto.params) : [];
      
      const query = `SELECT * FROM ${rpcDto.functionName}(${paramKeys.map((_, i) => `$${i + 1}`).join(', ')})`;
      
      return await queryRunner.manager.query(query, paramValues);
    } finally {
      await queryRunner.release();
    }
  }

  async getSchema(workspaceId: string, schema = 'public'): Promise<any> {
    const connection = await this.getConnection(workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();
      
      // Get all tables in the schema
      const tables = await queryRunner.manager.query(`
        SELECT table_name, table_type 
        FROM information_schema.tables 
        WHERE table_schema = $1
        ORDER BY table_name
      `, [schema]);

      // Get columns for each table
      const tablesWithColumns = await Promise.all(
        tables.map(async (table: any) => {
          const columns = await queryRunner.manager.query(`
            SELECT 
              column_name,
              data_type,
              is_nullable,
              column_default,
              ordinal_position
            FROM information_schema.columns 
            WHERE table_schema = $1 AND table_name = $2
            ORDER BY ordinal_position
          `, [schema, table.table_name]);

          return {
            ...table,
            columns
          };
        })
      );

      return tablesWithColumns;
    } finally {
      await queryRunner.release();
    }
  }
}