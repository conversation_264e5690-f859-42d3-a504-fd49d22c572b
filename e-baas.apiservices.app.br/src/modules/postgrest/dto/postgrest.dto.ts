import { IsNotEmpty, IsOptional, IsString, IsObject, IsArray, IsEnum } from "class-validator";

export enum PostgrestOperation {
  SELECT = 'select',
  INSERT = 'insert',
  UPDATE = 'update',
  DELETE = 'delete',
  UPSERT = 'upsert'
}

export class PostgrestQueryDto {
  @IsNotEmpty()
  @IsString()
  table: string;

  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsString()
  select?: string;

  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @IsOptional()
  @IsString()
  order?: string;

  @IsOptional()
  limit?: number;

  @IsOptional()
  offset?: number;

  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @IsOptional()
  @IsArray()
  dataArray?: Record<string, any>[];

  @IsOptional()
  @IsEnum(PostgrestOperation)
  operation?: PostgrestOperation;

  @IsOptional()
  @IsString()
  prefer?: string; // return=representation, return=minimal, etc.
}

export class PostgrestRpcDto {
  @IsNotEmpty()
  @IsString()
  functionName: string;

  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsObject()
  params?: Record<string, any>;
}

export class PostgrestSchemaDto {
  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsString()
  schema?: string;
}
