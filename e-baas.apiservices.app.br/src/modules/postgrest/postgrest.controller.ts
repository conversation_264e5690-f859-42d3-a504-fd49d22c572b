import { Router, Request, Response } from "express";
import { PostgrestService } from "./postgrest.service";
import { PostgrestQueryDto, PostgrestRpcDto, PostgrestSchemaDto, PostgrestOperation } from "./dto/postgrest.dto";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";
import { requireReadAccess, requireWriteAccess, requireDeleteAccess } from "../../infra/middlewares/apiKeyAuth.middleware";

const postgrestRouter = Router();
const postgrestService = new PostgrestService();

// PostgREST-style routes
// GET /table?select=*&filter=value - SELECT
postgrestRouter.get("/:table", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { table } = req.params;
    const { workspaceId, select, order, limit, offset, ...filters } = req.query as any;

    const queryDto = plainToClass(PostgrestQueryDto, {
      table,
      workspaceId,
      select,
      order,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
      filters: Object.keys(filters).length > 0 ? filters : undefined,
      operation: PostgrestOperation.SELECT
    });

    const errors = await validate(queryDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await postgrestService.executeQuery(queryDto);
    
    // Set count header if requested
    res.set('Content-Range', `0-${result.length - 1}/${result.length}`);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// POST /table - INSERT
postgrestRouter.post("/:table", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const { table } = req.params;
    const { workspaceId } = req.query as any;
    const prefer = req.headers.prefer as string;
    
    const data = req.body;
    const isArray = Array.isArray(data);

    const queryDto = plainToClass(PostgrestQueryDto, {
      table,
      workspaceId,
      data: isArray ? undefined : data,
      dataArray: isArray ? data : undefined,
      operation: PostgrestOperation.INSERT,
      prefer
    });

    const errors = await validate(queryDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await postgrestService.executeQuery(queryDto);
    
    return res.status(201).json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// PATCH /table?filter=value - UPDATE
postgrestRouter.patch("/:table", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const { table } = req.params;
    const { workspaceId, ...filters } = req.query as any;
    const prefer = req.headers.prefer as string;
    const data = req.body;

    const queryDto = plainToClass(PostgrestQueryDto, {
      table,
      workspaceId,
      data,
      filters: Object.keys(filters).length > 0 ? filters : undefined,
      operation: PostgrestOperation.UPDATE,
      prefer
    });

    const errors = await validate(queryDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await postgrestService.executeQuery(queryDto);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// DELETE /table?filter=value - DELETE
postgrestRouter.delete("/:table", requireDeleteAccess, async (req: Request, res: Response) => {
  try {
    const { table } = req.params;
    const { workspaceId, ...filters } = req.query as any;

    const queryDto = plainToClass(PostgrestQueryDto, {
      table,
      workspaceId,
      filters: Object.keys(filters).length > 0 ? filters : undefined,
      operation: PostgrestOperation.DELETE
    });

    const errors = await validate(queryDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await postgrestService.executeQuery(queryDto);
    
    return res.status(204).json();
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// POST /rpc/function_name - RPC calls
postgrestRouter.post("/rpc/:functionName", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const { functionName } = req.params;
    const { workspaceId } = req.query as any;
    const params = req.body;

    const rpcDto = plainToClass(PostgrestRpcDto, {
      functionName,
      workspaceId,
      params
    });

    const errors = await validate(rpcDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await postgrestService.executeRpc(rpcDto);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// GET /schema - Get schema information
postgrestRouter.get("/", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { workspaceId, schema } = req.query as any;

    const schemaDto = plainToClass(PostgrestSchemaDto, {
      workspaceId,
      schema
    });

    const errors = await validate(schemaDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await postgrestService.getSchema(workspaceId, schema);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

export default postgrestRouter;