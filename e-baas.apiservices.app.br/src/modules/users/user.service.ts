import { Request, Response } from "express";
import IContractUseCases from "../../infra/contracts";
import { UserDto, UserResponseDto } from "./dto/user.dto";

export default class UserServices {
  private userUseCases: IContractUseCases<UserResponseDto>;

  constructor(userUseCases: IContractUseCases<UserResponseDto>) {
    this.userUseCases = userUseCases;
  }

  async getAll(req: Request, res: Response) {
    const response = await this.userUseCases.getAll(req?.query);
    return res.json(response);
  }

  async getOne(req: Request, res: Response) {
    const response = await this.userUseCases.getOne(req?.params?.id);
    return res.json(response);
  }

  async create(req: Request, res: Response) {
    const data = req.body as UserDto;
    // Convert UserDto to User partial for creation
    const userData = {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      password: data.password,
      isActive: data.isActive ?? true
    };
    const response = await this.userUseCases.create(userData);
    return res.json(response);
  }

  async update(req: Request, res: Response) {
    const data = req.body as UserDto;
    const userId = req.params.id;
    // Convert UserDto to User partial for update
    const userData = {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      password: data.password,
      isActive: data.isActive
    };
    const response = await this.userUseCases.update(userId, userData);
    return res.status(200).json(response);
  }

  async delete(req: Request, res: Response) {
    const response = await this.userUseCases.delete(req?.params?.id);
    return res.status(204).json(response);
  }
}
