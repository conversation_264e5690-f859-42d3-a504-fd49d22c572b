import { User } from "./entity/user.entity";
import { UserResponseDto } from "./dto/user.dto";
import { userRepository } from "../../infra/repository";
import IContractUseCases from "../../infra/contracts";
import { <PERSON>rrorHand<PERSON> } from "../../infra/errorHandlers";

export default class UserUseCases implements IContractUseCases<UserResponseDto> {
  constructor() {}

  async getAll(): Promise<UserResponseDto[]> {
    try {
      const users = await userRepository.find();
      return users.map(user => this.mapToResponseDto(user));
    } catch (error: unknown) {
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async getOne(id: string): Promise<UserResponseDto> {
    try {
      const user = await userRepository.findOneBy({ id });
      if (!user) {
        throw ErrorHandler.NotFound("User not found");
      }
      return this.mapToResponseDto(user);
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "User not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async create(data: any): Promise<UserResponseDto> {
    try {
      const user = await userRepository.save(data);
      return this.mapToResponseDto(user);
    } catch (error: unknown) {
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async update(id: string, data: any): Promise<UserResponseDto> {
    try {
      const existingUser = await userRepository.findOneBy({ id });
      if (!existingUser) {
        throw ErrorHandler.NotFound("User not found");
      }
      await userRepository.update(id, data);
      const updatedUser = await userRepository.findOneBy({ id });
      return this.mapToResponseDto(updatedUser!);
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "User not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async delete(id: string): Promise<void> {
    try {
      const user = await userRepository.findOneBy({ id });
      if (!user) {
        throw ErrorHandler.NotFound("User not found");
      }
      await userRepository.delete(id);
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "User not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  private mapToResponseDto(user: User): UserResponseDto {
    return {
      id: user.id,
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };
  }
}
