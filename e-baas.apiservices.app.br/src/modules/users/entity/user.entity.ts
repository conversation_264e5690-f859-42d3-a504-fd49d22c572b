import "reflect-metadata";
import {
  BeforeI<PERSON><PERSON>,
  Before<PERSON>p<PERSON>,
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import bcrypt from "bcryptjs";
import config from "config";

@Entity("users")
@Index(["email", "workspaceId"], { unique: true })
export class User {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  email: string;

  @Column({ type: "varchar", length: 100, select: false, nullable: true })
  password?: string;

  @Column({ name: "first_name", type: "varchar", length: 100, nullable: true })
  firstName?: string;

  @Column({ name: "last_name", type: "varchar", length: 100, nullable: true })
  lastName?: string;

  @Column({ name: "workspace_id", type: "varchar", length: 255 })
  workspaceId: string;

  @Column({ type: "varchar", length: 50, default: 'authenticated' })
  role: string;

  @Column({ name: "admin_role", type: "varchar", length: 50, nullable: true })
  adminRole?: string;

  @Column({ name: "auth_provider", type: "varchar", length: 50, default: 'email' })
  provider: string;

  @Column({ name: "provider_id", type: "varchar", length: 255, nullable: true })
  providerId?: string;

  @Column({ name: "oauth_providers", type: "jsonb", nullable: true })
  oauthProviders?: Record<string, { providerId: string; lastSignIn: Date }>;

  @Column({ name: "is_email_verified", type: "boolean", default: false })
  isEmailVerified: boolean;

  @Column({ name: "email_verified", type: "boolean", default: false })
  emailVerified: boolean;

  @Column({ name: "email_verify_token", type: "varchar", length: 500, nullable: true, select: false })
  emailVerifyToken?: string;

  @Column({ name: "password_reset_token", type: "varchar", length: 500, nullable: true, select: false })
  passwordResetToken?: string;

  @Column({ name: "password_reset_expires", type: "timestamp", nullable: true })
  passwordResetExpires?: Date;

  @Column({ name: "magic_link_token", type: "varchar", length: 500, nullable: true, select: false })
  magicLinkToken?: string;

  @Column({ name: "magic_link_expires", type: "timestamp", nullable: true })
  magicLinkExpires?: Date;

  @Column({ name: "last_sign_in", type: "timestamp", nullable: true })
  lastSignIn?: Date;

  @Column({ name: "last_sign_in_at", type: "timestamp", nullable: true })
  lastSignInAt?: Date;

  @Column({ name: "sign_in_count", type: "integer", default: 0 })
  signInCount: number;

  @Column({ name: "is_active", type: "boolean", default: true })
  isActive: boolean;

  @Column({ name: "is_anonymous", type: "boolean", default: false })
  isAnonymous: boolean;

  @Column({ type: "jsonb", nullable: true })
  metadata?: Record<string, any>;

  @Column({ name: "raw_app_meta_data", type: "jsonb", nullable: true })
  rawAppMetaData?: Record<string, any>;

  @Column({ name: "raw_user_meta_data", type: "jsonb", nullable: true })
  rawUserMetaData?: Record<string, any>;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @Column({ name: "confirmed_at", type: "timestamp", nullable: true })
  confirmedAt?: Date;

  @Column({ name: "banned_until", type: "timestamp", nullable: true })
  bannedUntil?: Date;

  // Lazy loading para evitar importação circular
  @OneToMany("Workspace", "owner")
  workspaces?: Array<any>;

  @OneToMany("RefreshToken", "user")
  refreshTokens?: Array<any>;

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password && !this.password.startsWith('$2')) { // Check if not already hashed
      const saltRounds = (config as any).security?.bcryptSaltRounds || 10;
      this.password = await bcrypt.hash(this.password, saltRounds);
    }
  }

  async comparePassword(candidatePassword: string): Promise<boolean> {
    if (!this.password) return false;
    return bcrypt.compare(candidatePassword, this.password);
  }

  // Get user's full name
  get fullName(): string {
    return [this.firstName, this.lastName].filter(Boolean).join(' ') || this.email;
  }

  // Check if user email is verified
  get isVerified(): boolean {
    return this.emailVerified && !!this.confirmedAt;
  }

  // Check if user is banned
  get isBanned(): boolean {
    return this.bannedUntil ? new Date() < this.bannedUntil : false;
  }

  // Get safe user data (without sensitive fields)
  toSafeObject() {
    const { password, emailVerifyToken, passwordResetToken, ...safeUser } = this;
    return safeUser;
  }

  // Get JWT payload
  getJwtPayload() {
    return {
      sub: this.id,
      user_id: this.id,
      email: this.email,
      role: this.role,
      admin_role: this.adminRole,
      workspaceId: this.workspaceId,
      workspace_id: this.workspaceId, // compatibilidade
      email_verified: this.emailVerified,
      app_metadata: this.rawAppMetaData || {},
      user_metadata: this.rawUserMetaData || {},
      permissions: this.getPermissions(),
      can_access_admin: this.canAccessAdmin,
      is_platform_admin: this.isPlatformAdmin,
      is_workspace_admin: this.isWorkspaceAdmin,
      aud: 'authenticated',
      exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour
      iat: Math.floor(Date.now() / 1000),
    };
  }

  // Get user permissions based on role and admin role
  getPermissions(): string[] {
    const permissions: string[] = [];

    // Admin role permissions (higher level)
    switch (this.adminRole) {
      case 'platform_admin':
        permissions.push(
          'platform:manage:all',
          'platform:read:all',
          'platform:write:all',
          'platform:delete:all',
          'manage:all_workspaces',
          'manage:all_users',
          'manage:platform_settings',
          'manage:billing',
          'manage:api_keys',
          'view:analytics:global'
        );
        break;
      
      case 'workspace_owner':
        permissions.push(
          'workspace:manage:all',
          'workspace:read:all',
          'workspace:write:all',
          'workspace:delete:all',
          'manage:workspace_settings',
          'manage:workspace_users',
          'manage:workspace_billing',
          'manage:workspace_api_keys',
          'view:analytics:workspace'
        );
        break;
      
      case 'workspace_admin':
        permissions.push(
          'workspace:manage:content',
          'workspace:read:all',
          'workspace:write:all',
          'manage:workspace_users',
          'manage:database',
          'manage:storage',
          'manage:functions',
          'view:analytics:workspace'
        );
        break;
      
      case 'service_role':
        permissions.push(
          'api:full_access',
          'bypass:rls',
          'service:read:all',
          'service:write:all'
        );
        break;
    }

    // Regular user role permissions (application level)
    switch (this.role) {
      case 'admin':
        permissions.push(
          'read:all',
          'write:all', 
          'delete:all',
          'manage:workspace',
          'manage:users',
          'manage:database',
          'manage:storage',
          'manage:functions',
          'manage:auth'
        );
        break;
      
      case 'editor':
        permissions.push(
          'read:all',
          'write:own',
          'write:shared',
          'delete:own',
          'manage:own_profile'
        );
        break;
      
      case 'viewer':
        permissions.push(
          'read:own',
          'read:shared',
          'manage:own_profile'
        );
        break;
      
      case 'authenticated':
      default:
        permissions.push(
          'read:own',
          'write:own',
          'delete:own',
          'manage:own_profile'
        );
        break;
    }

    return [...new Set(permissions)]; // Remove duplicates
  }

  // Check if user is platform admin
  get isPlatformAdmin(): boolean {
    return this.adminRole === 'platform_admin';
  }

  // Check if user is workspace owner or admin
  get isWorkspaceAdmin(): boolean {
    return ['workspace_owner', 'workspace_admin', 'platform_admin'].includes(this.adminRole || '');
  }

  // Check if user can access admin interface
  get canAccessAdmin(): boolean {
    return this.isWorkspaceAdmin || this.isPlatformAdmin;
  }
}
