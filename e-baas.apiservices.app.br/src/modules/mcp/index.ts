import { Router } from 'express';
import { <PERSON>c<PERSON><PERSON>ontroller } from './mcp.controller';

const router = Router();
const mcpController = new McpController();

/**
 * @swagger
 * components:
 *   schemas:
 *     McpCommand:
 *       type: object
 *       required:
 *         - command
 *       properties:
 *         command:
 *           type: string
 *           description: Nome do comando a ser executado
 *           example: "query"
 *         args:
 *           type: array
 *           description: Argumentos do comando
 *           items:
 *             type: any
 *           example: ["SELECT * FROM users", []]
 *         context:
 *           type: object
 *           properties:
 *             workspaceId:
 *               type: string
 *               example: "workspace-123"
 *             userId:
 *               type: string
 *               example: "user-456"
 *     SystemInfo:
 *       type: object
 *       properties:
 *         version:
 *           type: string
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         features:
 *           type: array
 *           items:
 *             type: string
 *         endpoints:
 *           type: object
 *         environment:
 *           type: string
 *         uptime:
 *           type: number
 *         memory:
 *           type: object
 *         timestamp:
 *           type: string
 *     HealthStatus:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [healthy, degraded, unhealthy]
 *         checks:
 *           type: object
 *           properties:
 *             database:
 *               type: boolean
 *             queues:
 *               type: boolean
 *             storage:
 *               type: boolean
 *             auth:
 *               type: boolean
 *         timestamp:
 *           type: string
 *         uptime:
 *           type: number
 */

/**
 * @swagger
 * /api/mcp/execute:
 *   post:
 *     summary: Executar comando MCP
 *     description: Executa um comando MCP para interação com o backend
 *     tags: [MCP]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/McpCommand'
 *     responses:
 *       200:
 *         description: Comando executado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 result:
 *                   type: any
 *                 timestamp:
 *                   type: string
 *       400:
 *         description: Dados inválidos
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/execute', mcpController.executeCommand.bind(mcpController));

/**
 * @swagger
 * /api/mcp/commands:
 *   get:
 *     summary: Listar comandos disponíveis
 *     description: Retorna lista de todos os comandos MCP disponíveis
 *     tags: [MCP]
 *     responses:
 *       200:
 *         description: Lista de comandos
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 commands:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       parameters:
 *                         type: array
 *                         items:
 *                           type: string
 *                       examples:
 *                         type: array
 *                         items:
 *                           type: string
 */
router.get('/commands', mcpController.getAvailableCommands.bind(mcpController));

/**
 * @swagger
 * /api/mcp/system/info:
 *   get:
 *     summary: Obter informações do sistema
 *     description: Retorna informações detalhadas sobre o sistema E-BaaS
 *     tags: [MCP]
 *     responses:
 *       200:
 *         description: Informações do sistema
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 info:
 *                   $ref: '#/components/schemas/SystemInfo'
 */
router.get('/system/info', mcpController.getSystemInfo.bind(mcpController));

/**
 * @swagger
 * /api/mcp/system/health:
 *   get:
 *     summary: Verificar saúde do sistema
 *     description: Verifica o status de saúde de todos os componentes do sistema
 *     tags: [MCP]
 *     responses:
 *       200:
 *         description: Status de saúde do sistema
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 health:
 *                   $ref: '#/components/schemas/HealthStatus'
 *                 timestamp:
 *                   type: string
 */
router.get('/system/health', mcpController.healthCheck.bind(mcpController));

/**
 * @swagger
 * /api/mcp/workspace/{workspaceId}/schema:
 *   get:
 *     summary: Obter schema do banco de dados
 *     description: Retorna o schema completo do banco de dados do workspace
 *     tags: [MCP]
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do workspace
 *     responses:
 *       200:
 *         description: Schema do banco de dados
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 schema:
 *                   type: object
 *                 workspaceId:
 *                   type: string
 */
router.get('/workspace/:workspaceId/schema', mcpController.getDatabaseSchema.bind(mcpController));

/**
 * @swagger
 * /api/mcp/workspace/{workspaceId}/query:
 *   post:
 *     summary: Executar query SQL
 *     description: Executa uma query SQL no banco de dados do workspace
 *     tags: [MCP]
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do workspace
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *             properties:
 *               query:
 *                 type: string
 *                 description: Query SQL a ser executada
 *                 example: "SELECT * FROM users LIMIT 10"
 *               params:
 *                 type: array
 *                 description: Parâmetros da query
 *                 items:
 *                   type: any
 *                 example: []
 *     responses:
 *       200:
 *         description: Resultado da query
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 result:
 *                   type: any
 *                 workspaceId:
 *                   type: string
 *                 query:
 *                   type: string
 */
router.post('/workspace/:workspaceId/query', mcpController.executeQuery.bind(mcpController));

/**
 * @swagger
 * /api/mcp/workspace/{workspaceId}/table:
 *   post:
 *     summary: Gerenciar tabelas
 *     description: Executar operações de gerenciamento de tabelas (criar, deletar, etc.)
 *     tags: [MCP]
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do workspace
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *               - tableName
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [create, drop, describe, list]
 *                 description: Ação a ser executada
 *               tableName:
 *                 type: string
 *                 description: Nome da tabela
 *               schema:
 *                 type: object
 *                 description: Schema da tabela (necessário para criar)
 *                 example: { "name": "string", "email": "string", "age": "number" }
 *     responses:
 *       200:
 *         description: Operação executada com sucesso
 */
router.post('/workspace/:workspaceId/table', mcpController.manageTable.bind(mcpController));

/**
 * @swagger
 * /api/mcp/workspace/{workspaceId}/storage:
 *   post:
 *     summary: Gerenciar storage
 *     description: Executar operações de gerenciamento de arquivos
 *     tags: [MCP]
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do workspace
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [upload, download, delete, list]
 *                 description: Ação a ser executada
 *               bucketName:
 *                 type: string
 *                 description: Nome do bucket
 *               fileName:
 *                 type: string
 *                 description: Nome do arquivo
 *               data:
 *                 type: string
 *                 description: Dados do arquivo (base64)
 *     responses:
 *       200:
 *         description: Operação executada com sucesso
 */
router.post('/workspace/:workspaceId/storage', mcpController.manageStorage.bind(mcpController));

/**
 * @swagger
 * /api/mcp/workspace/{workspaceId}/auth:
 *   post:
 *     summary: Gerenciar autenticação
 *     description: Executar operações de gerenciamento de usuários e autenticação
 *     tags: [MCP]
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do workspace
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [create_user, delete_user, get_user, list_users, update_permissions]
 *                 description: Ação a ser executada
 *               userId:
 *                 type: string
 *                 description: ID do usuário
 *               userData:
 *                 type: object
 *                 description: Dados do usuário
 *                 properties:
 *                   email:
 *                     type: string
 *                   password:
 *                     type: string
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Lista de permissões
 *     responses:
 *       200:
 *         description: Operação executada com sucesso
 */
router.post('/workspace/:workspaceId/auth', mcpController.manageAuth.bind(mcpController));

/**
 * @swagger
 * /api/mcp/workspace/{workspaceId}/event:
 *   post:
 *     summary: Publicar evento
 *     description: Publicar evento na fila do workspace
 *     tags: [MCP]
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do workspace
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - eventType
 *               - data
 *             properties:
 *               eventType:
 *                 type: string
 *                 description: Tipo do evento
 *                 example: "notification"
 *               data:
 *                 type: object
 *                 description: Dados do evento
 *                 example: { "message": "Hello from MCP!" }
 *               options:
 *                 type: object
 *                 description: Opções de publicação
 *     responses:
 *       200:
 *         description: Evento publicado com sucesso
 */
router.post('/workspace/:workspaceId/event', mcpController.publishEvent.bind(mcpController));

export default router;