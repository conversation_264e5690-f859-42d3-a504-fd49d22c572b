import { IsString, IsOptional, IsObject, IsArray, IsEnum } from 'class-validator';

export class ExecuteCommandDto {
  @IsString()
  command!: string;

  @IsOptional()
  @IsArray()
  args?: any[];

  @IsOptional()
  @IsObject()
  context?: McpContext;
}

export class QueryDto {
  @IsString()
  query!: string;

  @IsOptional()
  @IsArray()
  params?: any[];
}

export class TableManagementDto {
  @IsEnum(['create', 'drop', 'describe', 'list'])
  action!: TableOperation;

  @IsString()
  tableName!: string;

  @IsOptional()
  @IsObject()
  schema?: any;
}

export class StorageManagementDto {
  @IsEnum(['upload', 'download', 'delete', 'list'])
  action!: StorageOperation;

  @IsOptional()
  @IsString()
  bucketName?: string;

  @IsOptional()
  @IsString()
  fileName?: string;

  @IsOptional()
  data?: any;
}

export class AuthManagementDto {
  @IsEnum(['create_user', 'delete_user', 'get_user', 'list_users', 'update_permissions'])
  action!: AuthOperation;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsObject()
  userData?: any;

  @IsOptional()
  @IsArray()
  permissions?: string[];
}

export class PublishEventDto {
  @IsString()
  eventType!: string;

  @IsObject()
  data!: any;

  @IsOptional()
  @IsObject()
  options?: any;
}

// Interfaces
export interface McpContext {
  workspaceId?: string;
  userId?: string;
  permissions?: string[];
  metadata?: any;
}

export interface McpCommand {
  name: string;
  description: string;
  parameters: string[];
  examples: string[];
}

export interface SystemInfo {
  version: string;
  name: string;
  description: string;
  features: string[];
  endpoints: {
    database: string;
    auth: string;
    storage: string;
    realtime: string;
    queues: string;
    mcp: string;
  };
  environment: string;
  uptime: number;
  memory: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
  };
  timestamp: string;
}

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: {
    database: boolean;
    queues: boolean;
    storage: boolean;
    auth: boolean;
  };
  timestamp: string;
  uptime: number;
  error?: string;
}

// Type definitions
export type TableOperation = 'create' | 'drop' | 'describe' | 'list';
export type StorageOperation = 'upload' | 'download' | 'delete' | 'list';
export type AuthOperation = 'create_user' | 'delete_user' | 'get_user' | 'list_users' | 'update_permissions';

// MCP Protocol Types
export interface McpRequest {
  jsonrpc: '2.0';
  id: string | number;
  method: string;
  params?: any;
}

export interface McpResponse {
  jsonrpc: '2.0';
  id: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

export interface McpNotification {
  jsonrpc: '2.0';
  method: string;
  params?: any;
}

// Command execution result
export interface CommandResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime?: number;
  timestamp: string;
}

// MCP Tool definitions
export interface McpTool {
  name: string;
  description: string;
  inputSchema: {
    type: 'object';
    properties: any;
    required?: string[];
  };
}

export const MCP_TOOLS: McpTool[] = [
  {
    name: 'execute_sql',
    description: 'Execute SQL query on workspace database',
    inputSchema: {
      type: 'object',
      properties: {
        workspaceId: { type: 'string', description: 'Workspace ID' },
        query: { type: 'string', description: 'SQL query to execute' },
        params: { type: 'array', description: 'Query parameters' }
      },
      required: ['workspaceId', 'query']
    }
  },
  {
    name: 'get_schema',
    description: 'Get database schema for workspace',
    inputSchema: {
      type: 'object',
      properties: {
        workspaceId: { type: 'string', description: 'Workspace ID' }
      },
      required: ['workspaceId']
    }
  },
  {
    name: 'create_table',
    description: 'Create new table in workspace database',
    inputSchema: {
      type: 'object',
      properties: {
        workspaceId: { type: 'string', description: 'Workspace ID' },
        tableName: { type: 'string', description: 'Name of the table' },
        schema: { type: 'object', description: 'Table schema definition' }
      },
      required: ['workspaceId', 'tableName', 'schema']
    }
  },
  {
    name: 'upload_file',
    description: 'Upload file to workspace storage',
    inputSchema: {
      type: 'object',
      properties: {
        workspaceId: { type: 'string', description: 'Workspace ID' },
        bucketName: { type: 'string', description: 'Storage bucket name' },
        fileName: { type: 'string', description: 'File name' },
        data: { type: 'string', description: 'File data (base64 encoded)' }
      },
      required: ['workspaceId', 'bucketName', 'fileName', 'data']
    }
  },
  {
    name: 'create_user',
    description: 'Create new user in workspace',
    inputSchema: {
      type: 'object',
      properties: {
        workspaceId: { type: 'string', description: 'Workspace ID' },
        email: { type: 'string', description: 'User email' },
        password: { type: 'string', description: 'User password' },
        metadata: { type: 'object', description: 'Additional user metadata' }
      },
      required: ['workspaceId', 'email', 'password']
    }
  },
  {
    name: 'publish_event',
    description: 'Publish event to workspace queue',
    inputSchema: {
      type: 'object',
      properties: {
        workspaceId: { type: 'string', description: 'Workspace ID' },
        eventType: { type: 'string', description: 'Type of event' },
        data: { type: 'object', description: 'Event data' },
        options: { type: 'object', description: 'Publishing options' }
      },
      required: ['workspaceId', 'eventType', 'data']
    }
  }
];