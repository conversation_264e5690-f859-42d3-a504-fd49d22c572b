import { McpService } from './mcp.service';
import { 
  Mcp<PERSON>ommand, 
  McpContext, 
  SystemInfo, 
  HealthStatus,
  StorageOperation,
  AuthOperation,
  TableOperation 
} from './dto/mcp.dto';

export class McpUseCases {
  private mcpService: McpService;

  constructor() {
    this.mcpService = new McpService();
  }

  async executeCommand(command: string, args?: any[], context?: McpContext): Promise<any> {
    try {
      // Validar comando
      const validCommands = await this.getAvailableCommands();
      const commandInfo = validCommands.find(cmd => cmd.name === command);
      
      if (!commandInfo) {
        throw new Error(`Command '${command}' not found`);
      }

      // Log da execução
      console.log(`🎯 Executing MCP command: ${command}`, { args, context });

      // Executar comando baseado no tipo
      switch (command) {
        case 'query':
          return await this.executeQuery(
            context?.workspaceId || 'default', 
            args?.[0], 
            args?.[1]
          );

        case 'schema':
          return await this.getDatabaseSchema(context?.workspaceId || 'default');

        case 'create_table':
          return await this.manageTable(
            context?.workspaceId || 'default',
            'create',
            args?.[0],
            args?.[1]
          );

        case 'storage_upload':
          return await this.manageStorage(
            context?.workspaceId || 'default',
            'upload',
            {
              bucketName: args?.[0],
              fileName: args?.[1],
              data: args?.[2]
            }
          );

        case 'auth_create_user':
          return await this.manageAuth(
            context?.workspaceId || 'default',
            'create_user',
            {
              userData: args?.[0]
            }
          );

        case 'publish_event':
          return await this.publishEvent(
            context?.workspaceId || 'default',
            args?.[0],
            args?.[1],
            args?.[2]
          );

        case 'system_info':
          return await this.getSystemInfo();

        case 'health':
          return await this.healthCheck();

        default:
          return await this.mcpService.executeCustomCommand(command, args, context);
      }
    } catch (error) {
      console.error(`❌ Failed to execute MCP command '${command}':`, error);
      throw error;
    }
  }

  async getDatabaseSchema(workspaceId: string): Promise<any> {
    try {
      return await this.mcpService.getDatabaseSchema(workspaceId);
    } catch (error) {
      console.error(`❌ Failed to get database schema for workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  async executeQuery(workspaceId: string, query: string, params?: any[]): Promise<any> {
    try {
      // Validar query
      if (!query || typeof query !== 'string') {
        throw new Error('Invalid query provided');
      }

      // Verificar se é uma query permitida (prevenir operações perigosas)
      const dangerousOperations = ['DROP', 'TRUNCATE', 'DELETE FROM', 'ALTER TABLE'];
      const upperQuery = query.toUpperCase();
      
      for (const operation of dangerousOperations) {
        if (upperQuery.includes(operation)) {
          console.warn(`⚠️ Potentially dangerous operation detected: ${operation}`);
          // Em produção, você pode querer bloquear isso ou exigir confirmação
        }
      }

      return await this.mcpService.executeQuery(workspaceId, query, params);
    } catch (error) {
      console.error(`❌ Failed to execute query in workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  async manageTable(
    workspaceId: string, 
    action: TableOperation, 
    tableName: string, 
    schema?: any
  ): Promise<any> {
    try {
      return await this.mcpService.manageTable(workspaceId, action, tableName, schema);
    } catch (error) {
      console.error(`❌ Failed to manage table ${tableName} in workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  async manageStorage(
    workspaceId: string, 
    action: StorageOperation, 
    options: {
      bucketName?: string;
      fileName?: string;
      data?: any;
    }
  ): Promise<any> {
    try {
      return await this.mcpService.manageStorage(workspaceId, action, options);
    } catch (error) {
      console.error(`❌ Failed to manage storage in workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  async manageAuth(
    workspaceId: string, 
    action: AuthOperation, 
    options: {
      userId?: string;
      userData?: any;
      permissions?: string[];
    }
  ): Promise<any> {
    try {
      return await this.mcpService.manageAuth(workspaceId, action, options);
    } catch (error) {
      console.error(`❌ Failed to manage auth in workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  async publishEvent(
    workspaceId: string, 
    eventType: string, 
    data: any, 
    options?: any
  ): Promise<string> {
    try {
      return await this.mcpService.publishEvent(workspaceId, eventType, data, options);
    } catch (error) {
      console.error(`❌ Failed to publish event in workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  async getSystemInfo(): Promise<SystemInfo> {
    try {
      return await this.mcpService.getSystemInfo();
    } catch (error) {
      console.error('❌ Failed to get system info:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<HealthStatus> {
    try {
      return await this.mcpService.healthCheck();
    } catch (error) {
      console.error('❌ Health check failed:', error);
      throw error;
    }
  }

  async getAvailableCommands(): Promise<McpCommand[]> {
    return [
      {
        name: 'query',
        description: 'Execute SQL query',
        parameters: ['query', 'params?'],
        examples: ['SELECT * FROM users', 'SELECT * FROM users WHERE id = ?']
      },
      {
        name: 'schema',
        description: 'Get database schema',
        parameters: [],
        examples: ['schema']
      },
      {
        name: 'create_table',
        description: 'Create new table',
        parameters: ['tableName', 'schema'],
        examples: ['create_table users {name: string, email: string}']
      },
      {
        name: 'storage_upload',
        description: 'Upload file to storage',
        parameters: ['bucketName', 'fileName', 'data'],
        examples: ['storage_upload images avatar.jpg <data>']
      },
      {
        name: 'storage_download',
        description: 'Download file from storage',
        parameters: ['bucketName', 'fileName'],
        examples: ['storage_download images avatar.jpg']
      },
      {
        name: 'auth_create_user',
        description: 'Create new user',
        parameters: ['userData'],
        examples: ['auth_create_user {email: "<EMAIL>", password: "123456"}']
      },
      {
        name: 'auth_delete_user',
        description: 'Delete user',
        parameters: ['userId'],
        examples: ['auth_delete_user user-123']
      },
      {
        name: 'publish_event',
        description: 'Publish event to queue',
        parameters: ['eventType', 'data', 'options?'],
        examples: ['publish_event notification {message: "Hello"}']
      },
      {
        name: 'system_info',
        description: 'Get system information',
        parameters: [],
        examples: ['system_info']
      },
      {
        name: 'health',
        description: 'Get health status',
        parameters: [],
        examples: ['health']
      }
    ];
  }
}