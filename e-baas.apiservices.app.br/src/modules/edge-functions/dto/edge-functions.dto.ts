import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsObject,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  Min,
  Max,
  ValidateNested,
  IsDateString
} from "class-validator";
import { Type } from "class-transformer";

export enum FunctionRuntime {
  DENO = 'deno',
  DENO_DEPLOY = 'deno-deploy'
}

export enum FunctionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEPLOYING = 'deploying',
  ERROR = 'error'
}

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS'
}

export enum FunctionTrigger {
  HTTP = 'http',
  CRON = 'cron',
  DATABASE = 'database',
  EVENT = 'event'
}

export class FunctionConfigDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(60000)
  timeoutMs?: number = 10000;

  @IsOptional()
  @IsNumber()
  @Min(64)
  @Max(512)
  memoryMB?: number = 128;

  @IsOptional()
  @IsArray()
  @IsEnum(HttpMethod, { each: true })
  allowedMethods?: HttpMethod[] = [HttpMethod.GET, HttpMethod.POST];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedOrigins?: string[] = ['*'];

  @IsOptional()
  @IsBoolean()
  enableCors?: boolean = true;

  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;
}

export class CreateFunctionDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  workspaceId: string;

  @IsString()
  @IsNotEmpty()
  slug: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  @IsNotEmpty()
  sourceCode: string;

  @IsOptional()
  @IsEnum(FunctionRuntime)
  runtime?: FunctionRuntime = FunctionRuntime.DENO;

  @IsOptional()
  @IsEnum(FunctionTrigger)
  trigger?: FunctionTrigger = FunctionTrigger.HTTP;

  @IsOptional()
  @ValidateNested()
  @Type(() => FunctionConfigDto)
  config?: FunctionConfigDto;

  @IsOptional()
  @IsObject()
  environmentVariables?: Record<string, string>;

  @IsOptional()
  @IsObject()
  secrets?: Record<string, string>;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean = false;

  @IsOptional()
  @IsString()
  cronSchedule?: string;

  @IsOptional()
  @IsObject()
  databaseTrigger?: {
    table: string;
    schema?: string;
    events: string[];
  };

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  eventTypes?: string[];
}

export class UpdateFunctionDto {
  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  sourceCode?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => FunctionConfigDto)
  config?: FunctionConfigDto;

  @IsOptional()
  @IsObject()
  environmentVariables?: Record<string, string>;

  @IsOptional()
  @IsObject()
  secrets?: Record<string, string>;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @IsOptional()
  @IsEnum(FunctionStatus)
  status?: FunctionStatus;

  @IsOptional()
  @IsString()
  cronSchedule?: string;

  @IsOptional()
  @IsObject()
  databaseTrigger?: {
    table: string;
    schema?: string;
    events: string[];
  };

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  eventTypes?: string[];
}

export class ExecuteFunctionDto {
  @IsString()
  @IsNotEmpty()
  functionSlug: string;

  @IsString()
  @IsNotEmpty()
  workspaceId: string;

  @IsOptional()
  @IsEnum(HttpMethod)
  method?: HttpMethod = HttpMethod.POST;

  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @IsOptional()
  body?: any;

  @IsOptional()
  @IsObject()
  queryParams?: Record<string, string>;

  @IsOptional()
  @IsString()
  path?: string;
}

export class FunctionInvocationDto {
  @IsString()
  @IsNotEmpty()
  functionId: string;

  @IsOptional()
  @IsObject()
  payload?: any;

  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

export class DeployFunctionDto {
  @IsString()
  @IsNotEmpty()
  functionSlug: string;

  @IsString()
  @IsNotEmpty()
  workspaceId: string;

  @IsOptional()
  @IsString()
  version?: string;

  @IsOptional()
  @IsBoolean()
  force?: boolean = false;
}

export class ListFunctionsDto {
  @IsString()
  @IsNotEmpty()
  workspaceId: string;

  @IsOptional()
  @IsEnum(FunctionStatus)
  status?: FunctionStatus;

  @IsOptional()
  @IsEnum(FunctionRuntime)
  runtime?: FunctionRuntime;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number = 0;
}

export class FunctionLogsDto {
  @IsString()
  @IsNotEmpty()
  functionSlug: string;

  @IsString()
  @IsNotEmpty()
  workspaceId: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  limit?: number = 100;

  @IsOptional()
  @IsString()
  since?: string; // ISO date string

  @IsOptional()
  @IsString()
  until?: string; // ISO date string

  @IsOptional()
  @IsString()
  level?: 'info' | 'warn' | 'error' | 'debug';
}

export interface FunctionExecutionResult {
  statusCode: number;
  headers: Record<string, string>;
  body: any;
  executionTime: number;
  memoryUsed: number;
  logs: FunctionLog[];
}

export interface FunctionLog {
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  metadata?: Record<string, any>;
}

export interface FunctionMetrics {
  invocations: number;
  errors: number;
  averageExecutionTime: number;
  averageMemoryUsage: number;
  totalExecutionTime: number;
  period: {
    start: Date;
    end: Date;
  };
}

export interface RuntimeContext {
  functionId: string;
  workspaceId: string;
  requestId: string;
  timestamp: Date;
  environment: Record<string, string>;
  secrets: Record<string, string>;
  config: FunctionConfigDto;
}

export interface DeploymentInfo {
  version: string;
  deployedAt: Date;
  status: 'success' | 'failed' | 'pending';
  buildLogs: string[];
  size: number;
}