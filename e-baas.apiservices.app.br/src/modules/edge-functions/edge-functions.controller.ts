import { Request, Response, Router } from "express";
import { EdgeFunctionsService } from "./edge-functions.service";
import {
  CreateFunctionDto,
  UpdateFunctionDto,
  ExecuteFunctionDto,
  DeployFunctionDto,
  ListFunctionsDto,
  FunctionLogsDto
} from "./dto/edge-functions.dto";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";
import { requireReadAccess, requireWriteAccess, requireDeleteAccess } from "../../infra/middlewares/apiKeyAuth.middleware";

const edgeFunctionsRouter = Router();
const edgeFunctionsService = new EdgeFunctionsService();

// Helper function for validation
const validateDto = async (DtoClass: any, data: any) => {
  const dto = plainToClass(DtoClass, data);
  const errors = await validate(dto);
  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
  }
  return dto;
};

// Function Management Routes

/**
 * @swagger
 * /functions/v1:
 *   post:
 *     summary: Create a new edge function
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - workspaceId
 *               - sourceCode
 *             properties:
 *               name:
 *                 type: string
 *                 description: Function name
 *               workspaceId:
 *                 type: string
 *                 description: Workspace ID
 *               slug:
 *                 type: string
 *                 description: Function slug (auto-generated if not provided)
 *               description:
 *                 type: string
 *                 description: Function description
 *               sourceCode:
 *                 type: string
 *                 description: TypeScript/JavaScript source code
 *               runtime:
 *                 type: string
 *                 enum: [deno, deno-deploy]
 *                 description: Runtime environment
 *               trigger:
 *                 type: string
 *                 enum: [http, cron, database, event]
 *                 description: Function trigger type
 *               config:
 *                 type: object
 *                 description: Function configuration
 *               environmentVariables:
 *                 type: object
 *                 description: Environment variables
 *               secrets:
 *                 type: object
 *                 description: Secret variables
 *               isPublic:
 *                 type: boolean
 *                 description: Whether function is publicly accessible
 *     responses:
 *       201:
 *         description: Function created successfully
 *       400:
 *         description: Invalid request data
 *       409:
 *         description: Function already exists
 */
edgeFunctionsRouter.post("/", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const createFunctionDto = req.body as CreateFunctionDto;
    const createdBy = (req as any).user?.id;
    
    const functionEntity = await edgeFunctionsService.createFunction(createFunctionDto, createdBy);
    
    return res.status(201).json({
      success: true,
      function: functionEntity.toSafeObject()
    });
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /functions/v1:
 *   get:
 *     summary: List edge functions
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, deploying, error]
 *         description: Filter by status
 *       - in: query
 *         name: runtime
 *         schema:
 *           type: string
 *           enum: [deno, deno-deploy]
 *         description: Filter by runtime
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in name and description
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of functions to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of functions to skip
 *     responses:
 *       200:
 *         description: List of functions
 */
edgeFunctionsRouter.get("/", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const listFunctionsDto = req.query as ListFunctionsDto;
    
    const { functions, total } = await edgeFunctionsService.listFunctions(listFunctionsDto);
    
    return res.status(200).json({
      success: true,
      functions: functions.map(f => f.toSafeObject()),
      total,
      limit: listFunctionsDto.limit,
      offset: listFunctionsDto.offset
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /functions/v1/{functionSlug}:
 *   get:
 *     summary: Get function details
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *     responses:
 *       200:
 *         description: Function details
 *       404:
 *         description: Function not found
 */
edgeFunctionsRouter.get("/:functionSlug", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const { workspaceId } = req.query as any;
    
    const functionEntity = await edgeFunctionsService.getFunction(functionSlug, workspaceId);
    
    if (!functionEntity) {
      return res.status(404).json({ error: 'Function not found' });
    }
    
    return res.status(200).json({
      success: true,
      function: functionEntity.toSafeObject()
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /functions/v1/{functionSlug}:
 *   put:
 *     summary: Update function
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               description:
 *                 type: string
 *               sourceCode:
 *                 type: string
 *               config:
 *                 type: object
 *               environmentVariables:
 *                 type: object
 *               secrets:
 *                 type: object
 *               isPublic:
 *                 type: boolean
 *               status:
 *                 type: string
 *                 enum: [active, inactive, deploying, error]
 *     responses:
 *       200:
 *         description: Function updated successfully
 *       404:
 *         description: Function not found
 */
edgeFunctionsRouter.put("/:functionSlug", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const { workspaceId } = req.query as any;
    
    const updateFunctionDto = req.body as UpdateFunctionDto;
    const updatedBy = (req as any).user?.id;
    
    const functionEntity = await edgeFunctionsService.updateFunction(functionSlug, workspaceId, updateFunctionDto, updatedBy);
    
    return res.status(200).json({
      success: true,
      function: functionEntity.toSafeObject()
    });
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /functions/v1/{functionSlug}:
 *   delete:
 *     summary: Delete function
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *     responses:
 *       200:
 *         description: Function deleted successfully
 *       404:
 *         description: Function not found
 */
edgeFunctionsRouter.delete("/:functionSlug", requireDeleteAccess, async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const { workspaceId } = req.query as any;
    
    const result = await edgeFunctionsService.deleteFunction(functionSlug, workspaceId);
    
    return res.status(200).json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Deployment Routes

/**
 * @swagger
 * /functions/v1/{functionSlug}/deploy:
 *   post:
 *     summary: Deploy function
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - workspaceId
 *             properties:
 *               workspaceId:
 *                 type: string
 *               version:
 *                 type: string
 *               force:
 *                 type: boolean
 *                 default: false
 *     responses:
 *       200:
 *         description: Function deployed successfully
 *       404:
 *         description: Function not found
 */
edgeFunctionsRouter.post("/:functionSlug/deploy", requireWriteAccess, async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const deployFunctionDto = {
      functionSlug,
      ...req.body
    } as DeployFunctionDto;
    
    const deploymentInfo = await edgeFunctionsService.deployFunction(deployFunctionDto);
    
    return res.status(200).json({
      success: true,
      deployment: deploymentInfo
    });
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Execution Routes

/**
 * @swagger
 * /functions/v1/invoke/{functionSlug}:
 *   post:
 *     summary: Execute function (authenticated)
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               headers:
 *                 type: object
 *               body:
 *                 type: object
 *               queryParams:
 *                 type: object
 *               path:
 *                 type: string
 *     responses:
 *       200:
 *         description: Function executed successfully
 *       404:
 *         description: Function not found
 */
edgeFunctionsRouter.post("/invoke/:functionSlug", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const { workspaceId } = req.query as any;
    
    const executeFunctionDto = {
      functionSlug,
      workspaceId,
      ...req.body
    } as ExecuteFunctionDto;
    
    const result = await edgeFunctionsService.executeFunction(executeFunctionDto);
    
    // Set response headers
    Object.entries(result.headers).forEach(([key, value]) => {
      res.setHeader(key, value);
    });
    
    return res.status(result.statusCode).json({
      success: true,
      result: result.body,
      execution: {
        executionTime: result.executionTime,
        memoryUsed: result.memoryUsed
      },
      logs: result.logs
    });
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    if (error.message.includes('not executable')) {
      return res.status(422).json({ error: error.message });
    }
    return res.status(500).json({ error: error.message });
  }
});

/**
 * @swagger
 * /functions/v1/public/{functionSlug}:
 *   get:
 *     summary: Execute public function (GET)
 *     tags: [Edge Functions]
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *     responses:
 *       200:
 *         description: Function executed successfully
 */
edgeFunctionsRouter.get("/public/:functionSlug", async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const { workspaceId, ...queryParams } = req.query as any;
    
    const executeFunctionDto = {
      functionSlug,
      workspaceId,
      method: 'GET',
      headers: req.headers,
      queryParams
    } as ExecuteFunctionDto;
    
    const result = await edgeFunctionsService.executeFunction(executeFunctionDto);
    
    // Set response headers
    Object.entries(result.headers).forEach(([key, value]) => {
      res.setHeader(key, value);
    });
    
    return res.status(result.statusCode).send(result.body);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(500).json({ error: error.message });
  }
});

/**
 * @swagger
 * /functions/v1/public/{functionSlug}:
 *   post:
 *     summary: Execute public function (POST)
 *     tags: [Edge Functions]
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Function executed successfully
 */
edgeFunctionsRouter.post("/public/:functionSlug", async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const { workspaceId, ...queryParams } = req.query as any;
    
    const executeFunctionDto = {
      functionSlug,
      workspaceId,
      method: 'POST',
      headers: req.headers,
      body: req.body,
      queryParams
    } as ExecuteFunctionDto;
    
    const result = await edgeFunctionsService.executeFunction(executeFunctionDto);
    
    // Set response headers
    Object.entries(result.headers).forEach(([key, value]) => {
      res.setHeader(key, value);
    });
    
    return res.status(result.statusCode).send(result.body);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(500).json({ error: error.message });
  }
});

// Monitoring Routes

/**
 * @swagger
 * /functions/v1/{functionSlug}/metrics:
 *   get:
 *     summary: Get function metrics
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *       - in: query
 *         name: start
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for metrics
 *       - in: query
 *         name: end
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for metrics
 *     responses:
 *       200:
 *         description: Function metrics
 */
edgeFunctionsRouter.get("/:functionSlug/metrics", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const { workspaceId, start, end } = req.query as any;
    
    const period = {
      start: start ? new Date(start) : new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
      end: end ? new Date(end) : new Date()
    };
    
    const metrics = await edgeFunctionsService.getFunctionMetrics(functionSlug, workspaceId, period);
    
    return res.status(200).json({
      success: true,
      metrics
    });
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

/**
 * @swagger
 * /functions/v1/{functionSlug}/logs:
 *   get:
 *     summary: Get function logs
 *     tags: [Edge Functions]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: functionSlug
 *         required: true
 *         schema:
 *           type: string
 *         description: Function slug
 *       - in: query
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workspace ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *           default: 100
 *         description: Number of logs to return
 *       - in: query
 *         name: since
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for logs
 *       - in: query
 *         name: until
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for logs
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [info, warn, error, debug]
 *         description: Log level filter
 *     responses:
 *       200:
 *         description: Function logs
 */
edgeFunctionsRouter.get("/:functionSlug/logs", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { functionSlug } = req.params;
    const functionLogsDto = {
      functionSlug,
      ...req.query
    } as FunctionLogsDto;
    
    const logs = await edgeFunctionsService.getFunctionLogs(functionLogsDto);
    
    return res.status(200).json({
      success: true,
      logs
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Health and Status Routes

/**
 * @swagger
 * /functions/v1/health:
 *   get:
 *     summary: Edge Functions health check
 *     tags: [Edge Functions]
 *     responses:
 *       200:
 *         description: Service health status
 */
edgeFunctionsRouter.get("/health", async (req: Request, res: Response) => {
  try {
    const health = await edgeFunctionsService.healthCheck();
    
    return res.status(200).json({
      service: 'edge-functions',
      status: health.status,
      timestamp: new Date().toISOString(),
      details: health
    });
  } catch (error: any) {
    return res.status(500).json({
      service: 'edge-functions',
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

export default edgeFunctionsRouter;