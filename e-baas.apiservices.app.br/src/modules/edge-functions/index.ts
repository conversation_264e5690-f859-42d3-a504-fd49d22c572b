// Edge Functions Module
export { default as edgeFunctionsRouter } from "./edge-functions.controller";
export { EdgeFunctionsService } from "./edge-functions.service";
export { EdgeFunctionsUseCases } from "./edge-functions.useCases";
export { EdgeFunction } from "./entity/EdgeFunction.entity";
export * from "./dto/edge-functions.dto";

// Module information
export const MODULE_INFO = {
  name: "edge-functions",
  version: "1.0.0",
  description: "Edge Functions with Deno Runtime - Serverless functions execution",
  endpoints: [
    "POST /functions/v1 - Create function",
    "GET /functions/v1 - List functions", 
    "GET /functions/v1/:slug - Get function",
    "PUT /functions/v1/:slug - Update function",
    "DELETE /functions/v1/:slug - Delete function",
    "POST /functions/v1/:slug/deploy - Deploy function",
    "POST /functions/v1/invoke/:slug - Execute function (authenticated)",
    "GET /functions/v1/public/:slug - Execute public function (GET)",
    "POST /functions/v1/public/:slug - Execute public function (POST)",
    "GET /functions/v1/:slug/metrics - Get function metrics",
    "GET /functions/v1/:slug/logs - Get function logs",
    "GET /functions/v1/health - Health check"
  ],
  features: [
    "Deno Runtime Integration",
    "TypeScript Support",
    "HTTP/CRON/Database/Event Triggers",
    "Public and Authenticated Execution",
    "Environment Variables and Secrets",
    "Real-time Metrics and Logging",
    "Function Deployment and Versioning",
    "CORS Support",
    "Timeout and Memory Management"
  ]
};