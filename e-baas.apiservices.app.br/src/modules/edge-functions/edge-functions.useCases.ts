import { EdgeFunctionsService } from "./edge-functions.service";
import {
  CreateFunctionDto,
  UpdateFunctionDto,
  ExecuteFunctionDto,
  DeployFunctionDto,
  ListFunctionsDto,
  FunctionLogsDto,
  FunctionExecutionResult,
  FunctionLog,
  FunctionMetrics,
  DeploymentInfo
} from "./dto/edge-functions.dto";
import { EdgeFunction } from "./entity/EdgeFunction.entity";

export class EdgeFunctionsUseCases {
  private edgeFunctionsService: EdgeFunctionsService;

  constructor() {
    this.edgeFunctionsService = new EdgeFunctionsService();
  }

  // Function Management Use Cases
  async createFunction(createFunctionDto: CreateFunctionDto, createdBy?: string): Promise<EdgeFunction> {
    try {
      // Validate function requirements
      this.validateFunctionCreation(createFunctionDto);

      // Create function
      const functionEntity = await this.edgeFunctionsService.createFunction(createFunctionDto, createdBy);

      // Log creation
      console.log(`✅ Edge function created: ${functionEntity.slug} by ${createdBy || 'system'}`);

      return functionEntity;
    } catch (error: any) {
      console.error(`❌ Failed to create edge function: ${error.message}`);
      throw error;
    }
  }

  async updateFunction(
    functionSlug: string,
    workspaceId: string,
    updateFunctionDto: UpdateFunctionDto,
    updatedBy?: string
  ): Promise<EdgeFunction> {
    try {
      // Validate update permissions
      await this.validateFunctionAccess(functionSlug, workspaceId);

      // Update function
      const functionEntity = await this.edgeFunctionsService.updateFunction(
        functionSlug,
        workspaceId,
        updateFunctionDto,
        updatedBy
      );

      // Log update
      console.log(`✅ Edge function updated: ${functionSlug} by ${updatedBy || 'system'}`);

      return functionEntity;
    } catch (error: any) {
      console.error(`❌ Failed to update edge function ${functionSlug}: ${error.message}`);
      throw error;
    }
  }

  async getFunction(functionSlug: string, workspaceId: string): Promise<EdgeFunction> {
    try {
      const functionEntity = await this.edgeFunctionsService.getFunction(functionSlug, workspaceId);
      
      if (!functionEntity) {
        throw new Error(`Function '${functionSlug}' not found in workspace '${workspaceId}'`);
      }

      return functionEntity;
    } catch (error: any) {
      console.error(`❌ Failed to get edge function ${functionSlug}: ${error.message}`);
      throw error;
    }
  }

  async listFunctions(listFunctionsDto: ListFunctionsDto): Promise<{ functions: EdgeFunction[]; total: number }> {
    try {
      return await this.edgeFunctionsService.listFunctions(listFunctionsDto);
    } catch (error: any) {
      console.error(`❌ Failed to list edge functions: ${error.message}`);
      throw error;
    }
  }

  async deleteFunction(functionSlug: string, workspaceId: string, deletedBy?: string): Promise<{ message: string }> {
    try {
      // Validate delete permissions
      await this.validateFunctionAccess(functionSlug, workspaceId);

      // Delete function
      const result = await this.edgeFunctionsService.deleteFunction(functionSlug, workspaceId);

      // Log deletion
      console.log(`✅ Edge function deleted: ${functionSlug} by ${deletedBy || 'system'}`);

      return result;
    } catch (error: any) {
      console.error(`❌ Failed to delete edge function ${functionSlug}: ${error.message}`);
      throw error;
    }
  }

  // Deployment Use Cases
  async deployFunction(deployFunctionDto: DeployFunctionDto, deployedBy?: string): Promise<DeploymentInfo> {
    try {
      // Validate deployment requirements
      await this.validateFunctionDeployment(deployFunctionDto.functionSlug, deployFunctionDto.workspaceId);

      // Deploy function
      const deploymentInfo = await this.edgeFunctionsService.deployFunction(deployFunctionDto);

      // Log deployment
      console.log(`✅ Edge function deployed: ${deployFunctionDto.functionSlug} v${deploymentInfo.version} by ${deployedBy || 'system'}`);

      return deploymentInfo;
    } catch (error: any) {
      console.error(`❌ Failed to deploy edge function ${deployFunctionDto.functionSlug}: ${error.message}`);
      throw error;
    }
  }

  // Execution Use Cases
  async executeFunction(executeFunctionDto: ExecuteFunctionDto, executedBy?: string): Promise<FunctionExecutionResult> {
    try {
      // Validate execution requirements
      await this.validateFunctionExecution(executeFunctionDto.functionSlug, executeFunctionDto.workspaceId);

      // Execute function
      const startTime = Date.now();
      const result = await this.edgeFunctionsService.executeFunction(executeFunctionDto);
      const totalTime = Date.now() - startTime;

      // Log execution
      console.log(`✅ Edge function executed: ${executeFunctionDto.functionSlug} (${totalTime}ms) by ${executedBy || 'anonymous'}`);

      return result;
    } catch (error: any) {
      console.error(`❌ Failed to execute edge function ${executeFunctionDto.functionSlug}: ${error.message}`);
      throw error;
    }
  }

  // Monitoring Use Cases
  async getFunctionMetrics(
    functionSlug: string,
    workspaceId: string,
    period: { start: Date; end: Date }
  ): Promise<FunctionMetrics> {
    try {
      return await this.edgeFunctionsService.getFunctionMetrics(functionSlug, workspaceId, period);
    } catch (error: any) {
      console.error(`❌ Failed to get metrics for edge function ${functionSlug}: ${error.message}`);
      throw error;
    }
  }

  async getFunctionLogs(functionLogsDto: FunctionLogsDto): Promise<FunctionLog[]> {
    try {
      return await this.edgeFunctionsService.getFunctionLogs(functionLogsDto);
    } catch (error: any) {
      console.error(`❌ Failed to get logs for edge function ${functionLogsDto.functionSlug}: ${error.message}`);
      throw error;
    }
  }

  // Health Check Use Case
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    denoAvailable: boolean;
    functionsCount: number;
    activeFunctions: number;
  }> {
    try {
      return await this.edgeFunctionsService.healthCheck();
    } catch (error: any) {
      console.error(`❌ Edge functions health check failed: ${error.message}`);
      throw error;
    }
  }

  // Validation Methods
  private validateFunctionCreation(createFunctionDto: CreateFunctionDto): void {
    // Validate function name
    if (!createFunctionDto.name || createFunctionDto.name.trim().length === 0) {
      throw new Error('Function name is required');
    }

    if (createFunctionDto.name.length < 3 || createFunctionDto.name.length > 100) {
      throw new Error('Function name must be between 3 and 100 characters');
    }

    // Validate source code
    if (!createFunctionDto.sourceCode || createFunctionDto.sourceCode.trim().length === 0) {
      throw new Error('Source code is required');
    }

    if (createFunctionDto.sourceCode.length > 1024 * 1024) { // 1MB limit
      throw new Error('Source code size exceeds 1MB limit');
    }

    // Validate workspace ID
    if (!createFunctionDto.workspaceId || createFunctionDto.workspaceId.trim().length === 0) {
      throw new Error('Workspace ID is required');
    }

    // Validate configuration
    if (createFunctionDto.config) {
      if (createFunctionDto.config.timeoutMs && (createFunctionDto.config.timeoutMs < 1000 || createFunctionDto.config.timeoutMs > 60000)) {
        throw new Error('Timeout must be between 1 and 60 seconds');
      }

      if (createFunctionDto.config.memoryMB && (createFunctionDto.config.memoryMB < 64 || createFunctionDto.config.memoryMB > 512)) {
        throw new Error('Memory must be between 64MB and 512MB');
      }
    }

    // Validate cron schedule if trigger is CRON
    if (createFunctionDto.trigger === 'cron' && !createFunctionDto.cronSchedule) {
      throw new Error('Cron schedule is required for cron triggers');
    }

    // Validate database trigger
    if (createFunctionDto.trigger === 'database') {
      if (!createFunctionDto.databaseTrigger?.table) {
        throw new Error('Database trigger requires table configuration');
      }
      if (!createFunctionDto.databaseTrigger?.events?.length) {
        throw new Error('Database trigger requires at least one event');
      }
    }

    // Validate event types
    if (createFunctionDto.trigger === 'event') {
      if (!createFunctionDto.eventTypes?.length) {
        throw new Error('Event trigger requires at least one event type');
      }
    }
  }

  private async validateFunctionAccess(functionSlug: string, workspaceId: string): Promise<void> {
    const functionEntity = await this.edgeFunctionsService.getFunction(functionSlug, workspaceId);
    
    if (!functionEntity) {
      throw new Error(`Function '${functionSlug}' not found in workspace '${workspaceId}'`);
    }
  }

  private async validateFunctionDeployment(functionSlug: string, workspaceId: string): Promise<void> {
    const functionEntity = await this.edgeFunctionsService.getFunction(functionSlug, workspaceId);
    
    if (!functionEntity) {
      throw new Error(`Function '${functionSlug}' not found in workspace '${workspaceId}'`);
    }

    if (!functionEntity.sourceCode || functionEntity.sourceCode.trim().length === 0) {
      throw new Error('Cannot deploy function without source code');
    }

    if (functionEntity.status === 'deploying') {
      throw new Error('Function is already being deployed');
    }
  }

  private async validateFunctionExecution(functionSlug: string, workspaceId: string): Promise<void> {
    const functionEntity = await this.edgeFunctionsService.getFunction(functionSlug, workspaceId);
    
    if (!functionEntity) {
      throw new Error(`Function '${functionSlug}' not found in workspace '${workspaceId}'`);
    }

    if (!functionEntity.canExecute()) {
      throw new Error('Function is not executable. Check function status and deployment.');
    }
  }

  // Utility Methods
  async validateSourceCode(sourceCode: string): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Basic validations
    if (!sourceCode || sourceCode.trim().length === 0) {
      errors.push('Source code cannot be empty');
    }

    // Check for Deno patterns
    if (!sourceCode.includes('Deno.serve') && !sourceCode.includes('serve(')) {
      errors.push('Function must use Deno.serve() or serve() to handle requests');
    }

    // Check for dangerous patterns
    const dangerousPatterns = [
      { pattern: /Deno\.exit/, message: 'Deno.exit() is not allowed' },
      { pattern: /process\.exit/, message: 'process.exit() is not allowed' },
      { pattern: /require\s*\(/, message: 'require() is not allowed, use import instead' },
      { pattern: /eval\s*\(/, message: 'eval() is not allowed for security reasons' },
      { pattern: /Function\s*\(/, message: 'Function constructor is not allowed' },
      { pattern: /import\s*['"`]fs['"`]/, message: 'Direct filesystem access is restricted' },
      { pattern: /import\s*['"`]child_process['"`]/, message: 'Child process execution is not allowed' }
    ];

    for (const { pattern, message } of dangerousPatterns) {
      if (pattern.test(sourceCode)) {
        errors.push(message);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  generateFunctionTemplate(functionName: string, trigger: string = 'http'): string {
    const templates = {
      http: `// E-BaaS Edge Function - HTTP Trigger
// Function: ${functionName}

Deno.serve(async (req: Request) => {
  const url = new URL(req.url);
  const method = req.method;
  
  // Get environment variables
  const env = globalThis.env || {};
  
  // Log request
  console.log(\`\${method} \${url.pathname}\`);
  
  try {
    switch (method) {
      case 'GET':
        return new Response(JSON.stringify({
          message: 'Hello from ${functionName}!',
          path: url.pathname,
          query: Object.fromEntries(url.searchParams)
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
        
      case 'POST':
        const body = await req.json();
        return new Response(JSON.stringify({
          message: 'Data received',
          data: body
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
        
      default:
        return new Response('Method not allowed', { status: 405 });
    }
  } catch (error) {
    console.error('Function error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
});`,

      cron: `// E-BaaS Edge Function - Cron Trigger
// Function: ${functionName}

export default async function cronHandler() {
  console.log('Cron job executed:', new Date().toISOString());
  
  // Your cron logic here
  try {
    // Example: cleanup, data processing, notifications, etc.
    console.log('Executing scheduled task...');
    
    return {
      success: true,
      timestamp: new Date().toISOString(),
      message: 'Cron job completed successfully'
    };
  } catch (error) {
    console.error('Cron job error:', error);
    throw error;
  }
}`,

      database: `// E-BaaS Edge Function - Database Trigger
// Function: ${functionName}

export default async function databaseHandler(event: any) {
  const { operation, table, old_record, new_record } = event;
  
  console.log(\`Database event: \${operation} on \${table}\`);
  
  try {
    switch (operation) {
      case 'INSERT':
        console.log('New record:', new_record);
        // Handle insert logic
        break;
        
      case 'UPDATE':
        console.log('Updated record:', { old: old_record, new: new_record });
        // Handle update logic
        break;
        
      case 'DELETE':
        console.log('Deleted record:', old_record);
        // Handle delete logic
        break;
    }
    
    return {
      success: true,
      processed: true
    };
  } catch (error) {
    console.error('Database handler error:', error);
    throw error;
  }
}`,

      event: `// E-BaaS Edge Function - Event Trigger
// Function: ${functionName}

export default async function eventHandler(event: any) {
  const { type, data, timestamp } = event;
  
  console.log(\`Event received: \${type}\`, data);
  
  try {
    // Handle different event types
    switch (type) {
      case 'user.created':
        // Handle user creation
        break;
        
      case 'order.completed':
        // Handle order completion
        break;
        
      default:
        console.log('Unknown event type:', type);
    }
    
    return {
      success: true,
      eventType: type,
      processedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error('Event handler error:', error);
    throw error;
  }
}`
    };

    return templates[trigger as keyof typeof templates] || templates.http;
  }
}