import { Repository } from "typeorm";
import { AppDataSource } from "../../infra/database/data-source";
import { EdgeFunction } from "./entity/EdgeFunction.entity";
import {
  CreateFunctionDto,
  UpdateFunctionDto,
  ExecuteFunctionDto,
  DeployFunctionDto,
  ListFunctionsDto,
  FunctionLogsDto,
  FunctionExecutionResult,
  FunctionLog,
  FunctionMetrics,
  RuntimeContext,
  DeploymentInfo,
  FunctionRuntime,
  FunctionStatus,
  FunctionTrigger
} from "./dto/edge-functions.dto";
import { execSync, spawn } from "child_process";
import { writeFileSync, readFileSync, existsSync, mkdirSync, rmSync } from "fs";
import { join } from "path";
import { randomUUID } from "crypto";

export class EdgeFunctionsService {
  private functionRepository: Repository<EdgeFunction>;
  private functionsDir: string;
  private denoPath: string;

  constructor() {
    this.functionRepository = AppDataSource.getRepository(EdgeFunction);
    this.functionsDir = join(process.cwd(), 'functions');
    this.denoPath = this.getDenoPath();
    this.ensureFunctionsDirectory();
  }

  private getDenoPath(): string {
    try {
      // Try to find Deno in PATH
      const denoPath = execSync('which deno', { encoding: 'utf8' }).trim();
      return denoPath;
    } catch (error) {
      console.warn('Deno not found in PATH, using default');
      return 'deno';
    }
  }

  private ensureFunctionsDirectory(): void {
    if (!existsSync(this.functionsDir)) {
      mkdirSync(this.functionsDir, { recursive: true });
    }
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  private getFunctionPath(workspaceId: string, slug: string): string {
    return join(this.functionsDir, workspaceId, slug);
  }

  private prepareFunctionCode(sourceCode: string, context: RuntimeContext): string {
    const wrapperCode = `
// E-BaaS Edge Function Runtime
const FUNCTION_CONTEXT = ${JSON.stringify(context)};

// Global context for the function
globalThis.context = FUNCTION_CONTEXT;
globalThis.env = FUNCTION_CONTEXT.environment;

// Enhanced logging
const originalConsole = globalThis.console;
const logs = [];

globalThis.console = {
  ...originalConsole,
  log: (...args) => {
    logs.push({ level: 'info', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsole.log('[INFO]', ...args);
  },
  warn: (...args) => {
    logs.push({ level: 'warn', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsole.warn('[WARN]', ...args);
  },
  error: (...args) => {
    logs.push({ level: 'error', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsole.error('[ERROR]', ...args);
  },
  debug: (...args) => {
    logs.push({ level: 'debug', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsole.debug('[DEBUG]', ...args);
  }
};

// Function timeout handler
const timeoutMs = ${context.config.timeoutMs || 10000};
let timeoutId;

// Performance monitoring
const startTime = Date.now();
let memoryUsed = 0;

// Wrap the user function
async function executeUserFunction() {
  try {
    // User function code
    ${sourceCode}
  } catch (error) {
    console.error('Function execution error:', error);
    throw error;
  }
}

// Main execution wrapper
async function main() {
  return new Promise(async (resolve, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error('Function execution timeout'));
    }, timeoutMs);

    try {
      const result = await executeUserFunction();
      
      // Calculate memory usage (approximation)
      memoryUsed = Math.round((process.memoryUsage().heapUsed - process.memoryUsage().heapTotal) / 1024 / 1024);
      
      const executionTime = Date.now() - startTime;
      
      clearTimeout(timeoutId);
      resolve({
        result,
        executionTime,
        memoryUsed,
        logs
      });
    } catch (error) {
      clearTimeout(timeoutId);
      reject(error);
    }
  });
}

if (import.meta.main) {
  main().then(result => {
    console.log('__EBAAS_RESULT__', JSON.stringify(result));
    Deno.exit(0);
  }).catch(error => {
    console.error('__EBAAS_ERROR__', error.message);
    Deno.exit(1);
  });
}
`;
    return wrapperCode;
  }

  // CRUD Operations
  async createFunction(createFunctionDto: CreateFunctionDto, createdBy?: string): Promise<EdgeFunction> {
    const slug = this.generateSlug(createFunctionDto.slug || createFunctionDto.name);
    
    // Check if function with slug already exists
    const existingFunction = await this.functionRepository.findOne({
      where: { workspaceId: createFunctionDto.workspaceId, slug }
    });

    if (existingFunction) {
      throw new Error(`Function with slug '${slug}' already exists`);
    }

    const functionEntity = this.functionRepository.create({
      ...createFunctionDto,
      slug,
      createdBy,
      status: FunctionStatus.INACTIVE
    });

    const savedFunction = await this.functionRepository.save(functionEntity);

    // Create function directory and write initial code
    const functionPath = this.getFunctionPath(savedFunction.workspaceId, savedFunction.slug);
    mkdirSync(functionPath, { recursive: true });
    
    writeFileSync(
      join(functionPath, 'index.ts'),
      savedFunction.sourceCode
    );

    return savedFunction;
  }

  async updateFunction(functionSlug: string, workspaceId: string, updateFunctionDto: UpdateFunctionDto, updatedBy?: string): Promise<EdgeFunction> {
    const functionEntity = await this.functionRepository.findOne({
      where: { slug: functionSlug, workspaceId }
    });

    if (!functionEntity) {
      throw new Error('Function not found');
    }

    Object.assign(functionEntity, {
      ...updateFunctionDto,
      updatedBy
    });

    const updatedFunction = await this.functionRepository.save(functionEntity);

    // Update source code file if changed
    if (updateFunctionDto.sourceCode) {
      const functionPath = this.getFunctionPath(updatedFunction.workspaceId, updatedFunction.slug);
      writeFileSync(
        join(functionPath, 'index.ts'),
        updatedFunction.sourceCode
      );
    }

    return updatedFunction;
  }

  async getFunction(functionSlug: string, workspaceId: string): Promise<EdgeFunction | null> {
    return await this.functionRepository.findOne({
      where: { slug: functionSlug, workspaceId }
    });
  }

  async listFunctions(listFunctionsDto: ListFunctionsDto): Promise<{ functions: EdgeFunction[]; total: number }> {
    const queryBuilder = this.functionRepository.createQueryBuilder('func');
    
    queryBuilder.where('func.workspaceId = :workspaceId', { workspaceId: listFunctionsDto.workspaceId });

    if (listFunctionsDto.status) {
      queryBuilder.andWhere('func.status = :status', { status: listFunctionsDto.status });
    }

    if (listFunctionsDto.runtime) {
      queryBuilder.andWhere('func.runtime = :runtime', { runtime: listFunctionsDto.runtime });
    }

    if (listFunctionsDto.search) {
      queryBuilder.andWhere(
        '(func.name ILIKE :search OR func.description ILIKE :search)',
        { search: `%${listFunctionsDto.search}%` }
      );
    }

    queryBuilder.orderBy('func.createdAt', 'DESC');

    if (listFunctionsDto.limit) {
      queryBuilder.limit(listFunctionsDto.limit);
    }

    if (listFunctionsDto.offset) {
      queryBuilder.offset(listFunctionsDto.offset);
    }

    const [functions, total] = await queryBuilder.getManyAndCount();

    return { functions, total };
  }

  async deleteFunction(functionSlug: string, workspaceId: string): Promise<{ message: string }> {
    const functionEntity = await this.functionRepository.findOne({
      where: { slug: functionSlug, workspaceId }
    });

    if (!functionEntity) {
      throw new Error('Function not found');
    }

    // Remove function directory
    const functionPath = this.getFunctionPath(functionEntity.workspaceId, functionEntity.slug);
    if (existsSync(functionPath)) {
      rmSync(functionPath, { recursive: true, force: true });
    }

    await this.functionRepository.remove(functionEntity);

    return { message: `Function '${functionSlug}' deleted successfully` };
  }

  // Deployment and Execution
  async deployFunction(deployFunctionDto: DeployFunctionDto): Promise<DeploymentInfo> {
    const functionEntity = await this.functionRepository.findOne({
      where: { slug: deployFunctionDto.functionSlug, workspaceId: deployFunctionDto.workspaceId }
    });

    if (!functionEntity) {
      throw new Error('Function not found');
    }

    functionEntity.status = FunctionStatus.DEPLOYING;
    await this.functionRepository.save(functionEntity);

    try {
      const functionPath = this.getFunctionPath(functionEntity.workspaceId, functionEntity.slug);
      const buildLogs: string[] = [];

      // Validate Deno syntax
      try {
        const checkResult = execSync(
          `${this.denoPath} check ${join(functionPath, 'index.ts')}`,
          { encoding: 'utf8', timeout: 30000 }
        );
        buildLogs.push('✅ TypeScript syntax validation passed');
      } catch (error: any) {
        buildLogs.push(`❌ TypeScript validation failed: ${error.message}`);
        throw new Error('Function validation failed');
      }

      // Update function status and deployment info
      const version = deployFunctionDto.version || `${Date.now()}`;
      functionEntity.status = FunctionStatus.ACTIVE;
      functionEntity.version = version;
      functionEntity.lastDeployedAt = new Date();
      
      // Calculate deployment size
      const stats = require('fs').statSync(join(functionPath, 'index.ts'));
      functionEntity.deploymentSize = stats.size;

      await this.functionRepository.save(functionEntity);

      return {
        version,
        deployedAt: new Date(),
        status: 'success',
        buildLogs,
        size: functionEntity.deploymentSize
      };
    } catch (error: any) {
      functionEntity.status = FunctionStatus.ERROR;
      await this.functionRepository.save(functionEntity);

      return {
        version: 'failed',
        deployedAt: new Date(),
        status: 'failed',
        buildLogs: [`❌ Deployment failed: ${error.message}`],
        size: 0
      };
    }
  }

  async executeFunction(executeFunctionDto: ExecuteFunctionDto): Promise<FunctionExecutionResult> {
    const functionEntity = await this.functionRepository.findOne({
      where: { slug: executeFunctionDto.functionSlug, workspaceId: executeFunctionDto.workspaceId }
    });

    if (!functionEntity) {
      throw new Error('Function not found');
    }

    if (!functionEntity.canExecute()) {
      throw new Error('Function is not executable');
    }

    const requestId = randomUUID();
    const context: RuntimeContext = {
      functionId: functionEntity.id,
      workspaceId: functionEntity.workspaceId,
      requestId,
      timestamp: new Date(),
      environment: {
        ...functionEntity.environmentVariables,
        EBAAS_FUNCTION_ID: functionEntity.id,
        EBAAS_WORKSPACE_ID: functionEntity.workspaceId,
        EBAAS_REQUEST_ID: requestId
      },
      secrets: functionEntity.secrets || {},
      config: functionEntity.config || {}
    };

    const functionPath = this.getFunctionPath(functionEntity.workspaceId, functionEntity.slug);
    const wrappedCode = this.prepareFunctionCode(functionEntity.sourceCode, context);
    const tempFile = join(functionPath, `temp_${requestId}.ts`);

    try {
      // Write wrapped code to temporary file
      writeFileSync(tempFile, wrappedCode);

      const startTime = Date.now();
      
      // Execute with Deno
      const result = await new Promise<FunctionExecutionResult>((resolve, reject) => {
        const denoProcess = spawn(this.denoPath, [
          'run',
          '--allow-all',
          '--no-check',
          tempFile
        ], {
          stdio: ['pipe', 'pipe', 'pipe'],
          timeout: functionEntity.getTimeout()
        });

        let stdout = '';
        let stderr = '';

        denoProcess.stdout?.on('data', (data) => {
          stdout += data.toString();
        });

        denoProcess.stderr?.on('data', (data) => {
          stderr += data.toString();
        });

        denoProcess.on('close', (code) => {
          const executionTime = Date.now() - startTime;

          try {
            // Parse result from stdout
            const resultMatch = stdout.match(/__EBAAS_RESULT__\s+(.+)/);
            const errorMatch = stderr.match(/__EBAAS_ERROR__\s+(.+)/);

            if (errorMatch) {
              reject(new Error(errorMatch[1]));
              return;
            }

            if (resultMatch) {
              const executionResult = JSON.parse(resultMatch[1]);
              resolve({
                statusCode: 200,
                headers: { 'Content-Type': 'application/json' },
                body: executionResult.result,
                executionTime: executionResult.executionTime || executionTime,
                memoryUsed: executionResult.memoryUsed || 0,
                logs: executionResult.logs || []
              });
            } else {
              resolve({
                statusCode: 200,
                headers: { 'Content-Type': 'text/plain' },
                body: stdout || 'Function executed successfully',
                executionTime,
                memoryUsed: 0,
                logs: []
              });
            }
          } catch (error: any) {
            reject(new Error(`Failed to parse function result: ${error.message}`));
          }
        });

        denoProcess.on('error', (error) => {
          reject(new Error(`Function execution failed: ${error.message}`));
        });

        // Send request data to function if needed
        if (executeFunctionDto.body) {
          denoProcess.stdin?.write(JSON.stringify(executeFunctionDto.body));
        }
        denoProcess.stdin?.end();
      });

      // Update function statistics
      functionEntity.incrementInvocation();
      functionEntity.updateExecutionTime(result.executionTime);
      await this.functionRepository.save(functionEntity);

      return result;
    } catch (error: any) {
      // Update error statistics
      functionEntity.incrementInvocation();
      functionEntity.incrementError();
      await this.functionRepository.save(functionEntity);

      throw new Error(`Function execution failed: ${error.message}`);
    } finally {
      // Clean up temporary file
      if (existsSync(tempFile)) {
        rmSync(tempFile);
      }
    }
  }

  // Monitoring and Analytics
  async getFunctionMetrics(functionSlug: string, workspaceId: string, period: { start: Date; end: Date }): Promise<FunctionMetrics> {
    const functionEntity = await this.functionRepository.findOne({
      where: { slug: functionSlug, workspaceId }
    });

    if (!functionEntity) {
      throw new Error('Function not found');
    }

    return {
      invocations: functionEntity.invocationCount,
      errors: functionEntity.errorCount,
      averageExecutionTime: functionEntity.averageExecutionTime,
      averageMemoryUsage: 0, // Would need execution logs to calculate
      totalExecutionTime: functionEntity.totalExecutionTime,
      period
    };
  }

  async getFunctionLogs(functionLogsDto: FunctionLogsDto): Promise<FunctionLog[]> {
    // For now, return empty logs since we'd need a proper logging system
    // In a real implementation, you'd store logs in a database or log service
    return [];
  }

  // Health and Status
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    denoAvailable: boolean;
    functionsCount: number;
    activeFunctions: number;
  }> {
    try {
      // Check if Deno is available
      let denoAvailable = false;
      try {
        execSync(`${this.denoPath} --version`, { timeout: 5000 });
        denoAvailable = true;
      } catch {
        denoAvailable = false;
      }

      const [functionsCount, activeFunctions] = await Promise.all([
        this.functionRepository.count(),
        this.functionRepository.count({ where: { status: FunctionStatus.ACTIVE } })
      ]);

      const status = denoAvailable ? 'healthy' : 'degraded';

      return {
        status,
        denoAvailable,
        functionsCount,
        activeFunctions
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        denoAvailable: false,
        functionsCount: 0,
        activeFunctions: 0
      };
    }
  }
}