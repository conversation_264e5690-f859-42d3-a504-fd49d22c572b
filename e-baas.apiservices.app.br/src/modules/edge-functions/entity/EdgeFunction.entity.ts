import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  BeforeInsert,
  BeforeUpdate
} from "typeorm";
import { FunctionRuntime, FunctionStatus, FunctionTrigger, FunctionConfigDto } from "../dto/edge-functions.dto";

@Entity("edge_functions")
@Index(["workspaceId", "slug"], { unique: true })
@Index(["workspaceId", "status"])
@Index(["status", "isPublic"])
export class EdgeFunction {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ length: 100, unique: true })
  @Index()
  slug: string;

  @Column({ name: "workspace_id", length: 36 })
  @Index()
  workspaceId: string;

  @Column({ type: "text", nullable: true })
  description?: string;

  @Column({ name: "source_code", type: "text" })
  sourceCode: string;

  @Column({ name: "source_code_hash", length: 64 })
  sourceCodeHash: string;

  @Column({
    type: "enum",
    enum: FunctionRuntime,
    default: FunctionRuntime.DENO
  })
  runtime: FunctionRuntime;

  @Column({
    type: "enum",
    enum: FunctionTrigger,
    default: FunctionTrigger.HTTP
  })
  trigger: FunctionTrigger;

  @Column({
    type: "enum",
    enum: FunctionStatus,
    default: FunctionStatus.ACTIVE
  })
  @Index()
  status: FunctionStatus;

  @Column({ type: "jsonb", nullable: true })
  config?: FunctionConfigDto;

  @Column({ name: "environment_variables", type: "jsonb", nullable: true })
  environmentVariables?: Record<string, string>;

  @Column({ type: "jsonb", nullable: true })
  secrets?: Record<string, string>;

  @Column({ name: "is_public", default: false })
  @Index()
  isPublic: boolean;

  @Column({ name: "cron_schedule", nullable: true })
  cronSchedule?: string;

  @Column({ name: "database_trigger", type: "jsonb", nullable: true })
  databaseTrigger?: {
    table: string;
    schema?: string;
    events: string[];
  };

  @Column({ name: "event_types", type: "text", array: true, nullable: true })
  eventTypes?: string[];

  @Column({ name: "created_by", nullable: true })
  createdBy?: string;

  @Column({ name: "updated_by", nullable: true })
  updatedBy?: string;

  @Column({ name: "last_deployed_at", nullable: true })
  lastDeployedAt?: Date;

  @Column({ name: "last_invoked_at", nullable: true })
  lastInvokedAt?: Date;

  @Column({ name: "invocation_count", default: 0 })
  invocationCount: number;

  @Column({ name: "error_count", default: 0 })
  errorCount: number;

  @Column({ name: "total_execution_time", type: "bigint", default: 0 })
  totalExecutionTime: number;

  @Column({ name: "average_execution_time", type: "float", default: 0 })
  averageExecutionTime: number;

  @Column({ name: "version", default: "1.0.0" })
  version: string;

  @Column({ name: "deployment_size", type: "bigint", default: 0 })
  deploymentSize: number;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @BeforeInsert()
  @BeforeUpdate()
  generateSourceCodeHash() {
    const crypto = require('crypto');
    this.sourceCodeHash = crypto
      .createHash('sha256')
      .update(this.sourceCode)
      .digest('hex');
  }

  // Helper methods
  isHTTPFunction(): boolean {
    return this.trigger === FunctionTrigger.HTTP;
  }

  isCronFunction(): boolean {
    return this.trigger === FunctionTrigger.CRON && !!this.cronSchedule;
  }

  isDatabaseFunction(): boolean {
    return this.trigger === FunctionTrigger.DATABASE && !!this.databaseTrigger;
  }

  isEventFunction(): boolean {
    return this.trigger === FunctionTrigger.EVENT && !!this.eventTypes?.length;
  }

  isActive(): boolean {
    return this.status === FunctionStatus.ACTIVE;
  }

  canExecute(): boolean {
    return this.isActive() && !!this.sourceCode;
  }

  getTimeout(): number {
    return this.config?.timeoutMs || 10000;
  }

  getMemoryLimit(): number {
    return this.config?.memoryMB || 128;
  }

  getAllowedMethods(): string[] {
    return this.config?.allowedMethods || ['GET', 'POST'];
  }

  getAllowedOrigins(): string[] {
    return this.config?.allowedOrigins || ['*'];
  }

  isCorsEnabled(): boolean {
    return this.config?.enableCors ?? true;
  }

  incrementInvocation(): void {
    this.invocationCount += 1;
    this.lastInvokedAt = new Date();
  }

  incrementError(): void {
    this.errorCount += 1;
  }

  updateExecutionTime(executionTime: number): void {
    this.totalExecutionTime += executionTime;
    this.averageExecutionTime = this.totalExecutionTime / this.invocationCount;
  }

  getErrorRate(): number {
    if (this.invocationCount === 0) return 0;
    return (this.errorCount / this.invocationCount) * 100;
  }

  toSafeObject() {
    const { secrets, ...safe } = this;
    return {
      ...safe,
      hasSecrets: !!secrets && Object.keys(secrets).length > 0
    };
  }

  getExecutionUrl(baseUrl: string): string {
    if (this.isPublic) {
      return `${baseUrl}/functions/v1/public/${this.slug}`;
    }
    return `${baseUrl}/functions/v1/invoke/${this.slug}`;
  }
}