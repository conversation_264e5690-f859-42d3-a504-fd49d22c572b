import { Request, Response } from 'express';
import { RlsPoliciesUseCases } from './rlsPolicies.useCases.v2';
import { RLSHelpers } from '../rls-policies/rls-helpers.service';

export class RlsPoliciesController {
  private rlsPoliciesUseCases: RlsPoliciesUseCases;
  private rlsHelpers: RLSHelpers;

  constructor() {
    this.rlsPoliciesUseCases = new RlsPoliciesUseCases();
    this.rlsHelpers = new RLSHelpers();
  }

  async createPolicy(req: Request, res: Response): Promise<void> {
    try {
      const { tableName, policyName, operation, condition } = req.body;

      if (!tableName || !policyName || !operation || !condition) {
        res.status(400).json({
          error: 'tableName, policyName, operation and condition are required'
        });
        return;
      }

      const result = await this.rlsPoliciesUseCases.createPolicy(
        tableName, 
        policyName, 
        operation, 
        condition
      );

      res.status(201).json({
        success: true,
        policy: result
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to create RLS policy',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async createDefaultPolicies(req: Request, res: Response): Promise<void> {
    try {
      const { tableName, options } = req.body;

      if (!tableName) {
        res.status(400).json({
          error: 'tableName is required'
        });
        return;
      }

      await this.rlsHelpers.createDefaultPolicies(tableName, options);

      res.json({
        success: true,
        message: `Default RLS policies created for table ${tableName}`
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to create default RLS policies',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async listPolicies(req: Request, res: Response): Promise<void> {
    try {
      const { tableName } = req.params;
      
      const policies = await this.rlsHelpers.listPolicies(tableName);

      res.json({
        success: true,
        policies
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to list RLS policies',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async dropPolicies(req: Request, res: Response): Promise<void> {
    try {
      const { tableName } = req.params;

      if (!tableName) {
        res.status(400).json({
          error: 'tableName is required'
        });
        return;
      }

      await this.rlsHelpers.dropAllPolicies(tableName);

      res.json({
        success: true,
        message: `All RLS policies dropped for table ${tableName}`
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to drop RLS policies',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async testPolicy(req: Request, res: Response): Promise<void> {
    try {
      const { tableName, operation, testData, userId, workspaceId } = req.body;

      if (!tableName || !operation) {
        res.status(400).json({
          error: 'tableName and operation are required'
        });
        return;
      }

      const result = await this.rlsHelpers.testPolicy(
        tableName,
        operation,
        testData,
        userId,
        workspaceId
      );

      res.json({
        success: true,
        testResult: result
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to test RLS policy',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async initializeHelpers(req: Request, res: Response): Promise<void> {
    try {
      await this.rlsHelpers.createRLSHelperFunctions();

      res.json({
        success: true,
        message: 'RLS helper functions initialized successfully'
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to initialize RLS helpers',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async enableRLS(req: Request, res: Response): Promise<void> {
    try {
      const { tableName } = req.params;

      if (!tableName) {
        res.status(400).json({
          error: 'tableName is required'
        });
        return;
      }

      await this.rlsPoliciesUseCases.enableRLS(tableName);

      res.json({
        success: true,
        message: `RLS enabled for table ${tableName}`
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to enable RLS',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async disableRLS(req: Request, res: Response): Promise<void> {
    try {
      const { tableName } = req.params;

      if (!tableName) {
        res.status(400).json({
          error: 'tableName is required'
        });
        return;
      }

      await this.rlsPoliciesUseCases.disableRLS(tableName);

      res.json({
        success: true,
        message: `RLS disabled for table ${tableName}`
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to disable RLS',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async getRLSStatus(req: Request, res: Response): Promise<void> {
    try {
      const { tableName } = req.params;
      
      const status = await this.rlsPoliciesUseCases.getRLSStatus(tableName);

      res.json({
        success: true,
        status
      });
    } catch (error) {
      res.status(500).json({
        error: 'Failed to get RLS status',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}