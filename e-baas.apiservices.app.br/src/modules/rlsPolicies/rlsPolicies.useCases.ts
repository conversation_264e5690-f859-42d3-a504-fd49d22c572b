import { RlsPolicies } from "./entity/rls-policies.entity";
import { rlsPoliciesRepository } from "infra/repository";
import IContractUseCases from "infra/contracts";
import { ErrorHandler } from "infra/errorHandlers";

export default class RlsPoliciesUseCases
  implements IContractUseCases<RlsPolicies>
{
  constructor() {}

  async getAll(): Promise<RlsPolicies[]> {
    try {
      return await rlsPoliciesRepository.find();
    } catch (error: unknown) {
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async getOne(id: string): Promise<RlsPolicies> {
    try {
      const rlsPolicies = await rlsPoliciesRepository.findOneBy({ id });
      if (!rlsPolicies) {
        throw ErrorHandler.NotFound("RlsPolicies not found");
      }
      return rlsPolicies;
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "RlsPolicies not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async create(data: Partial<RlsPolicies>): Promise<RlsPolicies> {
    try {
      return await rlsPoliciesRepository.save(data);
    } catch (error: unknown) {
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async update(id: string, data: Partial<RlsPolicies>): Promise<RlsPolicies> {
    try {
      const rlsPolicies = await this.getOne(id);
      await rlsPoliciesRepository.update(id, data);
      return { ...rlsPolicies, ...data };
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "RlsPolicies not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.getOne(id);
      await rlsPoliciesRepository.delete(id);
    } catch (error: unknown) {
      if (error instanceof Error && error.message === "RlsPolicies not found") {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }
}
