import { Router } from "express";
import { validator } from "infra";

import RlsPoliciesService from "modules/rls-policies/rls-policies.service";
import RlsPoliciesUseCases from "modules/rls-policies/rls-policies.useCases";
import { RlsPoliciesDto } from "modules/rls-policies/dto/rls-policies.dto";

const controller = Router();
const rlsPoliciesUseCases = new RlsPoliciesUseCases();
const rlsPoliciesService = new RlsPoliciesService(rlsPoliciesUseCases);

controller.get("/", (req, res) => rlsPoliciesService.getAll(req, res));
controller.get("/:id", (req, res) => rlsPoliciesService.getOne(req, res));
controller.post("/", validator(RlsPoliciesDto), (req, res) =>
  rlsPoliciesService.create(req, res)
);
controller.put("/:id", validator(RlsPoliciesDto), (req, res) =>
  rlsPoliciesService.update(req, res)
);
controller.delete("/:id", (req, res) => rlsPoliciesService.delete(req, res));

export default controller;
