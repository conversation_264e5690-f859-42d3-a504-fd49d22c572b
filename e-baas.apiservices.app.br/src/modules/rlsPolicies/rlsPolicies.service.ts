import { DataSource } from "typeorm";
import { getWorkspaceConnection } from "../../infra/database/data-source";
import { 
  RlsPolicyDto, 
  RlsTableConfigDto, 
  RlsPolicyTestDto, 
  RlsHelperFunctionDto,
  PolicyCommand 
} from "../rls-policies/dto/rlsPolicies.dto";

interface PolicyInfo {
  policyname: string;
  tablename: string;
  cmd: string;
  roles: string[];
  qual: string;
  with_check: string;
  permissive: boolean;
}

export class RlsService {
  private async getConnection(workspaceId: string): Promise<DataSource> {
    const config = {
      type: "postgres" as const,
      host: process.env.DB_HOST || "localhost",
      port: parseInt(process.env.DB_PORT || "5432"),
      username: process.env.DB_USERNAME || "postgres",
      password: process.env.DB_PASSWORD || "postgres",
      database: `workspace_${workspaceId}`,
      synchronize: false,
      logging: false,
    };

    return await getWorkspaceConnection(workspaceId, config);
  }

  // Enable/Disable RLS on a table
  async configureTableRls(configDto: RlsTableConfigDto): Promise<{ success: boolean; message: string }> {
    const connection = await this.getConnection(configDto.workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      if (configDto.enableRls) {
        // Enable RLS
        await queryRunner.query(`ALTER TABLE ${configDto.tableName} ENABLE ROW LEVEL SECURITY`);
        
        if (configDto.forceRls) {
          // Force RLS even for table owners
          await queryRunner.query(`ALTER TABLE ${configDto.tableName} FORCE ROW LEVEL SECURITY`);
        }
        
        return {
          success: true,
          message: `RLS enabled for table ${configDto.tableName}${configDto.forceRls ? ' (forced)' : ''}`
        };
      } else {
        // Disable RLS
        await queryRunner.query(`ALTER TABLE ${configDto.tableName} DISABLE ROW LEVEL SECURITY`);
        
        return {
          success: true,
          message: `RLS disabled for table ${configDto.tableName}`
        };
      }
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to configure RLS: ${error.message}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Create a new RLS policy
  async createPolicy(policyDto: RlsPolicyDto): Promise<{ success: boolean; message: string; policy?: any }> {
    const connection = await this.getConnection(policyDto.workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      // Build the CREATE POLICY statement
      let policySQL = `CREATE POLICY ${policyDto.name} ON ${policyDto.tableName}`;
      
      // Add PERMISSIVE/RESTRICTIVE clause
      if (policyDto.permissive !== undefined) {
        policySQL += ` AS ${policyDto.permissive ? 'PERMISSIVE' : 'RESTRICTIVE'}`;
      }
      
      // Add command clause
      policySQL += ` FOR ${policyDto.command}`;
      
      // Add role clause
      policySQL += ` TO ${policyDto.role}`;
      
      // Add USING clause (for all operations or SELECT/UPDATE/DELETE)
      if (policyDto.usingExpression) {
        policySQL += ` USING (${policyDto.usingExpression})`;
      }
      
      // Add WITH CHECK clause (for INSERT/UPDATE)
      if (policyDto.withCheckExpression) {
        policySQL += ` WITH CHECK (${policyDto.withCheckExpression})`;
      }

      await queryRunner.query(policySQL);

      // If policy should be disabled after creation
      if (policyDto.enabled === false) {
        await queryRunner.query(`ALTER POLICY ${policyDto.name} ON ${policyDto.tableName} DISABLE`);
      }

      return {
        success: true,
        message: `Policy ${policyDto.name} created successfully`,
        policy: policyDto
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to create policy: ${error.message}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Update an existing RLS policy
  async updatePolicy(policyName: string, policyDto: RlsPolicyDto): Promise<{ success: boolean; message: string }> {
    const connection = await this.getConnection(policyDto.workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      // PostgreSQL doesn't have ALTER POLICY for changing expressions
      // So we need to drop and recreate the policy
      await queryRunner.query(`DROP POLICY IF EXISTS ${policyName} ON ${policyDto.tableName}`);
      
      const createResult = await this.createPolicy(policyDto);
      
      return createResult;
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to update policy: ${error.message}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Delete an RLS policy
  async deletePolicy(workspaceId: string, tableName: string, policyName: string): Promise<{ success: boolean; message: string }> {
    const connection = await this.getConnection(workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      await queryRunner.query(`DROP POLICY IF EXISTS ${policyName} ON ${tableName}`);

      return {
        success: true,
        message: `Policy ${policyName} deleted successfully`
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to delete policy: ${error.message}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // List all policies for a table
  async listPolicies(workspaceId: string, tableName?: string): Promise<PolicyInfo[]> {
    const connection = await this.getConnection(workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      let query = `
        SELECT 
          pol.policyname,
          pol.tablename,
          pol.cmd,
          pol.roles,
          pol.qual,
          pol.with_check,
          pol.permissive
        FROM pg_policies pol
        JOIN pg_class cls ON cls.relname = pol.tablename
        JOIN pg_namespace nsp ON nsp.oid = cls.relnamespace
        WHERE nsp.nspname = 'public'
      `;

      const params: any[] = [];
      if (tableName) {
        query += ` AND pol.tablename = $1`;
        params.push(tableName);
      }

      query += ` ORDER BY pol.tablename, pol.policyname`;

      const policies = await queryRunner.query(query, params);
      
      return policies;
    } finally {
      await queryRunner.release();
    }
  }

  // Test a policy by simulating a query
  async testPolicy(testDto: RlsPolicyTestDto): Promise<{ success: boolean; result?: any; error?: string; executionPlan?: any }> {
    const connection = await this.getConnection(testDto.workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();
      
      // Start a transaction for testing
      await queryRunner.startTransaction();

      // Set the role context if provided
      if (testDto.role) {
        await queryRunner.query(`SET ROLE ${testDto.role}`);
      }

      // Set JWT context if provided (simulating Supabase auth context)
      if (testDto.contextData) {
        if (testDto.contextData.user_id) {
          await queryRunner.query(`SET request.jwt.claims TO '${JSON.stringify(testDto.contextData)}'`);
        }
      }

      // Get execution plan
      const explainResult = await queryRunner.query(`EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${testDto.testQuery}`);
      
      // Execute the actual query
      const result = await queryRunner.query(testDto.testQuery);

      // Rollback the transaction (we don't want to persist test data)
      await queryRunner.rollbackTransaction();

      return {
        success: true,
        result,
        executionPlan: explainResult
      };
    } catch (error: any) {
      try {
        await queryRunner.rollbackTransaction();
      } catch (rollbackError) {
        // Ignore rollback errors
      }

      return {
        success: false,
        error: error.message
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Create helper functions for RLS policies
  async createHelperFunction(functionDto: RlsHelperFunctionDto): Promise<{ success: boolean; message: string }> {
    const connection = await this.getConnection(functionDto.workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      // Build function parameters
      const params = functionDto.parameters 
        ? functionDto.parameters.map(p => `${p.name} ${p.type}`).join(', ')
        : '';

      // Build the CREATE FUNCTION statement
      const functionSQL = `
        CREATE OR REPLACE FUNCTION ${functionDto.functionName}(${params})
        RETURNS ${functionDto.returnType || 'BOOLEAN'}
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          ${functionDto.functionBody}
        END;
        $$;
      `;

      await queryRunner.query(functionSQL);

      return {
        success: true,
        message: `Helper function ${functionDto.functionName} created successfully`
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to create helper function: ${error.message}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Get common RLS helper functions (like auth.uid(), auth.jwt(), etc.)
  async createBuiltinHelpers(workspaceId: string): Promise<{ success: boolean; message: string }> {
    const connection = await this.getConnection(workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      // Create auth schema if it doesn't exist
      await queryRunner.query(`CREATE SCHEMA IF NOT EXISTS auth`);

      // auth.uid() function - returns current user ID from JWT
      await queryRunner.query(`
        CREATE OR REPLACE FUNCTION auth.uid()
        RETURNS uuid
        LANGUAGE sql
        STABLE
        AS $$
          SELECT COALESCE(
            current_setting('request.jwt.claims', true)::json->>'sub',
            current_setting('request.jwt.claims', true)::json->>'user_id'
          )::uuid;
        $$;
      `);

      // auth.role() function - returns current user role
      await queryRunner.query(`
        CREATE OR REPLACE FUNCTION auth.role()
        RETURNS text
        LANGUAGE sql
        STABLE
        AS $$
          SELECT COALESCE(
            current_setting('request.jwt.claims', true)::json->>'role',
            current_user
          )::text;
        $$;
      `);

      // auth.jwt() function - returns the full JWT claims
      await queryRunner.query(`
        CREATE OR REPLACE FUNCTION auth.jwt()
        RETURNS json
        LANGUAGE sql
        STABLE
        AS $$
          SELECT current_setting('request.jwt.claims', true)::json;
        $$;
      `);

      return {
        success: true,
        message: 'Built-in RLS helper functions created successfully'
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to create built-in helpers: ${error.message}`
      };
    } finally {
      await queryRunner.release();
    }
  }

  // Get RLS status for tables
  async getRlsStatus(workspaceId: string): Promise<any[]> {
    const connection = await this.getConnection(workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      const query = `
        SELECT 
          cls.relname as table_name,
          cls.relrowsecurity as rls_enabled,
          cls.relforcerowsecurity as rls_forced,
          COUNT(pol.policyname) as policy_count
        FROM pg_class cls
        JOIN pg_namespace nsp ON nsp.oid = cls.relnamespace
        LEFT JOIN pg_policies pol ON pol.tablename = cls.relname
        WHERE nsp.nspname = 'public' 
          AND cls.relkind = 'r'
        GROUP BY cls.relname, cls.relrowsecurity, cls.relforcerowsecurity
        ORDER BY cls.relname
      `;

      return await queryRunner.query(query);
    } finally {
      await queryRunner.release();
    }
  }

  // Enable/Disable a specific policy
  async togglePolicy(workspaceId: string, tableName: string, policyName: string, enabled: boolean): Promise<{ success: boolean; message: string }> {
    const connection = await this.getConnection(workspaceId);
    const queryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();

      const action = enabled ? 'ENABLE' : 'DISABLE';
      await queryRunner.query(`ALTER POLICY ${policyName} ON ${tableName} ${action}`);

      return {
        success: true,
        message: `Policy ${policyName} ${enabled ? 'enabled' : 'disabled'} successfully`
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to ${enabled ? 'enable' : 'disable'} policy: ${error.message}`
      };
    } finally {
      await queryRunner.release();
    }
  }
}