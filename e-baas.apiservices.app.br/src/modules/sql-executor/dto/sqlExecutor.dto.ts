import { IsNotEmpty, IsOptional, IsString, IsBoolean, IsArray, IsN<PERSON><PERSON> } from "class-validator";

export enum SqlQueryType {
  SELECT = 'SELECT',
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  CREATE = 'CREATE',
  ALTER = 'ALTER',
  DROP = 'DROP',
  TRUNCATE = 'TRUNCATE'
}

export class SqlExecutorDto {
  @IsNotEmpty()
  @IsString()
  query: string;

  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsArray()
  params?: any[];

  @IsOptional()
  @IsBoolean()
  readonly?: boolean;

  @IsOptional()
  @IsNumber()
  timeout?: number; // in milliseconds

  @IsOptional()
  @IsBoolean()
  explainPlan?: boolean;

  @IsOptional()
  @IsString()
  userId?: string; // for audit trails
}

export class SqlValidationDto {
  @IsNotEmpty()
  @IsString()
  query: string;

  @IsOptional()
  @IsBoolean()
  strict?: boolean;
}

export class SqlQueryHistoryDto {
  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsNumber()
  limit?: number;

  @IsOptional()
  @IsNumber()
  offset?: number;
}
