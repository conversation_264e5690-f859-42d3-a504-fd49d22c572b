import { IsNotEmpty, IsOptional, IsString, IsBoolean, IsEnum, IsArray } from "class-validator";

export enum PolicyCommand {
  ALL = 'ALL',
  SELECT = 'SELECT',
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE'
}

export enum PolicyRole {
  AUTHENTICATED = 'authenticated',
  ANONYMOUS = 'anonymous',
  SERVICE_ROLE = 'service_role',
  CUSTOM = 'custom'
}

export class RlsPolicyDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  tableName: string;

  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsNotEmpty()
  @IsEnum(PolicyCommand)
  command: PolicyCommand;

  @IsNotEmpty()
  @IsString()
  role: string;

  @IsOptional()
  @IsString()
  usingExpression?: string; // WHERE clause for policy

  @IsOptional()
  @IsString()
  withCheckExpression?: string; // CHECK clause for INSERT/UPDATE

  @IsOptional()
  @IsBoolean()
  permissive?: boolean; // true for PERMISSIVE, false for RESTRICTIVE

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  enabled?: boolean;
}

export class RlsTableConfigDto {
  @IsNotEmpty()
  @IsString()
  tableName: string;

  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsNotEmpty()
  @IsBoolean()
  enableRls: boolean;

  @IsOptional()
  @IsBoolean()
  forceRls?: boolean; // Force RLS even for table owners
}

export class RlsPolicyTestDto {
  @IsNotEmpty()
  @IsString()
  tableName: string;

  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsNotEmpty()
  @IsString()
  testQuery: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsString()
  role?: string;

  @IsOptional()
  contextData?: Record<string, any>; // JWT claims or other context
}

export class RlsHelperFunctionDto {
  @IsNotEmpty()
  @IsString()
  functionName: string;

  @IsNotEmpty()
  @IsString()
  workspaceId: string;

  @IsNotEmpty()
  @IsString()
  functionBody: string;

  @IsOptional()
  @IsString()
  returnType?: string;

  @IsOptional()
  @IsArray()
  parameters?: { name: string; type: string }[];

  @IsOptional()
  @IsString()
  description?: string;
}
