import { AppDataSource } from "../../infra/database/data-source";

export class RLSHelpers {
  private connection = AppDataSource.manager.connection;

  /**
   * <PERSON><PERSON> as funções helper RLS no PostgreSQL
   */
  async createRLSHelperFunctions(): Promise<void> {
    try {
      // Função para obter o user_id atual
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.uid()
        RETURNS uuid
        LANGUAGE sql
        STABLE
        AS $$
          SELECT NULLIF(current_setting('app.current_user_id', true), '')::uuid;
        $$;
      `);

      // Função para obter o workspace_id atual
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.workspace_id()
        RETURNS text
        LANGUAGE sql
        STABLE
        AS $$
          SELECT NULLIF(current_setting('app.current_workspace_id', true), '');
        $$;
      `);

      // Função para obter o role atual
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.role()
        RETURNS text
        LANGUAGE sql
        STABLE
        AS $$
          SELECT NULLIF(current_setting('app.current_user_role', true), '');
        $$;
      `);

      // Função para verificar se usuário tem permissão específica
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.has_permission(permission text)
        RETURNS boolean
        LANGUAGE sql
        STABLE
        AS $$
          SELECT COALESCE(
            current_setting('app.current_permissions', true)::jsonb ? permission,
            false
          );
        $$;
      `);

      // Função para verificar se usuário é admin
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.is_admin()
        RETURNS boolean
        LANGUAGE sql
        STABLE
        AS $$
          SELECT auth.role() = 'admin';
        $$;
      `);

      // Função para verificar se usuário pode acessar workspace
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.can_access_workspace(target_workspace_id text)
        RETURNS boolean
        LANGUAGE sql
        STABLE
        AS $$
          SELECT auth.workspace_id() = target_workspace_id OR auth.is_admin();
        $$;
      `);

      // Função para verificar se usuário é dono do registro
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.is_owner(record_user_id uuid)
        RETURNS boolean
        LANGUAGE sql
        STABLE
        AS $$
          SELECT auth.uid() = record_user_id;
        $$;
      `);

      // Função para verificar se usuário pode ler dados
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.can_read(record_user_id uuid DEFAULT NULL, record_workspace_id text DEFAULT NULL)
        RETURNS boolean
        LANGUAGE sql
        STABLE
        AS $$
          SELECT 
            auth.is_admin() OR
            auth.has_permission('read:all') OR
            (record_user_id IS NOT NULL AND auth.is_owner(record_user_id)) OR
            (record_workspace_id IS NOT NULL AND auth.can_access_workspace(record_workspace_id)) OR
            auth.has_permission('read:shared');
        $$;
      `);

      // Função para verificar se usuário pode escrever dados
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.can_write(record_user_id uuid DEFAULT NULL, record_workspace_id text DEFAULT NULL)
        RETURNS boolean
        LANGUAGE sql
        STABLE
        AS $$
          SELECT 
            auth.is_admin() OR
            auth.has_permission('write:all') OR
            (record_user_id IS NOT NULL AND auth.is_owner(record_user_id)) OR
            (record_workspace_id IS NOT NULL AND auth.can_access_workspace(record_workspace_id)) OR
            auth.has_permission('write:shared');
        $$;
      `);

      // Função para verificar se usuário pode deletar dados
      await this.connection.query(`
        CREATE OR REPLACE FUNCTION auth.can_delete(record_user_id uuid DEFAULT NULL, record_workspace_id text DEFAULT NULL)
        RETURNS boolean
        LANGUAGE sql
        STABLE
        AS $$
          SELECT 
            auth.is_admin() OR
            auth.has_permission('delete:all') OR
            (record_user_id IS NOT NULL AND auth.is_owner(record_user_id));
        $$;
      `);

      console.log('✅ RLS helper functions created successfully');
    } catch (error) {
      console.error('❌ Failed to create RLS helper functions:', error);
      throw error;
    }
  }

  /**
   * Cria políticas RLS padrão para uma tabela
   */
  async createDefaultPolicies(
    tableName: string,
    options: {
      hasUserId?: boolean;
      hasWorkspaceId?: boolean;
      userIdColumn?: string;
      workspaceIdColumn?: string;
      enableSelect?: boolean;
      enableInsert?: boolean;
      enableUpdate?: boolean;
      enableDelete?: boolean;
    } = {}
  ): Promise<void> {
    const {
      hasUserId = true,
      hasWorkspaceId = true,
      userIdColumn = 'user_id',
      workspaceIdColumn = 'workspace_id',
      enableSelect = true,
      enableInsert = true,
      enableUpdate = true,
      enableDelete = true
    } = options;

    try {
      // Ativar RLS na tabela
      await this.connection.query(`ALTER TABLE ${tableName} ENABLE ROW LEVEL SECURITY`);

      // Política para SELECT
      if (enableSelect) {
        const selectCondition = this.buildCondition(hasUserId, hasWorkspaceId, userIdColumn, workspaceIdColumn, 'read');
        await this.connection.query(`
          CREATE POLICY "${tableName}_select_policy" ON ${tableName}
          FOR SELECT
          USING (${selectCondition});
        `);
      }

      // Política para INSERT
      if (enableInsert) {
        const insertCondition = this.buildCondition(hasUserId, hasWorkspaceId, userIdColumn, workspaceIdColumn, 'write');
        await this.connection.query(`
          CREATE POLICY "${tableName}_insert_policy" ON ${tableName}
          FOR INSERT
          WITH CHECK (${insertCondition});
        `);
      }

      // Política para UPDATE
      if (enableUpdate) {
        const updateCondition = this.buildCondition(hasUserId, hasWorkspaceId, userIdColumn, workspaceIdColumn, 'write');
        await this.connection.query(`
          CREATE POLICY "${tableName}_update_policy" ON ${tableName}
          FOR UPDATE
          USING (${updateCondition})
          WITH CHECK (${updateCondition});
        `);
      }

      // Política para DELETE
      if (enableDelete) {
        const deleteCondition = this.buildCondition(hasUserId, hasWorkspaceId, userIdColumn, workspaceIdColumn, 'delete');
        await this.connection.query(`
          CREATE POLICY "${tableName}_delete_policy" ON ${tableName}
          FOR DELETE
          USING (${deleteCondition});
        `);
      }

      console.log(`✅ Default RLS policies created for table ${tableName}`);
    } catch (error) {
      console.error(`❌ Failed to create RLS policies for table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Remove todas as políticas RLS de uma tabela
   */
  async dropAllPolicies(tableName: string): Promise<void> {
    try {
      // Buscar todas as políticas da tabela
      const policies = await this.connection.query(`
        SELECT policyname FROM pg_policies WHERE tablename = $1
      `, [tableName]);

      // Remover cada política
      for (const policy of policies) {
        await this.connection.query(`DROP POLICY IF EXISTS ${policy.policyname} ON ${tableName}`);
      }

      console.log(`✅ All RLS policies dropped for table ${tableName}`);
    } catch (error) {
      console.error(`❌ Failed to drop RLS policies for table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Lista todas as políticas RLS de uma tabela
   */
  async listPolicies(tableName?: string): Promise<any[]> {
    try {
      let query = `
        SELECT 
          schemaname,
          tablename,
          policyname,
          permissive,
          roles,
          cmd,
          qual,
          with_check
        FROM pg_policies
      `;
      
      const params: string[] = [];
      if (tableName) {
        query += ' WHERE tablename = $1';
        params.push(tableName);
      }
      
      query += ' ORDER BY tablename, policyname';

      return await this.connection.query(query, params);
    } catch (error) {
      console.error('❌ Failed to list RLS policies:', error);
      throw error;
    }
  }

  /**
   * Testa se uma política RLS está funcionando
   */
  async testPolicy(
    tableName: string,
    operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE',
    testData: any = {},
    userId?: string,
    workspaceId?: string
  ): Promise<{ success: boolean; result?: any; error?: string }> {
    try {
      // Configurar contexto de teste
      if (userId) {
        await this.connection.query(`SET LOCAL app.current_user_id = '${userId}'`);
      }
      if (workspaceId) {
        await this.connection.query(`SET LOCAL app.current_workspace_id = '${workspaceId}'`);
      }

      let query: string;
      let params: any[] = [];

      switch (operation) {
        case 'SELECT':
          query = `SELECT * FROM ${tableName} LIMIT 1`;
          break;
        case 'INSERT':
          const columns = Object.keys(testData).join(', ');
          const values = Object.keys(testData).map((_, i) => `$${i + 1}`).join(', ');
          params = Object.values(testData);
          query = `INSERT INTO ${tableName} (${columns}) VALUES (${values}) RETURNING *`;
          break;
        case 'UPDATE':
          const setClause = Object.keys(testData).map((key, i) => `${key} = $${i + 1}`).join(', ');
          params = Object.values(testData);
          query = `UPDATE ${tableName} SET ${setClause} WHERE id = $${params.length + 1} RETURNING *`;
          params.push('test-id');
          break;
        case 'DELETE':
          query = `DELETE FROM ${tableName} WHERE id = $1 RETURNING *`;
          params = ['test-id'];
          break;
        default:
          throw new Error('Invalid operation');
      }

      const result = await this.connection.query(query, params);
      
      return { success: true, result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Constrói condição RLS baseada nos parâmetros
   */
  private buildCondition(
    hasUserId: boolean,
    hasWorkspaceId: boolean,
    userIdColumn: string,
    workspaceIdColumn: string,
    operation: 'read' | 'write' | 'delete'
  ): string {
    const conditions: string[] = [];

    // Sempre permitir para admin
    conditions.push('auth.is_admin()');

    // Condições baseadas no tipo de operação
    if (hasUserId) {
      conditions.push(`auth.can_${operation}(${userIdColumn})`);
    }

    if (hasWorkspaceId) {
      conditions.push(`auth.can_${operation}(NULL, ${workspaceIdColumn})`);
    }

    // Se não tem nem userId nem workspaceId, usar permissão geral
    if (!hasUserId && !hasWorkspaceId) {
      conditions.push(`auth.has_permission('${operation}:all')`);
    }

    return conditions.join(' OR ');
  }
}