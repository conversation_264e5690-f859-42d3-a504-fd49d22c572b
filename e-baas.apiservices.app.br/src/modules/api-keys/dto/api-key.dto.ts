import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsUUID,
  IsDateString,
  IsBoolean,
  IsEnum,
  IsArray,
  IsNumber,
  <PERSON>,
  <PERSON>
} from "class-validator";

export enum ApiKeyType {
  ANON = 'anon',
  SERVICE_ROLE = 'service_role',
  AUTHENTICATED = 'authenticated'
}

export enum ApiKeyScope {
  ALL = '*',
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin',
  DATABASE = 'database',
  STORAGE = 'storage',
  AUTH = 'auth',
  REALTIME = 'realtime',
  EDGE_FUNCTIONS = 'edge_functions'
}

export class CreateApiKeyDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  workspaceId: string;

  @IsEnum(ApiKeyType)
  type: ApiKeyType;

  @IsOptional()
  @IsArray()
  @IsEnum(ApiKeyScope, { each: true })
  scopes?: ApiKeyScope[];

  @IsOptional()
  @IsDateString()
  expiresAt?: Date;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  rateLimit?: number; // requests per minute

  @IsOptional()
  @IsArray()
  allowedOrigins?: string[]; // CORS origins

  @IsOptional()
  @IsArray()
  allowedIps?: string[]; // IP whitelist
}

export class UpdateApiKeyDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsArray()
  @IsEnum(ApiKeyScope, { each: true })
  scopes?: ApiKeyScope[];

  @IsOptional()
  @IsDateString()
  expiresAt?: Date;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  rateLimit?: number;

  @IsOptional()
  @IsArray()
  allowedOrigins?: string[];

  @IsOptional()
  @IsArray()
  allowedIps?: string[];
}

export class ApiKeyResponseDto {
  id: string;
  name: string;
  workspaceId: string;
  type: ApiKeyType;
  scopes: ApiKeyScope[];
  prefix: string; // First 8 chars of the key for identification
  isActive: boolean;
  expiresAt?: Date;
  description?: string;
  rateLimit?: number;
  allowedOrigins?: string[];
  allowedIps?: string[];
  lastUsedAt?: Date;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export class ApiKeyWithSecretDto extends ApiKeyResponseDto {
  apiKey: string; // Full secret key (only returned once on creation)
}

export class ApiKeyUsageDto {
  @IsString()
  @IsNotEmpty()
  workspaceId: string;

  @IsOptional()
  @IsString()
  keyId?: string;

  @IsOptional()
  @IsDateString()
  startDate?: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  limit?: number;
}

export class RateLimitDto {
  @IsString()
  @IsNotEmpty()
  keyId: string;

  @IsString()
  @IsNotEmpty()
  endpoint: string;

  @IsString()
  @IsNotEmpty()
  ipAddress: string;

  @IsOptional()
  @IsString()
  userAgent?: string;
}
