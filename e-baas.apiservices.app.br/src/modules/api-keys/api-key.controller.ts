import { Router, Request, Response } from "express";
import { ApiKeyService } from "./api-key.service";
import {
  CreateApiKeyDto,
  UpdateApiKeyDto,
  ApiKeyUsageDto,
  ApiKeyType
} from "./dto/api-key.dto";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";

const apiKeyRouter = Router();
const apiKeyService = new ApiKeyService();

// Helper function for validation
const validateDto = async (DtoClass: any, data: any) => {
  const dto = plainToClass(DtoClass, data);
  const errors = await validate(dto);
  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
  }
  return dto;
};

// Create API key
apiKeyRouter.post("/", async (req: Request, res: Response) => {
  try {
    const createDto = req.body as CreateApiKeyDto;
    const createdBy = (req as any).user?.id;
    
    const apiKey = await apiKeyService.createApiKey(createDto, createdBy);
    
    return res.status(201).json(apiKey);
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Get all API keys for workspace
apiKeyRouter.get("/", async (req: Request, res: Response) => {
  try {
    const { workspaceId, type } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }
    
    const apiKeys = await apiKeyService.getApiKeys(workspaceId, type as ApiKeyType);
    
    return res.status(200).json(apiKeys);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get specific API key
apiKeyRouter.get("/:keyId", async (req: Request, res: Response) => {
  try {
    const { keyId } = req.params;
    const { workspaceId } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }
    
    const apiKey = await apiKeyService.getApiKey(keyId, workspaceId);
    
    if (!apiKey) {
      return res.status(404).json({ error: 'API key not found' });
    }
    
    return res.status(200).json(apiKey);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Update API key
apiKeyRouter.put("/:keyId", async (req: Request, res: Response) => {
  try {
    const { keyId } = req.params;
    const { workspaceId } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }
    
    const updateDto = req.body as UpdateApiKeyDto;
    const apiKey = await apiKeyService.updateApiKey(keyId, workspaceId, updateDto);
    
    return res.status(200).json(apiKey);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Delete API key
apiKeyRouter.delete("/:keyId", async (req: Request, res: Response) => {
  try {
    const { keyId } = req.params;
    const { workspaceId } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }
    
    const result = await apiKeyService.deleteApiKey(keyId, workspaceId);
    
    return res.status(200).json(result);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Rotate API key (generate new secret)
apiKeyRouter.post("/:keyId/rotate", async (req: Request, res: Response) => {
  try {
    const { keyId } = req.params;
    const { workspaceId } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }
    
    const apiKey = await apiKeyService.rotateApiKey(keyId, workspaceId);
    
    return res.status(200).json(apiKey);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Validate API key
apiKeyRouter.post("/validate", async (req: Request, res: Response) => {
  try {
    const { apiKey, scope, ipAddress, origin } = req.body;
    
    if (!apiKey) {
      return res.status(400).json({ error: 'API key is required' });
    }
    
    const validKey = await apiKeyService.validateApiKey(apiKey, scope, ipAddress, origin);
    
    if (!validKey) {
      return res.status(401).json({ valid: false, error: 'Invalid API key' });
    }
    
    return res.status(200).json({
      valid: true,
      key: validKey.toSafeObject(),
      workspaceId: validKey.workspaceId,
      type: validKey.type,
      scopes: validKey.scopes
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get API key usage statistics
apiKeyRouter.get("/usage/stats", async (req: Request, res: Response) => {
  try {
    const usageDto = req.query as any as ApiKeyUsageDto;
    const usage = await apiKeyService.getApiKeyUsage(usageDto);
    
    return res.status(200).json(usage);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get workspace API key statistics
apiKeyRouter.get("/workspace/:workspaceId/stats", async (req: Request, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const stats = await apiKeyService.getWorkspaceStats(workspaceId);
    
    return res.status(200).json(stats);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get rate limit status for API key
apiKeyRouter.get("/:keyId/rate-limit", async (req: Request, res: Response) => {
  try {
    const { keyId } = req.params;
    const rateLimitStatus = await apiKeyService.getRateLimitStatus(keyId);
    
    if (!rateLimitStatus) {
      return res.status(404).json({ error: 'Rate limit info not found' });
    }
    
    return res.status(200).json(rateLimitStatus);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Create default API keys for new workspace
apiKeyRouter.post("/workspace/:workspaceId/defaults", async (req: Request, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const createdBy = (req as any).user?.id;
    
    const defaultKeys = await apiKeyService.createDefaultApiKeys(workspaceId, createdBy);
    
    return res.status(201).json(defaultKeys);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Start the cleanup interval when the module loads
apiKeyService.startCleanupInterval();

export default apiKeyRouter;