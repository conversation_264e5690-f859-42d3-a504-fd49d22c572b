import { IsString, IsOptional, IsArray, IsBoolean, IsEnum, IsNumber, IsObject, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

export enum ColumnType {
  VARCHAR = 'varchar',
  TEXT = 'text',
  INTEGER = 'integer',
  BIGINT = 'bigint',
  DECIMAL = 'decimal',
  BOOLEAN = 'boolean',
  DATE = 'date',
  DATETIME = 'datetime',
  TIMESTAMP = 'timestamp',
  JSON = 'json',
  UUID = 'uuid'
}

export enum IndexType {
  BTREE = 'btree',
  HASH = 'hash',
  GIN = 'gin',
  GIST = 'gist'
}

export class ColumnDefinitionDto {
  @IsString()
  name: string;

  @IsEnum(ColumnType)
  type: ColumnType;

  @IsOptional()
  @IsNumber()
  length?: number;

  @IsOptional()
  @IsNumber()
  precision?: number;

  @IsOptional()
  @IsNumber()
  scale?: number;

  @IsOptional()
  @IsBoolean()
  nullable?: boolean = true;

  @IsOptional()
  @IsBoolean()
  unique?: boolean = false;

  @IsOptional()
  @IsBoolean()
  primary?: boolean = false;

  @IsOptional()
  @IsString()
  defaultValue?: string;

  @IsOptional()
  @IsString()
  comment?: string;

  @IsOptional()
  @IsString()
  foreignKeyTable?: string;

  @IsOptional()
  @IsString()
  foreignKeyColumn?: string;

  @IsOptional()
  @IsString()
  onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION';

  @IsOptional()
  @IsString()
  onUpdate?: 'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION';
}

export class IndexDefinitionDto {
  @IsString()
  name: string;

  @IsArray()
  @IsString({ each: true })
  columns: string[];

  @IsOptional()
  @IsBoolean()
  unique?: boolean = false;

  @IsOptional()
  @IsEnum(IndexType)
  type?: IndexType = IndexType.BTREE;

  @IsOptional()
  @IsString()
  where?: string; // For partial indexes
}

export class CreateTableDto {
  @IsString()
  tableName: string;

  @IsString()
  workspaceId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColumnDefinitionDto)
  columns: ColumnDefinitionDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IndexDefinitionDto)
  indexes?: IndexDefinitionDto[];

  @IsOptional()
  @IsString()
  comment?: string;

  @IsOptional()
  @IsBoolean()
  enableRLS?: boolean = false;
}

export class AlterTableDto {
  @IsString()
  tableName: string;

  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColumnDefinitionDto)
  addColumns?: ColumnDefinitionDto[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dropColumns?: string[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColumnDefinitionDto)
  modifyColumns?: ColumnDefinitionDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IndexDefinitionDto)
  addIndexes?: IndexDefinitionDto[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dropIndexes?: string[];

  @IsOptional()
  @IsString()
  newTableName?: string;

  @IsOptional()
  @IsString()
  comment?: string;
}

export class DropTableDto {
  @IsString()
  tableName: string;

  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsBoolean()
  cascade?: boolean = false;
}

export class TableInfoDto {
  @IsString()
  workspaceId: string;

  @IsOptional()
  @IsString()
  tableName?: string;

  @IsOptional()
  @IsBoolean()
  includeColumns?: boolean = true;

  @IsOptional()
  @IsBoolean()
  includeIndexes?: boolean = true;

  @IsOptional()
  @IsBoolean()
  includeConstraints?: boolean = true;
}

export class CreateViewDto {
  @IsString()
  viewName: string;

  @IsString()
  workspaceId: string;

  @IsString()
  query: string;

  @IsOptional()
  @IsString()
  comment?: string;

  @IsOptional()
  @IsBoolean()
  materialized?: boolean = false;
}

export class FunctionDefinitionDto {
  @IsString()
  functionName: string;

  @IsString()
  workspaceId: string;

  @IsString()
  body: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  parameters?: string[];

  @IsOptional()
  @IsString()
  returnType?: string = 'void';

  @IsOptional()
  @IsString()
  language?: string = 'plpgsql';

  @IsOptional()
  @IsString()
  comment?: string;
}

export interface TableSchema {
  tableName: string;
  columns: {
    name: string;
    type: string;
    nullable: boolean;
    default: any;
    primary: boolean;
    unique: boolean;
    comment?: string;
  }[];
  indexes: {
    name: string;
    columns: string[];
    unique: boolean;
    primary: boolean;
  }[];
  constraints: {
    name: string;
    type: 'foreign_key' | 'check' | 'unique' | 'primary';
    columns: string[];
    referencedTable?: string;
    referencedColumns?: string[];
    definition?: string;
  }[];
  comment?: string;
}

export interface DatabaseSchema {
  tables: TableSchema[];
  views: {
    name: string;
    definition: string;
    materialized: boolean;
  }[];
  functions: {
    name: string;
    definition: string;
    returnType: string;
    parameters: string[];
  }[];
}