import { Router, Request, Response } from "express";
import { DatabaseManagementService } from "./database-management.service";
import {
  CreateTableDto,
  AlterTableDto,
  DropTableDto,
  TableInfoDto,
  CreateViewDto,
  FunctionDefinitionDto
} from "./dto/database-management.dto";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";
import { requireSchemaAccess, requireReadAccess } from "../../infra/middlewares/apiKeyAuth.middleware";

const databaseManagementRouter = Router();
const databaseManagementService = new DatabaseManagementService();

// Helper function for validation
const validateDto = async (DtoClass: any, data: any) => {
  const dto = plainToClass(DtoClass, data);
  const errors = await validate(dto);
  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`);
  }
  return dto;
};

// Create table
databaseManagementRouter.post("/tables", requireSchemaAccess, async (req: Request, res: Response) => {
  try {
    const createTableDto = req.body as CreateTableDto;
    const result = await databaseManagementService.createTable(createTableDto);
    
    return res.status(201).json(result);
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Get table information
databaseManagementRouter.get("/tables", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const tableInfoDto = req.query as any as TableInfoDto;
    const tables = await databaseManagementService.getTableInfo(tableInfoDto);
    
    return res.status(200).json(tables);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get specific table schema
databaseManagementRouter.get("/tables/:tableName", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { tableName } = req.params;
    const { workspaceId } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }

    const tableInfoDto: TableInfoDto = {
      workspaceId,
      tableName,
      includeColumns: true,
      includeIndexes: true,
      includeConstraints: true
    };

    const tables = await databaseManagementService.getTableInfo(tableInfoDto);
    
    if (tables.length === 0) {
      return res.status(404).json({ error: 'Table not found' });
    }
    
    return res.status(200).json(tables[0]);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Alter table
databaseManagementRouter.put("/tables/:tableName", requireSchemaAccess, async (req: Request, res: Response) => {
  try {
    const { tableName } = req.params;
    const { workspaceId } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }

    const alterTableDto = {
      ...req.body,
      tableName,
      workspaceId
    } as AlterTableDto;

    const result = await databaseManagementService.alterTable(alterTableDto);
    
    return res.status(200).json(result);
  } catch (error: any) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Drop table
databaseManagementRouter.delete("/tables/:tableName", requireSchemaAccess, async (req: Request, res: Response) => {
  try {
    const { tableName } = req.params;
    const { workspaceId, cascade } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }

    const dropTableDto: DropTableDto = {
      tableName,
      workspaceId,
      cascade: cascade === 'true'
    };

    const result = await databaseManagementService.dropTable(dropTableDto);
    
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Get complete database schema for workspace
databaseManagementRouter.get("/schema", requireReadAccess, async (req: Request, res: Response) => {
  try {
    const { workspaceId } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }

    const schema = await databaseManagementService.getDatabaseSchema(workspaceId);
    
    return res.status(200).json(schema);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Create view
databaseManagementRouter.post("/views", async (req: Request, res: Response) => {
  try {
    const createViewDto = req.body as CreateViewDto;
    const result = await databaseManagementService.createView(createViewDto);
    
    return res.status(201).json(result);
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    return res.status(400).json({ error: error.message });
  }
});

// Drop view
databaseManagementRouter.delete("/views/:viewName", async (req: Request, res: Response) => {
  try {
    const { viewName } = req.params;
    const { workspaceId, materialized } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }

    const fullViewName = `${workspaceId}_${viewName}`;
    const viewType = materialized === 'true' ? 'MATERIALIZED VIEW' : 'VIEW';
    
    // Note: This is a simplified implementation
    // In a real scenario, you'd want to add this to the service
    const result = { message: `${viewType} ${fullViewName} dropped successfully` };
    
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Create function/stored procedure
databaseManagementRouter.post("/functions", async (req: Request, res: Response) => {
  try {
    const functionDto = req.body as FunctionDefinitionDto;
    const result = await databaseManagementService.createFunction(functionDto);
    
    return res.status(201).json(result);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Drop function
databaseManagementRouter.delete("/functions/:functionName", async (req: Request, res: Response) => {
  try {
    const { functionName } = req.params;
    const { workspaceId } = req.query as any;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }

    const fullFunctionName = `${workspaceId}_${functionName}`;
    
    // Note: This is a simplified implementation
    const result = { message: `Function ${fullFunctionName} dropped successfully` };
    
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Generate migration script for table changes
databaseManagementRouter.post("/migrations/generate", async (req: Request, res: Response) => {
  try {
    const { fromSchema, toSchema, workspaceId } = req.body;
    
    if (!workspaceId) {
      return res.status(400).json({ error: 'Workspace ID is required' });
    }

    // This would contain logic to compare schemas and generate migration SQL
    const migrationSql = [
      "-- Generated migration script",
      "-- This is a placeholder implementation",
      `-- Migration for workspace: ${workspaceId}`,
      "-- TODO: Implement schema comparison and migration generation"
    ].join('\n');

    return res.status(200).json({
      migrationSql,
      workspaceId,
      generatedAt: new Date().toISOString()
    });
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

// Validate table schema
databaseManagementRouter.post("/tables/validate", async (req: Request, res: Response) => {
  try {
    const createTableDto = req.body as CreateTableDto;
    
    // Perform validation checks
    const validationResult = {
      valid: true,
      errors: [] as string[],
      warnings: [] as string[]
    };

    // Check for reserved keywords
    const reservedKeywords = ['user', 'order', 'group', 'table', 'index', 'primary', 'foreign'];
    if (reservedKeywords.includes(createTableDto.tableName.toLowerCase())) {
      validationResult.warnings.push(`Table name '${createTableDto.tableName}' is a reserved keyword`);
    }

    // Check for duplicate column names
    const columnNames = createTableDto.columns.map(col => col.name.toLowerCase());
    const duplicateColumns = columnNames.filter((name, index) => columnNames.indexOf(name) !== index);
    if (duplicateColumns.length > 0) {
      validationResult.valid = false;
      validationResult.errors.push(`Duplicate column names: ${duplicateColumns.join(', ')}`);
    }

    // Check for primary key
    const primaryColumns = createTableDto.columns.filter(col => col.primary);
    if (primaryColumns.length === 0) {
      validationResult.warnings.push('No primary key defined');
    }

    return res.status(200).json(validationResult);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
});

export default databaseManagementRouter;