import { ApiKeyService } from "./entity/api-key-service.entity";
import { apiKeyServiceRepository } from "infra/repository";
import IContractUseCases from "infra/contracts";
import { <PERSON>rrorHandler } from "infra/errorHandlers";

export default class ApiKeyServiceUseCases
  implements IContractUseCases<ApiKeyService>
{
  constructor() {}

  async getAll(): Promise<ApiKeyService[]> {
    try {
      return await apiKeyServiceRepository.find();
    } catch (error: unknown) {
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async getOne(id: string): Promise<ApiKeyService> {
    try {
      const apiKeyService = await apiKeyServiceRepository.findOneBy({ id });
      if (!apiKeyService) {
        throw ErrorHandler.NotFound("ApiKeyService not found");
      }
      return apiKeyService;
    } catch (error: unknown) {
      if (
        error instanceof Error &&
        error.message === "ApiKeyService not found"
      ) {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async create(data: Partial<ApiKeyService>): Promise<ApiKeyService> {
    try {
      return await apiKeyServiceRepository.save(data);
    } catch (error: unknown) {
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async update(
    id: string,
    data: Partial<ApiKeyService>
  ): Promise<ApiKeyService> {
    try {
      const apiKeyService = await this.getOne(id);
      await apiKeyServiceRepository.update(id, data);
      return { ...apiKeyService, ...data };
    } catch (error: unknown) {
      if (
        error instanceof Error &&
        error.message === "ApiKeyService not found"
      ) {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.getOne(id);
      await apiKeyServiceRepository.delete(id);
    } catch (error: unknown) {
      if (
        error instanceof Error &&
        error.message === "ApiKeyService not found"
      ) {
        throw error;
      }
      throw ErrorHandler.InternalServerError(error);
    }
  }
}
