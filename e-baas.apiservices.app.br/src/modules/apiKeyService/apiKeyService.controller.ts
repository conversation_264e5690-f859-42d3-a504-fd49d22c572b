import { Router } from "express";
import { validator } from "infra";

import ApiKeyServiceService from "modules/api-key-service/api-key-service.service";
import ApiKeyServiceUseCases from "modules/api-key-service/api-key-service.useCases";
import { ApiKeyServiceDto } from "modules/api-key-service/dto/api-key-service.dto";

const controller = Router();
const apiKeyServiceUseCases = new ApiKeyServiceUseCases();
const apiKeyServiceService = new ApiKeyServiceService(apiKeyServiceUseCases);

controller.get("/", (req, res) => apiKeyServiceService.getAll(req, res));
controller.get("/:id", (req, res) => apiKeyServiceService.getOne(req, res));
controller.post("/", validator(ApiKeyServiceDto), (req, res) =>
  apiKeyServiceService.create(req, res)
);
controller.put("/:id", validator(ApiKeyServiceDto), (req, res) =>
  apiKeyServiceService.update(req, res)
);
controller.delete("/:id", (req, res) => apiKeyServiceService.delete(req, res));

export default controller;
