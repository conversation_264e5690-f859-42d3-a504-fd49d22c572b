import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('sql_executor')
export class SqlExecutor {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  workspaceId: string;

  @Column('text')
  query: string;

  @Column('json', { nullable: true })
  parameters?: any[];

  @Column('json', { nullable: true })
  result?: any;

  @Column({ nullable: true })
  executedBy?: string;

  @Column('text', { nullable: true })
  error?: string;

  @Column({ default: 'completed' })
  status: 'pending' | 'running' | 'completed' | 'failed';

  @Column({ type: 'int', default: 0 })
  executionTime?: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}