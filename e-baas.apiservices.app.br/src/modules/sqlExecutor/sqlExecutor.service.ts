import { DataSource } from "typeorm";
import { getWorkspaceConnection } from "../../infra/database/data-source";
import { SqlExecutorDto, SqlValidationDto, SqlQueryHistoryDto, SqlQueryType } from "../sql-executor/dto/sqlExecutor.dto";

interface SqlExecutionResult {
  success: boolean;
  data?: any[];
  rowCount?: number;
  executionTime: number;
  queryType: string;
  error?: string;
  warnings?: string[];
}

interface SqlQueryHistory {
  id: string;
  query: string;
  workspaceId: string;
  userId?: string;
  executionTime: number;
  success: boolean;
  error?: string;
  executedAt: Date;
}

export class SqlExecutorService {
  private queryHistory: Map<string, SqlQueryHistory[]> = new Map();
  private readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private readonly MAX_ROWS = 10000; // Maximum rows to return

  private async getConnection(workspaceId: string): Promise<DataSource> {
    const config = {
      type: "postgres" as const,
      host: process.env.DB_HOST || "localhost",
      port: parseInt(process.env.DB_PORT || "5432"),
      username: process.env.DB_USERNAME || "postgres",
      password: process.env.DB_PASSWORD || "postgres",
      database: `workspace_${workspaceId}`,
      synchronize: false,
      logging: false,
    };

    return await getWorkspaceConnection(workspaceId, config);
  }

  async executeQuery(sqlDto: SqlExecutorDto): Promise<SqlExecutionResult> {
    const startTime = Date.now();
    let connection: DataSource | null = null;
    
    try {
      // Validate SQL query first
      const validation = await this.validateQuery({ query: sqlDto.query });
      if (!validation.valid) {
        throw new Error(`Invalid SQL: ${validation.errors?.join(', ')}`);
      }

      connection = await this.getConnection(sqlDto.workspaceId);
      const queryRunner = connection.createQueryRunner();

      try {
        await queryRunner.connect();

        // Set timeout if specified
        const timeout = sqlDto.timeout || this.DEFAULT_TIMEOUT;
        
        // For readonly queries, start a transaction and set read-only
        if (sqlDto.readonly || this.isReadOnlyQuery(sqlDto.query)) {
          await queryRunner.startTransaction('READ COMMITTED');
          await queryRunner.query('SET TRANSACTION READ ONLY');
        }

        let result: any;
        
        if (sqlDto.explainPlan) {
          // Execute EXPLAIN for query analysis
          result = await this.executeWithTimeout(
            queryRunner.query(`EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${sqlDto.query}`, sqlDto.params),
            timeout
          );
        } else {
          // Execute the actual query
          result = await this.executeWithTimeout(
            queryRunner.query(sqlDto.query, sqlDto.params),
            timeout
          );
        }

        const executionTime = Date.now() - startTime;
        const queryType = this.getQueryType(sqlDto.query);

        // Limit result size for SELECT queries
        if (Array.isArray(result) && result.length > this.MAX_ROWS) {
          result = result.slice(0, this.MAX_ROWS);
        }

        const executionResult: SqlExecutionResult = {
          success: true,
          data: result,
          rowCount: Array.isArray(result) ? result.length : result?.rowCount || 0,
          executionTime,
          queryType,
          warnings: result?.length === this.MAX_ROWS ? [`Result limited to ${this.MAX_ROWS} rows`] : undefined
        };

        // Log query to history
        await this.logQueryHistory({
          query: sqlDto.query,
          workspaceId: sqlDto.workspaceId,
          userId: sqlDto.userId,
          executionTime,
          success: true
        });

        return executionResult;

      } finally {
        await queryRunner.release();
      }

    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      
      // Log failed query to history
      await this.logQueryHistory({
        query: sqlDto.query,
        workspaceId: sqlDto.workspaceId,
        userId: sqlDto.userId,
        executionTime,
        success: false,
        error: error.message
      });

      return {
        success: false,
        executionTime,
        queryType: this.getQueryType(sqlDto.query),
        error: error.message
      };
    }
  }

  async validateQuery(validationDto: SqlValidationDto): Promise<{ valid: boolean; errors?: string[] }> {
    const query = validationDto.query.trim().toLowerCase();
    const errors: string[] = [];

    // Basic SQL injection prevention
    const dangerousPatterns = [
      /;\s*(drop|delete|truncate|alter)\s+/i,
      /union\s+select/i,
      /'\s*or\s*'1'\s*=\s*'1/i,
      /--\s*$/,
      /\/\*.*\*\//,
      /xp_cmdshell/i,
      /sp_executesql/i
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(query)) {
        errors.push(`Potentially dangerous SQL pattern detected: ${pattern.source}`);
      }
    }

    // Check for multiple statements (basic check)
    if (query.includes(';') && !query.match(/;\s*$/)) {
      errors.push('Multiple SQL statements are not allowed');
    }

    // Validate basic SQL syntax
    if (!this.isValidSqlSyntax(query)) {
      errors.push('Invalid SQL syntax');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    };
  }

  async getQueryHistory(historyDto: SqlQueryHistoryDto): Promise<SqlQueryHistory[]> {
    const workspaceHistory = this.queryHistory.get(historyDto.workspaceId) || [];
    
    let filteredHistory = workspaceHistory;
    
    if (historyDto.userId) {
      filteredHistory = filteredHistory.filter(h => h.userId === historyDto.userId);
    }

    // Sort by execution time (most recent first)
    filteredHistory.sort((a, b) => b.executedAt.getTime() - a.executedAt.getTime());

    // Apply pagination
    const offset = historyDto.offset || 0;
    const limit = historyDto.limit || 50;
    
    return filteredHistory.slice(offset, offset + limit);
  }

  private async executeWithTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Query timeout after ${timeout}ms`)), timeout);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  private isReadOnlyQuery(query: string): boolean {
    const readOnlyPattern = /^\s*(select|with|explain|show|describe|desc)\s+/i;
    return readOnlyPattern.test(query.trim());
  }

  private getQueryType(query: string): string {
    const trimmed = query.trim().toLowerCase();
    
    if (trimmed.startsWith('select') || trimmed.startsWith('with')) return SqlQueryType.SELECT;
    if (trimmed.startsWith('insert')) return SqlQueryType.INSERT;
    if (trimmed.startsWith('update')) return SqlQueryType.UPDATE;
    if (trimmed.startsWith('delete')) return SqlQueryType.DELETE;
    if (trimmed.startsWith('create')) return SqlQueryType.CREATE;
    if (trimmed.startsWith('alter')) return SqlQueryType.ALTER;
    if (trimmed.startsWith('drop')) return SqlQueryType.DROP;
    if (trimmed.startsWith('truncate')) return SqlQueryType.TRUNCATE;
    
    return 'UNKNOWN';
  }

  private isValidSqlSyntax(query: string): boolean {
    // Basic SQL syntax validation
    const sqlKeywords = /^(select|insert|update|delete|create|alter|drop|truncate|with|explain|show|describe|desc)\s+/i;
    return sqlKeywords.test(query.trim());
  }

  private async logQueryHistory(historyData: {
    query: string;
    workspaceId: string;
    userId?: string;
    executionTime: number;
    success: boolean;
    error?: string;
  }): Promise<void> {
    const historyEntry: SqlQueryHistory = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ...historyData,
      executedAt: new Date()
    };

    if (!this.queryHistory.has(historyData.workspaceId)) {
      this.queryHistory.set(historyData.workspaceId, []);
    }

    const workspaceHistory = this.queryHistory.get(historyData.workspaceId)!;
    workspaceHistory.push(historyEntry);

    // Keep only last 1000 queries per workspace
    if (workspaceHistory.length > 1000) {
      workspaceHistory.splice(0, workspaceHistory.length - 1000);
    }
  }

  async explainQuery(sqlDto: SqlExecutorDto): Promise<any> {
    const explainDto = { ...sqlDto, explainPlan: true };
    return this.executeQuery(explainDto);
  }

  async getQueryStats(workspaceId: string): Promise<any> {
    const history = this.queryHistory.get(workspaceId) || [];
    
    const stats = {
      totalQueries: history.length,
      successfulQueries: history.filter(h => h.success).length,
      failedQueries: history.filter(h => !h.success).length,
      averageExecutionTime: history.length > 0 
        ? history.reduce((sum, h) => sum + h.executionTime, 0) / history.length 
        : 0,
      queryTypeDistribution: this.getQueryTypeDistribution(history),
      recentErrors: history
        .filter(h => !h.success)
        .slice(0, 10)
        .map(h => ({ query: h.query, error: h.error, executedAt: h.executedAt }))
    };

    return stats;
  }

  private getQueryTypeDistribution(history: SqlQueryHistory[]): Record<string, number> {
    const distribution: Record<string, number> = {};
    
    history.forEach(h => {
      const type = this.getQueryType(h.query);
      distribution[type] = (distribution[type] || 0) + 1;
    });

    return distribution;
  }
}