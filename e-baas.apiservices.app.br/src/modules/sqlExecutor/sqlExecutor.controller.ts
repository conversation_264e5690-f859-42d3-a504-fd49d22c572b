import { Router, Request, Response } from "express";
import { SqlExecutorService } from "./sqlExecutor.service";
import { SqlExecutorDto, SqlValidationDto, SqlQueryHistoryDto } from "../sql-executor/dto/sqlExecutor.dto";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";

const sqlExecutorRouter = Router();
const sqlExecutorService = new SqlExecutorService();

// POST /execute - Execute SQL query
sqlExecutorRouter.post("/execute", async (req: Request, res: Response) => {
  try {
    const { query, workspaceId, params, readonly, timeout, userId } = req.body;

    const sqlDto = plainToClass(SqlExecutorDto, {
      query,
      workspaceId,
      params,
      readonly,
      timeout,
      userId
    });

    const errors = await validate(sqlDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await sqlExecutorService.executeQuery(sqlDto);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// POST /validate - Validate SQL query without executing
sqlExecutorRouter.post("/validate", async (req: Request, res: Response) => {
  try {
    const { query, strict } = req.body;

    const validationDto = plainToClass(SqlValidationDto, {
      query,
      strict
    });

    const errors = await validate(validationDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await sqlExecutorService.validateQuery(validationDto);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// POST /explain - Explain query execution plan
sqlExecutorRouter.post("/explain", async (req: Request, res: Response) => {
  try {
    const { query, workspaceId, params, userId } = req.body;

    const sqlDto = plainToClass(SqlExecutorDto, {
      query,
      workspaceId,
      params,
      explainPlan: true,
      readonly: true,
      userId
    });

    const errors = await validate(sqlDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await sqlExecutorService.explainQuery(sqlDto);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// GET /history - Get query execution history
sqlExecutorRouter.get("/history", async (req: Request, res: Response) => {
  try {
    const { workspaceId, userId, limit, offset } = req.query as any;

    const historyDto = plainToClass(SqlQueryHistoryDto, {
      workspaceId,
      userId,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined
    });

    const errors = await validate(historyDto);
    if (errors.length > 0) {
      return res.status(400).json({ error: "Validation failed", details: errors });
    }

    const result = await sqlExecutorService.getQueryHistory(historyDto);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// GET /stats/:workspaceId - Get query execution statistics
sqlExecutorRouter.get("/stats/:workspaceId", async (req: Request, res: Response) => {
  try {
    const { workspaceId } = req.params;

    if (!workspaceId) {
      return res.status(400).json({ error: "Workspace ID is required" });
    }

    const result = await sqlExecutorService.getQueryStats(workspaceId);
    
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

// POST /batch - Execute multiple queries in batch
sqlExecutorRouter.post("/batch", async (req: Request, res: Response) => {
  try {
    const { queries, workspaceId, userId } = req.body;

    if (!Array.isArray(queries)) {
      return res.status(400).json({ error: "Queries must be an array" });
    }

    const results = [];
    
    for (const queryData of queries) {
      const sqlDto = plainToClass(SqlExecutorDto, {
        query: queryData.query,
        workspaceId,
        params: queryData.params,
        readonly: queryData.readonly,
        timeout: queryData.timeout,
        userId
      });

      const errors = await validate(sqlDto);
      if (errors.length > 0) {
        results.push({ error: "Validation failed", details: errors });
        continue;
      }

      try {
        const result = await sqlExecutorService.executeQuery(sqlDto);
        results.push(result);
      } catch (error: any) {
        results.push({ error: error.message });
      }
    }
    
    return res.json({ results });
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
});

export default sqlExecutorRouter;