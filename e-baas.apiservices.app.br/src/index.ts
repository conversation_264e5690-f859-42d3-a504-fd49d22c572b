import cors from "cors";
import { AppDataSource } from "./infra/database/data-source";
import dotenv from "dotenv";
import express, { Express } from "express";
import { createServer } from "http";
import userRouter from "./modules/users/user.controller";
import authRouter from "./modules/auth/auth.controller";
import postgrestRouter from "./modules/postgrest/postgrest.controller";
import sqlExecutorRouter from "./modules/sqlExecutor/sqlExecutor.controller";
import rlsPoliciesRouter from "./modules/rlsPolicies";
import apiKeysRouter from "./modules/api-keys/api-key.controller";
import databaseManagementRouter from "./modules/database-management/database-management.controller";
import storageRouter from "./modules/storage/storage.controller";
import realtimeRouter from "./modules/realtime/realtime.controller";
import queuesRouter from "./modules/queues";
import mcpRouter from "./modules/mcp";
import { edgeFunctionsRouter } from "./modules/edge-functions";
import adminRouter from "./modules/admin/admin.controller";
import WebSocketServer from "./modules/realtime/websocket.server";
import { QueuesUseCases } from "./modules/queues/queues.useCases";

dotenv.config();

const app: Express = express();
app.use(express.json());
app.use(
  cors({
    origin: "*",
  })
);

// Auth routes
app.use("/auth/v1", authRouter);

// API routes
app.use("/api/users", userRouter);
app.use("/rest/v1", postgrestRouter);
app.use("/sql/v1", sqlExecutorRouter);
app.use("/rls/v1", rlsPoliciesRouter);
app.use("/api-keys/v1", apiKeysRouter);
app.use("/database/v1", databaseManagementRouter);
app.use("/storage/v1", storageRouter);
app.use("/realtime/v1", realtimeRouter);
app.use("/queues/v1", queuesRouter);
app.use("/mcp/v1", mcpRouter);
app.use("/functions/v1", edgeFunctionsRouter);
app.use("/admin/v1", adminRouter);

app.use("/", (req, res) => {
  res.json({ message: "Api Status Ok" });
});

// Create HTTP server
const httpServer = createServer(app);

// Initialize WebSocket server and Queue consumers
let webSocketServer: WebSocketServer;
let queuesUseCases: QueuesUseCases;

AppDataSource.initialize()
  .then(async () => {
    console.log("Data source has been initialized!");

    // Initialize WebSocket server after database connection
    webSocketServer = new WebSocketServer(httpServer);
    console.log("WebSocket server initialized!");

    // Initialize queue consumers
    try {
      console.log("Initializing queue consumers...");
      queuesUseCases = new QueuesUseCases();
      await queuesUseCases.setupDefaultConsumers();
      console.log("Queue consumers initialized!");
    } catch (error) {
      console.error("Failed to initialize queue consumers:", error);
      console.log("Continuing without queue consumers...");
    }

    const PORT = process.env.PORT || 4000;
    httpServer.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log(
        `WebSocket endpoint: ws://localhost:${PORT}/realtime/socket.io`
      );
      console.log(`Queue API endpoint: http://localhost:${PORT}/queues/v1`);
      console.log(`MCP API endpoint: http://localhost:${PORT}/mcp/v1`);
    });
  })
  .catch((error: Error) => console.log(error));

export default app;
