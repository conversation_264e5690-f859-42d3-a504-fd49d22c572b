import cors from "cors";
import { AppDataSource } from "./infra/database/data-source";
import dotenv from "dotenv";
import express, { Express } from "express";
import { createServer } from "http";
import userRouter from "./modules/users/user.controller";
import authRouter from "./modules/auth/auth.controller";
import postgrestRouter from "./modules/postgrest/postgrest.controller";
import sqlExecutorRouter from "./modules/sqlExecutor/sqlExecutor.controller";
import rlsPoliciesRouter from "./modules/rlsPolicies";
import apiKeysRouter from "./modules/api-keys/api-key.controller";
import databaseManagementRouter from "./modules/database-management/database-management.controller";
import storageRouter from "./modules/storage/storage.controller";
import realtimeRouter from "./modules/realtime/realtime.controller";
import queuesRouter from "./modules/queues";
import mcpRouter from "./modules/mcp";
import { edgeFunctionsRouter } from "./modules/edge-functions";
import adminRouter from "./modules/admin/admin.controller";

dotenv.config();

const app: Express = express();
app.use(express.json());
app.use(
  cors({
    origin: "*",
  })
);

// Basic health check endpoint
app.get("/", (req, res) => {
  res.json({ 
    message: "E-BaaS Backend Server Running", 
    timestamp: new Date().toISOString(),
    status: "OK",
    version: "1.0.0",
    database: AppDataSource.isInitialized ? "connected" : "disconnected"
  });
});

// Auth routes
app.use("/auth/v1", authRouter);

// API routes  
app.use("/api/users", userRouter);
app.use("/rest/v1", postgrestRouter);
app.use("/sql/v1", sqlExecutorRouter);
app.use("/rls/v1", rlsPoliciesRouter);
app.use("/api-keys/v1", apiKeysRouter);
app.use("/database/v1", databaseManagementRouter);
app.use("/storage/v1", storageRouter);
app.use("/realtime/v1", realtimeRouter);
app.use("/queues/v1", queuesRouter);
app.use("/mcp/v1", mcpRouter);
app.use("/functions/v1", edgeFunctionsRouter);
app.use("/admin/v1", adminRouter);

// Create HTTP server
const httpServer = createServer(app);

AppDataSource.initialize()
  .then(async () => {
    console.log("✅ Database connection initialized successfully!");

    const PORT = process.env.PORT || 3000;
    httpServer.listen(PORT, () => {
      console.log(`🚀 E-BaaS Backend Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/`);
      console.log(`🔐 Auth endpoint: http://localhost:${PORT}/auth/v1`);
      console.log(`💾 Database endpoint: http://localhost:${PORT}/rest/v1`);
      console.log(`⚡ SQL endpoint: http://localhost:${PORT}/sql/v1`);
      console.log("✨ Full E-BaaS backend server is ready!");
    });
  })
  .catch((error: Error) => {
    console.error("❌ Failed to initialize database:", error);
    
    // Fallback: start server without database for debugging
    const PORT = process.env.PORT || 3000;
    httpServer.listen(PORT, () => {
      console.log(`⚠️  E-BaaS Backend Server running on port ${PORT} (WITHOUT DATABASE)`);
      console.log(`📊 Health check: http://localhost:${PORT}/`);
      console.log("❌ Database connection failed - some features may not work");
    });
  });

export default app;