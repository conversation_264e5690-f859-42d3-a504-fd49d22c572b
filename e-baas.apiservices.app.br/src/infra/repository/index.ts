import { AppDataSource } from "../database/data-source";

import { User } from "../../modules/users/entity/user.entity";
import { Workspace } from "../../modules/workspaces/entity/Workspace.entity";
import { ApiKey } from "../../modules/api-keys/entity/ApiKey.entity";
import { DatabaseConfig } from "../../modules/database-configs/entity/DatabaseConfig.entity";
import { RefreshToken } from "../../modules/auth/entity/RefreshToken.entity";

// Repositórios principais da aplicação
export const userRepository = AppDataSource.getRepository(User);
export const workspaceRepository = AppDataSource.getRepository(Workspace);
export const apiKeyRepository = AppDataSource.getRepository(ApiKey);
export const databaseConfigRepository =
  AppDataSource.getRepository(DatabaseConfig);
export const refreshTokenRepository = AppDataSource.getRepository(RefreshToken);

import { Postgrest } from "../../modules/postgrest/entity/postgrest.entity";
export const postgrestRepository = AppDataSource.getRepository(Postgrest);
import { SqlExecutor } from "../../modules/sql-executor/entity/sqlExecutor.entity";
export const sqlExecutorRepository = AppDataSource.getRepository(SqlExecutor);
import { RlsPolicies } from "../../modules/rls-policies/entity/rlsPolicies.entity";
export const rlsPoliciesRepository = AppDataSource.getRepository(RlsPolicies);
import { ApiKeyService } from "../../modules/api-key-service/entity/apiKeyService.entity";
export const apiKeyServiceRepository =
  AppDataSource.getRepository(ApiKeyService);
