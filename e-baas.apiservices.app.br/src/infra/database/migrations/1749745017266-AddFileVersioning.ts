import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class AddFileVersioning1749745017266 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add versioning columns to storage_files table
        await queryRunner.query(`
            ALTER TABLE storage_files 
            ADD COLUMN versioning_enabled BOOLEAN DEFAULT FALSE,
            ADD COLUMN current_version INTEGER DEFAULT 1,
            ADD COLUMN total_versions INTEGER DEFAULT 1
        `);

        // Create file_versions table
        await queryRunner.createTable(
            new Table({
                name: "file_versions",
                columns: [
                    {
                        name: "id",
                        type: "uuid",
                        isPrimary: true,
                        generationStrategy: "uuid",
                        default: "uuid_generate_v4()"
                    },
                    {
                        name: "file_id",
                        type: "varchar",
                        length: "255"
                    },
                    {
                        name: "version_number",
                        type: "integer"
                    },
                    {
                        name: "version_hash",
                        type: "varchar",
                        length: "64",
                        isUnique: true
                    },
                    {
                        name: "size",
                        type: "bigint"
                    },
                    {
                        name: "mime_type",
                        type: "varchar",
                        length: "100"
                    },
                    {
                        name: "etag",
                        type: "varchar",
                        length: "100"
                    },
                    {
                        name: "storage_path",
                        type: "varchar",
                        length: "1000"
                    },
                    {
                        name: "status",
                        type: "enum",
                        enum: ["active", "archived", "deleted"],
                        default: "'active'"
                    },
                    {
                        name: "type",
                        type: "enum",
                        enum: ["manual", "auto", "rollback"],
                        default: "'auto'"
                    },
                    {
                        name: "is_latest",
                        type: "boolean",
                        default: false
                    },
                    {
                        name: "metadata",
                        type: "json",
                        isNullable: true
                    },
                    {
                        name: "changelog",
                        type: "text",
                        isNullable: true
                    },
                    {
                        name: "created_by",
                        type: "varchar",
                        length: "255",
                        isNullable: true
                    },
                    {
                        name: "checksum",
                        type: "varchar",
                        length: "128",
                        isNullable: true
                    },
                    {
                        name: "compression_type",
                        type: "varchar",
                        length: "50",
                        isNullable: true
                    },
                    {
                        name: "original_size",
                        type: "bigint",
                        isNullable: true
                    },
                    {
                        name: "expires_at",
                        type: "timestamp",
                        isNullable: true
                    },
                    {
                        name: "created_at",
                        type: "timestamp",
                        default: "CURRENT_TIMESTAMP"
                    }
                ]
            }),
            true
        );

        // Create indexes for file_versions table
        await queryRunner.query(`
            CREATE UNIQUE INDEX IDX_file_versions_file_id_version_number 
            ON file_versions (file_id, version_number)
        `);

        await queryRunner.query(`
            CREATE INDEX IDX_file_versions_file_id_status 
            ON file_versions (file_id, status)
        `);

        await queryRunner.query(`
            CREATE INDEX IDX_file_versions_file_id_is_latest 
            ON file_versions (file_id, is_latest)
        `);

        await queryRunner.query(`
            CREATE INDEX IDX_file_versions_created_at 
            ON file_versions (created_at)
        `);

        await queryRunner.query(`
            CREATE INDEX IDX_file_versions_size 
            ON file_versions (size)
        `);

        await queryRunner.query(`
            CREATE INDEX IDX_file_versions_file_id 
            ON file_versions (file_id)
        `);

        // Add foreign key constraint
        await queryRunner.query(`
            ALTER TABLE file_versions 
            ADD CONSTRAINT FK_file_versions_file_id 
            FOREIGN KEY (file_id) REFERENCES storage_files(id) 
            ON DELETE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes first
        await queryRunner.query(`DROP INDEX IF EXISTS IDX_file_versions_file_id_version_number`);
        await queryRunner.query(`DROP INDEX IF EXISTS IDX_file_versions_file_id_status`);
        await queryRunner.query(`DROP INDEX IF EXISTS IDX_file_versions_file_id_is_latest`);
        await queryRunner.query(`DROP INDEX IF EXISTS IDX_file_versions_created_at`);
        await queryRunner.query(`DROP INDEX IF EXISTS IDX_file_versions_size`);
        await queryRunner.query(`DROP INDEX IF EXISTS IDX_file_versions_file_id`);

        // Drop foreign key constraint
        await queryRunner.query(`ALTER TABLE file_versions DROP CONSTRAINT IF EXISTS FK_file_versions_file_id`);

        // Drop file_versions table
        await queryRunner.dropTable("file_versions");

        // Remove versioning columns from storage_files table
        await queryRunner.query(`
            ALTER TABLE storage_files 
            DROP COLUMN IF EXISTS versioning_enabled,
            DROP COLUMN IF EXISTS current_version,
            DROP COLUMN IF EXISTS total_versions
        `);
    }

}
