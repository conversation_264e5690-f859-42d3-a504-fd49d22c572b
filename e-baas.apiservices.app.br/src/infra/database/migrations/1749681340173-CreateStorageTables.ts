import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateStorageTables1749681340173 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create storage_buckets table
        await queryRunner.query(`
            CREATE TABLE "storage_buckets" (
                "id" varchar PRIMARY KEY NOT NULL,
                "name" varchar(100) NOT NULL,
                "workspace_id" varchar(255) NOT NULL,
                "visibility" varchar CHECK( visibility IN ('public','private') ) NOT NULL DEFAULT ('private'),
                "description" text,
                "allowed_mime_types" text,
                "max_file_size" bigint,
                "enable_versioning" boolean NOT NULL DEFAULT (0),
                "enable_cors" boolean NOT NULL DEFAULT (1),
                "cors_origins" text,
                "files_count" integer NOT NULL DEFAULT (0),
                "total_size" bigint NOT NULL DEFAULT (0),
                "created_by" varchar(255),
                "is_active" boolean NOT NULL DEFAULT (1),
                "created_at" datetime NOT NULL DEFAULT (datetime('now')),
                "updated_at" datetime NOT NULL DEFAULT (datetime('now'))
            )
        `);

        // Create storage_files table
        await queryRunner.query(`
            CREATE TABLE "storage_files" (
                "id" varchar PRIMARY KEY NOT NULL,
                "name" varchar(255) NOT NULL,
                "path" varchar(500) NOT NULL,
                "bucket_id" varchar(255) NOT NULL,
                "workspace_id" varchar(255) NOT NULL,
                "size" bigint NOT NULL,
                "mime_type" varchar(100) NOT NULL,
                "etag" varchar(100) NOT NULL,
                "cache_control" varchar(100),
                "metadata" text,
                "version" varchar(50),
                "storage_path" varchar(1000) NOT NULL,
                "is_public" boolean NOT NULL DEFAULT (0),
                "owner_id" varchar(255),
                "last_accessed_at" datetime,
                "access_count" integer NOT NULL DEFAULT (0),
                "is_deleted" boolean NOT NULL DEFAULT (0),
                "deleted_at" datetime,
                "created_at" datetime NOT NULL DEFAULT (datetime('now')),
                "updated_at" datetime NOT NULL DEFAULT (datetime('now')),
                FOREIGN KEY ("bucket_id") REFERENCES "storage_buckets" ("id") ON DELETE CASCADE
            )
        `);

        // Create indexes for storage_buckets
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_storage_buckets_workspace_name" ON "storage_buckets" ("workspace_id", "name")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_storage_buckets_workspace" ON "storage_buckets" ("workspace_id")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_storage_buckets_active" ON "storage_buckets" ("is_active")
        `);

        // Create indexes for storage_files
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_storage_files_bucket_path" ON "storage_files" ("bucket_id", "path")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_storage_files_bucket" ON "storage_files" ("bucket_id")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_storage_files_workspace" ON "storage_files" ("workspace_id")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_storage_files_mime_type" ON "storage_files" ("mime_type")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_storage_files_created_at" ON "storage_files" ("created_at")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_storage_files_deleted" ON "storage_files" ("is_deleted")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_storage_files_owner" ON "storage_files" ("owner_id")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_storage_files_owner"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_files_deleted"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_files_created_at"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_files_mime_type"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_files_workspace"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_files_bucket"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_files_bucket_path"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_buckets_active"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_buckets_workspace"`);
        await queryRunner.query(`DROP INDEX "IDX_storage_buckets_workspace_name"`);

        // Drop tables
        await queryRunner.query(`DROP TABLE "storage_files"`);
        await queryRunner.query(`DROP TABLE "storage_buckets"`);
    }

}
