import { MigrationInterface, QueryRunner } from "typeorm";
import bcrypt from "bcryptjs";

export class CreateSuperAdminUser1749681709877 implements MigrationInterface {
  name = "CreateSuperAdminUser1749681709877";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Verificar se o usuário super admin já existe
    const existingUser = await queryRunner.query(`
      SELECT id FROM "users"
      WHERE email = '<EMAIL>'
      AND workspace_id = 'default'
    `);

    if (existingUser.length > 0) {
      console.log("Super admin user already exists, skipping creation...");
      return;
    }

    // Hash da senha
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash("E-b@@s*2025", saltRounds);

    // Detectar o tipo de banco de dados
    const dbType = queryRunner.connection.options.type;

    let insertQuery: string;
    let idValue: string;
    let timestampValue: string;

    if (dbType === "sqlite") {
      idValue = "lower(hex(randomblob(16)))";
      timestampValue = "datetime('now')";
    } else if (dbType === "postgres") {
      idValue = "gen_random_uuid()";
      timestampValue = "NOW()";
    } else if (dbType === "mysql") {
      idValue = "UUID()";
      timestampValue = "NOW()";
    } else {
      // Fallback para UUID manual
      const { v4: uuidv4 } = await import("uuid");
      idValue = `'${uuidv4()}'`;
      timestampValue = "NOW()";
    }

    // Inserir o usuário super admin
    insertQuery = `
      INSERT INTO "users" (
        id,
        email,
        password,
        first_name,
        last_name,
        workspace_id,
        role,
        admin_role,
        auth_provider,
        is_email_verified,
        email_verified,
        is_active,
        is_anonymous,
        sign_in_count,
        confirmed_at,
        created_at,
        updated_at,
        raw_app_meta_data,
        raw_user_meta_data
      ) VALUES (
        ${idValue},
        '<EMAIL>',
        '${hashedPassword}',
        'Super',
        'Admin',
        'default',
        'admin',
        'platform_admin',
        'email',
        true,
        true,
        true,
        false,
        0,
        ${timestampValue},
        ${timestampValue},
        ${timestampValue},
        '{"role": "platform_admin", "created_by": "system"}',
        '{"name": "Super Admin", "created_by": "migration"}'
      )
    `;

    await queryRunner.query(insertQuery);

    console.log("✅ Super admin user created successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: E-b@@s*2025");
    console.log("👑 Role: platform_admin");
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remover o usuário super admin
    await queryRunner.query(`
      DELETE FROM "users" 
      WHERE email = '<EMAIL>' 
      AND workspace_id = 'default'
      AND admin_role = 'platform_admin'
    `);

    console.log("Super admin user removed.");
  }
}
