import { MigrationInterface, QueryRunner } from "typeorm";

export class Add<PERSON>agic<PERSON>inkFields1749681709876 implements MigrationInterface {
    name = 'AddMagicLinkFields1749681709876'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" 
            ADD COLUMN "magic_link_token" varchar(500), 
            ADD COLUMN "magic_link_expires" timestamp
        `);
        
        await queryRunner.query(`
            CREATE INDEX "IDX_user_magic_link_token" ON "users" ("magic_link_token") 
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "IDX_user_magic_link_token"`);
        await queryRunner.query(`
            ALTER TABLE "users" 
            DROP COLUMN "magic_link_expires", 
            DROP COLUMN "magic_link_token"
        `);
    }
}