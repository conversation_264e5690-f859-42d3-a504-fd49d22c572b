import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import config from "config";
import { userRepository, apiKeyRepository } from "../repository";
import { AuthService } from "../../modules/auth/auth.service";

export interface AuthRequest extends Request {
  user?: {
    id: string;
    email: string;
    workspaceId: string;
    role: string;
    permissions?: string[];
    iat?: number;
    exp?: number;
  };
  workspace?: {
    id: string;
    name?: string;
  };
  session?: {
    id: string;
    isActive: boolean;
  };
}

export const authenticateJWT = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res
        .status(401)
        .json({ error: "Authorization header is required" });
    }

    const parts = authHeader.split(" ");

    if (parts.length !== 2) {
      return res.status(401).json({ error: "Token error" });
    }

    const [scheme, token] = parts;

    if (!/^Bearer$/i.test(scheme)) {
      return res.status(401).json({ error: "Token malformatted" });
    }

    try {
      const authService = new AuthService();
      const decoded = authService.verifyAccessToken(token);

      // Verificar se o usuário existe e está ativo
      const user = await userRepository.findOne({
        where: { id: decoded.sub },
      });

      if (!user) {
        return res.status(401).json({ error: "User not found" });
      }

      if (!user.isActive) {
        return res.status(401).json({ error: "User is inactive" });
      }

      if (user.isBanned) {
        return res.status(401).json({ error: "User is banned" });
      }

      // Adiciona o usuário completo ao objeto de requisição
      req.user = {
        id: decoded.sub,
        email: decoded.email,
        workspaceId: decoded.workspaceId || user.workspaceId,
        role: decoded.role || user.role,
        permissions: decoded.permissions || [],
        iat: decoded.iat,
        exp: decoded.exp,
      };

      // Adiciona workspace info se disponível
      if (decoded.workspaceId) {
        req.workspace = {
          id: decoded.workspaceId,
        };
      }

      return next();
    } catch (jwtError) {
      return res.status(401).json({ error: "Invalid or expired token" });
    }
  } catch (error) {
    console.error("Auth middleware error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const authenticateApiKey = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const apiKey = req.headers["x-api-key"] as string;

    if (!apiKey) {
      return res.status(401).json({ error: "API Key is required" });
    }

    // Buscar API Key no banco de dados
    const apiKeyEntity = await apiKeyRepository.findOne({
      where: { apiKey, isActive: true },
      relations: ["workspace"],
    });

    if (!apiKeyEntity) {
      return res.status(401).json({ error: "Invalid API Key" });
    }

    // Verificar se o workspace está ativo
    if (!apiKeyEntity.workspace.isActive) {
      return res.status(401).json({ error: "Workspace is inactive" });
    }

    // Verificar se a API Key está expirada
    if (apiKeyEntity.expiresAt && new Date() > apiKeyEntity.expiresAt) {
      return res.status(401).json({ error: "API Key expired" });
    }

    // Adiciona o workspace ao objeto de requisição
    req.workspace = {
      id: apiKeyEntity.workspaceId,
    };

    return next();
  } catch (error) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Middleware para verificar permissões específicas
export const requirePermission = (permission: string) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      // Se não há usuário autenticado, retorna erro
      if (!req.user) {
        return res.status(401).json({ error: "Authentication required" });
      }

      // Se o usuário tem permissões e a permissão específica, permite
      if (req.user.permissions && req.user.permissions.includes(permission)) {
        return next();
      }

      // Se não tem a permissão específica, retorna erro
      return res
        .status(403)
        .json({ error: `Permission '${permission}' required` });
    } catch (error) {
      return res.status(500).json({ error: "Internal server error" });
    }
  };
};
