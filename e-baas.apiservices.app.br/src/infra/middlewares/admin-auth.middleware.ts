import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import config from 'config';

export interface AdminRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
    adminRole?: string;
    workspaceId: string;
    permissions: string[];
    isPlatformAdmin: boolean;
    isWorkspaceAdmin: boolean;
    canAccessAdmin: boolean;
  };
}

export function requireAdmin(req: AdminRequest, res: Response, next: NextFunction) {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authorization token required' });
    }

    const token = authHeader.substring(7);
    const jwtSecret = config.get<string>('jwt.secret');
    
    const decoded = jwt.verify(token, jwtSecret) as any;
    
    // Check if user can access admin interface
    if (!decoded.can_access_admin) {
      return res.status(403).json({ 
        error: 'Admin access required',
        details: 'User does not have admin privileges'
      });
    }

    req.user = {
      id: decoded.user_id || decoded.sub,
      email: decoded.email,
      role: decoded.role,
      adminRole: decoded.admin_role,
      workspaceId: decoded.workspaceId || decoded.workspace_id,
      permissions: decoded.permissions || [],
      isPlatformAdmin: decoded.is_platform_admin || false,
      isWorkspaceAdmin: decoded.is_workspace_admin || false,
      canAccessAdmin: decoded.can_access_admin || false
    };

    next();
  } catch (error) {
    return res.status(401).json({ 
      error: 'Invalid or expired token',
      details: error instanceof Error ? error.message : 'Token verification failed'
    });
  }
}

export function requirePlatformAdmin(req: AdminRequest, res: Response, next: NextFunction) {
  requireAdmin(req, res, () => {
    if (!req.user?.isPlatformAdmin) {
      return res.status(403).json({ 
        error: 'Platform admin access required',
        details: 'This operation requires platform administrator privileges'
      });
    }
    next();
  });
}

export function requireWorkspaceAdmin(req: AdminRequest, res: Response, next: NextFunction) {
  requireAdmin(req, res, () => {
    if (!req.user?.isWorkspaceAdmin && !req.user?.isPlatformAdmin) {
      return res.status(403).json({ 
        error: 'Workspace admin access required',
        details: 'This operation requires workspace administrator privileges'
      });
    }
    next();
  });
}

export function requirePermission(permission: string) {
  return (req: AdminRequest, res: Response, next: NextFunction) => {
    requireAdmin(req, res, () => {
      if (!req.user?.permissions.includes(permission) && !req.user?.isPlatformAdmin) {
        return res.status(403).json({ 
          error: 'Insufficient permissions',
          details: `Operation requires permission: ${permission}`
        });
      }
      next();
    });
  };
}

export function scopeToWorkspace(req: AdminRequest, res: Response, next: NextFunction) {
  requireAdmin(req, res, () => {
    // Platform admins can access any workspace
    if (req.user?.isPlatformAdmin) {
      return next();
    }

    // Workspace admins are restricted to their workspace
    const requestedWorkspaceId = req.params.workspaceId || req.query.workspaceId || req.body.workspaceId;
    
    if (requestedWorkspaceId && requestedWorkspaceId !== req.user?.workspaceId) {
      return res.status(403).json({ 
        error: 'Access denied',
        details: 'Cannot access resources from other workspaces'
      });
    }

    // If no workspace specified, default to user's workspace
    if (!requestedWorkspaceId) {
      req.query.workspaceId = req.user?.workspaceId;
      req.body.workspaceId = req.user?.workspaceId;
    }

    next();
  });
}