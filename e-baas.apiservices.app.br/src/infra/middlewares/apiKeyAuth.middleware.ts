import { Request, Response, NextFunction } from 'express';
import { ApiKeyService } from '../../modules/api-keys/api-key.service';
import { ApiKeyScope, ApiKeyType } from '../../modules/api-keys/dto/api-key.dto';

interface AuthenticatedRequest extends Request {
  apiKey?: {
    id: string;
    workspaceId: string;
    type: ApiKeyType;
    scopes: ApiKeyScope[];
  };
  workspaceId?: string;
}

export class ApiKeyAuthMiddleware {
  private apiKeyService: ApiKeyService;

  constructor() {
    this.apiKeyService = new ApiKeyService();
  }

  // Middleware to validate API keys
  validateApiKey = (requiredScope?: ApiKeyScope) => {
    return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      try {
        const apiKeyHeader = req.headers['apikey'] as string;
        const authHeader = req.headers['authorization'] as string;
        
        let apiKeyString: string | undefined;
        
        // Check for API key in headers (priority: apikey header, then Bearer token)
        if (apiKeyHeader) {
          apiKeyString = apiKeyHeader;
        } else if (authHeader && authHeader.startsWith('Bearer ')) {
          const token = authHeader.substring(7);
          // Check if this is an API key (not a JWT)
          if (token.startsWith('ebaas_')) {
            apiKeyString = token;
          }
        }

        if (!apiKeyString) {
          return res.status(401).json({ 
            error: 'API key required',
            hint: 'Provide API key in "apikey" header or as Bearer token'
          });
        }

        // Get client IP and origin
        const ipAddress = req.ip || req.connection.remoteAddress;
        const origin = req.headers.origin as string;

        // Validate the API key
        const validKey = await this.apiKeyService.validateApiKey(
          apiKeyString,
          requiredScope,
          ipAddress,
          origin
        );

        if (!validKey) {
          return res.status(401).json({ 
            error: 'Invalid or expired API key',
            hint: 'Check that your API key is correct and has the required permissions'
          });
        }

        // Attach key info to request
        req.apiKey = {
          id: validKey.id,
          workspaceId: validKey.workspaceId,
          type: validKey.type,
          scopes: validKey.scopes
        };
        req.workspaceId = validKey.workspaceId;

        next();
      } catch (error) {
        console.error('API key validation error:', error);
        return res.status(500).json({ 
          error: 'Authentication service unavailable' 
        });
      }
    };
  };

  // Middleware to require specific API key type
  requireKeyType = (allowedTypes: ApiKeyType[]) => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.apiKey) {
        return res.status(401).json({ 
          error: 'API key required' 
        });
      }

      if (!allowedTypes.includes(req.apiKey.type)) {
        return res.status(403).json({ 
          error: `Access denied. Required key types: ${allowedTypes.join(', ')}`,
          hint: `Your key type '${req.apiKey.type}' does not have permission for this operation`
        });
      }

      next();
    };
  };

  // Middleware to require specific scopes
  requireScope = (requiredScopes: ApiKeyScope[]) => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.apiKey) {
        return res.status(401).json({ 
          error: 'API key required' 
        });
      }

      const hasRequiredScope = requiredScopes.some(scope => 
        req.apiKey!.scopes.includes(ApiKeyScope.ALL) || 
        req.apiKey!.scopes.includes(scope)
      );

      if (!hasRequiredScope) {
        return res.status(403).json({ 
          error: `Access denied. Required scopes: ${requiredScopes.join(', ')}`,
          hint: `Your API key does not have the required permissions`
        });
      }

      next();
    };
  };

  // Middleware to ensure workspace isolation
  enforceWorkspaceIsolation = () => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.apiKey) {
        return res.status(401).json({ 
          error: 'API key required' 
        });
      }

      const requestWorkspaceId = req.query.workspaceId || req.body.workspaceId || req.params.workspaceId;
      
      if (requestWorkspaceId && requestWorkspaceId !== req.apiKey.workspaceId) {
        return res.status(403).json({ 
          error: 'Access denied. Workspace mismatch',
          hint: 'API key can only access resources in its assigned workspace'
        });
      }

      // Ensure workspace ID is available in request
      if (!requestWorkspaceId) {
        req.query.workspaceId = req.apiKey.workspaceId;
        req.body = { ...req.body, workspaceId: req.apiKey.workspaceId };
      }

      next();
    };
  };
}

// Create singleton instance
export const apiKeyAuthMiddleware = new ApiKeyAuthMiddleware();

// Export commonly used middleware combinations
export const requireAnonKey = [
  apiKeyAuthMiddleware.validateApiKey(),
  apiKeyAuthMiddleware.requireKeyType([ApiKeyType.ANON, ApiKeyType.AUTHENTICATED, ApiKeyType.SERVICE_ROLE])
];

export const requireAuthenticatedKey = [
  apiKeyAuthMiddleware.validateApiKey(),
  apiKeyAuthMiddleware.requireKeyType([ApiKeyType.AUTHENTICATED, ApiKeyType.SERVICE_ROLE])
];

export const requireServiceRoleKey = [
  apiKeyAuthMiddleware.validateApiKey(),
  apiKeyAuthMiddleware.requireKeyType([ApiKeyType.SERVICE_ROLE])
];

export const requireReadAccess = [
  apiKeyAuthMiddleware.validateApiKey(ApiKeyScope.READ),
  apiKeyAuthMiddleware.enforceWorkspaceIsolation()
];

export const requireWriteAccess = [
  apiKeyAuthMiddleware.validateApiKey(ApiKeyScope.WRITE),
  apiKeyAuthMiddleware.enforceWorkspaceIsolation()
];

export const requireDeleteAccess = [
  apiKeyAuthMiddleware.validateApiKey(ApiKeyScope.WRITE),
  apiKeyAuthMiddleware.enforceWorkspaceIsolation()
];

export const requireSchemaAccess = [
  apiKeyAuthMiddleware.validateApiKey(ApiKeyScope.DATABASE),
  apiKeyAuthMiddleware.enforceWorkspaceIsolation(),
  apiKeyAuthMiddleware.requireKeyType([ApiKeyType.SERVICE_ROLE])
];