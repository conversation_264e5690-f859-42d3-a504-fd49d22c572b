import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AuthService } from '../../modules/auth/auth.service';
import config from '../config';

interface JwtPayload {
  sub: string; // user ID
  email: string;
  role: string;
  workspaceId: string;
  aud: string;
  iss: string;
  iat: number;
  exp: number;
}

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
    workspaceId: string;
  };
  workspaceId?: string;
  jwt?: JwtPayload;
}

export class JwtAuthMiddleware {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  // Middleware to validate JWT tokens
  validateJwt = () => {
    return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      try {
        const authHeader = req.headers['authorization'] as string;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return res.status(401).json({ 
            error: 'Access token required',
            hint: 'Provide JWT token in Authorization header as "Bearer <token>"'
          });
        }

        const token = authHeader.substring(7);
        
        // Check if this looks like an API key (not a JWT)
        if (token.startsWith('ebaas_')) {
          return res.status(401).json({ 
            error: 'JWT token expected, API key provided',
            hint: 'Use the JWT authentication endpoint, not an API key'
          });
        }

        // Verify and decode the JWT
        const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;

        // Validate that the user still exists and is active
        const user = await this.authService.getUserById(decoded.sub);
        if (!user || !user.isActive) {
          return res.status(401).json({ 
            error: 'User account not found or inactive' 
          });
        }

        // Attach user info to request
        req.user = {
          id: user.id,
          email: user.email,
          role: user.role || 'authenticated',
          workspaceId: user.workspaceId
        };
        req.workspaceId = user.workspaceId;
        req.jwt = decoded;

        next();
      } catch (error) {
        if (error instanceof jwt.JsonWebTokenError) {
          return res.status(401).json({ 
            error: 'Invalid access token',
            hint: 'Token may be expired, malformed, or invalid'
          });
        }

        console.error('JWT validation error:', error);
        return res.status(500).json({ 
          error: 'Authentication service unavailable' 
        });
      }
    };
  };

  // Middleware to require specific roles
  requireRole = (allowedRoles: string[]) => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return res.status(401).json({ 
          error: 'Authentication required' 
        });
      }

      if (!allowedRoles.includes(req.user.role)) {
        return res.status(403).json({ 
          error: `Access denied. Required roles: ${allowedRoles.join(', ')}`,
          hint: `Your role '${req.user.role}' does not have permission for this operation`
        });
      }

      next();
    };
  };

  // Middleware to ensure workspace isolation for JWT
  enforceWorkspaceIsolation = () => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return res.status(401).json({ 
          error: 'Authentication required' 
        });
      }

      const requestWorkspaceId = req.query.workspaceId || req.body.workspaceId || req.params.workspaceId;
      
      if (requestWorkspaceId && requestWorkspaceId !== req.user.workspaceId) {
        return res.status(403).json({ 
          error: 'Access denied. Workspace mismatch',
          hint: 'User can only access resources in their assigned workspace'
        });
      }

      // Ensure workspace ID is available in request
      if (!requestWorkspaceId) {
        req.query.workspaceId = req.user.workspaceId;
        req.body = { ...req.body, workspaceId: req.user.workspaceId };
      }

      next();
    };
  };

  // Optional JWT middleware - doesn't fail if no token provided
  optionalJwt = () => {
    return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      try {
        const authHeader = req.headers['authorization'] as string;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return next(); // No token provided, continue without user context
        }

        const token = authHeader.substring(7);
        
        // Skip if this looks like an API key
        if (token.startsWith('ebaas_')) {
          return next();
        }

        // Try to verify and decode the JWT
        const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
        const user = await this.authService.getUserById(decoded.sub);
        
        if (user && user.isActive) {
          req.user = {
            id: user.id,
            email: user.email,
            role: user.role || 'authenticated',
            workspaceId: user.workspaceId
          };
          req.workspaceId = user.workspaceId;
          req.jwt = decoded;
        }

        next();
      } catch (error) {
        // If JWT is invalid, just continue without user context
        next();
      }
    };
  };

  // Create RLS context for database queries
  createRlsContext = () => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user && !req.jwt) {
        return next(); // No authentication context available
      }

      // Create JWT-compatible context for RLS policies
      const rlsContext = {
        'request.jwt.claims': {
          sub: req.user?.id || req.jwt?.sub,
          email: req.user?.email || req.jwt?.email,
          role: req.user?.role || req.jwt?.role || 'anon',
          workspace_id: req.user?.workspaceId || req.jwt?.workspaceId,
          aud: req.jwt?.aud || 'authenticated',
          iss: req.jwt?.iss || config.jwt.issuer || 'ebaas'
        }
      };

      // Attach RLS context to request for use in database operations
      (req as any).rlsContext = rlsContext;

      next();
    };
  };
}

// Create singleton instance
export const jwtAuthMiddleware = new JwtAuthMiddleware();

// Export commonly used middleware combinations
export const requireAuthenticated = [
  jwtAuthMiddleware.validateJwt(),
  jwtAuthMiddleware.enforceWorkspaceIsolation(),
  jwtAuthMiddleware.createRlsContext()
];

export const requireAdmin = [
  jwtAuthMiddleware.validateJwt(),
  jwtAuthMiddleware.requireRole(['admin', 'owner']),
  jwtAuthMiddleware.enforceWorkspaceIsolation(),
  jwtAuthMiddleware.createRlsContext()
];

export const requireOwner = [
  jwtAuthMiddleware.validateJwt(),
  jwtAuthMiddleware.requireRole(['owner']),
  jwtAuthMiddleware.enforceWorkspaceIsolation(),
  jwtAuthMiddleware.createRlsContext()
];

export const optionalAuth = [
  jwtAuthMiddleware.optionalJwt(),
  jwtAuthMiddleware.createRlsContext()
];