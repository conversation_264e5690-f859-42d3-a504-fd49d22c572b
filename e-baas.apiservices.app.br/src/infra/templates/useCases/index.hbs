import { {{pascalCase moduleName}} } from "./entity/{{moduleName}}.entity";
import { {{moduleName}}Repository } from "infra/repository";
import IContractUseCases from "infra/contracts";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "infra/errorHandlers";

export default class {{pascalCase moduleName}}UseCases implements IContractUseCases<{{pascalCase moduleName}}> {
    constructor() {}

    async getAll(): Promise<{{pascalCase moduleName}}[]> {
        try {
            return await {{moduleName}}Repository.find();
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async getOne(id: string): Promise<{{pascalCase moduleName}}> {
        try {
            const {{moduleName}} = await {{moduleName}}Repository.findOneBy({ id });
            if (!{{moduleName}}) {
                throw ErrorHandler.NotFound("{{pascalCase moduleName}} not found");
            }
            return {{moduleName}};
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "{{pascalCase moduleName}} not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async create(data: Partial<{{pascalCase moduleName}}>): Promise<{{pascalCase moduleName}}> {
        try {
            return await {{moduleName}}Repository.save(data);
        } catch (error: unknown) {
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async update(id: string, data: Partial<{{pascalCase moduleName}}>): Promise<{{pascalCase moduleName}}> {
        try {
            const {{moduleName}} = await this.getOne(id);
            await {{moduleName}}Repository.update(id, data);
            return { ...{{moduleName}}, ...data };
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "{{pascalCase moduleName}} not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await this.getOne(id);
            await {{moduleName}}Repository.delete(id);
        } catch (error: unknown) {
            if (error instanceof Error && error.message === "{{pascalCase moduleName}} not found") {
                throw error;
            }
            throw ErrorHandler.InternalServerError(error);
        }
    }
}