import cors from "cors";
import { AppDataSource } from "./infra/database/data-source";
import dotenv from "dotenv";
import express, { Express } from "express";
import { createServer } from "http";
import userRouter from "./modules/users/user.controller";
import authRouter from "./modules/auth/auth.controller";
import postgrestRouter from "./modules/postgrest/postgrest.controller";
import sqlExecutorRouter from "./modules/sqlExecutor/sqlExecutor.controller";
import rlsPoliciesRouter from "./modules/rlsPolicies";
import apiKeysRouter from "./modules/api-keys/api-key.controller";
import databaseManagementRouter from "./modules/database-management/database-management.controller";
import storageRouter from "./modules/storage/storage.controller";
import realtimeRouter from "./modules/realtime/realtime.controller";
import queuesRouter from "./modules/queues";
import mcpRouter from "./modules/mcp";
import { edgeFunctionsRouter } from "./modules/edge-functions";
import adminRouter from "./modules/admin/admin.controller";

dotenv.config();

const app: Express = express();
app.use(express.json());
app.use(
  cors({
    origin: "*",
  })
);

// Auth routes
app.use("/auth/v1", authRouter);

// API routes
app.use("/api/users", userRouter);
app.use("/rest/v1", postgrestRouter);
app.use("/sql/v1", sqlExecutorRouter);
app.use("/rls/v1", rlsPoliciesRouter);
app.use("/api-keys/v1", apiKeysRouter);
app.use("/database/v1", databaseManagementRouter);
app.use("/storage/v1", storageRouter);
app.use("/realtime/v1", realtimeRouter);
app.use("/queues/v1", queuesRouter);
app.use("/mcp/v1", mcpRouter);
app.use("/functions/v1", edgeFunctionsRouter);
app.use("/admin/v1", adminRouter);

app.use("/", (req, res) => {
  res.json({ message: "E-BaaS Development Server - API Status Ok", timestamp: new Date().toISOString() });
});

// Create HTTP server
const httpServer = createServer(app);

AppDataSource.initialize()
  .then(async () => {
    console.log("✅ Database connection initialized successfully!");

    // Skip Redis and WebSocket initialization for development
    console.log("⚠️  Skipping Redis and WebSocket initialization for development mode");

    const PORT = process.env.PORT || 3000;
    httpServer.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/`);
      console.log(`🔐 Auth endpoint: http://localhost:${PORT}/auth/v1`);
      console.log(`💾 Database endpoint: http://localhost:${PORT}/rest/v1`);
      console.log("✨ Backend server is ready!");
    });
  })
  .catch((error: Error) => {
    console.error("❌ Failed to initialize database:", error);
    process.exit(1);
  });

export default app;