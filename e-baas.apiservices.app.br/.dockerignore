node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.nyc_output
.DS_Store
*.log
dist
.vscode
.idea
*.swp
*.swo
*~
.tmp
.sass-cache
.cache
.parcel-cache
.next
.nuxt
.vuepress/dist
.serverless
.fusebox
.dynamodb
.tern-port
.env.local
.env.development.local
.env.test.local
.env.production.local
.npm
.eslintcache
.stylelintcache
.rpt2_cache
.rts2_cache_cjs
.rts2_cache_es
.rts2_cache_umd
.optional
.pnp
.pnp.js
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
Dockerfile
docker-compose.yml
.dockerignore
