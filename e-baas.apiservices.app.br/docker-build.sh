#!/bin/bash

# Script para build otimizado do E-BaaS com SDK privado
set -e

echo "🚀 Iniciando build do E-BaaS com SDK privado..."

# Verificar se o PAT_TOKEN está configurado
if [ -z "$PAT_TOKEN" ]; then
    echo "⚠️  PAT_TOKEN não encontrado nas variáveis de ambiente"
    echo "💡 Usando token hardcoded do docker-compose.yml"
fi

# Limpar containers e imagens antigas se existirem
echo "🧹 Limpando containers antigos..."
docker-compose down --remove-orphans 2>/dev/null || true

# Remover imagem antiga se existir
echo "🗑️  Removendo imagem antiga..."
docker rmi e-baasapiservicesappbr-app 2>/dev/null || true

# Build da nova imagem com cache limpo
echo "🔨 Construindo nova imagem com SDK privado..."
docker-compose build --no-cache app

# Verificar se o build foi bem-sucedido
if [ $? -eq 0 ]; then
    echo "✅ Build concluído com sucesso!"
    
    # Subir os serviços
    echo "🚀 Iniciando serviços..."
    docker-compose up -d
    
    # Aguardar um pouco para os serviços iniciarem
    echo "⏳ Aguardando serviços iniciarem..."
    sleep 15
    
    # Verificar status
    echo "📊 Status dos serviços:"
    docker-compose ps
    
    # Mostrar logs da aplicação
    echo "📋 Logs da aplicação (últimas 20 linhas):"
    docker-compose logs --tail=20 app
    
    echo ""
    echo "🎉 E-BaaS está rodando!"
    echo "🌐 API: http://localhost:3333"
    echo "📚 Swagger: http://localhost:3333/api-docs"
    echo "🔄 Queue API: http://localhost:3333/queues/v1"
    echo "🤖 MCP API: http://localhost:3333/mcp/v1"
    echo ""
    echo "📝 Para ver logs em tempo real: docker-compose logs -f app"
    echo "🛑 Para parar: docker-compose down"
    
else
    echo "❌ Falha no build! Verifique os logs acima."
    exit 1
fi
