FROM node:21-alpine

WORKDIR /usr/src/app

# Instalar dependências necessárias para sharp, bcrypt e outras packages nativas
RUN apk add --no-cache \
    python3 \
    py3-setuptools \
    make \
    g++ \
    gcc \
    git \
    libc6-compat \
    vips-dev \
    pkgconfig \
    cairo-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    curl

# Build arguments para PAT_TOKEN
ARG PAT_TOKEN

# Configurar git para usar token de acesso pessoal
RUN git config --global url."https://${PAT_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

# Copiar package files
COPY package*.json ./

# Instalar dependências
RUN npm install --verbose

# Copiar source code
COPY . .

# Build do TypeScript
RUN npm run build

# Criar diretórios necessários
RUN mkdir -p storage logs

# Expor porta
EXPOSE 3333

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3333/health || exit 1

# Usar usuário não-root para segurança
RUN addgroup -g 1001 -S nodejs
RUN adduser -S ebaas -u 1001
RUN chown -R ebaas:nodejs /usr/src/app
USER ebaas

CMD ["npm", "run", "start"] 