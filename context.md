# Documentação do Frontend - E-BaaS Admin

## Visão Geral

O frontend do E-BaaS Admin é uma aplicação React construída com Vite que serve como interface de administração para gerenciar projetos Supabase. A aplicação oferece funcionalidades como gerenciamento de banco de dados, tabelas, workspaces e configurações.

## Arquitetura Técnica

### Stack Principal

- **Framework**: React 18.3.1 com TypeScript
- **Build Tool**: Vite 5.4.1
- **Styling**: TailwindCSS com Tailwind Animate
- **UI Components**: Radix UI primitives com shadcn/ui
- **Roteamento**: React Router DOM 6.26.2
- **Estado Global**: React Context API
- **HTTP Client**: Fetch API nativo
- **Ícones**: Lucide React
- **Notificações**: Sonner + React Hook Form + Zod

### Estrutura de Pastas

```
src/
├── components/          # Componentes React
│   ├── ui/             # Componentes de UI (shadcn/ui)
│   ├── AppSidebar.tsx  # Barra lateral principal
│   ├── AuthWrapper.tsx # Wrapper de autenticação
│   ├── CodeEditor.tsx  # Editor de código
│   ├── Layout.tsx      # Layout principal
│   └── SupabaseConfig.tsx # Configuração Supabase
├── contexts/           # Contextos React
│   └── SupabaseContext.tsx # Contexto global Supabase
├── hooks/              # Custom hooks
│   ├── use-mobile.tsx  # Hook para detecção mobile
│   ├── use-toast.ts    # Hook de notificações
│   └── usePostgrest.ts # Hook para API PostgREST
├── lib/                # Utilitários
│   └── utils.ts        # Funções auxiliares
├── pages/              # Páginas da aplicação
│   ├── CloudFunctions.tsx
│   ├── Database.tsx
│   ├── Index.tsx
│   ├── NotFound.tsx
│   ├── ProjectOverview.tsx
│   ├── SQLEditor.tsx
│   ├── Storage.tsx
│   ├── SupabaseSettings.tsx
│   ├── TableEditor.tsx
│   └── Workspaces.tsx
├── App.tsx             # Componente principal
├── main.tsx            # Ponto de entrada
└── index.css           # Estilos globais
```

## Funcionalidades Principais

### 1. Gerenciamento de Conexão Supabase

- **Contexto**: `SupabaseContext` (src/contexts/SupabaseContext.tsx)
- **Funcionalidade**: Gerencia configurações de URL e API key do Supabase
- **Persistência**: LocalStorage para manter configurações entre sessões
- **Estado**: Indica se a conexão está configurada

### 2. Interface de Navegação

- **Sidebar**: `AppSidebar` com três seções organizadas:
  - **Project**: Overview, Table Editor, SQL Editor
  - **Database**: Database, Authentication, Storage, Realtime
  - **Management**: Workspaces, API Docs, Logs
- **Layout**: Header com status de conexão e botões de ação
- **Roteamento**: Navegação SPA com React Router

### 3. Integração PostgREST

- **Hook**: `usePostgrest` (src/hooks/usePostgrest.ts)
- **Operações**: CRUD completo (select, insert, update, delete)
- **Recursos**: Filtros, ordenação, paginação, RPC calls
- **SQL**: Execução de queries SQL customizadas

### 4. Sistema de UI

- **Design System**: shadcn/ui com componentes Radix UI
- **Tema**: Sistema de cores consistente com suporte a dark/light mode
- **Componentes**: 40+ componentes de UI reutilizáveis
- **Responsividade**: Layout adaptável para desktop e mobile

## Páginas e Funcionalidades

### Project Overview (/)

- Dashboard principal com estatísticas do projeto
- Links para bibliotecas client em diferentes linguagens
- Projetos exemplo e ações rápidas
- Cards informativos com métricas

### Table Editor (/table-editor)

- Interface para gerenciamento de tabelas
- CRUD de estruturas de banco de dados

### Database (/database)

- Visualização e gerenciamento do banco de dados
- Operações administrativas

### Workspaces (/workspaces)

- Gerenciamento de workspaces do projeto
- Organização de ambientes

### SQL Editor (/sql-editor)

- Editor de código SQL (Em desenvolvimento)
- Execução de queries customizadas

### Settings (/settings)

- Configurações do Supabase
- Gerenciamento de conexão

## Configuração e Build

### Scripts Disponíveis

```json
{
  "dev": "vite", // Servidor de desenvolvimento
  "build": "vite build", // Build de produção
  "build:dev": "vite build --mode development", // Build desenvolvimento
  "lint": "eslint .", // Linting
  "preview": "vite preview" // Preview do build
}
```

### Configurações

- **Vite**: Configurado para React SWC, alias de path (@/), porta 8080
- **TypeScript**: Configuração estrita com paths absolutos
- **ESLint**: Regras para React e TypeScript
- **TailwindCSS**: Classes utilitárias com tema customizado

## Estado da Aplicação

### Contexto Global

- **SupabaseContext**: Configuração e estado de conexão
- **QueryClient**: Cache e estado de queries (@tanstack/react-query)

### Hooks Customizados

- **useSupabase**: Acesso ao contexto Supabase
- **usePostgrest**: Operações com banco de dados
- **useToast**: Sistema de notificações
- **useMobile**: Detecção de dispositivos móveis

## Dependências Principais

### Core

- React 18.3.1 + React DOM
- TypeScript 5.5.3
- Vite 5.4.1

### UI/UX

- @radix-ui/\* (componentes primitivos)
- tailwindcss 3.4.11
- lucide-react (ícones)
- next-themes (gerenciamento de tema)

### Estado e Formulários

- @tanstack/react-query 5.56.2
- react-hook-form 7.53.0
- zod 3.23.8

### Supabase

- @supabase/supabase-js 2.49.8

## Padrões de Desenvolvimento

### Convenções de Código

- Componentes em PascalCase
- Hooks customizados com prefixo "use"
- TypeScript interfaces com sufixo "Type"
- Props tipadas com interfaces

### Estrutura de Componentes

- Componentes funcionais com hooks
- Props tipadas
- Separação clara entre lógica e apresentação
- Reutilização através de composição

### Gerenciamento de Estado

- Context API para estado global
- Custom hooks para lógica compartilhada
- React Query para cache de dados remotos
- LocalStorage para persistência local

## Status de Desenvolvimento

### Funcionalidades Implementadas

- ✅ Configuração Supabase
- ✅ Navegação e roteamento
- ✅ Layout responsivo
- ✅ Project Overview
- ✅ Sistema de UI completo
- ✅ Integração PostgREST

### Em Desenvolvimento

- 🚧 SQL Editor
- 🚧 Authentication
- 🚧 Storage
- 🚧 Realtime
- 🚧 API Documentation
- 🚧 Logs

### Próximos Passos

- Implementação completa do SQL Editor
- Sistema de autenticação
- Gerenciamento de arquivos (Storage)
- Dashboard de logs e métricas
- Documentação da API
